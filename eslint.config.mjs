import { sheriff, tseslint } from 'eslint-config-sheriff';
import customRules from './local-eslint-rules/index.mjs';
import eslintComments from '@eslint-community/eslint-plugin-eslint-comments';
import pluginLingui from 'eslint-plugin-lingui';

const sheriffConfig = tseslint.config(
    sheriff({
        pathsOverrides: {
            tests: [
                '**/*.{test,spec}.{js,jsx,mjs,cjs,ts,tsx,mts,cts}',
                '**/tests/**/*.{js,jsx,mjs,cjs,ts,tsx,mts,cts}',
                '**/__tests__/**/*.{js,jsx,mjs,cjs,ts,tsx,mts,cts}',
            ],
            playwrightTests: [
                '**/*.{test,spec}.{js,jsx,mjs,cjs,ts,tsx,mts,cts}',
                '**/tests/**/*.{js,jsx,mjs,cjs,ts,tsx,mts,cts}',
                '**/__tests__/**/*.{js,jsx,mjs,cjs,ts,tsx,mts,cts}',
            ],
            tsconfigLocation: './tsconfig.eslint.json',
        },
        astro: false,
        jest: false,
        lodash: true,
        next: false,
        playwright: false,
        react: true,
        remeda: false,
        storybook: true,
        vitest: true,
    }),
);

/** @type {import('typescript-eslint').ConfigArray} */
const config = [
    ...sheriffConfig,

    pluginLingui.configs['flat/recommended'],

    {
        settings: {
            'jsx-a11y': {
                components: {
                    Box: 'div',
                    Stack: 'div',
                },
            },
        },
    },
    {
        ignores: [
            'dist',
            '**/dist',
            'node_modules',
            '**/node_modules',
            '.git',
            '**/.git',
            '.nx',
            '**/.nx',
            '**/*.config.*.timestamp-*',
            '**/generated',
            'api',
            'globals/i18n/messages',
            '**/*.json',
        ],
    },
    {
        files: ['**/*.ts', '**/*.tsx', '**/*.js', '**/*.jsx'],
        rules: {
            'no-dupe-keys': 'error',
        },
    },
    {
        files: ['**/*.js', '**/*.jsx', '**/*.cjs', '**/*.mjs'],
        rules: {
            '@typescript-eslint/explicit-module-boundary-types': 'off',
        },
    },
    {
        files: ['**/*.ts', '**/.tsx'],
        rules: {
            '@typescript-eslint/explicit-module-boundary-types': [
                'error',
                {
                    allowDirectConstAssertionInArrowFunctions: true,
                    allowHigherOrderFunctions: true,
                    allowTypedFunctionExpressions: true,
                },
            ],
        },
    },
    {
        plugins: {
            '@eslint-community/eslint-comments': eslintComments,
        },
        files: [
            '**/*.ts',
            '**/*.tsx',
            '**/*.js',
            '**/*.jsx',
            '**/*.mjs',
            '**/*.cjs',
        ],
        // Override or add rules here
        rules: {
            'func-style': 'off',
            'logical-assignment-operators': 'off',

            'fsecond/prefer-destructured-optionals': 'off',

            'no-restricted-syntax/noClasses': 'off',
            'no-restricted-syntax/noProxy': 'off',

            'storybook/csf-component': 'off', // no idea what this even does, but it yells a lot on working code

            'sonarjs/no-duplicate-string': 'off',

            'jsdoc/no-types': 'off',

            'arrow-return-style/arrow-return-style': 'off', // buggy

            'import/prefer-default': 'off',
            'import/no-default-export': 'error',
            'import/no-duplicates': 'error',
            'import/no-cycle': 'off',

            'react/no-multi-comp': ['error', { ignoreStateless: true }],

            '@eslint-community/eslint-comments/require-description': 'error',

            '@typescript-eslint/no-deprecated': 'off', // just really slow :(
            '@typescript-eslint/no-unnecessary-type-parameters': 'off',
            '@typescript-eslint/naming-convention': 'off', // needs more fine-tuned configuring
            '@typescript-eslint/switch-exhaustiveness-check': [
                'error',
                {
                    allowDefaultCaseForExhaustiveSwitch: true,
                    considerDefaultExhaustiveForUnions: true,
                },
            ],
            '@typescript-eslint/restrict-template-expressions': [
                'error',
                { allowNullish: true },
            ],
            'no-restricted-imports': [
                'error',
                {
                    paths: [
                        {
                            name: '@globals/feature-access',
                            message:
                                'Use the FeatureAccessModel instead. https://www.notion.so/drata/Feature-Access-Feature-Flags-Entitlements-RBAC-1f8095aed4f180be8716fe570759e43a',
                            importNames: ['sharedFeatureFlagsController'],
                        },
                        {
                            name: 'mobx',
                            message: 'Import from @globals/mobx instead.',
                        },
                        {
                            name: 'mobx-react',
                            message: 'Import from @globals/mobx instead.',
                        },
                        {
                            name: '@tanstack/react-table',
                            message:
                                'Import from @cosmos/components/datatable instead.',
                        },
                        {
                            name: '@cosmos/components/link',
                            message:
                                "Import AppLink from '@ui/app-link' instead.",
                            importNames: ['Link'],
                        },
                        {
                            name: '@radix-ui/react-separator',
                            message:
                                "Import Divider from '@cosmos-lab/components/divider' instead.",
                            importNames: ['Separator'],
                        },
                        {
                            name: '@remix-run/react',
                            message:
                                "Import AppLink from '@ui/app-link' instead.",
                            importNames: ['Link'],
                        },
                        {
                            name: '@semcore/ui/select',
                            message:
                                'Importing from @semcore/ui/select is forbidden. It was introduced as a legacy allowance for the Oscal components but will be removed in the future.',
                        },
                        {
                            name: '@radix-ui/themes',
                            message:
                                "Import Grid from '@cosmos/components/grid' instead.",
                            importNames: ['Grid'],
                        },
                        {
                            name: '@radix-ui/themes',
                            message:
                                "Import Box from '@cosmos/components/box' instead.",
                            importNames: ['Box'],
                        },
                        {
                            name: '@radix-ui/themes',
                            message:
                                "Import Button from '@cosmos/components/button' instead.",
                            importNames: ['Button'],
                        },
                        {
                            name: '@mui/base',
                            message:
                                "Import Dropdown from '@cosmos/components/dropdown' instead.",
                            importNames: ['Dropdown'],
                        },
                        {
                            name: '@radix-ui/themes',
                            message:
                                "Import VisuallyHidden from '@cosmos/components/visually-hidden' instead.",
                            importNames: ['VisuallyHidden'],
                        },
                        {
                            name: '@radix-ui/themes',
                            message:
                                "Import TextField from '@cosmos/components/text-field' instead.",
                            importNames: ['TextField'],
                        },
                        {
                            name: '@radix-ui/themes',
                            message:
                                "Import Skeleton from '@cosmos/components/skeleton' instead.",
                            importNames: ['Skeleton'],
                        },
                        {
                            name: '@lingui/core',
                            message: 'Import from @globals/i18n instead.',
                        },
                        {
                            name: '@lingui/react',
                            message: 'Import from @globals/i18n instead.',
                        },
                        {
                            name: '@lingui/core/macro',
                            message: 'Import from @globals/i18n/macro instead.',
                        },
                        {
                            name: '@lingui/react/macro',
                            message: 'Import from @globals/i18n/macro instead.',
                        },
                        {
                            name: 'formik',
                            message:
                                'Formik is not allowed. It was introduced as a legacy allowance for the Oscal components but will be removed in the future.',
                        },
                    ],
                },
            ],
            '@typescript-eslint/no-misused-promises': [
                2,
                {
                    checksVoidReturn: {
                        attributes: false,
                        properties: false,
                    },
                },
            ],
            '@typescript-eslint/no-floating-promises': [
                'error',
                {
                    allowForKnownSafeCalls: [],
                    allowForKnownSafePromises: [],
                    checkThenables: false,
                    ignoreIIFE: false,
                    ignoreVoid: false,
                },
            ],

            'lodash-f/prefer-constant': 'off',

            'lingui/t-call-in-function': 'error',
            'lingui/no-single-tag-to-translate': 'error',
            'lingui/no-single-variables-to-translate': 'error',
            'lingui/no-trans-inside-trans': 'error',
            'lingui/no-expression-in-message': 'error',

            // CRITICAL: eslint-plugin-react-you-might-not-need-an-effect version compatibility fix
            //
            // Problem: eslint-config-sheriff v28.x references the old rule name 'you-might-not-need-an-effect'
            // from plugin v0.0.33, but we're using plugin v0.4.1 which completely restructured its rules.
            // The old rule name no longer exists, causing ESLint to crash with "Could not find rule" errors.
            //
            // Solution: Disable the non-existent old rule and manually enable all the new specific rules
            // from the updated plugin. This maintains the same linting behavior while fixing the crash.
            //
            // When to remove: When eslint-config-sheriff is updated to support the new plugin structure,
            // or when we upgrade to a sheriff version that's compatible with plugin v0.4.1+.
            // Check: https://github.com/AndreaPontrandolfo/sheriff/releases for compatibility updates.
            //
            // DO NOT remove the pnpm override for this plugin without updating this configuration!
            'react-you-might-not-need-an-effect/you-might-not-need-an-effect':
                'off', // Old rule name that no longer exists in v0.4.1+
            'react-you-might-not-need-an-effect/no-empty-effect': 'error',
            'react-you-might-not-need-an-effect/no-event-handler': 'error',
            'react-you-might-not-need-an-effect/no-initialize-state': 'error',
            'react-you-might-not-need-an-effect/no-derived-state': 'error',
            'react-you-might-not-need-an-effect/no-chain-state-updates':
                'error',
            'react-you-might-not-need-an-effect/no-pass-data-to-parent':
                'error',
            'react-you-might-not-need-an-effect/no-pass-live-state-to-parent':
                'error',
            'react-you-might-not-need-an-effect/no-manage-parent': 'error',
            'react-you-might-not-need-an-effect/no-reset-all-state-when-a-prop-changes':
                'error',
        },
    },
    {
        files: ['**/*.tsx'],
        rules: {
            '@typescript-eslint/no-floating-promises': 'off',
        },
    },
    {
        files: [
            '**/@types/**/*',
            '**/routes/**/*',
            '**/root.tsx',
            '**/root.server.tsx',
            '**/local-eslint-rules/**/*',
            '**/eslint.config.mjs',
            '**/*.config.*',
            '**/.storybook/**/*',
            '**/*.story.*',
            '**/*.stories.*',
        ],
        rules: {
            'react-refresh/only-export-components': 'off',
            'import/no-default-export': 'off', // needed for remix routes
        },
    },
    {
        files: [
            '**/lingui.config.ts',
            '**/babel.config.mjs',
            '**/vite.config.ts',
            'globals/i18n/**/*',
        ],
        rules: {
            'no-restricted-imports': 'off', // Allow direct Lingui imports in configuration files and i18n module
        },
    },
    {
        files: [
            'cosmos/**/*.story.*',
            'cosmos/**/*.stories.*',
            'cosmos-lab/**/*.story.*',
            'cosmos-lab/**/*.stories.*',
        ],
        rules: {
            'no-restricted-imports': [
                'error',
                {
                    paths: [
                        {
                            name: 'mobx',
                            message: 'Import from @globals/mobx instead.',
                        },
                        {
                            name: 'mobx-react',
                            message: 'Import from @globals/mobx instead.',
                        },
                        {
                            name: '@tanstack/react-table',
                            message:
                                'Import from @cosmos/components/datatable instead.',
                        },
                        // Note: @cosmos/components/link Link import is allowed in cosmos stories
                        {
                            name: '@radix-ui/react-separator',
                            message:
                                "Import Divider from '@cosmos-lab/components/divider' instead.",
                            importNames: ['Separator'],
                        },
                        {
                            name: '@remix-run/react',
                            message:
                                "Import AppLink from '@ui/app-link' instead.",
                            importNames: ['Link'],
                        },
                        {
                            name: '@semcore/ui/select',
                            message:
                                'Importing from @semcore/ui/select is forbidden.',
                        },
                        {
                            name: '@radix-ui/themes',
                            message:
                                "Import Grid from '@cosmos/components/grid' instead.",
                            importNames: ['Grid'],
                        },
                        {
                            name: '@radix-ui/themes',
                            message:
                                "Import Box from '@cosmos/components/box' instead.",
                            importNames: ['Box'],
                        },
                        {
                            name: '@radix-ui/themes',
                            message:
                                "Import Button from '@cosmos/components/button' instead.",
                            importNames: ['Button'],
                        },
                        {
                            name: '@mui/base',
                            message:
                                "Import Dropdown from '@cosmos/components/dropdown' instead.",
                            importNames: ['Dropdown'],
                        },
                        {
                            name: '@radix-ui/themes',
                            message:
                                "Import VisuallyHidden from '@cosmos/components/visually-hidden' instead.",
                            importNames: ['VisuallyHidden'],
                        },
                        {
                            name: '@radix-ui/themes',
                            message:
                                "Import TextField from '@cosmos/components/text-field' instead.",
                            importNames: ['TextField'],
                        },
                        {
                            name: '@radix-ui/themes',
                            message:
                                "Import Skeleton from '@cosmos/components/skeleton' instead.",
                            importNames: ['Skeleton'],
                        },
                        {
                            name: '@lingui/core',
                            message: 'Import from @globals/i18n instead.',
                        },
                        {
                            name: '@lingui/react',
                            message: 'Import from @globals/i18n instead.',
                        },
                        {
                            name: '@lingui/core/macro',
                            message: 'Import from @globals/i18n/macro instead.',
                        },
                        {
                            name: '@lingui/react/macro',
                            message: 'Import from @globals/i18n/macro instead.',
                        },
                        {
                            name: 'formik',
                            message:
                                'Formik is not allowed. Use alternative form libraries.',
                        },
                    ],
                },
            ],
        },
    },
    {
        files: [
            '**/*.ts',
            '**/*.tsx',
            '**/*.js',
            '**/*.jsx',
            '**/*.cjs',
            '**/*.mjs',
        ],
        plugins: { custom: customRules },
        rules: {
            'custom/button-loading-props': 'error',
            'custom/enforce-data-testid': 'warn',
            'custom/enforce-data-id': 'warn',
            'custom/no-dataid-prop': 'warn',
            'custom/link-string-only-children': 'error',
            'custom/datatable-require-getrowid': 'warn',
            'custom/datatable-prefer-row-actions': 'warn',
            'custom/no-t-in-iife': 'error',
            'custom/no-direct-dom-manipulation': 'error',
            'custom/no-events-on-noninteractive': [
                'error',
                {
                    targets: ['Box', 'Stack', 'div', 'span'],
                    forbiddenEvents: [
                        'onClick',
                        'onDoubleClick',
                        'onAuxClick',
                        'onPointerDown',
                        'onPointerUp',
                        'onTouchStart',
                        'onTouchEnd',
                        'onFocus',
                    ],
                    // Allow a last-resort escape hatch only when full a11y is present.
                    // If you MUST, add role="button", tabIndex, and keyboard handlers.
                    allowEscapeHatchWithFullA11y: false,
                },
            ],
        },
    },
];

export default config;
