import { find, isEmpty } from 'lodash-es';
import type { AllowedBackgroundToken } from '@cosmos/components/box';
import type { DataPostureBox } from '@cosmos-lab/components/data-posture';
import type { RiskScoreSeverity } from '@cosmos-lab/components/risk-score';
import type {
    DashboardResponseDto,
    RiskThresholdResponseDto,
} from '@globals/api-sdk/types';
import {
    DEFAULT_RISK_LEVEL_COLOR,
    INTENSITIES_BY_THRESHOLDS,
    RISK_CALCULATION_CONSTANTS,
    RISK_LEVEL_COLOR_MAPPINGS,
} from './constants/risk-score-helper.constants';
import type { ValidThresholdRange } from './types/risk-score-helper.types';

export const isValidThresholdLength = (
    length: number,
): length is keyof ValidThresholdRange => length >= 2 && length <= 5;

export const generateRiskPostureBoxes = (
    riskPosture: DashboardResponseDto['riskPosture'],
    thresholds: RiskThresholdResponseDto[],
): DataPostureBox[] => {
    const keys = Object.keys(riskPosture);

    return isEmpty(keys)
        ? []
        : keys
              .filter((key: string) => Number(riskPosture[key]) > 0)
              .map((key: string) => {
                  const splittedKey = key.split('_');
                  const thresholdId = Number(splittedKey[2]);

                  // Should always be a threshold
                  const threshold = thresholds.find(
                      (t) => t.id === thresholdId,
                  );

                  const value = Number(riskPosture[key] ?? 0);

                  return {
                      id: `${thresholdId}`,
                      value,
                      color: threshold?.color ?? 'neutral100',
                  } satisfies DataPostureBox;
              })
              .sort((a, b) => Number(a.id) - Number(b.id));
};

export const getScoreIntensityByThresholds = (
    score: number | null | undefined,
    thresholds: RiskThresholdResponseDto[],
): RiskScoreSeverity => {
    if (!score || isEmpty(thresholds)) {
        return 'low';
    }

    const thresholdRange = find(
        thresholds,
        ({ minThreshold, maxThreshold }) =>
            score >= minThreshold && score <= maxThreshold,
    );

    if (!thresholdRange) {
        return 'critical';
    }

    const thresholdLength = thresholds.length;

    if (!isValidThresholdLength(thresholdLength)) {
        throw new Error(
            `Threshold length must be between 2 and 5. Current length: ${thresholdLength}`,
        );
    }

    const thresholdIndex = thresholds.indexOf(thresholdRange);

    return INTENSITIES_BY_THRESHOLDS[thresholdLength][thresholdIndex];
};

export const mapColorToBackgroundToken = (
    color?: string,
): AllowedBackgroundToken | undefined => {
    if (!color) {
        return undefined;
    }

    const normalizedColor = color.toLowerCase();
    const hexColor = normalizedColor.startsWith('#')
        ? normalizedColor
        : `#${normalizedColor}`;

    return RISK_LEVEL_COLOR_MAPPINGS[hexColor] ?? DEFAULT_RISK_LEVEL_COLOR;
};

export {
    DEFAULT_RISK_LEVEL_COLOR,
    RISK_CALCULATION_CONSTANTS,
    RISK_LEVEL_COLOR_MAPPINGS,
};
