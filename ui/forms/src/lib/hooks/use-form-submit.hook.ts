import { useCallback, useMemo, useRef } from 'react';
import type { FormValues } from '../types/form-values.type';

interface FormRefHandle extends HTMLFormElement {
    submitForm: () => Promise<boolean>;
    clearForm: () => void;
    resetForm: (values?: FormValues, options?: Record<string, boolean>) => void;
    getValues: () => FormValues;
    setValues: (values: FormValues) => void;
}

export interface UseFormSubmitReturn {
    formRef: React.RefObject<FormRefHandle>;
    triggerSubmit: () => Promise<boolean>;
    triggerClearForm: () => void;
    triggerResetForm: (
        values?: FormValues,
        options?: Record<string, boolean>,
    ) => void;
    getFormValues: () => FormValues | null;
}

export const useFormSubmit = (): UseFormSubmitReturn => {
    const formRef = useRef<FormRefHandle>(null);

    const triggerSubmit = useCallback(async (): Promise<boolean> => {
        if (!formRef.current?.submitForm) {
            return Promise.resolve(false);
        }

        // Still return the promise, allowing callers to handle errors if needed
        return formRef.current.submitForm();
    }, []);

    /**
     * @deprecated Use triggerResetForm() instead. This function will be removed in a future version.
     */
    const triggerClearForm = useCallback(() => {
        const form = formRef.current;

        if (form) {
            form.clearForm();
        }
    }, []);

    const triggerResetForm = useCallback(
        (values?: FormValues, options?: Record<string, boolean>) => {
            const form = formRef.current;

            if (form) {
                form.resetForm(values, options);
            }
        },
        [],
    );

    const getFormValues = useCallback((): FormValues | null => {
        const form = formRef.current;

        if (form?.getValues) {
            return form.getValues();
        }

        return null;
    }, []);

    return useMemo(
        () => ({
            formRef,
            triggerSubmit,
            triggerClearForm,
            triggerResetForm,
            getFormValues,
        }),
        [triggerClearForm, triggerResetForm, triggerSubmit, getFormValues],
    );
};
