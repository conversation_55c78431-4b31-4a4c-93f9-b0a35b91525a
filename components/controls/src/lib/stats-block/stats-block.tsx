import { isEmpty, isNil } from 'lodash-es';
import { Card } from '@cosmos/components/card';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { DataLegend } from '@cosmos-lab/components/data-legend';
import type { StatsBlockProps } from '../types/stats-block.types';

export const StatsBlock = ({
    'data-id': dataId,
    'data-testid': dataTestId,
    isLoading,
    title,
    iconName,
    iconColor,
    statusLabel,
    legendValues,
    emptyStateProps,
}: StatsBlockProps): React.JSX.Element => {
    const showEmptyState = isEmpty(legendValues);
    const { title: emptyStateTitle, leftAction } = emptyStateProps;

    return (
        <Card
            data-id={dataId}
            data-testid={dataTestId}
            title={title}
            isLoading={isLoading}
            body={
                <>
                    {!isLoading && !showEmptyState && (
                        <Stack gap="4x">
                            <DataLegend data={legendValues}>
                                <Stack gap="2x" align="center">
                                    {iconName && (
                                        <Icon
                                            name={iconName}
                                            colorScheme={iconColor}
                                            backgroundType="round"
                                            size="200"
                                        />
                                    )}
                                    <Text type="headline" size="400">
                                        {statusLabel}
                                    </Text>
                                </Stack>
                            </DataLegend>
                        </Stack>
                    )}
                    {!isLoading && showEmptyState && (
                        <Stack direction="column" gap="2xl">
                            <Text
                                type="subheadline"
                                size="300"
                                colorScheme="neutral"
                                as="h1"
                                data-id={`${dataId}-empty-state-title`}
                                data-testid={`${dataTestId}-empty-state-title`}
                            >
                                {emptyStateTitle}
                            </Text>
                            {!isNil(leftAction) && (
                                <Stack align="center">{leftAction}</Stack>
                            )}
                        </Stack>
                    )}
                </>
            }
        />
    );
};
