import { useEffect } from 'react';
import {
    KeyValuePair,
    type MetadataValue,
} from '@cosmos/components/key-value-pair';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { action, runInAction } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    type CustomFieldRenderProps,
    UniversalFormField,
    useUniversalFieldController,
} from '@ui/forms';
import { unmapObjectFromControlModel } from '../models/unmap-object-from-control.model';

export const LinkedWorkspacesComboboxField = ({
    'data-id': dataId,
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    const [linkedWorkspaces] = useUniversalFieldController<'combobox'>(name);

    const currentWorkspace = action((): MetadataValue[] => {
        if (!sharedWorkspacesController.currentWorkspace) {
            return [];
        }

        return [
            {
                label: sharedWorkspacesController.currentWorkspace.name,
                colorScheme: 'neutral',
                type: 'tag',
            },
        ];
    })();

    useEffect(() => {
        runInAction(() => {
            unmapObjectFromControlModel.currentValue =
                linkedWorkspaces.value as ListBoxItemData[];
        });
    }, [linkedWorkspaces.value]);

    return (
        <Stack
            direction="column"
            gap="xl"
            data-testid="LinkedWorkspacesComboboxField"
            data-id="3v16946O"
        >
            <UniversalFormField
                __fromCustomRender
                formId={formId}
                name={name}
                data-id={dataId}
            />
            <KeyValuePair
                label={t`Current workspace`}
                type="TAG"
                value={currentWorkspace}
            />
        </Stack>
    );
};
