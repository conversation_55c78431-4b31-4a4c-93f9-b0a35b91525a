import { isEmpty } from 'lodash-es';
import {
    ControlEvidenceReadinessCard,
    ControlMonitoringReadinessCard,
    ControlPoliciesReadinessCard,
} from '@components/controls';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedControlPanelModel } from '../model/control-panel.model';
import { ReviewAndApprovalCard } from './review-and-approval-card/review-and-approval-card';

export const ReadinessSection = observer((): React.JSX.Element => {
    const { controlReviewers, shouldDisplayMonitoringCard } =
        sharedControlPanelModel;

    return (
        <Stack
            direction="column"
            gap="2xl"
            data-id="twwP9EaA"
            data-testid="ReadinessSection"
        >
            <Text type="title" size="400">
                <Trans>Readiness</Trans>
            </Text>
            <Grid
                columns={shouldDisplayMonitoringCard ? '3' : '2'}
                gap="4x"
                pb="md"
            >
                <ControlEvidenceReadinessCard />
                {shouldDisplayMonitoringCard && (
                    <ControlMonitoringReadinessCard />
                )}
                <ControlPoliciesReadinessCard />
            </Grid>
            {!isEmpty(controlReviewers) && <ReviewAndApprovalCard />}
        </Stack>
    );
});
