import { isNil } from 'lodash-es';
import { sharedControlApprovalsController } from '@controllers/controls';
import { Card } from '@cosmos/components/card';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { getFullName, getInitials } from '@helpers/formatters';
import { controlReviewCardModel } from '@models/controls';
import { REVIEW_APPROVAL_STATUS } from '../../../constants/review-approval-status.constant';
import { getStageColor } from '../../../helpers/get-stage-color';
import { getStageTextLabel } from '../../../helpers/get-stage-text-label';
import { sharedControlPanelModel } from '../../model/control-panel.model';

export const ReviewAndApprovalCard = observer((): React.JSX.Element => {
    const { currentControlApproval } = sharedControlApprovalsController;
    const { deadlineCurrentValue } = controlReviewCardModel;

    const { controlReviewers } = sharedControlPanelModel;

    const controlApprovalStatusIsInitialize =
        isNil(currentControlApproval) ||
        currentControlApproval.approvalStatus ===
            REVIEW_APPROVAL_STATUS.INITIALIZE;

    const cardBody = controlApprovalStatusIsInitialize ? null : (
        <Stack direction="column" gap="xl">
            <KeyValuePair
                label={t`Stage`}
                type="BADGE"
                value={{
                    label: getStageTextLabel(
                        currentControlApproval.approvalStatus,
                    ),
                    colorScheme: getStageColor(
                        currentControlApproval.approvalStatus,
                    ),
                }}
            />
            <Stack direction="column" align="end" gap="md">
                <StackedList>
                    {controlReviewers.map((reviewer) => (
                        <StackedListItem
                            key={reviewer.id}
                            data-id="Cx14w5u2"
                            primaryColumn={
                                <AvatarIdentity
                                    fallbackText={getInitials(
                                        getFullName(
                                            reviewer.reviewer.firstName,
                                            reviewer.reviewer.lastName,
                                        ),
                                    )}
                                    primaryLabel={getFullName(
                                        reviewer.reviewer.firstName,
                                        reviewer.reviewer.lastName,
                                    )}
                                />
                            }
                        />
                    ))}
                    <Divider />
                </StackedList>
            </Stack>
            <KeyValuePair
                label={t`Approval deadline`}
                value={formatDate('sentence', deadlineCurrentValue)}
            />
        </Stack>
    );

    return (
        <Card
            title={t`Review and approval`}
            data-testid="ReviewAndApprovalCard"
            data-id="jwbR7qpL"
            body={cardBody}
        />
    );
});
