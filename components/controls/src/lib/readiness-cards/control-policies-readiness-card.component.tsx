import { StatsBlock } from '@components/controls';
import { Button } from '@cosmos/components/button';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedControlsDetailsReadinessCardsData } from '@models/controls';
import { useNavigate } from '@remix-run/react';

export const ControlPoliciesReadinessCard = observer((): React.JSX.Element => {
    const { isLoading, controlId, policiesReadinessCardData } =
        sharedControlsDetailsReadinessCardsData;
    const { dataLegendValues, statusLabel, iconName, iconColor } =
        policiesReadinessCardData;
    const navigate = useNavigate();
    const { currentWorkspaceId } = sharedWorkspacesController;
    const { hasWriteControlPermission } = sharedFeatureAccessModel;

    const emptyStateProps = {
        title: t`Controls help enforce your policies.`,
        ...(hasWriteControlPermission && {
            leftAction: (
                <Button
                    label={t`Link policies`}
                    level="secondary"
                    onClick={() => {
                        navigate(
                            `/workspaces/${currentWorkspaceId}/compliance/controls/${controlId}/policies`,
                        );
                    }}
                />
            ),
        }),
    };

    return (
        <StatsBlock
            data-id="policies-stats-block"
            data-testid="PoliciesStatsBlock"
            title={t`Policies`}
            isLoading={isLoading}
            iconName={iconName}
            iconColor={iconColor}
            statusLabel={statusLabel}
            legendValues={dataLegendValues}
            emptyStateProps={emptyStateProps}
        />
    );
});
