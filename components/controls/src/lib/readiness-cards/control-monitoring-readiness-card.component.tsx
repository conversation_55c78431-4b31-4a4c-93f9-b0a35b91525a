import { StatsBlock } from '@components/controls';
import { Button } from '@cosmos/components/button';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedControlsDetailsReadinessCardsData } from '@models/controls';
import { useNavigate } from '@remix-run/react';

export const ControlMonitoringReadinessCard = observer(
    (): React.JSX.Element => {
        const { isLoading, controlId, monitoringReadinessCardData } =
            sharedControlsDetailsReadinessCardsData;
        const { dataLegendValues, statusLabel, iconName, iconColor } =
            monitoringReadinessCardData;
        const navigate = useNavigate();
        const { currentWorkspaceId } = sharedWorkspacesController;
        const { hasWriteControlPermission } = sharedFeatureAccessModel;

        const emptyStateProps = {
            title: t`Continuously monitor the effectiveness of your control.`,
            ...(hasWriteControlPermission && {
                leftAction: (
                    <Button
                        label={t`Map tests`}
                        level="secondary"
                        onClick={() => {
                            navigate(
                                `/workspaces/${currentWorkspaceId}/compliance/controls/${controlId}/monitoring`,
                            );
                        }}
                    />
                ),
            }),
        };

        return (
            <StatsBlock
                data-id="monitoring-stats-block"
                data-testid="MonitoringStatsBlock"
                title={t`Monitoring`}
                isLoading={isLoading}
                iconName={iconName}
                iconColor={iconColor}
                statusLabel={statusLabel}
                legendValues={dataLegendValues}
                emptyStateProps={emptyStateProps}
            />
        );
    },
);
