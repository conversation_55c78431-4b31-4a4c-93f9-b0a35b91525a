import React from 'react';
import {
    closeMonitorUpdateExclusionReasonBulkModal,
    openMonitorUpdateExclusionReasonBulkModal,
} from '@components/monitor-update-exclusion-reason-modal';
import { openMonitoringRemoveExclusionConfirmationModal } from '@components/monitoring-remove-exclusions-confirmation-modal';
import { sharedMonitoringCodeExclusionsController } from '@controllers/monitoring';
import {
    activeMonitoringController,
    sharedCodebaseFindingExclusionMutationController,
} from '@controllers/monitoring-details';
import { Button } from '@cosmos/components/button';
import type {
    BulkAction,
    DatatableRef,
    DatatableRowSelectionState,
    ExtendedDataTableColumnDef,
} from '@cosmos/components/datatable';
import type { EmptyStateProps } from '@cosmos/components/empty-state';
import type { MonitorCodebaseExclusionResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import { MonitoringCodebaseActionsCell } from '../cells/monitoring-codebase-action-cell';
import { MonitoringCodebaseDateExcludedCell } from '../cells/monitoring-codebase-date-exclude-cell';
import { MonitoringCodebaseExcludedByCell } from '../cells/monitoring-codebase-excluded-by-cell';

class MonitoringDetailsCodebaseExclusionsTableModel {
    selectedTargetIds: number[] = [];
    isAllRowsSelected = false;
    datatableRef: React.RefObject<DatatableRef> =
        React.createRef<DatatableRef>();

    constructor() {
        makeAutoObservable(this);
    }

    get bulkActions(): BulkAction[] {
        return [
            {
                actionType: 'button',
                id: 'bulk-actions-update-exclusion',
                typeProps: {
                    label: t`Update reason`,
                    level: 'tertiary',
                    onClick: action(() => {
                        openMonitorUpdateExclusionReasonBulkModal({
                            onConfirm: (reason: string) => {
                                const { testId } =
                                    sharedMonitoringCodeExclusionsController;

                                if (!testId) {
                                    return;
                                }

                                sharedCodebaseFindingExclusionMutationController.updateExclusionReasonBulk(
                                    testId,
                                    this.selectedTargetIds,
                                    reason,
                                    () => {
                                        closeMonitorUpdateExclusionReasonBulkModal();
                                        this.datatableRef.current?.resetRowSelection();
                                    },
                                );
                            },
                        });
                    }),
                },
            },
            {
                actionType: 'button',
                id: 'bulk-actions-remove-exclusions',
                typeProps: {
                    label: t`Remove exclusions`,
                    level: 'tertiary',
                    onClick: action(() => {
                        openMonitoringRemoveExclusionConfirmationModal({
                            onConfirm: this.handleRemoveExclusionsConfirm,
                        });
                    }),
                },
            },
        ];
    }

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;
        const selectedIds = Object.keys(selectedRows);

        this.selectedTargetIds = selectedIds.map(Number);
        this.isAllRowsSelected = isAllRowsSelected;
    };

    handleRemoveExclusionsConfirm = (): void => {
        const { testId } = sharedMonitoringCodeExclusionsController;

        if (!testId) {
            return;
        }

        sharedCodebaseFindingExclusionMutationController.removeExclusionsBulk({
            testId,
            targetIds: this.selectedTargetIds,
            onSuccess: () => {
                this.datatableRef.current?.resetRowSelection();
            },
        });
    };

    get isRemovingExclusions(): boolean {
        return sharedCodebaseFindingExclusionMutationController.isRemovingExclusionsBulk;
    }

    get emptyStateProps(): EmptyStateProps {
        const { lastTestResult } = activeMonitoringController;

        if (lastTestResult === 'ERROR') {
            return {
                illustrationName: 'Warning',
                title: t`Cannot display exclusions due to error state.`,
                description: t`This test was unable to generate findings due to an error that prevented it from running. Please review the details of the error to resolve it and try again.`,
                rightAction: (
                    <Button label={t`View error details`} level="secondary" />
                ),
            };
        }

        return {
            illustrationName: 'NoAccess',
            title: t`No exclusions made`,
            description: t`[Exclusions made will appear here]`,
        };
    }

    get columns(): ExtendedDataTableColumnDef<MonitorCodebaseExclusionResponseDto>[] {
        return [
            {
                id: 'ACTIONS',
                header: '',
                enableSorting: false,
                isActionColumn: true,
                cell: MonitoringCodebaseActionsCell,
            },
            {
                id: 'CODEBASE_REPOSITORY',
                header: t`Repository`,
                accessorKey: 'repositoryName',
                enableSorting: true,
                isActionColumn: true,
            },
            {
                id: 'CODEBASE_RESOURCE',
                header: t`Resource name`,
                accessorKey: 'resourceName',
                enableSorting: true,
                isActionColumn: true,
            },
            {
                id: 'CODEBASE_PROPERTY',
                header: t`Property`,
                accessorKey: 'propertyName',
                enableSorting: true,
                isActionColumn: true,
            },
            {
                id: 'CREATED_BY',
                header: t`Excluded by`,
                enableSorting: true,
                isActionColumn: true,
                cell: MonitoringCodebaseExcludedByCell,
            },
            {
                id: 'START_DATE',
                header: t`Date excluded`,
                enableSorting: true,
                isActionColumn: true,
                cell: MonitoringCodebaseDateExcludedCell,
            },
            {
                id: 'EXCLUSION_REASON',
                header: t`Reason`,
                accessorKey: 'reason',
                enableSorting: true,
                isActionColumn: true,
            },
        ];
    }
}

export const sharedMonitoringDetailsCodebaseExclusionsTableModel =
    new MonitoringDetailsCodebaseExclusionsTableModel();
