import type { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedChangeAuditPeriodController } from '@controllers/change-audit-period';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';

interface ChangeAuditPeriodModalProps {
    auditFramework: NonNullable<
        typeof sharedAuditHubController.auditByIdData
    >['framework'];
    onClose: () => void;
}

const getFormSchema = (): FormSchema => ({
    auditPeriod: {
        type: 'dateRange',
        label: t`Update audit period`,
        initialValue: {
            start: null,
            end: null,
        },
        locale: 'en-US',
    },
});

export const ChangeAuditPeriodModal = observer(
    ({
        auditFramework,
        onClose,
    }: ChangeAuditPeriodModalProps): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();

        const handleSubmit = (values: FormValues) => {
            const auditPeriod = values.auditPeriod as {
                start: TDateISODate | null;
                end: TDateISODate | null;
            };

            sharedChangeAuditPeriodController.updateAuditPeriod(
                auditFramework.id,
                auditPeriod.start,
                auditPeriod.end,
                onClose,
            );
        };

        return (
            <>
                <Modal.Header
                    title={t`Change audit period`}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={onClose}
                />
                <Modal.Body>
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        data-id="change-audit-period-form"
                        formId="change-audit-period-form"
                        schema={getFormSchema()}
                        onSubmit={handleSubmit}
                    />
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: onClose,
                            cosmosUseWithCaution_isDisabled:
                                sharedChangeAuditPeriodController.isUpdating,
                        },
                        {
                            label: t`Save`,
                            level: 'primary',
                            onClick: triggerSubmit,
                            isLoading:
                                sharedChangeAuditPeriodController.isUpdating,
                            a11yLoadingLabel: t`Saving...`,
                        },
                    ]}
                />
            </>
        );
    },
);
