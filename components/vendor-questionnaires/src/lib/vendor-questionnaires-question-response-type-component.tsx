import { isNil, isNumber, noop } from 'lodash-es';
import { useMemo } from 'react';
import { Grid } from '@cosmos/components/grid';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { SelectField } from '@cosmos/components/select-field';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    FORM_ID,
    RESPONSE_TYPE_OPTIONS,
    SHORT_ANSWER_TYPE_OPTIONS,
} from './constants/vendor-questionnaires.constant';
import { createQuestionSelectHandler } from './helpers/vendor-questionnaire-handlers.helper';
import { VendorQuestionnaireQuestionModel } from './models/vendor-questionnaire-question.model';
import type { VendorQuestionnairesQuestionResponseTypeProps } from './types/vendor-questionnaires.type';

export const VendorQuestionnairesQuestionResponseTypeComponent = observer(
    ({
        item,
        isCreateMode = false,
        questionIndex,
    }: VendorQuestionnairesQuestionResponseTypeProps): React.JSX.Element => {
        const { type } = item;
        const { shortAnswerType } = item;

        // Create model instance for validation logic
        const questionModel = useMemo(
            () => new VendorQuestionnaireQuestionModel(item),
            [item],
        );

        const typeValue: ListBoxItemData | undefined = useMemo(
            () =>
                isNil(type)
                    ? undefined
                    : RESPONSE_TYPE_OPTIONS.find(
                          (option) => option.value === type,
                      ),
            [type],
        );

        const shortAnswerValue: ListBoxItemData | undefined = useMemo(
            () =>
                isNil(shortAnswerType)
                    ? undefined
                    : SHORT_ANSWER_TYPE_OPTIONS.find(
                          (option) => option.value === shortAnswerType,
                      ),
            [shortAnswerType],
        );

        const handleTypeChange = isNumber(questionIndex)
            ? createQuestionSelectHandler(questionIndex, 'type')
            : noop;

        const handleShortAnswerTypeChange = isNumber(questionIndex)
            ? createQuestionSelectHandler(questionIndex, 'shortAnswerType')
            : noop;

        return (
            <Grid
                gap={'6x'}
                data-testid="VendorQuestionnairesQuestionResponseTypeComponent"
                data-id="y6RlICX8"
            >
                <SelectField
                    data-id={`${FORM_ID}-responseType`}
                    formId={FORM_ID}
                    label={t`Response type`}
                    loaderLabel={t`Loading...`}
                    name="responseType"
                    options={RESPONSE_TYPE_OPTIONS}
                    value={typeValue}
                    onChange={isCreateMode ? handleTypeChange : noop}
                />
                {!isNil(type) &&
                    String(type).toLowerCase() === 'short_answer' && (
                        <SelectField
                            data-id={`${FORM_ID}-shortAnswerType`}
                            formId={FORM_ID}
                            label={t`Short Answer Type`}
                            loaderLabel={t`Loading...`}
                            name="shortAnswerType"
                            options={SHORT_ANSWER_TYPE_OPTIONS}
                            value={shortAnswerValue}
                            feedback={
                                questionModel.hasShortAnswerTypeError
                                    ? {
                                          type: 'error',
                                          message:
                                              questionModel.shortAnswerTypeErrorMessage ||
                                              '',
                                      }
                                    : undefined
                            }
                            onChange={
                                isCreateMode
                                    ? handleShortAnswerTypeChange
                                    : noop
                            }
                        />
                    )}
            </Grid>
        );
    },
);
