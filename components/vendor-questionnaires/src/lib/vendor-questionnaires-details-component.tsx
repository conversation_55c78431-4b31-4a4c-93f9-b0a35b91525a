import { noop } from 'lodash-es';
import {
    sharedVendorsQuestionnaireAddController,
    sharedVendorsTypeformQuestionnaireController,
} from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { CheckboxFieldGroup } from '@cosmos/components/checkbox-field-group';
import { Grid } from '@cosmos/components/grid';
import { TextField } from '@cosmos/components/text-field';
import { t } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';
import {
    FORM_ID,
    VENDOR_QUESTIONNAIRE_CATEGORY_CHECKBOX_OPTIONS,
    VENDOR_RISK_OPTIONS,
} from './constants/vendor-questionnaires.constant';
import {
    createFormFieldHandler,
    createFormFieldTextHandler,
} from './helpers/vendor-questionnaire-handlers.helper';

interface VendorQuestionnairesDetailsComponentProps {
    isCreateMode?: boolean;
}

export const VendorQuestionnairesDetailsComponent = observer(
    ({
        isCreateMode = false,
    }: VendorQuestionnairesDetailsComponentProps = {}): React.JSX.Element => {
        const readController = sharedVendorsTypeformQuestionnaireController;
        const createController = sharedVendorsQuestionnaireAddController;

        // Create handlers using helper functions
        const handleTitleChange = createFormFieldTextHandler('title');
        const handleCategoriesChange = createFormFieldHandler('categories');
        const handleRiskLevelsChange = createFormFieldHandler('riskLevels');

        return (
            <Grid
                gap={'6x'}
                data-testid="VendorQuestionnairesDetailsComponent"
                data-id="eg3O4_n3"
            >
                <Grid gap={'6x'} width="auto">
                    <TextField
                        data-id={`${FORM_ID}-name`}
                        formId={FORM_ID}
                        label={t`Questionnaire name`}
                        name="name"
                        value={
                            isCreateMode
                                ? createController.formModel.formData.title
                                : (readController.vendorsQuestionnaireQuery.data
                                      ?.title ?? '')
                        }
                        feedback={
                            isCreateMode &&
                            createController.titleValidationError
                                ? {
                                      type: 'error',
                                      message:
                                          createController.titleValidationError,
                                  }
                                : undefined
                        }
                        onChange={isCreateMode ? handleTitleChange : noop}
                    />
                </Grid>
                <Grid columns="2" gap={'6x'}>
                    <Box>
                        <CheckboxFieldGroup
                            selectAll
                            cosmosUseWithCaution_forceOptionOrientation="vertical"
                            data-id={`${FORM_ID}-categories`}
                            formId={FORM_ID}
                            label={t`Categories`}
                            name="categories"
                            value={
                                isCreateMode
                                    ? toJS(
                                          createController.formModel.formData
                                              .categories,
                                      )
                                    : readController.categories
                            }
                            options={
                                VENDOR_QUESTIONNAIRE_CATEGORY_CHECKBOX_OPTIONS
                            }
                            onChange={
                                isCreateMode ? handleCategoriesChange : noop
                            }
                        />
                    </Box>
                    <Box>
                        <CheckboxFieldGroup
                            selectAll
                            cosmosUseWithCaution_forceOptionOrientation="vertical"
                            data-id={`${FORM_ID}-risk-levels`}
                            formId={FORM_ID}
                            label={t`Risk Levels`}
                            name="riskLevels"
                            options={VENDOR_RISK_OPTIONS}
                            value={
                                isCreateMode
                                    ? toJS(
                                          createController.formModel.formData
                                              .riskLevels,
                                      )
                                    : readController.riskLevels
                            }
                            onChange={
                                isCreateMode ? handleRiskLevelsChange : noop
                            }
                        />
                    </Box>
                </Grid>
            </Grid>
        );
    },
);
