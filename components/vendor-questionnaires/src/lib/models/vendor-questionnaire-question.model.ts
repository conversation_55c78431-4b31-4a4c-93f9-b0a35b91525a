import { isEmpty } from 'lodash-es';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { VendorQuestionnaireFieldItem } from '../types/vendor-questionnaires.type';

/**
 * Model for vendor questionnaire question with validation logic.
 */
export class VendorQuestionnaireQuestionModel {
    ref: string;
    id?: string;
    title: string;
    type: string;
    required: boolean;
    shortAnswerType?: string;
    choices?: { ref: string; label: string }[];
    followUpQn?: string;
    allowOtherChoice?: boolean;
    includeFollowUpQn?: boolean;
    followUpQnTrigger?: boolean;

    constructor(data: VendorQuestionnaireFieldItem) {
        this.ref = data.ref;
        this.id = data.id;
        this.title = data.title;
        this.type = data.type;
        this.required = data.required;
        this.shortAnswerType = data.shortAnswerType;
        this.choices = data.choices;
        this.followUpQn = data.followUpQn;
        this.allowOtherChoice = data.allowOtherChoice;
        this.includeFollowUpQn = data.includeFollowUpQn;
        this.followUpQnTrigger = data.followUpQnTrigger;

        makeAutoObservable(this);
    }

    get isShortAnswerTypeRequired(): boolean {
        return this.type === 'SHORT_ANSWER';
    }

    get hasShortAnswerTypeError(): boolean {
        return this.isShortAnswerTypeRequired && isEmpty(this.shortAnswerType);
    }

    get shortAnswerTypeErrorMessage(): string | undefined {
        return this.hasShortAnswerTypeError
            ? t`Select a short answer type`
            : undefined;
    }

    /**
     * Additional computed properties for other validations.
     */
    get isChoiceTypeRequired(): boolean {
        return this.type === 'MULTIPLE_CHOICE' || this.type === 'SINGLE_CHOICE';
    }

    get hasChoicesError(): boolean {
        return (
            this.isChoiceTypeRequired &&
            (!this.choices || isEmpty(this.choices))
        );
    }

    get choicesErrorMessage(): string | undefined {
        return this.hasChoicesError
            ? t`Add at least one choice option`
            : undefined;
    }

    get isTitleValid(): boolean {
        return !isEmpty(this.title.trim());
    }

    get titleErrorMessage(): string | undefined {
        return this.isTitleValid ? undefined : t`Question title is required`;
    }

    /**
     * Overall validation state.
     */
    get hasValidationErrors(): boolean {
        return (
            this.hasShortAnswerTypeError ||
            this.hasChoicesError ||
            !this.isTitleValid
        );
    }

    get validationErrors(): string[] {
        const errors: string[] = [];

        if (this.titleErrorMessage) {
            errors.push(this.titleErrorMessage);
        }

        if (this.shortAnswerTypeErrorMessage) {
            errors.push(this.shortAnswerTypeErrorMessage);
        }

        if (this.choicesErrorMessage) {
            errors.push(this.choicesErrorMessage);
        }

        return errors;
    }

    /**
     * Data transformation methods.
     */
    toJSON(): VendorQuestionnaireFieldItem {
        return {
            ref: this.ref,
            id: this.id,
            title: this.title,
            type: this.type,
            required: this.required,
            shortAnswerType: this.shortAnswerType,
            choices: this.choices,
            followUpQn: this.followUpQn,
            allowOtherChoice: this.allowOtherChoice,
            includeFollowUpQn: this.includeFollowUpQn,
            followUpQnTrigger: this.followUpQnTrigger,
        };
    }
}
