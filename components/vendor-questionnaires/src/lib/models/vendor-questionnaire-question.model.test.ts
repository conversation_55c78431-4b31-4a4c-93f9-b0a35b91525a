import { describe, expect, test } from 'vitest';
import type { VendorQuestionnaireFieldItem } from '../types/vendor-questionnaires.type';
import { VendorQuestionnaireQuestionModel } from './vendor-questionnaire-question.model';

const createMockQuestion = (
    overrides: Partial<VendorQuestionnaireFieldItem> = {},
): VendorQuestionnaireFieldItem => ({
    ref: 'test-ref',
    title: 'Test Question',
    type: 'TEXT',
    required: false,
    ...overrides,
});

describe('vendorQuestionnaireQuestionModel', () => {
    describe('isShortAnswerTypeRequired', () => {
        test('should return true when type is SHORT_ANSWER', () => {
            const question = createMockQuestion({ type: 'SHORT_ANSWER' });
            const model = new VendorQuestionnaireQuestionModel(question);

            expect(model.isShortAnswerTypeRequired).toBeTruthy();
        });

        test('should return false when type is not SHORT_ANSWER', () => {
            const question = createMockQuestion({ type: 'TEXT' });
            const model = new VendorQuestionnaireQuestionModel(question);

            expect(model.isShortAnswerTypeRequired).toBeFalsy();
        });
    });

    describe('hasShortAnswerTypeError', () => {
        test('should return true when SHORT_ANSWER type is required but shortAnswerType is empty', () => {
            const question = createMockQuestion({
                type: 'SHORT_ANSWER',
                shortAnswerType: undefined,
            });
            const model = new VendorQuestionnaireQuestionModel(question);

            expect(model.hasShortAnswerTypeError).toBeTruthy();
        });

        test('should return false when SHORT_ANSWER type is required and shortAnswerType is provided', () => {
            const question = createMockQuestion({
                type: 'SHORT_ANSWER',
                shortAnswerType: 'TEXT',
            });
            const model = new VendorQuestionnaireQuestionModel(question);

            expect(model.hasShortAnswerTypeError).toBeFalsy();
        });

        test('should return false when type is not SHORT_ANSWER', () => {
            const question = createMockQuestion({
                type: 'TEXT',
                shortAnswerType: undefined,
            });
            const model = new VendorQuestionnaireQuestionModel(question);

            expect(model.hasShortAnswerTypeError).toBeFalsy();
        });
    });

    describe('shortAnswerTypeErrorMessage', () => {
        test('should return error message when there is a short answer type error', () => {
            const question = createMockQuestion({
                type: 'SHORT_ANSWER',
                shortAnswerType: undefined,
            });
            const model = new VendorQuestionnaireQuestionModel(question);

            expect(model.shortAnswerTypeErrorMessage).toBeDefined();
        });

        test('should return undefined when there is no error', () => {
            const question = createMockQuestion({
                type: 'SHORT_ANSWER',
                shortAnswerType: 'TEXT',
            });
            const model = new VendorQuestionnaireQuestionModel(question);

            expect(model.shortAnswerTypeErrorMessage).toBeUndefined();
        });
    });

    describe('hasValidationErrors', () => {
        test('should return true when there are validation errors', () => {
            const question = createMockQuestion({
                type: 'SHORT_ANSWER',
                shortAnswerType: undefined,
                title: '',
            });
            const model = new VendorQuestionnaireQuestionModel(question);

            expect(model.hasValidationErrors).toBeTruthy();
        });

        test('should return false when there are no validation errors', () => {
            const question = createMockQuestion({
                type: 'SHORT_ANSWER',
                shortAnswerType: 'TEXT',
                title: 'Valid Title',
            });
            const model = new VendorQuestionnaireQuestionModel(question);

            expect(model.hasValidationErrors).toBeFalsy();
        });
    });

    describe('toJSON', () => {
        test('should return the original data structure', () => {
            const originalData: VendorQuestionnaireFieldItem = {
                ref: 'test-ref',
                id: 'test-id',
                title: 'Test Question',
                type: 'SHORT_ANSWER',
                required: true,
                shortAnswerType: 'TEXT',
                choices: [{ ref: 'choice-1', label: 'Choice 1' }],
                followUpQn: 'Follow up question',
                allowOtherChoice: true,
                includeFollowUpQn: true,
                followUpQnTrigger: false,
            };
            const model = new VendorQuestionnaireQuestionModel(originalData);

            expect(model.toJSON()).toStrictEqual(originalData);
        });
    });
});
