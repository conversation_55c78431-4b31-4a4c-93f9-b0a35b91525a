import { isEmpty, omit } from 'lodash-es';
import { sharedVendorsQuestionnaireAddController } from '@controllers/vendors';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { QuestionnaireFieldChoiceRequestDto } from '@globals/api-sdk/types';
import { action } from '@globals/mobx';
import type {
    QuestionPropertyKey,
    VendorQuestionnaireFieldChoice,
    VendorQuestionnaireFieldItem,
} from '../types/vendor-questionnaires.type';

// Type for questions from the controller
type ControllerQuestionType =
    (typeof sharedVendorsQuestionnaireAddController.formModel.formData.questions)[0];

// Type for form field keys
type FormFieldKey =
    keyof typeof sharedVendorsQuestionnaireAddController.formModel.formData;

// Type for form data to ensure type safety
type QuestionnaireFormData =
    typeof sharedVendorsQuestionnaireAddController.formModel.formData;

/**
 * Creates a handler for updating form fields in the questionnaire controller.
 */
export const createFormFieldHandler = <K extends FormFieldKey>(
    field: K,
): ((value: QuestionnaireFormData[K]) => void) =>
    action((value: QuestionnaireFormData[K]) => {
        sharedVendorsQuestionnaireAddController.formModel.updateFormField(
            field,
            value,
        );
    });

/**
 * Creates a handler for text input changes on form fields.
 */
export const createFormFieldTextHandler = (
    field: FormFieldKey,
): ((event: React.ChangeEvent<HTMLInputElement>) => void) => {
    return action((event: React.ChangeEvent<HTMLInputElement>) => {
        const { value } = event.target;

        sharedVendorsQuestionnaireAddController.formModel.updateFormField(
            field,
            value,
        );

        if (
            field === 'title' &&
            'clearTitleValidationError' in
                sharedVendorsQuestionnaireAddController
        ) {
            sharedVendorsQuestionnaireAddController.clearTitleValidationError();
        }
    });
};

/**
 * Creates a handler for updating question properties.
 */
export const createQuestionUpdateHandler = <
    T extends
        | string
        | boolean
        | QuestionnaireFieldChoiceRequestDto[]
        | undefined,
>(
    questionIndex: number,
    property: QuestionPropertyKey,
): ((value: T) => void) =>
    action((value: T) => {
        sharedVendorsQuestionnaireAddController.formModel.updateQuestion(
            questionIndex,
            property,
            value,
        );
    });

/**
 * Creates a handler for text input changes on questions.
 */
export const createQuestionTextHandler = (
    questionIndex: number,
    property: QuestionPropertyKey,
): ((event: React.ChangeEvent<HTMLInputElement>) => void) =>
    action((event: React.ChangeEvent<HTMLInputElement>) => {
        sharedVendorsQuestionnaireAddController.formModel.updateQuestion(
            questionIndex,
            property,
            event.target.value,
        );
    });

/**
 * Creates a handler for boolean toggle on questions.
 */
export const createQuestionToggleHandler = (
    questionIndex: number,
    property: QuestionPropertyKey,
    currentValue: boolean,
): (() => void) =>
    action(() => {
        sharedVendorsQuestionnaireAddController.formModel.updateQuestion(
            questionIndex,
            property,
            !currentValue,
        );
    });

/**
 * Creates a handler for select field changes on questions.
 */
export const createQuestionSelectHandler = (
    questionIndex: number,
    property: QuestionPropertyKey,
): ((value: ListBoxItemData | undefined) => void) =>
    action((value: ListBoxItemData | undefined) => {
        if (!value) {
            return;
        }

        sharedVendorsQuestionnaireAddController.formModel.updateQuestion(
            questionIndex,
            property,
            value.value,
        );

        // If changing question type to MULTIPLE_CHOICE or CHECKBOXES,
        // ensure there's at least one choice
        if (
            property === 'type' &&
            (value.value === 'MULTIPLE_CHOICE' || value.value === 'CHECKBOXES')
        ) {
            const currentQuestion =
                sharedVendorsQuestionnaireAddController.formModel.formData
                    .questions[questionIndex];

            // If no choices exist or choices array is empty, create a default choice
            if (isEmpty(currentQuestion.choices)) {
                const defaultChoice = {
                    ref: `choice-${Date.now()}`,
                    label: '',
                };

                sharedVendorsQuestionnaireAddController.formModel.updateQuestion(
                    questionIndex,
                    'choices',
                    [defaultChoice],
                );
            }
        }
    });

/**
 * Creates a handler for removing a question.
 */
export const createQuestionRemoveHandler = (
    questionIndex: number,
): (() => void) =>
    action(() => {
        sharedVendorsQuestionnaireAddController.formModel.removeQuestion(
            questionIndex,
        );
    });

/**
 * Creates a handler for copying a question.
 */
export const createQuestionCopyHandler = (
    questionIndex: number,
    item: VendorQuestionnaireFieldItem,
): (() => void) =>
    action(() => {
        const questionCopy = omit(item, 'id');

        questionCopy.title = `${questionCopy.title} (Copy)`;
        sharedVendorsQuestionnaireAddController.formModel.formData.questions.splice(
            questionIndex + 1,
            0,
            questionCopy as ControllerQuestionType,
        );
    });

/**
 * Creates a handler for adding a new choice to a question.
 */
export const createChoiceAddHandler = (questionIndex: number): (() => void) =>
    action(() => {
        const newChoice = {
            ref: `choice-${Date.now()}`,
            label: '',
        };
        const currentChoices =
            sharedVendorsQuestionnaireAddController.formModel.formData
                .questions[questionIndex].choices ?? [];

        sharedVendorsQuestionnaireAddController.formModel.updateQuestion(
            questionIndex,
            'choices',
            [...currentChoices, newChoice],
        );
    });

/**
 * Creates a handler for deleting a choice from a question.
 */
export const createChoiceDeleteHandler = (
    questionIndex: number,
): ((choiceToDelete: VendorQuestionnaireFieldChoice) => void) =>
    action((choiceToDelete: VendorQuestionnaireFieldChoice) => {
        const currentChoices =
            sharedVendorsQuestionnaireAddController.formModel.formData
                .questions[questionIndex].choices ?? [];
        const updatedChoices = currentChoices.filter(
            (choice: VendorQuestionnaireFieldChoice) =>
                choice.ref !== choiceToDelete.ref,
        );

        sharedVendorsQuestionnaireAddController.formModel.updateQuestion(
            questionIndex,
            'choices',
            updatedChoices,
        );
    });

/**
 * Creates a handler for updating a choice label.
 */
export const createChoiceUpdateHandler = (
    questionIndex: number,
): ((choiceIndex: number, newLabel: string) => void) =>
    action((choiceIndex: number, newLabel: string) => {
        const currentChoices = [
            ...(sharedVendorsQuestionnaireAddController.formModel.formData
                .questions[questionIndex].choices ?? []),
        ];

        if (currentChoices[choiceIndex]) {
            currentChoices[choiceIndex] = {
                ...currentChoices[choiceIndex],
                label: newLabel,
            };
            sharedVendorsQuestionnaireAddController.formModel.updateQuestion(
                questionIndex,
                'choices',
                currentChoices,
            );
        }
    });

/**
 * Creates a handler for toggling "mark all as required".
 */
export const createMarkAllAsRequiredHandler = (): (() => void) =>
    action(() => {
        sharedVendorsQuestionnaireAddController.formModel.toggleMarkAllAsRequired();
    });

/**
 * Creates a handler for follow-up question trigger changes.
 */
export const createFollowUpTriggerHandler = (
    questionIndex: number,
): ((event: React.ChangeEvent<HTMLInputElement>) => void) =>
    action((event: React.ChangeEvent<HTMLInputElement>) => {
        sharedVendorsQuestionnaireAddController.formModel.updateQuestion(
            questionIndex,
            'followUpQnTrigger',
            event.target.value === 'yes',
        );
    });
