import { isEmpty } from 'lodash-es';
import {
    sharedRiskInsightsController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { sharedRiskInsightsOverTimeController } from '@controllers/risk-insights-over-time';
import { Box } from '@cosmos/components/box';
import { Skeleton } from '@cosmos/components/skeleton';
import { DataLine } from '@cosmos-lab/components/data-line';
import { observer } from '@globals/mobx';
import { getRiskLevelDataColor } from './helpers/risk-level-color.helper';

export const RiskInsightsOverTimeChart = observer(() => {
    const { data, availableRiskLevels } = sharedRiskInsightsOverTimeController;
    const { isLoading } = sharedRiskInsightsController;
    const { riskSettings } = sharedRiskSettingsController;

    if (isLoading) {
        return (
            <Box
                data-testid="risk-insights-over-time-chart-skeleton"
                data-id="risk-insights-over-time-chart-skeleton"
                style={{ height: 300 }}
            >
                <Skeleton />
            </Box>
        );
    }

    if (!data || isEmpty(availableRiskLevels)) {
        return null;
    }

    const chartData = data.data;
    const thresholds = riskSettings?.thresholds ?? [];

    const lines = availableRiskLevels.map((level) => {
        const color = getRiskLevelDataColor(level, thresholds);

        return {
            dataKey: level,
            stroke: color,
            strokeWidth: 3,
            type: 'linear' as const,
            dot: false,
            activeDot: true,
        };
    });

    return (
        <DataLine
            data={chartData}
            categoryKey="month"
            lines={lines}
            height={300}
            data-testid="risk-insights-over-time-chart"
            data-id="risk-insights-over-time-chart"
        />
    );
});
