import { isEmpty } from 'lodash-es';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { Skeleton } from '@cosmos/components/skeleton';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    getRiskTreatmentLabel,
    getTreatmentColor,
    getTreatmentIcon,
} from '@helpers/risk-treatment';
// eslint-disable-next-line no-restricted-imports -- Official use case of @remix-run/react
import { Link as RemixLink } from '@remix-run/react';
import type { TreatmentOverviewCardProps } from './risk-insights-treatment-overview-card';

const TREATMENT_TYPES = ['ACCEPT', 'AVOID', 'MITIGATE', 'TRANSFER'] as const;

export const TreatmentOverviewCardPresentation = observer(
    ({
        isLoading,
        treatmentOverview,
        untreatedValue,
        onDownloadClick,
        onStatBlockClick,
    }: TreatmentOverviewCardProps): React.JSX.Element => {
        const { currentWorkspaceId, isLoading: isWorkspaceLoading } =
            sharedWorkspacesController;

        const isDataLoading = isLoading || isWorkspaceLoading;

        return (
            <Card
                title={t`Treatment overview`}
                tooltipText={t`The treatment overview shows how you're addressing risks and how many still need to be addressed.`}
                data-testid="TreatmentOverviewCardPresentation"
                data-id="o96LYHxl"
                body={
                    isDataLoading ? (
                        <Skeleton barCount={5} />
                    ) : (
                        <Box
                            data-id="q6JvnF-e"
                            borderColor="neutralBorderFaded"
                            p="lg"
                            width="100%"
                        >
                            <Grid
                                gap="md"
                                align="stretch"
                                columns={{
                                    initial: '1',
                                    sm: '2',
                                    md: '3',
                                    lg: '5',
                                }}
                            >
                                <StatBlock
                                    isInteractive
                                    title={getRiskTreatmentLabel('UNTREATED')}
                                    statValue={
                                        untreatedValue || (
                                            <EmptyValue label="--" />
                                        )
                                    }
                                    statIcon={getTreatmentIcon(
                                        'UNTREATED',
                                        untreatedValue,
                                    )}
                                    statIconColor={getTreatmentColor(
                                        'UNTREATED',
                                        untreatedValue,
                                    )}
                                    onClick={
                                        untreatedValue > 0
                                            ? onStatBlockClick
                                            : undefined
                                    }
                                />
                                {TREATMENT_TYPES.map((type) => (
                                    <StatBlock
                                        isInteractive
                                        key={type}
                                        as={RemixLink}
                                        /*
                                         * TODO: this navigation has to go to /risk/management/registers/[register_id]/register-risks/[task-id]
                                         * That created a problem because we don't have the register_id outside of /risk/management/registers/[register_id].
                                         *
                                         * At the moment it was decided to hardcode the register_id to 1 since that's all tenants will have.
                                         *
                                         * See: https://drata.atlassian.net/browse/ENG-72803
                                         */
                                        to={`/workspaces/${currentWorkspaceId}/risk/management/registers/1/register-risks`}
                                        title={getRiskTreatmentLabel(type)}
                                        data-id="aCfZhtEg"
                                        statValue={
                                            treatmentOverview[type] || (
                                                <EmptyValue label="--" />
                                            )
                                        }
                                    />
                                ))}
                            </Grid>
                        </Box>
                    )
                }
                actions={[
                    {
                        actionType: 'button',
                        id: 'download-treatment-overview-button',
                        typeProps: {
                            isIconOnly: true,
                            cosmosUseWithCaution_isDisabled:
                                isEmpty(treatmentOverview),
                            label: 'Download',
                            startIconName: 'Download',
                            level: 'tertiary',
                            onClick: onDownloadClick,
                        },
                    },
                ]}
            />
        );
    },
);
