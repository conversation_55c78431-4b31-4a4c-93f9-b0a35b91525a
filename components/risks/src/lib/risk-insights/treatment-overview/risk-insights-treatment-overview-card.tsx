import { useCallback } from 'react';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import {
    sharedRiskInsightsController,
    sharedRiskInsightsDownloadController,
} from '@controllers/risk';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { TreatmentOverviewCardPresentation } from './treatment-overview-presentation';

export interface TreatmentOverviewCardProps {
    isLoading: boolean;
    treatmentOverview: { [key: string]: unknown };
    untreatedValue: number;
    onDownloadClick: () => void;
    onStatBlockClick: () => void;
}

export const TreatmentOverviewCard = observer((): React.JSX.Element => {
    const { riskInsights, isLoading } = sharedRiskInsightsController;
    const { downloadRiskInsightsReport } = sharedRiskInsightsDownloadController;

    const handleDownloadClick = () => {
        downloadRiskInsightsReport('TREATMENT_OVERVIEW');
    };

    const treatmentOverview = riskInsights?.treatmentOverview ?? {};
    const untreatedValue = Number(treatmentOverview.UNTREATED) || 0;

    const { currentWorkspace } = sharedWorkspacesController;

    const handleOnClick = useCallback(() => {
        if (!currentWorkspace) {
            return;
        }
        /*
         * TODO: this navigation has to go to /risk/management/registers/[register_id]/register-risks/[task-id]
         * That created a problem because we don't have the register_id outside of /risk/management/registers/[register_id].
         *
         * At the moment it was decided to hardcode the register_id to 1 since that's all tenants will have.
         *
         * See: https://drata.atlassian.net/browse/ENG-72803
         */
        sharedProgrammaticNavigationController.navigateTo(
            `/workspaces/${currentWorkspace.id}/risk/management/registers/1/register-risks`,
        );
    }, [currentWorkspace]);

    return (
        <TreatmentOverviewCardPresentation
            isLoading={isLoading}
            treatmentOverview={treatmentOverview}
            untreatedValue={untreatedValue}
            data-id="ViubRv6y"
            onDownloadClick={handleDownloadClick}
            onStatBlockClick={handleOnClick}
        />
    );
});
