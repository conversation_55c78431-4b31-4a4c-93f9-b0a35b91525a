import { isNil } from 'lodash-es';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import { Metadata } from '@cosmos/components/metadata';
import { PanelHeader } from '@cosmos/components/panel';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useParams } from '@remix-run/react';

export const RiskRegisterPanelHeader = observer((): React.JSX.Element => {
    const { riskDetails } = sharedRiskDetailsController;
    const { riskId, title } = riskDetails ?? {};
    const { currentWorkspace } = sharedWorkspacesController;
    const params = useParams();

    return (
        <PanelHeader
            title={title ?? ''}
            data-id="b4-p6GSH"
            openPageLink={
                isNil(riskId)
                    ? undefined
                    : `/workspaces/${currentWorkspace?.id}/risk/management/registers/${params.registerId}/risks/${riskId}/overview`
            }
            slot={
                <Metadata
                    colorScheme="neutral"
                    label={riskId ?? ''}
                    type="tag"
                />
            }
        />
    );
});
