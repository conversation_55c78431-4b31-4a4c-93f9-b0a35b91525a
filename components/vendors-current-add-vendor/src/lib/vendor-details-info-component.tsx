import { Grid } from '@cosmos/components/grid';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Text } from '@cosmos/components/text';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { VendorResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { transformPasswordPolicyToLabel } from '../helpers/vendor-add-vendor.helper';

interface VendorDetailsInfoComponentProps {
    state: VendorResponseDto | null;
}

export const VendorDetailsInfoComponent = ({
    state,
}: VendorDetailsInfoComponentProps): React.JSX.Element => {
    return (
        <Grid
            gap="4x"
            data-testid="VendorDetailsInfoComponent"
            data-id="9FgIBPDr"
        >
            <KeyValuePair
                label={t`Vendor name`}
                type="REACT_NODE"
                value={
                    state?.name ? (
                        <Text>{state.name}</Text>
                    ) : (
                        <EmptyValue label={t`Vendor name`} />
                    )
                }
            />
            <KeyValuePair
                label={t`Website URL`}
                type="REACT_NODE"
                value={
                    state?.url ? (
                        <Text>{state.url}</Text>
                    ) : (
                        <EmptyValue label={t`Website URL`} />
                    )
                }
            />
            <KeyValuePair
                label={t`Provided services`}
                type="REACT_NODE"
                value={
                    state?.servicesProvided ? (
                        <Text>{state.servicesProvided}</Text>
                    ) : (
                        <EmptyValue label={t`Provided services`} />
                    )
                }
            />
            <KeyValuePair
                label={t`Password policy`}
                type="REACT_NODE"
                value={
                    state?.passwordPolicy ? (
                        <Text>
                            {transformPasswordPolicyToLabel(
                                state.passwordPolicy,
                            )}
                        </Text>
                    ) : (
                        <EmptyValue label={t`Password policy`} />
                    )
                }
            />
            <KeyValuePair
                label={t`Trust Center URL`}
                type="REACT_NODE"
                value={
                    state?.trustCenterUrl ? (
                        <Text>{state.trustCenterUrl}</Text>
                    ) : (
                        <EmptyValue label={t`Trust Center URL`} />
                    )
                }
            />
            <KeyValuePair
                label={t`Privacy policy URL`}
                type="REACT_NODE"
                value={
                    state?.privacyUrl ? (
                        <Text>{state.privacyUrl}</Text>
                    ) : (
                        <EmptyValue label={t`Privacy policy URL`} />
                    )
                }
            />
            <KeyValuePair
                label={t`Terms of use URL`}
                type="REACT_NODE"
                value={
                    state?.termsUrl ? (
                        <Text>{state.termsUrl}</Text>
                    ) : (
                        <EmptyValue label={t`Terms of use URL`} />
                    )
                }
            />
            <KeyValuePair
                label={t`Vendor contact name`}
                type="REACT_NODE"
                value={
                    state?.contactAtVendor ? (
                        <Text>{state.contactAtVendor}</Text>
                    ) : (
                        <EmptyValue label={t`Vendor contact name`} />
                    )
                }
            />
            <KeyValuePair
                label={t`Vendor contact email address`}
                type="REACT_NODE"
                value={
                    state?.contactsEmail ? (
                        <Text>{state.contactsEmail}</Text>
                    ) : (
                        <EmptyValue label={t`Vendor contact email address`} />
                    )
                }
            />
        </Grid>
    );
};
