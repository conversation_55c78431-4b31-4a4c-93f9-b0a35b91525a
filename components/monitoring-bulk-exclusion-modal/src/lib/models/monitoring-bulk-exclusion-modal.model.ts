import { isEmpty, isError } from 'lodash-es';
import { z } from 'zod';
import { sharedFindingExclusionMutationController } from '@controllers/monitoring-details';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable } from '@globals/mobx';
import type { FormSchema, FormValues } from '@ui/forms';

export class MonitoringBulkExclusionModalModel {
    constructor() {
        makeAutoObservable(this);
    }

    get isSubmitting(): boolean {
        return sharedFindingExclusionMutationController.createExclusionMutation
            .isPending;
    }

    get formSchema(): FormSchema {
        return {
            reason: {
                type: 'textarea',
                label: t`Exclusion reason`,
                placeholder: t`Provide a reason for excluding these findings...`,
                helpText: t`This reason will be surfaced on audit download packages`,
                validator: z
                    .string()
                    .min(1, t`Exclusion reason is required`)
                    .max(
                        1000,
                        t`Exclusion reason must be 1000 characters or less`,
                    )
                    .refine((val) => !isEmpty(val.trim()), {
                        message: t`Exclusion reason is required`,
                    }),
                initialValue: '',
                rows: 4,
                maxCharacters: 1000,
            },
        };
    }

    handleFormSubmit = (
        values: FormValues,
        onConfirm: (reason: string) => void,
    ): void => {
        const reason = values.reason as string;

        if (!reason.trim()) {
            return;
        }

        try {
            onConfirm(reason.trim());
        } catch (error) {
            logger.error(
                `Failed to confirm bulk exclusion: ${isError(error) ? error.message : String(error)}`,
            );
        }
    };
}

export const sharedMonitoringBulkExclusionModalModel =
    new MonitoringBulkExclusionModalModel();
