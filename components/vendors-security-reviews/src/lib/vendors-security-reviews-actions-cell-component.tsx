import { sharedVendorsSecurityReviewMutationController } from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Grid } from '@cosmos/components/grid';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { useNavigate } from '@remix-run/react';
import type { VendorSecurityReviewTableRowCellProps } from './types/vendors-security-reviews.type';

export const VendorSecurityReviewsActionsCellComponent = observer(
    ({
        row: { original },
    }: VendorSecurityReviewTableRowCellProps): React.JSX.Element => {
        const navigate = useNavigate();
        const completedReview = original.status === 'COMPLETED';
        const shouldShowDownloadButton =
            completedReview && original.type !== 'SECURITY';

        const { deleteSecurityReview, downloadingSecurityReviewId } =
            sharedVendorsSecurityReviewMutationController;

        const navigateToCompletedRoute = () => {
            navigate(`${original.id}/completed`);
        };

        const navigateToSOCCompletedRoute = () => {
            navigate(`soc/${original.id}/completed`);
        };

        const navigateToInProgressRoute = () => {
            navigate(`${original.id}`);
        };

        const navigateToSocRoute = () => {
            navigate(`soc/${original.id}`);
        };

        const handleOnClick = () => {
            if (original.status === 'COMPLETED') {
                if (original.type === 'SOC_REPORT') {
                    navigateToSOCCompletedRoute();

                    return;
                }

                navigateToCompletedRoute();

                return;
            }

            if (original.type === 'SOC_REPORT') {
                navigateToSocRoute();

                return;
            }

            navigateToInProgressRoute();
        };

        const handleDownloadClick = action(() => {
            sharedVendorsSecurityReviewMutationController.downloadSecurityReview(
                original,
            );
        });

        const colSpacing = () => {
            if (original.type === 'UPLOAD_REPORT') {
                return 'auto auto';
            }
            if (original.type === 'SECURITY' || !completedReview) {
                return '3fr auto';
            }

            return '3fr auto auto';
        };

        return (
            <Grid
                gap="3x"
                dir="row"
                data-testid="VendorSecurityReviewsActionsCellComponent"
                data-id="7xbJM0NT"
                justify="end"
                columns={colSpacing()}
            >
                {!completedReview && (
                    <Box>
                        <Button
                            width="full-width"
                            level="secondary"
                            label={t`Continue`}
                            onClick={handleOnClick}
                        />
                    </Box>
                )}

                {completedReview && original.type !== 'UPLOAD_REPORT' && (
                    <Box>
                        <Button
                            width="full-width"
                            level="secondary"
                            label={t`View`}
                            onClick={handleOnClick}
                        />
                    </Box>
                )}

                {shouldShowDownloadButton && (
                    <Box>
                        <Button
                            isIconOnly
                            label={t`Download`}
                            colorScheme="neutral"
                            startIconName="Download"
                            level="tertiary"
                            a11yLoadingLabel={t`Downloading...`}
                            isLoading={
                                downloadingSecurityReviewId === original.id
                            }
                            onClick={() => {
                                handleDownloadClick();
                            }}
                        />
                    </Box>
                )}

                <Box>
                    <Button
                        isIconOnly
                        label={t`Delete`}
                        colorScheme="danger"
                        startIconName="Trash"
                        level="tertiary"
                        onClick={() => {
                            openConfirmationModal({
                                title: t`Are you sure?`,
                                body: t`This will permanently delete the review.`,
                                confirmText: t`Delete`,
                                cancelText: t`Cancel`,
                                type: 'danger',
                                size: 'md',
                                onConfirm: () => {
                                    deleteSecurityReview(original.id);
                                },
                                onCancel: () => {
                                    closeConfirmationModal();
                                },
                            });
                        }}
                    />
                </Box>
            </Grid>
        );
    },
);
