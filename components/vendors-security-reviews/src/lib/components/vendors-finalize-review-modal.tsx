import { z } from 'zod';
import {
    sharedVendorsFinalizeReviewController,
    sharedVendorsSecurityReviewDetailsController,
    type VendorSecurityReviewDecision,
} from '@controllers/vendors';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';
import {
    FINALIZE_REVIEW_FORM_ID,
    getDecisionLabel,
    getDecisionOptions,
    VENDOR_SECURITY_REVIEW_DECISION_STATUS,
} from '../constants/finalize-review-modal.constants';
import { handleFinalizeReviewCloseModal } from '../helpers/open-finalize-review-modal.helper';

const getFormSchema = (): FormSchema => ({
    decision: {
        type: 'select',
        label: t`Decision`,
        isOptional: false,
        options: getDecisionOptions(),
        initialValue: {
            id: VENDOR_SECURITY_REVIEW_DECISION_STATUS.PENDING,
            label: getDecisionLabel('PENDING'),
            value: VENDOR_SECURITY_REVIEW_DECISION_STATUS.PENDING,
        },
        validator: z.object({
            id: z.string(),
            label: z.string(),
            value: z.string(),
        }),
    },
    note: {
        type: 'textarea',
        label: t`Note`,
        isOptional: true,
        rows: 7,
        validator: z
            .string()
            .max(1000, t`Note cannot be longer than 1000 characters.`)
            .optional(),
    },
});

export const FinalizeReviewModal = observer(() => {
    const { formRef, triggerSubmit } = useFormSubmit();
    const { securityReviewDetails } =
        sharedVendorsSecurityReviewDetailsController;
    const { isFinalizing } = sharedVendorsFinalizeReviewController;

    const formSchema = getFormSchema();

    const handleSubmit = (values: FormValues) => {
        if (!securityReviewDetails?.id) {
            return;
        }

        const { decision, note } = values as {
            decision: { value: string };
            note?: string;
        };

        sharedVendorsFinalizeReviewController.finalizeReview(
            securityReviewDetails.id,
            decision.value as VendorSecurityReviewDecision,
            note,
            handleFinalizeReviewCloseModal,
        );
    };

    const handleTriggerSubmit = () => {
        triggerSubmit();
    };

    return (
        <>
            <Modal.Header
                title={t`Finalize Security Review`}
                closeButtonAriaLabel={t`Close`}
                onClose={handleFinalizeReviewCloseModal}
            />
            <Modal.Body>
                <Form
                    hasExternalSubmitButton
                    data-id={FINALIZE_REVIEW_FORM_ID}
                    ref={formRef}
                    schema={formSchema}
                    formId={FINALIZE_REVIEW_FORM_ID}
                    onSubmit={handleSubmit}
                />
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        id: 'cancel-finalize-security-review-button',
                        label: t`Cancel`,
                        level: 'tertiary',
                        onClick: handleFinalizeReviewCloseModal,
                    },
                    {
                        id: 'finalize-security-review-button',
                        label: t`Mark review as complete`,
                        level: 'primary',
                        onClick: handleTriggerSubmit,
                        isLoading: isFinalizing,
                    },
                ]}
            />
        </>
    );
});
