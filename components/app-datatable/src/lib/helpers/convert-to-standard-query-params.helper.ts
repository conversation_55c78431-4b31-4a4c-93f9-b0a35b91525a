import {
    isBoolean,
    isDate,
    isEmpty,
    isNil,
    isNumber,
    isObject,
} from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
// eslint-disable-next-line no-restricted-imports -- Only allowed in Datatable
import type { SortingState } from '@tanstack/react-table';
import type { StandardQueryParams } from '../types/standard-query-params.type';

function applySortToQueryParams<TQueryParams>(
    queryParams: TQueryParams,
    sorting: SortingState,
): TQueryParams {
    if (isEmpty(sorting)) {
        return queryParams;
    }

    // Use the first sort item and create separate sort and sortDir parameters
    const firstSort = sorting[0];

    return {
        ...queryParams,
        sort: firstSort.id,
        sortDir: firstSort.desc ? 'DESC' : 'ASC',
    };
}

/**
 * Helper function to extract value from option objects.
 */
function extractValueFromOption(item: unknown): string {
    if (isObject(item) && 'value' in item) {
        return String((item as { value: unknown }).value);
    }

    return String(item);
}

/**
 * Helper function to process array values by extracting option values.
 */
function processArrayValues(values: unknown[]): string[] {
    return values.map(extractValueFromOption);
}

/**
 * Transform filter value based on its filter type.
 */
function transformFilterValueByType(
    value: unknown,
    filterType: string,
): string[] | string | undefined {
    switch (filterType) {
        case 'checkbox':
        case 'radio':
        case 'combobox': {
            // Handle checkbox/radio/combobox values - can be arrays or single values
            if (Array.isArray(value)) {
                const processedValues = processArrayValues(value);

                return isEmpty(processedValues) ? undefined : processedValues;
            }

            return [extractValueFromOption(value)];
        }

        case 'select': {
            // Handle select values - typically objects with value property
            return extractValueFromOption(value);
        }

        case 'slider': {
            // Handle slider values - numbers or number arrays [min, max]
            if (Array.isArray(value)) {
                return value.map(String).join(',');
            }
            if (isNumber(value)) {
                return String(value);
            }

            return String(value);
        }

        case 'date': {
            // Handle date values - Date objects or ISO strings
            if (isDate(value)) {
                return value.toISOString();
            }

            return String(value);
        }

        case 'text':
        case 'textarea': {
            // Handle text values - strings
            return String(value);
        }

        case 'toggle': {
            // Handle toggle values - booleans
            if (isBoolean(value)) {
                return value ? 'true' : 'false';
            }

            return String(value);
        }

        case 'file': {
            // Handle file upload values - extract meaningful identifiers
            if (Array.isArray(value)) {
                // File upload parameters array - extract file names or IDs
                return value
                    .map((file) => {
                        if (isObject(file) && 'name' in file) {
                            return String((file as { name: unknown }).name);
                        }

                        return String(file);
                    })
                    .join(',');
            }

            return String(value);
        }

        default: {
            // NOTE: what are you doing, come talk to precogs
            return undefined;
        }
    }
}

function applyFiltersToQueryParams<TQueryParams>(
    queryParams: TQueryParams,
    globalFilter: {
        search?: string;
        filters: Record<string, { value?: unknown; filterType: string }>;
    },
): TQueryParams {
    let result = { ...queryParams };

    // Apply global search as 'q' parameter
    if (globalFilter.search) {
        result = {
            ...result,
            q: globalFilter.search,
        };
    }

    // Apply individual filters with type-specific transformations
    Object.entries(globalFilter.filters).forEach(([key, filter]) => {
        if (isNil(filter.value)) {
            return;
        }

        const transformedValue = transformFilterValueByType(
            filter.value,
            filter.filterType,
        );

        if (transformedValue !== undefined) {
            result = {
                ...result,
                [key]: transformedValue,
            };
        }
    });

    return result;
}

export function convertToStandardQueryParams<
    TQueryParams = StandardQueryParams,
>(params: FetchDataResponseParams): TQueryParams {
    let baseParams = {
        page: params.pagination.page,
        limit: params.pagination.pageSize,
    } as TQueryParams;

    baseParams = applySortToQueryParams<TQueryParams>(
        baseParams,
        params.sorting,
    );

    baseParams = applyFiltersToQueryParams<TQueryParams>(
        baseParams,
        params.globalFilter,
    );

    return baseParams;
}
