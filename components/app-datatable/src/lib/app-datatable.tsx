import { useMemo } from 'react';
import { Box } from '@cosmos/components/box';
import {
    BulkActions,
    EmptyStateTable,
    FiltersViewModeProvider,
    GalleryContainer,
    Pagination,
    PinnedFilters,
    type RowData,
    Table,
    TopBar,
    useDatatable,
} from '@cosmos/components/datatable';
import { Stack } from '@cosmos/components/stack';
import { dimension32x } from '@cosmos/constants/tokens';
import { zIndex } from '@cosmos/constants/z-index';
import { useElementHeightAsCSSVar } from '@cosmos/hooks/use-element-height-as-css-var';
import { observer } from '@globals/mobx';
import { InternalAppDatatableController } from './controllers/internal-app-datatable.controller';
import type { AppDatatableProps } from './types/app-datatable-props.type';
import type { StandardQueryParams } from './types/standard-query-params.type';

export const AppDatatable = observer(
    <TData extends RowData, TQueryParams = StandardQueryParams>(
        allProps: AppDatatableProps<TData, TQueryParams>,
    ): React.JSX.Element => {
        const { controller } = allProps;

        const internalController = useMemo(
            () =>
                new InternalAppDatatableController<TData, TQueryParams>({
                    externalController: controller,
                }),
            [controller],
        );

        const datatableProps = internalController.prepareDatatableProps({
            props: allProps,
        });

        const { table } = useDatatable<TData>(datatableProps);

        const { viewMode, hidePagination, showEmptyState, showTopBar } =
            table.getDisplayState();

        const { 'data-id': dataId, isFullPageTable } = table.getMeta();

        const paginationElementRef = useElementHeightAsCSSVar({
            cssVariableName: 'appDatatablePaginationHeight',
        });

        return (
            <FiltersViewModeProvider<TData>
                table={table}
                data-testid="AppDatatable"
                data-id={dataId}
            >
                <Stack
                    gap="xl"
                    direction="row"
                    flexGrow={isFullPageTable ? '1' : undefined}
                    height={isFullPageTable ? undefined : '100%'}
                    minHeight={isFullPageTable ? undefined : '0'}
                    width="100%"
                    maxWidth="100%"
                    minWidth="0"
                    position="relative"
                >
                    {/* TODO: Give consumers access to "areFiltersPinnedOpen" */}
                    {/* <Stack
                        height="100%"
                        minHeight="0"
                        // TODO: padding should read from the layout padding token
                        pt="3xl"
                        pb="3xl"
                    > */}
                    <PinnedFilters
                        data-id={`${dataId}-pinned-filters`}
                        table={table}
                    />
                    {/* </Stack> */}

                    <Stack
                        direction="column"
                        width="100%"
                        minWidth="0"
                        position="relative"
                    >
                        {showTopBar && (
                            <Box
                                position={isFullPageTable ? 'sticky' : 'static'}
                                backgroundColor="neutralBackgroundSurfaceInitial"
                                top={
                                    isFullPageTable
                                        ? 'var(--contentNavigationMenuHeight, 0px)'
                                        : undefined
                                }
                                style={
                                    isFullPageTable
                                        ? {
                                              zIndex: zIndex.sticky,
                                          }
                                        : undefined
                                }
                            >
                                <TopBar table={table} data-id={dataId} />
                            </Box>
                        )}

                        <Box height="100%" minHeight="0" position={'relative'}>
                            {showEmptyState && (
                                <EmptyStateTable table={table} />
                            )}
                            {viewMode === 'table' && !showEmptyState && (
                                <Table table={table} />
                            )}
                            {viewMode === 'gallery' && !showEmptyState && (
                                <GalleryContainer table={table} />
                            )}
                            <Stack
                                position="sticky"
                                overflow="visible"
                                justify="center"
                                width="100%"
                                height="0x"
                                style={{
                                    zIndex: zIndex.sticky,
                                    bottom: `calc(var(--appDatatablePaginationHeight, 0px) + ${dimension32x})`,
                                }}
                            >
                                <BulkActions table={table} />
                            </Stack>
                        </Box>

                        {!hidePagination && (
                            <Box
                                ref={paginationElementRef}
                                position={isFullPageTable ? 'sticky' : 'static'}
                                bottom={isFullPageTable ? '0' : undefined}
                                backgroundColor="neutralBackgroundSurfaceInitial"
                                style={
                                    isFullPageTable
                                        ? {
                                              zIndex: zIndex.sticky,
                                          }
                                        : undefined
                                }
                            >
                                <Pagination table={table} />
                            </Box>
                        )}
                    </Stack>
                </Stack>
            </FiltersViewModeProvider>
        );
    },
);
