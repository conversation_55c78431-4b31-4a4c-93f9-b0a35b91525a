import { isEmpty } from 'lodash-es';
import { useCallback, useMemo } from 'react';
import { Card } from '@cosmos/components/card';
import { EmptyState } from '@cosmos/components/empty-state';
import { Feedback } from '@cosmos/components/feedback';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text, type TextProps } from '@cosmos/components/text';
import {
    dataCategorizeBlue2,
    dimension12x,
    dimension48x,
} from '@cosmos/constants/tokens';
import { DataBar } from '@cosmos-lab/components/data-bar';
import { DateTime } from '@cosmos-lab/components/date-time';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import type { UpcomingTaskDetailResponseDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getTimeDiff } from '@helpers/date-time';
import { getSeverityByDiffInDays } from '@helpers/severity';
import { useNavigate } from '@remix-run/react';
import { dashboardTaskForecastCardModel } from './models/dashboard-task-forecast-card-model';
import { TaskForecastChartTooltip } from './task-forecast-chart-tooltip';

const TASK_BARS = 10;
const DATA_BAR_HEIGHT = 200;

export const TaskForecastCard = observer((): React.JSX.Element => {
    const {
        customTasksOverview,
        customTasksDetailsData,
        isCustomTasksDetailsLoading,
        isCustomTasksOverviewLoading,
        chartData,
    } = dashboardTaskForecastCardModel;

    const navigate = useNavigate();

    const isLoading = useMemo(() => {
        return isCustomTasksDetailsLoading || isCustomTasksOverviewLoading;
    }, [isCustomTasksDetailsLoading, isCustomTasksOverviewLoading]);

    const displayEmptyState = useMemo(() => {
        return (
            isEmpty(customTasksDetailsData) &&
            isEmpty(customTasksOverview?.data) &&
            !isEmpty(chartData) &&
            !isLoading
        );
    }, [
        chartData,
        customTasksDetailsData,
        customTasksOverview?.data,
        isLoading,
    ]);

    const onTaskClickHandler = useCallback(
        (task: UpcomingTaskDetailResponseDto) => {
            if (!sharedWorkspacesController.currentWorkspace) {
                return;
            }

            const workspacePrefix = `/workspaces/${sharedWorkspacesController.currentWorkspace.id}`;

            switch (task.type) {
                case 'POLICY_RENEWALS': {
                    navigate(
                        `${workspacePrefix}/governance/policies/${task.id}/policy`,
                    );
                    break;
                }
                case 'REPORT':
                case 'EVIDENCE': {
                    navigate(
                        `${workspacePrefix}/compliance/evidence/${task.id}`,
                    );
                    break;
                }
                case 'VENDOR': {
                    navigate(
                        `${workspacePrefix}/vendors/questionnaires/completed`,
                    );
                    break;
                }
                case 'EXTERNAL_EVIDENCE':
                case 'CONTROL': {
                    navigate(
                        `${workspacePrefix}/compliance/controls/${task.id}/overview`,
                    );
                    break;
                }
                case 'GENERAL': {
                    navigate(`${workspacePrefix}/tasks`);
                    break;
                }
                case 'RISK': {
                    /**
                     * TODO: this navigation has to go to /risk/management/registers/[register_id]/register-risks/[task-id]
                     * That created a problem because we don't have the register_id outside of /risk/management/registers/[register_id].
                     *
                     * At the moment it was decided to hardcode the register_id to 1 since that's all tenants will have.
                     *
                     * See: https://drata.atlassian.net/browse/ENG-72803.
                     */
                    navigate(
                        `${workspacePrefix}/risk/management/registers/1/register-risks/${task.id}`,
                    );
                    break;
                }
                default: {
                    break;
                }
            }
        },
        [navigate],
    );

    const onViewAllTasksHandler = useCallback(() => {
        if (!sharedWorkspacesController.currentWorkspace) {
            return;
        }

        navigate(
            `/workspaces/${sharedWorkspacesController.currentWorkspace.id}/tasks`,
        );
    }, [navigate]);

    const getTaskTypeDisplayName = useCallback(
        (taskType: UpcomingTaskDetailResponseDto['type']): string => {
            switch (taskType) {
                case 'CONTROL': {
                    return t`Control`;
                }
                case 'EVIDENCE': {
                    return t`Evidence`;
                }
                case 'GENERAL': {
                    return t`General`;
                }
                case 'POLICY_RENEWALS': {
                    return t`Policy`;
                }
                case 'VENDOR': {
                    return t`Vendor`;
                }
                case 'RISK': {
                    return t`Risk`;
                }
                case 'CONTROL_APPROVALS': {
                    return t`Control Approval`;
                }
                case 'POLICY_APPROVALS': {
                    return t`Policy Approval`;
                }
                case 'EXTERNAL_EVIDENCE': {
                    return t`External Evidence`;
                }
                case 'REPORT': {
                    return t`Report`;
                }
                default: {
                    return t`Task`;
                }
            }
        },
        [],
    );

    // Calculate task severity using the standardized helper
    const getTaskSeverity = useCallback((renewalDate: string) => {
        const todayAtMidnight = new Date();

        todayAtMidnight.setHours(0, 0, 0, 0);
        const diffInDays = getTimeDiff(todayAtMidnight, renewalDate, 'days');

        return getSeverityByDiffInDays(diffInDays);
    }, []);

    const taskListItems = useMemo(() => {
        return customTasksDetailsData.map((task) => {
            const severity = getTaskSeverity(task.renewalDate);
            const taskTypeDisplayName = getTaskTypeDisplayName(task.type);

            // Check if the task is actually overdue (past due date)
            const isOverdue = new Date(task.renewalDate) < new Date();

            // Determine the appropriate textProps and format based on severity
            const dateFormat: 'overdue' | 'table' = isOverdue
                ? 'overdue'
                : 'table';

            let colorScheme: TextProps['colorScheme'] = 'primary';

            if (severity === 'critical') {
                colorScheme = 'critical';
            } else if (severity === 'warning') {
                colorScheme = 'warning';
            }

            const dateTextProps: TextProps = { colorScheme };

            return (
                <StackedListItem
                    key={task.id}
                    data-id={`task-item-${task.id}`}
                    eyebrow={<Text size="100">{taskTypeDisplayName}</Text>}
                    primaryColumn={<Text size="200">{task.name}</Text>}
                    rowButtonLabel={t`View task details`}
                    secondaryColumn={
                        <Feedback
                            severity={
                                severity === 'neutral' ? 'primary' : severity
                            }
                            title={
                                <Stack direction="row" gap="1x">
                                    <Trans>
                                        <Text colorScheme={colorScheme}>
                                            Due
                                        </Text>
                                        <DateTime
                                            date={task.renewalDate}
                                            format={dateFormat}
                                            textProps={dateTextProps}
                                            data-id={`task-date-${task.id}`}
                                        />
                                    </Trans>
                                </Stack>
                            }
                        />
                    }
                    onRowClick={() => {
                        onTaskClickHandler(task);
                    }}
                />
            );
        });
    }, [
        customTasksDetailsData,
        onTaskClickHandler,
        getTaskSeverity,
        getTaskTypeDisplayName,
    ]);

    const tasksSection = useMemo(() => {
        if (isLoading) {
            return <Skeleton barCount={TASK_BARS} barHeight={dimension12x} />;
        }

        return (
            <StackedList data-id="upcoming-tasks-list">
                {taskListItems}
            </StackedList>
        );
    }, [isLoading, taskListItems]);

    const chartSection = useMemo(() => {
        if (isLoading) {
            return <Skeleton barHeight={dimension48x} />;
        }

        return (
            <DataBar
                data={chartData}
                categoryKey="month"
                height={DATA_BAR_HEIGHT}
                width="100%"
                data-id="jQuC7YEL"
                tooltipComponent={TaskForecastChartTooltip}
                bars={[
                    {
                        dataKey: 'total',
                        fill: dataCategorizeBlue2,
                        barSize: 40,
                    },
                ]}
            />
        );
    }, [chartData, isLoading]);

    return (
        <Card
            title={t`Task forecast`}
            data-testid="TaskForecastCard"
            data-id="6mJdHm_b"
            body={
                <Stack gap="6x" direction="column">
                    {displayEmptyState ? (
                        <Stack pt="6xl">
                            <EmptyState
                                isInline
                                title={t`Tasks will appear here once they’re within 6 months of their due date.`}
                            />
                        </Stack>
                    ) : (
                        <>
                            {chartSection}

                            <Stack direction="column" gap="6x">
                                <Stack direction="column" gap="sm">
                                    {isLoading ? (
                                        <Skeleton barCount={2} />
                                    ) : (
                                        <>
                                            <Text type="title" size="300">
                                                <Trans>Upcoming Tasks</Trans>
                                            </Text>
                                            <Text
                                                type="subheadline"
                                                colorScheme="faded"
                                            >
                                                <Trans>
                                                    Within the next 6 months
                                                </Trans>
                                            </Text>
                                        </>
                                    )}
                                </Stack>
                                {tasksSection}
                            </Stack>
                        </>
                    )}
                </Stack>
            }
            actions={[
                {
                    actionType: 'button',
                    id: 'view-all-tasks-action-button',
                    typeProps: {
                        label: t`View all tasks`,
                        level: 'secondary',
                        onClick: onViewAllTasksHandler,
                    },
                },
            ]}
        />
    );
});
