import type { RiskScoreSeverity } from '@cosmos-lab/components/risk-score';
import type { RiskThresholdResponseDto } from '@globals/api-sdk/types';

export interface SelectFieldValue {
    value: string;
    label: string;
    id: string;
}

export interface ImpactLikelihoodFormValues {
    impact: SelectFieldValue;
    likelihood: SelectFieldValue;
}

export interface FormSchemaWithThresholds {
    thresholds?: {
        fields?: unknown;
    };
}

export interface ThresholdsFieldProps {
    formId: string;
    name: string;
    thresholds: RiskThresholdResponseDto[];
    onThresholdChange: (index: number, field: string, newValue: string) => void;
    onResetToDefaults: () => void;
    'data-id'?: string;
}

export interface ImpactLikelihoodFieldProps {
    formId: string;
    name: string;
    currentRiskScore: number;
    severity: RiskScoreSeverity;
    'data-id'?: string;
}
