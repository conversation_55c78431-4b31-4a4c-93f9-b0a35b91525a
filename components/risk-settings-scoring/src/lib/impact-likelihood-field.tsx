import type React from 'react';
import { sharedRiskSettingsScoringController } from '@controllers/risk-settings';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import {
    RiskScore,
    type RiskScoreSeverity,
} from '@cosmos-lab/components/risk-score';
import { Trans } from '@globals/i18n/macro';
import { type CustomFieldRenderProps, UniversalFormField } from '@ui/forms';
import type { ImpactLikelihoodFieldProps } from './types';

export const ImpactLikelihoodField = ({
    formId,
    name,
    'data-id': dataId,
    currentRiskScore,
    severity,
}: ImpactLikelihoodFieldProps): React.JSX.Element => {
    return (
        <Stack
            direction="column"
            gap="2xl"
            data-id={dataId}
            data-testid="ImpactLikelihoodField"
        >
            <Text size="300" type="title">
                <Trans>Impact and likelihood scale</Trans>
            </Text>
            <Text size="300" type="body">
                <Trans>
                    You can adjust ranges anywhere from 3 to 10 building a graph
                    that&apos;s right for your organization. If you reduce
                    ranges, you lose the scores for the eliminated rows or
                    columns.
                </Trans>
            </Text>

            <Stack direction="row" gap="md" align="end">
                <UniversalFormField
                    __fromCustomRender
                    formId={formId}
                    name={`${name}.impact`}
                    data-id={`${dataId}-impact`}
                />
                <Icon name="Close" size="200" />
                <UniversalFormField
                    __fromCustomRender
                    formId={formId}
                    name={`${name}.likelihood`}
                    data-id={`${dataId}-likelihood`}
                />

                <Text size="500"> = </Text>
                <RiskScore
                    intensity="strong"
                    severity={severity}
                    scoreNumber={currentRiskScore}
                    size="md"
                />
            </Stack>
        </Stack>
    );
};

/**
 * Form wrapper component that works with the form system.
 */
export const ImpactLikelihoodFormField = (
    props: CustomFieldRenderProps,
): React.JSX.Element => {
    const { formId, name, 'data-id': dataId } = props;
    const { currentRiskScore, riskSeverity } =
        sharedRiskSettingsScoringController;

    return (
        <ImpactLikelihoodField
            formId={formId}
            name={name}
            data-id={`${dataId}-display`}
            currentRiskScore={currentRiskScore}
            severity={riskSeverity as RiskScoreSeverity}
            data-testid="ImpactLikelihoodFormField"
        />
    );
};
