import { Stack } from '@cosmos/components/stack';
import { RISK_CALCULATION_CONSTANTS } from '@helpers/risk-score';
import { UniversalFormField } from '@ui/forms';

interface LevelDefinitionsGroupProps {
    type: 'Impact' | 'Likelihood';
    maxLevel: number;
    formId: string;
    fieldNamePrefix: string;
    'data-id'?: string;
}

export const LevelDefinitionsGroup = ({
    type,
    maxLevel,
    formId,
    fieldNamePrefix,
    'data-id': dataId,
}: LevelDefinitionsGroupProps): React.JSX.Element => {
    const validMaxLevel = Math.min(
        Math.max(maxLevel, RISK_CALCULATION_CONSTANTS.MIN_RISK_LEVELS),
        RISK_CALCULATION_CONSTANTS.MAX_RISK_LEVELS,
    );

    const levels = Array.from({ length: validMaxLevel }, (_, i) => i + 1);
    const typePrefix = type.toLowerCase();

    return (
        <Stack
            direction="column"
            gap="sm"
            data-testid={`LevelDefinitionsGroup-${type}`}
            data-id={dataId || `${typePrefix}-level-definitions`}
        >
            {levels.map((level) => (
                <UniversalFormField
                    key={`${type}-${level}`}
                    formId={formId}
                    name={`${fieldNamePrefix}.${typePrefix}Definition${level}`}
                    data-id={`${typePrefix}-level-${level}-field`}
                />
            ))}
        </Stack>
    );
};
