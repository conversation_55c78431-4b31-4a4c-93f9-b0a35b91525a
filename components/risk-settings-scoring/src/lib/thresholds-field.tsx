import { isEmpty } from 'lodash-es';
import type React from 'react';
import {
    sharedRiskSettingsController,
    sharedRiskSettingsScoringController,
} from '@controllers/risk-settings';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { Grid } from '@cosmos/components/grid';
import { Skeleton } from '@cosmos/components/skeleton';
import { Text } from '@cosmos/components/text';
import { Threshold } from '@cosmos-lab/components/threshold';
import { t, Trans } from '@globals/i18n/macro';
import { type CustomFieldRenderProps, useFormContext } from '@ui/forms';
import { ThresholdSettingsItem } from './threshold-settings-item';
import type { FormSchemaWithThresholds, ThresholdsFieldProps } from './types';

export const ThresholdsField = ({
    formId,
    name,
    'data-id': dataId,
    thresholds,
    onThresholdChange,
    onResetToDefaults,
}: ThresholdsFieldProps): React.JSX.Element => {
    const { schema } = useFormContext();

    if (isEmpty(thresholds)) {
        return (
            <Grid
                gap="2xl"
                data-testid="ThresholdsField-empty"
                data-id={dataId}
            >
                <EmptyState
                    title={t`No thresholds available`}
                    description={t`Threshold configuration is not available. Please check your risk settings.`}
                />
            </Grid>
        );
    }

    const schemaReady = Boolean(
        (schema as FormSchemaWithThresholds).thresholds?.fields,
    );

    if (!schemaReady) {
        return (
            <Grid
                gap="2xl"
                data-testid="ThresholdsField-loading"
                data-id={dataId}
            >
                <Text size="300" type="title">
                    <Trans>Thresholds</Trans>
                </Text>
                <Skeleton barCount={3} />
            </Grid>
        );
    }

    const thresholdBoundaries = thresholds
        .slice(0, -1)
        .map((threshold) => threshold.maxThreshold);

    const minValue = thresholds[0].minThreshold;
    const maxValue = thresholds[thresholds.length - 1].maxThreshold;

    const handleThresholdBoundaryChangeLocal = (newValues: number[]) => {
        onThresholdChange(0, 'boundaries', newValues.join(','));
    };

    return (
        <Grid gap="2xl" data-testid="ThresholdsField" data-id={dataId}>
            <Text size="300" type="title" id="threshold-settings-label">
                <Trans>Thresholds</Trans>
            </Text>
            <Text size="300" type="body">
                <Trans>
                    Create your own custom scoring with 2 to 5 thresholds and
                    adjust the values as you wish. The default setting includes
                    4 thresholds with distributed values.
                </Trans>
            </Text>
            <Threshold
                min={minValue}
                max={maxValue}
                displaySplitCount={thresholds.length}
                initialValues={[minValue, ...thresholdBoundaries, maxValue]}
                aria-labelledby="threshold-settings-label"
                id="risk-threshold-settings"
                name="thresholdBoundaries"
                onValueChange={handleThresholdBoundaryChangeLocal}
            />
            <Box>
                <Button
                    label={t`Reset to defaults`}
                    level="secondary"
                    colorScheme="danger"
                    onClick={onResetToDefaults}
                />
            </Box>
            <Text size="300" type="body">
                <Trans>
                    Customize what each threshold is called and what it means.
                    The name will show up when risk are selected and on the
                    insight page legend. The description will show up on the
                    legend.
                </Trans>
            </Text>
            {thresholds.map((threshold, index) => (
                <ThresholdSettingsItem
                    key={String(threshold.id)}
                    color={threshold.color}
                    formId={formId}
                    index={index}
                    thresholdId={threshold.id}
                    nameFieldName={`${name}.threshold${threshold.id}Name`}
                    descriptionFieldName={`${name}.threshold${threshold.id}Description`}
                    data-id={`threshold-settings-item-${threshold.id}`}
                    onThresholdChange={onThresholdChange}
                />
            ))}
        </Grid>
    );
};

/**
 * Form wrapper component that works with the form system.
 */
export const ThresholdsFormField = (
    props: CustomFieldRenderProps,
): React.JSX.Element => {
    const { formId, name, 'data-id': dataId } = props;
    const { riskSettings } = sharedRiskSettingsController;
    const handleThresholdChange =
        sharedRiskSettingsScoringController.handleThresholdChange.bind(
            sharedRiskSettingsScoringController,
        );
    const handleResetToDefaults =
        sharedRiskSettingsScoringController.handleResetToDefaults.bind(
            sharedRiskSettingsScoringController,
        );

    if (!riskSettings?.thresholds) {
        return (
            <Grid
                gap="2xl"
                data-testid="ThresholdsFormField-empty"
                data-id={dataId}
            >
                <EmptyState
                    title={t`No thresholds available`}
                    description={t`Threshold configuration is not available. Please check your risk settings.`}
                />
            </Grid>
        );
    }

    return (
        <ThresholdsField
            formId={formId}
            name={name}
            data-id={dataId}
            thresholds={riskSettings.thresholds}
            data-testid="ThresholdsFormField"
            onThresholdChange={handleThresholdChange}
            onResetToDefaults={handleResetToDefaults}
        />
    );
};
