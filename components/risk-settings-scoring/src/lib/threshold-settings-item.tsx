import type React from 'react';
import { Button } from '@cosmos/components/button';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import { t } from '@globals/i18n/macro';
import { UniversalFormField } from '@ui/forms';
import { ColoredSquare, type ColoredSquareProps } from './colored-square';

interface ThresholdSettingsItemProps {
    color: ColoredSquareProps['color'];
    formId: string;
    index: number;
    thresholdId: number;
    nameFieldName: string;
    descriptionFieldName: string;
    onThresholdChange: (index: number, field: string, newValue: string) => void;
}

export const ThresholdSettingsItem = ({
    color,
    formId,
    index,
    thresholdId,
    nameFieldName,
    descriptionFieldName,
    onThresholdChange,
}: ThresholdSettingsItemProps): React.JSX.Element => {
    return (
        <Stack
            direction="column"
            gap="2xl"
            data-testid="ThresholdSettingsItem"
            data-id={`threshold-settings-item-${thresholdId}`}
        >
            <Grid
                columns={'1fr 11fr'}
                gap="2xl"
                data-testid="ThresholdSettingsItem"
                data-id={`threshold-settings-item-${thresholdId}`}
            >
                <ColoredSquare
                    color={color}
                    data-id={`threshold-${thresholdId}-color-square`}
                />
                <Stack direction={'column'} gap="4x">
                    <Grid columns={'1fr 2fr'}>
                        <UniversalFormField
                            __fromCustomRender
                            formId={formId}
                            name={nameFieldName}
                            data-id={`${nameFieldName}-field`}
                        />
                    </Grid>
                    <Grid columns="1fr auto" gap="xl" align={'end'}>
                        <UniversalFormField
                            __fromCustomRender
                            formId={formId}
                            name={descriptionFieldName}
                            data-id={`${descriptionFieldName}-field`}
                        />
                        <Button
                            isIconOnly
                            label={t`Delete`}
                            level="tertiary"
                            colorScheme="danger"
                            startIconName="Trash"
                            onClick={() => {
                                onThresholdChange(index, 'delete', '');
                            }}
                        />
                    </Grid>
                </Stack>
            </Grid>

            <Divider />
        </Stack>
    );
};
