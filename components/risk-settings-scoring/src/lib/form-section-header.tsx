import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';

interface FormSectionHeaderProps {
    title: string;
    description?: string;
    level?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
    spacing?: 'sm' | 'md' | 'lg';
    'data-id'?: string;
}

export const FormSectionHeader = ({
    title,
    description,
    level = 'h3',
    spacing = 'md',
    'data-id': dataId,
}: FormSectionHeaderProps): React.JSX.Element => {
    const gapMap = {
        sm: 'sm',
        md: 'md',
        lg: 'lg',
    } as const;

    const sizeMap = {
        h1: '600',
        h2: '500',
        h3: '400',
        h4: '300',
        h5: '200',
        h6: '100',
    } as const;

    return (
        <Stack
            direction="column"
            gap={gapMap[spacing]}
            data-id={dataId}
            data-testid="FormSectionHeader"
        >
            <Text
                as={level}
                type="subheadline"
                size={sizeMap[level]}
                data-id={dataId ? `${dataId}-title` : undefined}
            >
                {title}
            </Text>
            {description && (
                <Text
                    size="200"
                    colorScheme="faded"
                    data-id={dataId ? `${dataId}-description` : undefined}
                >
                    {description}
                </Text>
            )}
        </Stack>
    );
};
