import type React from 'react';
import { Box } from '@cosmos/components/box';
import { mapColorToBackgroundToken } from '@helpers/risk-score';

export interface ColoredSquareProps {
    color?: string;
    'data-id'?: string;
}

export const ColoredSquare = ({
    color,
    'data-id': dataId = 'colored-square',
}: ColoredSquareProps): React.JSX.Element => {
    const backgroundColor = mapColorToBackgroundToken(color);

    return (
        <Box
            borderRadius="borderRadiusMd"
            width="5xl"
            height="5xl"
            data-testid="ColoredSquare"
            data-id={dataId}
            backgroundColor={backgroundColor}
        />
    );
};
