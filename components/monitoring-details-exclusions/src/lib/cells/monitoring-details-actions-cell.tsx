import {
    closeMonitorUpdateExclusionReasonModal,
    openMonitorUpdateExclusionReasonModal,
} from '@components/monitor-update-exclusion-reason-modal';
import { openMonitoringRemoveExclusionConfirmationModal } from '@components/monitoring-remove-exclusions-confirmation-modal';
import {
    sharedFindingExclusionMutationController,
    sharedMonitoringDetailsExclusionsController,
    sharedMonitoringFindingsExclusionReasonController,
} from '@controllers/monitoring-details';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { MonitorExclusionResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const MonitoringDetailsActionsCell = observer(
    ({
        row: { original },
    }: {
        row: { original: MonitorExclusionResponseDto };
    }): React.JSX.Element => {
        const { testId } = sharedMonitoringDetailsExclusionsController;

        if (!testId) {
            return <EmptyValue label="—" />;
        }

        return (
            <SchemaDropdown
                isIconOnly
                size="sm"
                startIconName="HorizontalMenu"
                level="tertiary"
                label="Horizontal menu"
                colorScheme="neutral"
                data-id="6GvBWQ_R"
                data-testid="MonitoringDetailsActionsCell"
                items={[
                    {
                        id: 'update-exclusion-reason',
                        label: t`Update reason`,
                        type: 'item',
                        value: 'update-exclusion-reason',
                        onClick: () => {
                            openMonitorUpdateExclusionReasonModal(
                                original,
                                (reason: string) => {
                                    sharedMonitoringFindingsExclusionReasonController.updateExclusionReason(
                                        original.id,
                                        testId,
                                        reason,
                                        () => {
                                            closeMonitorUpdateExclusionReasonModal();
                                        },
                                    );
                                },
                            );
                        },
                    },
                    {
                        id: 'remove-exclusion',
                        label: t`Include/Remove exclusion`,
                        type: 'item',
                        value: 'remove-exclusion',
                        colorScheme: 'critical',
                        onClick: () => {
                            openMonitoringRemoveExclusionConfirmationModal({
                                onConfirm: () => {
                                    sharedFindingExclusionMutationController.removeExclusionsBulk(
                                        {
                                            testId,
                                            targetIds: [original.id],
                                        },
                                    );
                                },
                            });
                        },
                    },
                ]}
            />
        );
    },
);
