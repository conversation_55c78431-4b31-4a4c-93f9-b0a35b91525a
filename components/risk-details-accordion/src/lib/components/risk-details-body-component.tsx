import { isEmpty } from 'lodash-es';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getFullName, getInitials } from '@helpers/formatters';
import { AppLink } from '@ui/app-link';

interface RiskDetailsAccordionBodyProps {
    id: string;
    description: string;
    owners?: {
        firstName: string;
        lastName: string;
        avatarUrl?: string;
    }[];
    onRemoveRisk?: () => void;
    'data-id'?: string;
}

export const RiskDetailsBodyComponent = ({
    id,
    description,
    owners,
    onRemoveRisk,
    'data-id': dataId = 'risk-details-body',
}: RiskDetailsAccordionBodyProps): React.JSX.Element => {
    const actions: Action[] = [];

    if (onRemoveRisk) {
        actions.push({
            actionType: 'button',
            id: 'remove-risk-action-stack',
            typeProps: {
                label: t`Remove`,
                level: 'tertiary',
                colorScheme: 'danger',
                size: 'sm',
                onClick: onRemoveRisk,
            },
        });
    }

    return (
        <Stack
            direction="column"
            gap="2xl"
            data-testid="RiskDetailsBodyComponent"
            data-id={dataId}
        >
            <Text>{description}</Text>
            <KeyValuePair
                label={t`Risk owners`}
                type="USER"
                value={owners?.map((owner) => ({
                    username: getFullName(owner.firstName, owner.lastName),
                    avatarProps: {
                        fallbackText: getInitials(
                            `${owner.firstName} ${owner.lastName}`,
                        ),
                        imgSrc: owner.avatarUrl ?? '',
                        imgAlt: getFullName(owner.firstName, owner.lastName),
                    },
                }))}
            />
            {/*
             * TODO: this navigation has to go to /risk/management/registers/[register_id]/register-risks/[task-id]
             * That created a problem because we don't have the register_id outside of /risk/management/registers/[register_id].
             *
             * At the moment it was decided to hardcode the register_id to 1 since that's all tenants will have.
             *
             * See: https://drata.atlassian.net/browse/ENG-72803
             */}
            <AppLink
                href={`/workspaces/${sharedWorkspacesController.currentWorkspace?.id}/risk/management/registers/1/register-risks/${id}/overview`}
            >{t`Go to risk`}</AppLink>

            {!isEmpty(actions) && (
                <ActionStack
                    gap="lg"
                    stacks={[
                        {
                            actions,
                            id: 'control-action-stack',
                        },
                    ]}
                />
            )}
        </Stack>
    );
};
