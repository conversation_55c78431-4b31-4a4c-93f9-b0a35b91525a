import { isEmpty } from 'lodash-es';
import { sharedRiskLibraryDetailsController } from '@controllers/risk';
import { Metadata } from '@cosmos/components/metadata';
import { PanelHeader } from '@cosmos/components/panel';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useParams } from '@remix-run/react';

export const RiskLibraryPanelHeader = observer((): React.JSX.Element => {
    const { riskLibraryDetails } = sharedRiskLibraryDetailsController;
    const { risks, riskId, title } = riskLibraryDetails ?? {};
    const { currentWorkspace } = sharedWorkspacesController;
    const params = useParams();

    return (
        <PanelHeader
            title={title ?? ''}
            data-id="b4-p6GSH"
            openPageLink={
                isEmpty(risks)
                    ? undefined
                    : `/workspaces/${currentWorkspace?.id}/risk/management/registers/${params.registerId}/register-risks/${riskId}/overview`
            }
            slot={
                <Metadata
                    colorScheme="neutral"
                    label={riskId ?? ''}
                    type="tag"
                />
            }
        />
    );
});
