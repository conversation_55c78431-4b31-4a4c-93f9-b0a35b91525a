import { size } from 'lodash-es';
import { sharedRiskLibraryDetailsController } from '@controllers/risk';
import { Accordion } from '@cosmos/components/accordion';
import type { IconName } from '@cosmos/components/icon';
import { type ColorScheme, Metadata } from '@cosmos/components/metadata';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { RiskControlResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { AppLink } from '@ui/app-link';

const getControlStatus = (
    control: RiskControlResponseDto,
): { iconName: IconName; colorScheme: ColorScheme } => {
    let iconName: IconName = 'NotReady';
    let colorScheme: ColorScheme = 'critical';

    if (control.archivedAt) {
        iconName = 'OutOfScope';
        colorScheme = 'neutral';
    } else if (control.isReady) {
        iconName = 'CheckCircle';
        colorScheme = 'success';
    }

    return {
        iconName,
        colorScheme,
    };
};

export const MitigatingControlsSection = observer((): React.JSX.Element => {
    const { riskLibraryDetails, isLoading } =
        sharedRiskLibraryDetailsController;
    const controls = riskLibraryDetails?.controls ?? [];
    const { currentWorkspace } = sharedWorkspacesController;

    const controlAccordion = controls.map((control) => (
        <Accordion
            key={control.id}
            title={control.name}
            data-Id={`risks-mitigating-control-${control.id}`}
            titleType="lg"
            data-id="dbbtGMGi"
            iconSlot={{
                slotType: 'metadata',
                typeProps: {
                    type: 'status',
                    label: control.code,
                    iconName: getControlStatus(control).iconName,
                    colorScheme: getControlStatus(control).colorScheme,
                },
            }}
            body={
                <Stack direction="column" gap="xl">
                    <Text type="body" size="100">
                        {control.description}
                    </Text>
                    <AppLink
                        href={`/workspaces/${currentWorkspace?.id}/compliance/controls/${control.id}/overview`}
                        label={t`Go to control`}
                    />
                </Stack>
            }
        />
    ));

    return (
        <Stack
            direction="column"
            gap="2xl"
            data-testid="MitigatingControlsSection"
            data-id="zEDfnyBR"
        >
            <Stack gap="lg">
                <Text type="title" size="400">
                    {t`Mitigating controls`}
                </Text>
                <Metadata
                    type="number"
                    colorScheme="neutral"
                    label={size(controls).toString()}
                />
            </Stack>

            <Stack direction="column" gap="xl">
                {isLoading ? <Skeleton barCount={5} /> : controlAccordion}
            </Stack>
        </Stack>
    );
});
