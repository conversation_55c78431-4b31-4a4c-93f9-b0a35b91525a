import { isEmpty } from 'lodash-es';
import { z } from 'zod';
import { sharedFindingExclusionMutationController } from '@controllers/monitoring-details';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';
import type { MonitoringExclusionModalProps } from './types/monitoring-exclusion-modal.types';

export const MonitoringExclusionModal = observer(
    ({
        finding,
        onConfirm,
        onCancel,
        'data-id': dataId = 'MonitoringExclusionModal',
    }: MonitoringExclusionModalProps): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();
        const isSubmitting =
            sharedFindingExclusionMutationController.createExclusionMutation
                .isPending;

        const handleFormSubmit = (values: FormValues) => {
            const reason = values.reason as string;

            if (!reason.trim()) {
                return;
            }

            try {
                onConfirm(reason.trim());
            } catch (error) {
                console.error('Failed to exclude finding:', error);
            }
        };

        const formSchema = {
            reason: {
                type: 'textarea',
                label: t`Exclusion reason`,
                placeholder: t`Provide a reason for excluding this finding...`,
                helpText: t`This reason will be surfaced on audit download packages`,
                validator: z
                    .string()
                    .min(1, t`Exclusion reason is required`)
                    .max(
                        1000,
                        t`Exclusion reason must be 1000 characters or less`,
                    )
                    .refine((val) => !isEmpty(val.trim()), {
                        message: t`Exclusion reason is required`,
                    }),
                initialValue: '',
                rows: 4,
                maxCharacters: 1000,
            },
        } as const satisfies FormSchema;

        return (
            <>
                <Modal.Header
                    title={t`Exclude finding`}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={onCancel}
                />
                <Modal.Body>
                    <Stack direction="column" gap="lg">
                        <Text data-id="exclusion-description">
                            {t`You are about to exclude the following finding:`}
                        </Text>
                        <Text type="title" data-id="finding-resource-name">
                            {finding.resourceName}
                        </Text>
                        <Form
                            hasExternalSubmitButton
                            ref={formRef}
                            schema={formSchema}
                            formId="monitoring-exclusion-form"
                            data-id={`${dataId}-form`}
                            onSubmit={handleFormSubmit}
                        />
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: onCancel,
                        },
                        {
                            label: t`Exclude finding`,
                            level: 'primary',
                            colorScheme: 'danger',
                            onClick: () => {
                                triggerSubmit().catch((error) => {
                                    console.error(
                                        'Failed to submit monitoring exclusion modal form:',
                                        error,
                                    );
                                });
                            },
                            isLoading: isSubmitting,
                            a11yLoadingLabel: t`Excluding finding...`,
                        },
                    ]}
                />
            </>
        );
    },
);
