import { ActionStack } from '@cosmos/components/action-stack';
import { dimension0x } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { sharedWorkspacesController } from '@globals/workspaces';

export class ComplianceAuditsContentHeaderModel {
    pageId = 'audits-header';

    title = 'Audits';

    get actionStack(): React.JSX.Element | undefined {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return undefined;
        }
        const href = `/workspaces/${currentWorkspace.id}/compliance/audits/create`;

        return (
            <ActionStack
                data-id="audits-page-header-action-stack"
                gap={dimension0x}
                data-testid="AuditsHeaderActionStack"
                actions={[
                    {
                        actionType: 'button',
                        id: 'create-audit-header-action-button',
                        typeProps: {
                            'data-id': 'create-audit-header-action-button',
                            label: t`Create Audit`,
                            href,
                            type: 'button',
                        },
                    },
                ]}
            />
        );
    }
}
