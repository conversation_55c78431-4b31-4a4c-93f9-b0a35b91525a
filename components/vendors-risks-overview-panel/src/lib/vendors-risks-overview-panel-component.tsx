import { panelController } from '@controllers/panel';
import {
    generatePanelPaginationOptions,
    getRiskOverviewPanelActions,
    sharedVendorsProfileRisksController,
    sharedVendorsRiskDetailsController,
    sharedVendorsRisksController,
    sharedVendorsRisksMutationController,
} from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import {
    PanelBody,
    PanelControls,
    PanelFooter,
    PanelHeader,
} from '@cosmos/components/panel';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { useLocation, useNavigate } from '@remix-run/react';
import { RisksOverviewSectionAssessmentComponent } from './components/risks-overview-section-assessment-component';
import { RisksOverviewSectionCurrentTreatmentComponent } from './components/risks-overview-section-current-treatment-component';
import { RisksOverviewSectionDetailsComponent } from './components/risks-overview-section-details-component';
import { RisksOverviewSectionMitigatingControlsComponent } from './components/risks-overview-section-mitigating-controls-component';

export const VendorsRisksOverviewPanelComponent = observer(
    (): React.JSX.Element | null => {
        const { vendorRiskDetail, isLoading } =
            sharedVendorsRiskDetailsController;
        const { riskId, title } = vendorRiskDetail ?? {};

        const { currentWorkspace } = sharedWorkspacesController;

        const location = useLocation();
        const { pathname } = location;

        const isVendorIdScope = !pathname.includes('vendors/risks');

        const risks = isVendorIdScope
            ? sharedVendorsProfileRisksController.allVendorsRisks
            : sharedVendorsRisksController.risks;

        const navigate = useNavigate();

        const navigateCallback = () => {
            /*
             * TODO: this navigation has to go to /risk/management/registers/[register_id]/register-risks/[task-id]
             * That created a problem because we don't have the register_id outside of /risk/management/registers/[register_id].
             *
             * At the moment it was decided to hardcode the register_id to 1 since that's all tenants will have.
             *
             * See: https://drata.atlassian.net/browse/ENG-72803
             */
            navigate(
                `/workspaces/${currentWorkspace?.id}/risk/management/registers/1/register-risks/${riskId}/overview`,
            );
        };

        if (isLoading) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        if (!vendorRiskDetail) {
            return null;
        }

        return (
            <Grid data-testid="VendorsRiskDetailsView" data-id="JZfmAppn">
                <PanelControls
                    closeButtonLabel={t`Close`}
                    data-id="panel-controls"
                    pagination={generatePanelPaginationOptions(
                        riskId ?? '',
                        risks,
                        () => (
                            <VendorsRisksOverviewPanelComponent data-id="jo99f_yY" />
                        ),
                    )}
                    onClose={() => {
                        panelController.closePanel();
                    }}
                />
                <PanelHeader
                    data-id="panel-header"
                    title={title ?? ''}
                    slot={
                        <ActionStack
                            isFullWidth
                            data-id="action-stack"
                            gap="3x"
                            stacks={getRiskOverviewPanelActions(
                                riskId ?? '',
                                navigateCallback,
                            )}
                        />
                    }
                />
                <PanelBody data-id="panel-body">
                    <Stack
                        direction="column"
                        gap="6x"
                        data-testid="VendorsRisksOverviewView"
                        data-id="66UG_uxi"
                    >
                        <RisksOverviewSectionDetailsComponent
                            vendorRiskDetail={vendorRiskDetail}
                        />

                        <RisksOverviewSectionAssessmentComponent
                            vendorRiskDetail={vendorRiskDetail}
                        />

                        <RisksOverviewSectionCurrentTreatmentComponent
                            vendorRiskDetail={vendorRiskDetail}
                        />

                        <RisksOverviewSectionMitigatingControlsComponent
                            vendorRiskDetail={vendorRiskDetail}
                        />
                    </Stack>
                </PanelBody>
                <PanelFooter
                    data-id="vendors-risks-overview-panel-footer"
                    leftActionStack={
                        sharedFeatureAccessModel.isRiskManagerWithRestrictedView
                            ? []
                            : [
                                  {
                                      colorScheme: 'danger',
                                      level: 'tertiary',
                                      label: t`Delete risk`,
                                      onClick: () => {
                                          openConfirmationModal({
                                              title: t`Are you sure?`,
                                              body: t`You are about to delete a risk. This action will remove the risk data from your account permanently, and this action cannot be undone.`,
                                              confirmText: t`Delete`,
                                              cancelText: t`Cancel`,
                                              type: 'danger',
                                              onConfirm: action(() => {
                                                  if (
                                                      !riskId ||
                                                      sharedVendorsRisksMutationController
                                                          .deleteRiskMutation
                                                          .isPending
                                                  ) {
                                                      return;
                                                  }

                                                  sharedVendorsRisksMutationController.deleteRisk(
                                                      riskId,
                                                  );
                                              }),
                                              onCancel: () => {
                                                  closeConfirmationModal();
                                              },
                                          });
                                      },
                                  },
                              ]
                    }
                />
            </Grid>
        );
    },
);
