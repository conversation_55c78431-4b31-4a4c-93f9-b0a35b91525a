import {
    sharedCodebaseFindingExclusionMutationController,
    sharedFindingExclusionMutationController,
} from '@controllers/monitoring-details';
import { makeAutoObservable } from '@globals/mobx';

class MonitoringRemoveExclusionConfirmationModalModel {
    constructor() {
        makeAutoObservable(this);
    }

    get isLoading(): boolean {
        return (
            sharedFindingExclusionMutationController.isRemovingExclusionsBulk ||
            sharedCodebaseFindingExclusionMutationController.isRemovingExclusionsBulk
        );
    }
}

export const sharedMonitoringRemoveExclusionConfirmationModalModel =
    new MonitoringRemoveExclusionConfirmationModalModel();
