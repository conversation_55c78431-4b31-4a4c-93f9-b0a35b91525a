import { Modal } from '@cosmos/components/modal';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedMonitoringRemoveExclusionConfirmationModalModel } from './models/monitoring-remove-exclusion-confirmation-modal.model';

interface MonitoringRemoveExclusionConfirmationModalProps {
    onConfirm: () => void;
    onCancel: () => void;
    'data-id'?: string;
}

export const MonitoringRemoveExclusionConfirmationModal = observer(
    ({
        onConfirm,
        onCancel,
        'data-id': dataId = 'MonitoringRemoveExclusionConfirmationModal',
    }: MonitoringRemoveExclusionConfirmationModalProps): React.JSX.Element => {
        const { isLoading } =
            sharedMonitoringRemoveExclusionConfirmationModalModel;

        return (
            <div data-id={dataId}>
                <Modal.Header
                    title={t`Remove exclusion?`}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={onCancel}
                />
                <Modal.Body>
                    <Text colorScheme="neutral">
                        {t`Are you sure you want to remove this exclusion and include it back in the test results?`}
                    </Text>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: onCancel,
                        },
                        {
                            label: t`Confirm`,
                            level: 'primary',
                            colorScheme: 'primary',
                            isLoading,
                            onClick: onConfirm,
                        },
                    ]}
                />
            </div>
        );
    },
);
