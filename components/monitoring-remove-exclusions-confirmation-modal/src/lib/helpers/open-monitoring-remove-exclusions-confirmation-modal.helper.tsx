import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import { MONITORING_REMOVE_EXCLUSION_CONFIRMATION_MODAL_ID } from '../constants/modal.constants';
import { MonitoringRemoveExclusionConfirmationModal } from '../monitoring-remove-exclusions-confirmation-modal.component';

export const openMonitoringRemoveExclusionConfirmationModal = action(
    ({ onConfirm }: { onConfirm: () => void }): void => {
        modalController.openModal({
            id: MONITORING_REMOVE_EXCLUSION_CONFIRMATION_MODAL_ID,
            content: () => (
                <MonitoringRemoveExclusionConfirmationModal
                    data-id="monitoring-remove-exclusion-confirmation-modal"
                    onConfirm={onConfirm}
                    onCancel={() => {
                        modalController.closeModal(
                            MONITORING_REMOVE_EXCLUSION_CONFIRMATION_MODAL_ID,
                        );
                    }}
                />
            ),
            centered: true,
            disableClickOutsideToClose: true,
            size: 'md',
        });
    },
);

export const closeMonitoringRemoveExclusionConfirmationModal = action(
    (): void => {
        modalController.closeModal(
            MONITORING_REMOVE_EXCLUSION_CONFIRMATION_MODAL_ID,
        );
    },
);
