import { debounce, isEmpty } from 'lodash-es';
import { useCallback, useEffect, useMemo } from 'react';
import { Accordion } from '@cosmos/components/accordion';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import {
    type CustomFieldRenderProps,
    UniversalFormField,
    useFormContext,
    useUniversalFieldController,
} from '@ui/forms';
import { VendorSecurityReviewsSocSupportingContentComponent } from '../components/vendors-security-reviews-soc-supporting-content-component';
import { END_USER_CONTROLS_COUNT_TOTAL } from '../constants/vendors-security-reviews-soc.constants';
import {
    getEndUserControlsCompletionCounter,
    hasFormSectionErrors,
    useAccordionExpansion,
} from '../helpers/soc-form-completion-counters.helper';
import type { SocReviewFormValuesType } from '../types/soc-review-form-values.type';

interface UserControl {
    inPlace?: string;
    [key: string]: unknown;
}

export const EndUserControlsAccordionField = ({
    'data-id': dataId,
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    const { watch, setValue, formState } = useFormContext();
    const formValues: SocReviewFormValuesType = watch();

    const endUserControlsCompletedCounter =
        getEndUserControlsCompletionCounter(formValues);

    const hasErrors = hasFormSectionErrors(formState.errors, 'endUserControls');

    const { isExpanded, onExpandedChange } = useAccordionExpansion(hasErrors);

    const [noControlsChecked] = useUniversalFieldController(
        `${name}.noControlsChecked`,
    );
    const [allControlsInPlace] = useUniversalFieldController(
        `${name}.allControlsInPlace`,
    );

    // useUniversalFieldController does not work because the state is deeply nested
    const userControls = watch(`${name}.userControls`);

    // Hack to trigger useEffect dependency check
    const concatenatedInPlace =
        Array.isArray(userControls) &&
        (userControls as UserControl[])
            .map((control) => control.inPlace)
            .join('-');

    const updateFormValue = useCallback(
        (fieldPath: string, value: string | UserControl[]) => {
            setValue(fieldPath, value as never, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true,
            });
        },
        [setValue],
    );

    const debouncedUpdateFormValue = useMemo(
        () => debounce(updateFormValue, 20),
        [updateFormValue],
    );

    // Effect: When "allControlsInPlace" changes, update all individual controls
    useEffect(() => {
        if (
            !allControlsInPlace.value ||
            !Array.isArray(userControls) ||
            isEmpty(userControls)
        ) {
            return;
        }

        const controls = userControls as UserControl[];
        const targetValue =
            allControlsInPlace.value === 'YES_TO_ALL' ? 'YES' : 'NO';

        const needsUpdate = controls.some(
            (control) => control.inPlace !== targetValue,
        );

        if (needsUpdate) {
            const updatedControls = controls.map((control) => ({
                ...control,
                inPlace: targetValue,
            }));

            debouncedUpdateFormValue(`${name}.userControls`, updatedControls);
        }
    }, [
        allControlsInPlace.value,
        userControls,
        name,
        debouncedUpdateFormValue,
    ]);

    // Effect: When individual controls change, update allControlsInPlace based on their state
    useEffect(() => {
        if (!Array.isArray(userControls) || isEmpty(userControls)) {
            return;
        }

        const controls = userControls as UserControl[];
        const allYes = controls.every((control) => control.inPlace === 'YES');
        const allNo = controls.every((control) => control.inPlace === 'NO');

        if (allYes && allControlsInPlace.value !== 'YES_TO_ALL') {
            debouncedUpdateFormValue(
                `${name}.allControlsInPlace`,
                'YES_TO_ALL',
            );
        } else if (allNo && allControlsInPlace.value !== 'NO_TO_ALL') {
            debouncedUpdateFormValue(`${name}.allControlsInPlace`, 'NO_TO_ALL');
        } else if (!allYes && !allNo && allControlsInPlace.value) {
            debouncedUpdateFormValue(`${name}.allControlsInPlace`, '');
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps -- do not run for allControlsInPlace.value
    }, [concatenatedInPlace, name, debouncedUpdateFormValue, userControls]);

    return (
        <FormField
            shouldHideLabel
            formId={formId}
            name={name}
            label={t`End User Controls`}
            data-testid="EndUserControlsAccordionField"
            data-id={dataId}
            renderInput={() => (
                <Accordion
                    title={t`End user controls`}
                    data-id={`${dataId}-accordion`}
                    data-testid="EndUserControlsAccordionField"
                    isExpanded={isExpanded}
                    supportingContent={
                        <VendorSecurityReviewsSocSupportingContentComponent
                            total={END_USER_CONTROLS_COUNT_TOTAL}
                            completed={endUserControlsCompletedCounter}
                        />
                    }
                    body={
                        <Stack
                            gap="xl"
                            direction="column"
                            data-id={`${dataId}-content`}
                        >
                            <UniversalFormField
                                __fromCustomRender
                                name={`${name}.noControlsChecked`}
                                formId={formId}
                                data-id={`${dataId}-no-controls-field`}
                            />

                            {!noControlsChecked.value && (
                                <UniversalFormField
                                    __fromCustomRender
                                    name={`${name}.allControlsInPlace`}
                                    formId={formId}
                                    data-id={`${dataId}-all-controls-in-place-field`}
                                />
                            )}

                            <UniversalFormField
                                __fromCustomRender
                                name={`${name}.userControls`}
                                formId={formId}
                                data-id={`${dataId}-user-controls-field`}
                            />
                        </Stack>
                    }
                    onExpandedChange={onExpandedChange}
                />
            )}
        />
    );
};
