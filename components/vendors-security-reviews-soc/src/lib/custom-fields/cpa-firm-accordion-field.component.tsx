import { Accordion } from '@cosmos/components/accordion';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import {
    type CustomFieldRenderProps,
    UniversalFormField,
    useFormContext,
} from '@ui/forms';
import { VendorSecurityReviewsSocSupportingContentComponent } from '../components/vendors-security-reviews-soc-supporting-content-component';
import {
    hasFormSectionErrors,
    useAccordionExpansion,
} from '../helpers/soc-form-completion-counters.helper';

export const CpaFirmAccordionField = ({
    'data-id': dataId,
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    const { formState } = useFormContext();
    const hasErrors = hasFormSectionErrors(formState.errors, 'cpaFirm');
    const { isExpanded, onExpandedChange } = useAccordionExpansion(hasErrors);

    return (
        <FormField
            shouldHideLabel
            formId={formId}
            name={name}
            label={t`CPA Firm`}
            data-testid="CpaFirmAccordionField"
            data-id={dataId}
            renderInput={() => (
                <Accordion
                    title={t`CPA firm`}
                    data-id={`${dataId}-accordion`}
                    data-testid="CpaFirmAccordionField"
                    isExpanded={isExpanded}
                    supportingContent={
                        <VendorSecurityReviewsSocSupportingContentComponent
                            label={t`Optional`}
                        />
                    }
                    body={
                        <Stack
                            gap="xl"
                            direction="column"
                            data-id={`${dataId}-content`}
                        >
                            <UniversalFormField
                                name={`${name}.cpaFirm`}
                                formId={formId}
                                data-id={`${dataId}-cpa-firm-field`}
                            />

                            <UniversalFormField
                                name={`${name}.cpaProcedurePerformed`}
                                formId={formId}
                                data-id={`${dataId}-cpa-procedure-performed-field`}
                            />
                        </Stack>
                    }
                    onExpandedChange={onExpandedChange}
                />
            )}
        />
    );
};
