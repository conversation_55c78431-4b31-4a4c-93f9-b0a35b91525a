import { Accordion } from '@cosmos/components/accordion';
import { FormField } from '@cosmos/components/form-field';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { formatDate } from '@helpers/date-time';
import {
    type CustomFieldRenderProps,
    UniversalFormField,
    useFormContext,
} from '@ui/forms';
import { VendorSecurityReviewsSocSupportingContentComponent } from '../components/vendors-security-reviews-soc-supporting-content-component';
import { REVIEWER_INFORMATION_COMPLETED_TOTAL } from '../constants/vendors-security-reviews-soc.constants';
import {
    getReviewerInfoCompletionCounter,
    hasFormSectionErrors,
    useAccordionExpansion,
} from '../helpers/soc-form-completion-counters.helper';
import type { SocReviewFormValuesType } from '../types/soc-review-form-values.type';

interface ReviewerInfoAccordionFieldProps extends CustomFieldRenderProps {
    reviewDate?: string;
}

export const ReviewerInfoAccordionField = ({
    'data-id': dataId,
    name,
    formId,
    reviewDate,
}: ReviewerInfoAccordionFieldProps): React.JSX.Element => {
    const { watch, formState } = useFormContext();
    const formValues: SocReviewFormValuesType = watch();

    const reviewerInfoCompletedCounter =
        getReviewerInfoCompletionCounter(formValues);

    const hasErrors = hasFormSectionErrors(formState.errors, 'reviewerInfo');

    const { isExpanded, onExpandedChange } = useAccordionExpansion(hasErrors);

    return (
        <FormField
            shouldHideLabel
            formId={formId}
            name={name}
            label={t`Reviewer Information`}
            data-testid="ReviewerInfoAccordionField"
            data-id={dataId}
            renderInput={() => (
                <Accordion
                    title={t`Reviewer information`}
                    data-id={`${dataId}-accordion`}
                    data-testid="ReviewerInfoAccordionField"
                    isExpanded={isExpanded}
                    supportingContent={
                        <VendorSecurityReviewsSocSupportingContentComponent
                            total={REVIEWER_INFORMATION_COMPLETED_TOTAL}
                            completed={reviewerInfoCompletedCounter}
                        />
                    }
                    body={
                        <Stack
                            gap="xl"
                            direction="column"
                            data-id={`${dataId}-content`}
                        >
                            <UniversalFormField
                                __fromCustomRender
                                name={`${name}.reviewer`}
                                formId={formId}
                                data-id={`${dataId}-reviewer-field`}
                            />

                            <UniversalFormField
                                __fromCustomRender
                                name={`${name}.reportIssueDate`}
                                formId={formId}
                                data-id={`${dataId}-report-issue-date-field`}
                            />

                            <KeyValuePair
                                label={t`Review date`}
                                data-id="emmCZ1Q4"
                                value={formatDate('sentence', reviewDate)}
                            />
                        </Stack>
                    }
                    onExpandedChange={onExpandedChange}
                />
            )}
        />
    );
};
