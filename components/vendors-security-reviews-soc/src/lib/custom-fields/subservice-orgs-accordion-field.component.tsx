import { Accordion } from '@cosmos/components/accordion';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import {
    type CustomFieldRenderProps,
    UniversalFormField,
    useFormContext,
} from '@ui/forms';
import { VendorSecurityReviewsSocSupportingContentComponent } from '../components/vendors-security-reviews-soc-supporting-content-component';
import {
    hasFormSectionErrors,
    useAccordionExpansion,
} from '../helpers/soc-form-completion-counters.helper';

export const SubserviceOrgsAccordionField = ({
    'data-id': dataId,
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    const { formState } = useFormContext();
    const hasErrors = hasFormSectionErrors(formState.errors, 'subserviceOrgs');
    const { isExpanded, onExpandedChange } = useAccordionExpansion(hasErrors);

    return (
        <FormField
            shouldHideLabel
            formId={formId}
            name={name}
            label={t`Subservice Organizations`}
            data-testid="SubserviceOrgsAccordionField"
            data-id={dataId}
            renderInput={() => (
                <Accordion
                    title={t`Subservice organization`}
                    data-id={`${dataId}-accordion`}
                    data-testid="SubserviceOrgsAccordionField"
                    isExpanded={isExpanded}
                    supportingContent={
                        <VendorSecurityReviewsSocSupportingContentComponent
                            label={t`Optional`}
                        />
                    }
                    body={
                        <Stack
                            gap="xl"
                            direction="column"
                            data-id={`${dataId}-content`}
                        >
                            <UniversalFormField
                                name={`${name}.subserviceOrganization`}
                                formId={formId}
                                data-id={`${dataId}-subservice-organization-field`}
                            />

                            <UniversalFormField
                                name={`${name}.subserviceOrganizationUsingInclusiveMethod`}
                                formId={formId}
                                data-id={`${dataId}-subservice-organization-using-inclusive-method-field`}
                            />

                            <UniversalFormField
                                name={`${name}.subserviceOrganizationProcedurePerformed`}
                                formId={formId}
                                data-id={`${dataId}-subservice-organization-procedure-performed-field`}
                            />
                        </Stack>
                    }
                    onExpandedChange={onExpandedChange}
                />
            )}
        />
    );
};
