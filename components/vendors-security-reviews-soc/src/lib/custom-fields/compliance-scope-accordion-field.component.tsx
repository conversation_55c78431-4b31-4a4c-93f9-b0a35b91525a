import { Accordion } from '@cosmos/components/accordion';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import {
    type CustomFieldRenderProps,
    UniversalFormField,
    useFormContext,
} from '@ui/forms';
import { VendorSecurityReviewsSocSupportingContentComponent } from '../components/vendors-security-reviews-soc-supporting-content-component';
import {
    getComplianceScopeCompletionCounter,
    hasFormSectionErrors,
    useAccordionExpansion,
} from '../helpers/soc-form-completion-counters.helper';
import type { SocReviewFormValuesType } from '../types/soc-review-form-values.type';

export const ComplianceScopeAccordionField = ({
    'data-id': dataId,
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    const { watch, formState } = useFormContext();
    const formValues: SocReviewFormValuesType = watch();

    const { completed, total } =
        getComplianceScopeCompletionCounter(formValues);

    const hasErrors = hasFormSectionErrors(formState.errors, 'complianceScope');

    const { isExpanded, onExpandedChange } = useAccordionExpansion(hasErrors);

    return (
        <FormField
            shouldHideLabel
            formId={formId}
            name={name}
            label={t`Compliance Report Scope`}
            data-testid="ComplianceScopeAccordionField"
            data-id={dataId}
            renderInput={() => (
                <Accordion
                    title={t`Compliance report scope`}
                    data-id={`${dataId}-accordion`}
                    data-testid="ComplianceScopeAccordionField"
                    isExpanded={isExpanded}
                    supportingContent={
                        <VendorSecurityReviewsSocSupportingContentComponent
                            total={total}
                            completed={completed}
                        />
                    }
                    body={
                        <Stack
                            gap="xl"
                            direction="column"
                            data-id={`${dataId}-content`}
                        >
                            <UniversalFormField
                                __fromCustomRender
                                name={`${name}.certification`}
                                formId={formId}
                                data-id={`${dataId}-certification-field`}
                            />

                            <UniversalFormField
                                __fromCustomRender
                                name={`${name}.scopeType`}
                                formId={formId}
                                data-id={`${dataId}-scope-type-field`}
                            />

                            <UniversalFormField
                                __fromCustomRender
                                name={`${name}.trustServiceCriteria`}
                                formId={formId}
                                data-id={`${dataId}-trust-service-criteria-field`}
                            />

                            <UniversalFormField
                                __fromCustomRender
                                name={`${name}.auditPeriod`}
                                formId={formId}
                                data-id={`${dataId}-audit-period-field`}
                            />

                            <UniversalFormField
                                __fromCustomRender
                                name={`${name}.auditPeriodRange`}
                                formId={formId}
                                data-id={`${dataId}-audit-period-range-field`}
                            />

                            <UniversalFormField
                                __fromCustomRender
                                name={`${name}.bridgeLetter`}
                                formId={formId}
                                data-id={`${dataId}-bridge-letter-field`}
                            />
                        </Stack>
                    }
                    onExpandedChange={onExpandedChange}
                />
            )}
        />
    );
};
