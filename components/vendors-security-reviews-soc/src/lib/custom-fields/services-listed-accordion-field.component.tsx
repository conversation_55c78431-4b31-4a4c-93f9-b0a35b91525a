import { Accordion } from '@cosmos/components/accordion';
import { FormField } from '@cosmos/components/form-field';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import {
    type CustomFieldRenderProps,
    UniversalFormField,
    useFormContext,
} from '@ui/forms';
import { VendorSecurityReviewsSocSupportingContentComponent } from '../components/vendors-security-reviews-soc-supporting-content-component';
import {
    hasFormSectionErrors,
    useAccordionExpansion,
} from '../helpers/soc-form-completion-counters.helper';

export const ServicesListedAccordionField = ({
    'data-id': dataId,
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    const { formState } = useFormContext();
    const hasErrors = hasFormSectionErrors(formState.errors, 'servicesListed');
    const { isExpanded, onExpandedChange } = useAccordionExpansion(hasErrors);

    return (
        <FormField
            shouldHideLabel
            formId={formId}
            name={name}
            label={t`Services Listed`}
            data-testid="ServicesListedAccordionField"
            data-id={dataId}
            renderInput={() => (
                <Accordion
                    title={t`Services listed`}
                    data-id={`${dataId}-accordion`}
                    data-testid="ServicesListedAccordionField"
                    isExpanded={isExpanded}
                    supportingContent={
                        <VendorSecurityReviewsSocSupportingContentComponent
                            label={t`Optional`}
                        />
                    }
                    body={
                        <Stack
                            gap="xl"
                            direction="column"
                            data-id={`${dataId}-content`}
                        >
                            <Text size="200">
                                {t`List of services included in the report`}
                            </Text>

                            <UniversalFormField
                                name={`${name}.services`}
                                formId={formId}
                                data-id={`${dataId}-services-field`}
                            />

                            <Text size="200">
                                {t`Locations covered by report (if applicable)`}
                            </Text>

                            <UniversalFormField
                                name={`${name}.locations`}
                                formId={formId}
                                data-id={`${dataId}-locations-field`}
                            />
                        </Stack>
                    }
                    onExpandedChange={onExpandedChange}
                />
            )}
        />
    );
};
