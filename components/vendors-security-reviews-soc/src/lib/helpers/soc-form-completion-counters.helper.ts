import { isEmpty, isNil, isObject } from 'lodash-es';
import { useEffect, useState } from 'react';
import type { FieldErrors } from 'react-hook-form';
import {
    CERTIFICATION_SCOPE_COMPLETED_TOTALS,
    CERTIFICATION_SCOPE_COMPLETED_TOTALS_DEFAULT,
    END_USER_CONTROLS_COUNT_TOTAL,
    NO_REPORT_FINDINGS_CHECKED,
    NO_REPORT_FINDINGS_NOT_CHECKED,
    REPORT_OPINION_COMPLETED_TOTAL,
    REVIEWER_INFORMATION_COMPLETED_TOTAL,
} from '../constants/vendors-security-reviews-soc.constants';
import type { SocReviewFormValuesType } from '../types/soc-review-form-values.type';

/**
 * Calculate completion counter for Reviewer Information accordion.
 * Required fields: reviewer, reportIssueDate.
 */
export const getReviewerInfoCompletionCounter = (
    formValues: SocReviewFormValuesType,
): number => {
    const { reviewerInfo } = formValues;

    if (
        isNil(reviewerInfo) ||
        (isNil(reviewerInfo.reviewer) && isNil(reviewerInfo.reportIssueDate))
    ) {
        return 0;
    }

    let completed = 0;

    // Reviewer field returns {id, label, value} object from select
    if (
        reviewerInfo.reviewer?.value &&
        String(reviewerInfo.reviewer.value).trim() !== ''
    ) {
        completed = completed + 1;
    }

    if (
        !isNil(reviewerInfo.reportIssueDate) &&
        String(reviewerInfo.reportIssueDate).trim() !== ''
    ) {
        completed = completed + 1;
    }

    return completed;
};

/**
 * Calculate completion counter for Compliance Scope accordion.
 * Required fields vary by SOC type:
 * - SOC 1: certification, scopeType, auditPeriod (3 fields).
 * - SOC 2: certification, scopeType, auditPeriod, trustServiceCriteria (4 fields).
 * - SOC 3: certification, auditPeriod (2 fields).
 */
export const getComplianceScopeCompletionCounter = (
    formValues: SocReviewFormValuesType,
): { completed: number; total: number } => {
    const { complianceScope } = formValues;
    // Certification field returns {id, label, value} object from select
    const certification = complianceScope?.certification?.value;

    let completed = 0;
    let total = CERTIFICATION_SCOPE_COMPLETED_TOTALS_DEFAULT;

    // Determine total based on certification type - this updates immediately when certification changes
    switch (certification) {
        case 'SOC_1': {
            total = CERTIFICATION_SCOPE_COMPLETED_TOTALS.SOC1;

            break;
        }
        case 'SOC_2': {
            total = CERTIFICATION_SCOPE_COMPLETED_TOTALS.SOC2;

            break;
        }
        case 'SOC_3': {
            total = CERTIFICATION_SCOPE_COMPLETED_TOTALS.SOC3;

            break;
        }
        default:
        // Do nothing
    }

    // If no compliance scope exists, return early with the calculated total
    if (!complianceScope) {
        return { completed: 0, total };
    }

    if (certification && String(certification).trim() !== '') {
        completed = completed + 1;
    }

    if (
        certification !== 'SOC_3' &&
        complianceScope.scopeType &&
        String(complianceScope.scopeType).trim() !== ''
    ) {
        completed = completed + 1;
    }

    if (certification !== 'SOC_3') {
        const hasAuditPeriod =
            complianceScope.scopeType === 'type1'
                ? complianceScope.auditPeriod &&
                  String(complianceScope.auditPeriod).trim() !== ''
                : complianceScope.auditPeriodRange?.start &&
                  complianceScope.auditPeriodRange.end;

        if (hasAuditPeriod) {
            completed = completed + 1;
        }
    }

    if (
        certification !== 'SOC_1' &&
        complianceScope.trustServiceCriteria &&
        Array.isArray(complianceScope.trustServiceCriteria) &&
        !isEmpty(complianceScope.trustServiceCriteria)
    ) {
        completed = completed + 1;
    }

    return { completed, total };
};

/**
 * Calculate completion counter for Report Opinion accordion.
 * Required fields: reportOpinion.
 */
export const getReportOpinionCompletionCounter = (
    formValues: SocReviewFormValuesType,
): number => {
    const { reportOpinion } = formValues;

    if (isNil(reportOpinion) || isNil(reportOpinion.reportOpinion)) {
        return 0;
    }

    let completed = 0;

    // ReportOpinion field returns {id, label, value} object from select
    if (String(reportOpinion.reportOpinion.value).trim() !== '') {
        completed = completed + 1;
    }

    return completed;
};

/**
 * Calculate completion counter for Report Findings accordion.
 * Logic:
 * - If "No findings" is checked: 1/1 completed.
 * - If "No findings" is not checked: need findings list + material impact = 2 total.
 */
export const getReportFindingsCompletionCounter = (
    formValues: SocReviewFormValuesType,
): { completed: number; total: number } => {
    const { reportFindings } = formValues;

    if (!reportFindings) {
        return { completed: 0, total: NO_REPORT_FINDINGS_NOT_CHECKED };
    }

    // If "No findings" is checked, it's considered complete
    if (reportFindings.noFindingsChecked === true) {
        return {
            completed: NO_REPORT_FINDINGS_CHECKED,
            total: NO_REPORT_FINDINGS_CHECKED,
        };
    }

    // If "No findings" is not checked, need findings list and material impact
    let completed = 0;
    const total = NO_REPORT_FINDINGS_NOT_CHECKED;

    // Check findings list
    if (
        reportFindings.findings &&
        Array.isArray(reportFindings.findings) &&
        reportFindings.findings.some(
            (finding) =>
                finding.description &&
                String(finding.description).trim() !== '',
        )
    ) {
        completed = completed + 1;
    }

    // Check material impact question
    if (!isNil(reportFindings.hasMaterialImpact)) {
        completed = completed + 1;
    }

    return { completed, total };
};

/**
 * Calculate completion counter for End User Controls accordion.
 * Logic:
 * - If "No controls" is checked: 1/1 completed.
 * - If "No controls" is not checked: need controls list = 1 total.
 */
export const getEndUserControlsCompletionCounter = (
    formValues: SocReviewFormValuesType,
): number => {
    const { endUserControls } = formValues;

    if (!endUserControls) {
        return 0;
    }

    if (endUserControls.noControlsChecked === true) {
        return END_USER_CONTROLS_COUNT_TOTAL;
    }

    if (
        endUserControls.userControls &&
        Array.isArray(endUserControls.userControls) &&
        endUserControls.userControls.some(
            (control) =>
                control.control && String(control.control).trim() !== '',
        )
    ) {
        return END_USER_CONTROLS_COUNT_TOTAL;
    }

    return 0;
};

/**
 * Get all completion counters for the SOC form.
 */
export const getSocFormCompletionCounters = (
    formValues: SocReviewFormValuesType,
): {
    reviewerInfo: { completed: number; total: number };
    complianceScope: { completed: number; total: number };
    reportOpinion: { completed: number; total: number };
    reportFindings: { completed: number; total: number };
    endUserControls: { completed: number; total: number };
} => {
    const reviewerInfo = getReviewerInfoCompletionCounter(formValues);
    const complianceScope = getComplianceScopeCompletionCounter(formValues);
    const reportOpinion = getReportOpinionCompletionCounter(formValues);
    const reportFindings = getReportFindingsCompletionCounter(formValues);
    const endUserControls = getEndUserControlsCompletionCounter(formValues);

    return {
        reviewerInfo: {
            completed: reviewerInfo,
            total: REVIEWER_INFORMATION_COMPLETED_TOTAL,
        },
        complianceScope: {
            completed: complianceScope.completed,
            total: complianceScope.total,
        },
        reportOpinion: {
            completed: reportOpinion,
            total: REPORT_OPINION_COMPLETED_TOTAL,
        },
        reportFindings: {
            completed: reportFindings.completed,
            total: reportFindings.total,
        },
        endUserControls: {
            completed: endUserControls,
            total: END_USER_CONTROLS_COUNT_TOTAL,
        },
    };
};

export const hasFormSectionErrors = (
    errors: FieldErrors | undefined,
    sectionName: string,
): boolean => {
    if (!errors || !isObject(errors) || isEmpty(errors)) {
        return false;
    }

    return Object.keys(errors).some((errorKey) =>
        errorKey.startsWith(sectionName),
    );
};

export const useAccordionExpansion = (
    hasErrors: boolean,
): {
    isExpanded: boolean | undefined;
    onExpandedChange: (expanded: boolean) => void;
} => {
    const [isManuallyExpanded, setIsManuallyExpanded] = useState<
        boolean | undefined
    >(undefined);

    // Auto-expand when errors occur (but only if user hasn't manually set a state)
    useEffect(() => {
        if (hasErrors && isManuallyExpanded === undefined) {
            // eslint-disable-next-line react-you-might-not-need-an-effect/no-chain-state-updates -- needed to expand accordion when errors occur
            setIsManuallyExpanded(true);
        }
    }, [hasErrors, isManuallyExpanded]);

    const isExpanded = isManuallyExpanded ?? hasErrors;

    return {
        isExpanded,
        onExpandedChange: setIsManuallyExpanded,
    };
};
