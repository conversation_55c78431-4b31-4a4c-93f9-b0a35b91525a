import { Stack } from '@cosmos/components/stack';
import { CodeViewer } from '@cosmos-lab/components/code-viewer';
import { formatEvaluator } from './helpers/library-test-logic.helper';
import type { LibraryTestLogicCardAdvancedComponentProps } from './types/library-test-logic-card-advanced-component.interface';

export const LibraryTestLogicCardAdvancedComponent = ({
    evaluator,
}: LibraryTestLogicCardAdvancedComponentProps): React.JSX.Element => {
    const formattedEvaluator = formatEvaluator(evaluator);

    return (
        <Stack
            direction="column"
            gap="2x"
            data-testid="LibraryTestLogicCardAdvancedComponent"
            data-id="MwMFs9vE"
        >
            <CodeViewer
                language="json"
                value={formattedEvaluator}
                data-id="library-test-logic-card-advanced-component"
            />
        </Stack>
    );
};
