import { Card } from '@cosmos/components/card';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { isAdvancedRecipe } from './helpers/library-test-logic.helper';
import { LibraryTestLogicCardAdvancedComponent } from './library-test-logic-card-advanced-component';
import { LibraryTestLogicCardConditionComponent } from './library-test-logic-card-condition-component';
import type { Recipe } from './types/recipe.type';

interface LibraryTestLogicContentComponentProps {
    recipe: Recipe | null;
}

export const LibraryTestLogicContentComponent = observer(
    ({ recipe }: LibraryTestLogicContentComponentProps): React.JSX.Element => {
        return (
            <Stack
                direction="column"
                gap="2x"
                data-testid="LibraryTestLogicContentComponent"
                data-id="MwMFs9vE"
            >
                {recipe?.providers?.flatMap(({ provider, resources }) =>
                    resources.map(
                        ({ resource, evaluator, filteringCriteria }) => (
                            <Card
                                key={`${provider}-${resource}`}
                                title={t`Condition group`}
                                data-id="2sXwTFkK"
                                data-testid="LibraryTestLogicContentCard"
                                body={
                                    <>
                                        <Stack
                                            direction="row"
                                            align="start"
                                            gap="6x"
                                        >
                                            <KeyValuePair
                                                key={`${provider}-${resource}-service`}
                                                label={t`Service`}
                                                type="TEXT"
                                                value={provider}
                                            />
                                            <KeyValuePair
                                                key={`${provider}-${resource}-resource`}
                                                label={t`Resource`}
                                                type="TEXT"
                                                value={resource}
                                            />
                                        </Stack>
                                        {isAdvancedRecipe(evaluator) ? (
                                            <LibraryTestLogicCardAdvancedComponent
                                                evaluator={evaluator}
                                            />
                                        ) : (
                                            <LibraryTestLogicCardConditionComponent
                                                isFirstNode
                                                testLogicKey={`test-logic-${provider}-${resource}`}
                                                evaluator={evaluator}
                                                filteringCriteria={
                                                    filteringCriteria
                                                }
                                            />
                                        )}
                                    </>
                                }
                            />
                        ),
                    ),
                )}
            </Stack>
        );
    },
);
