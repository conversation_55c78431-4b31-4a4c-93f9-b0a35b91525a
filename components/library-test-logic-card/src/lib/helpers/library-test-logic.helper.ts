import { head, isNil, isObject, isString } from 'lodash-es';
import { Operator } from '@drata/enums/dist/recipes/operator';
import { t } from '@globals/i18n/macro';
import type {
    ConditionProperties,
    FactValue,
    NestedCondition,
    TopLevelCondition,
    Value,
} from '../types/condition.type';

/**
 * Constants for condition options.
 */
const CONDITION_OPTION = {
    ANY_VALUE: 'any',
    ALL_VALUE: 'all',
} as const;

type ConditionKey = keyof typeof CONDITION_OPTION;
type ConditionValue = (typeof CONDITION_OPTION)[ConditionKey];

/**
 * Interface for property validation.
 */
interface PropertyDefinition {
    name: string;
}

/**
 * Regular expression to detect JSONPath special characters.
 */
const JSON_PATH_SPECIAL_CHARS = /[$@[\]*?]|\.\./;

/**
 * Checks if a key is a condition key (any or all).
 */
const isConditionKey = (key: string): key is ConditionValue => {
    return (
        key === CONDITION_OPTION.ANY_VALUE || key === CONDITION_OPTION.ALL_VALUE
    );
};

/**
 * Filters evaluator keys to find condition keys.
 */
const filterEvaluatorConditionKey = (key: string): boolean => {
    return isConditionKey(key);
};

/**
 * Formats complex JSONPath by removing special prefixes.
 */
const formatComplexPath = (path: string): string => {
    if (JSON_PATH_SPECIAL_CHARS.test(path)) {
        const specialCharacters = path.slice(0, 2);

        if (specialCharacters === '$.') {
            return path.slice(2);
        }
    }

    return path;
};

/**
 * Checks if a path contains complex JSONPath expressions.
 */
const isComplexPath = (path: string): boolean => {
    if (JSON_PATH_SPECIAL_CHARS.test(path)) {
        const complexPath = formatComplexPath(path);

        return JSON_PATH_SPECIAL_CHARS.test(complexPath);
    }

    return false;
};

/**
 * Type guard to check if a value is a FactValue object.
 */
const isFactValue = (value: unknown): value is FactValue => {
    return (
        isObject(value) &&
        'fact' in value &&
        isString((value as FactValue).fact)
    );
};

/**
 * Type guard to check if a condition has condition properties.
 */
const hasConditionProperties = (
    condition: unknown,
): condition is ConditionProperties => {
    return (
        isObject(condition) &&
        'fact' in condition &&
        'operator' in condition &&
        'value' in condition
    );
};

/**
 * Checks if a condition is a nested condition with all/any structure.
 */
const isNestedCondition = (
    condition: unknown,
): condition is TopLevelCondition => {
    if (!isObject(condition)) {
        return false;
    }

    const keys = Object.keys(condition);

    return keys.some((key) => isConditionKey(key));
};

/**
 * Determines if a recipe evaluator is advanced (invalid for simple builder).
 *
 * A recipe is considered advanced if it contains:
 * - Operators with 'any' or 'all' values
 * - Complex nested object values (FactValue structures)
 * - Complex JSONPath expressions in paths
 * - Paths that don't match available properties.
 *
 * @param evaluator - The TopLevelCondition to evaluate.
 * @param properties - Array of available property definitions for validation.
 * @param validatePath - Whether to validate paths against available properties (default: true).
 * @returns True if the evaluator is advanced/complex, false if it's simple.
 */
export const isAdvancedRecipe = (
    evaluator: TopLevelCondition,
    properties: PropertyDefinition[] = [],
    validatePath = true,
): boolean => {
    // Find the condition key (all or any)
    const evaluatorKeys = Object.keys(evaluator);
    const conditionKey = evaluatorKeys.find(filterEvaluatorConditionKey) as
        | ConditionValue
        | undefined;

    if (!conditionKey) {
        return false;
    }

    let conditions: NestedCondition[] | undefined;

    if ('all' in evaluator) {
        conditions = evaluator.all;
    } else if ('any' in evaluator) {
        conditions = evaluator.any;
    }

    if (!Array.isArray(conditions)) {
        return false;
    }

    return conditions.some((condition: NestedCondition) => {
        // If it's a nested condition (has all/any), recursively check
        if (isNestedCondition(condition)) {
            return isAdvancedRecipe(condition, properties, validatePath);
        }

        // Check if it has condition properties
        if (!hasConditionProperties(condition)) {
            return false;
        }

        const conditionKeys = Object.keys(condition);

        return conditionKeys.some((key) => {
            const conditionValue = condition[key as keyof ConditionProperties];

            // Check if operator uses any/all values
            if (
                key === 'operator' &&
                isString(conditionValue) &&
                isConditionKey(conditionValue)
            ) {
                return true;
            }

            // Check for complex object values (FactValue structures)
            if (key === 'value' && isFactValue(conditionValue)) {
                return true;
            }

            // Check for complex paths
            if (key === 'path' && isString(conditionValue) && validatePath) {
                const path = conditionValue;
                const { fact } = condition;

                // Check if path is not in available properties
                const pathNotInProperties = !properties.some(
                    ({ name }) => name === `${fact}.${formatComplexPath(path)}`,
                );

                // Check if path is complex
                const hasComplexPath = isComplexPath(path);

                return pathNotInProperties || hasComplexPath;
            }

            return false;
        });
    });
};

/**
 * Type guard to check if a value is a valid TopLevelCondition.
 */
const isValidTopLevelCondition = (
    value: unknown,
): value is TopLevelCondition => {
    return (
        isObject(value) &&
        (('all' in value && Array.isArray(value.all)) ||
            ('any' in value && Array.isArray(value.any)))
    );
};

/**
 * Formats a TopLevelCondition evaluator for display as JSON string.
 *
 * @param evaluator - The evaluator to format (will be validated as TopLevelCondition).
 * @returns Formatted JSON string representation of the evaluator.
 */
export const formatEvaluator = (evaluator: TopLevelCondition): string => {
    if (!isValidTopLevelCondition(evaluator)) {
        return 'Invalid evaluator format';
    }

    try {
        return JSON.stringify(evaluator, null, 2);
    } catch {
        return 'Invalid evaluator format';
    }
};

export const getConditionItemValue = (conditionItemValue: Value): string => {
    if (isFactValue(conditionItemValue)) {
        return String(conditionItemValue.value);
    }

    return String(conditionItemValue);
};

/**
 * Retrieves the complete fact path by combining the fact and path.
 */
export const getCompleteConditionFactPath = (
    fact: string,
    path?: string,
): string => {
    if (isNil(path)) {
        return fact;
    }

    const internalPath = path.replace('$', '.').replace('..', '.');

    return `${fact}${
        head(internalPath) === '.' ? internalPath : `.${internalPath}`
    }`;
};

/**
 * Retrieves the label for a given operator.
 */
export const getOperatorLabel = (operator: Operator): string => {
    switch (operator) {
        // Value should be equal to the comparison value (Operator for test builder)
        case Operator.equals: {
            return t`Equals`;
        }
        // Value should not be equal to the comparison value (Operator for test builder)
        case Operator.notEqual: {
            return t`Not Equals`;
        }
        // Value should be in the specified set of values (Operator for test builder)
        case Operator.in: {
            return t`In`;
        }
        // Value should not be in the specified set of values (Operator for test builder)
        case Operator.notIn: {
            return t`Not In`;
        }
        // String should contain the specified substring (Operator for test builder)
        case Operator.contains: {
            return t`Contains`;
        }
        // Value should be less than the comparison value (Operator for test builder)
        case Operator.lessThan: {
            return t`Less Than`;
        }
        // Value should be greater than the comparison value (Operator for test builder)
        case Operator.greaterThan: {
            return t`Greater Than`;
        }
        // Value should exist (Operator for test builder)
        case Operator.exists: {
            return t`Exist`;
        }
        // Value should be empty (Operator for test builder)
        case Operator.empty: {
            return t`Empty`;
        }
        // For two arrays, checks if they share any common elements. (Operator for test builder)
        case Operator.intersectsAny: {
            return t`Intersects Any`;
        }
        // For two arrays, checks if all elements of the first array exist in the second array. (Operator for test builder)
        case Operator.intersectsAll: {
            return t`Intersects All`;
        }
        // String should not contain the specified substring (Operator for test builder)
        case Operator.doesNotContain: {
            return t`Doesn't Contain`;
        }
        // Value should be less than or equal to the comparison value (Operator for test builder)
        case Operator.lessThanOrEqual: {
            return t`Less Than (Inclusive)`;
        }
        // Value should be greater than or equal to the comparison value (Operator for test builder)
        case Operator.greaterThanOrEqual: {
            return t`Greater Than (Inclusive)`;
        }
        // For strings, checks if one starts with the other. (Operator for test builder)
        case Operator.startsWith: {
            return t`Starts With`;
        }
        // For strings, checks if one ends with the other. (Operator for test builder)
        case Operator.endsWith: {
            return t`Ends With`;
        }
        // Date should be before the specified date (Operator for test builder)
        case Operator.isBefore: {
            return t`Before`;
        }
        // Date should be after the specified date (Operator for test builder)
        case Operator.isAfter: {
            return t`After`;
        }
        // For dates, checks if one date is within the last X number of days
        case Operator.withinLastDays: {
            return t`Within last (days)`;
        }
        // For dates, checks if one date is within the next X number of days
        case Operator.withinNextDays: {
            return t`Within next (days)`;
        }
        // For dates, checks if one date is within the last X number of hours
        case Operator.withinLastHours: {
            return t`Within last (hours)`;
        }
        // For an array, checks if all elements satisfy a given condition (Operator for test builder)
        case Operator.all: {
            return t`All`;
        }
        // For an array, checks if at least one element satisfies a given condition (Operator for test builder)
        case Operator.any: {
            return t`Any`;
        }
        // For an array, checks if no elements satisfy a given condition (Operator for test builder)
        case Operator.none: {
            return t`None`;
        }
        // For an array, checks if not all elements satisfy a given condition (Operator for test builder)
        case Operator.notAll: {
            return t`Not All`;
        }
        default: {
            return operator;
        }
    }
};
