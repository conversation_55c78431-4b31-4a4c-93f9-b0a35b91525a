import { some } from 'lodash-es';
import { Box } from '@cosmos/components/box';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import {
    getCompleteConditionFactPath,
    getConditionItemValue,
    getOperatorLabel,
} from './helpers/library-test-logic.helper';
import type { LibraryTestLogicCardConditionComponentProps } from './types/library-test-logic-card-condition-component.interface';

export const LibraryTestLogicCardConditionComponent = ({
    evaluator,
    testLogicKey,
    filteringCriteria,
    isGray = true,
    isFirstNode,
}: LibraryTestLogicCardConditionComponentProps): React.JSX.Element => {
    const conditionEvaluator = 'any' in evaluator ? 'Or' : 'And';
    const conditionKey = `${testLogicKey}-${conditionEvaluator}`;
    let condition;
    let isFlatCard;

    if ('any' in evaluator) {
        condition = evaluator.any;
    }

    if ('all' in evaluator) {
        condition = evaluator.all;
    }

    if (isFirstNode) {
        const hasNestedConditions = some(
            condition,
            (item) => !('fact' in item),
        );

        isFlatCard = !hasNestedConditions;
    }

    const isGrayCard = !(isFlatCard || !isGray);

    return (
        <Box
            data-testid="LibraryTestLogicCardConditionComponent"
            pl={isFlatCard ? '0x' : '3x'}
            data-id="RCd5TQnX"
        >
            {condition && condition.length > 1 && (
                <Text type="title">{conditionEvaluator}</Text>
            )}

            <Box
                p="3x"
                pl={isFlatCard ? '0x' : '3x'}
                borderColor="neutralBorderInitial"
                borderWidth={isFlatCard ? undefined : 'borderWidth2'}
                borderPosition="left"
                backgroundColor={
                    isGrayCard
                        ? 'neutralBackgroundMild'
                        : 'neutralBackgroundNone'
                }
            >
                {condition?.map((conditionItem) =>
                    'fact' in conditionItem ? (
                        <Stack
                            direction="row"
                            align="start"
                            gap="6xl"
                            py="2x"
                            key={`${conditionKey}-${conditionItem.operator}-${conditionItem.fact}`}
                        >
                            <KeyValuePair
                                label={t`Attribute`}
                                type="TEXT"
                                value={getCompleteConditionFactPath(
                                    conditionItem.fact,
                                    conditionItem.path,
                                )}
                            />
                            <KeyValuePair
                                label={t`Operator`}
                                type="TEXT"
                                value={getOperatorLabel(conditionItem.operator)}
                            />
                            <KeyValuePair
                                label={t`Value`}
                                type="TEXT"
                                value={getConditionItemValue(
                                    conditionItem.value,
                                )}
                            />
                        </Stack>
                    ) : (
                        <LibraryTestLogicCardConditionComponent
                            key={`${conditionKey}-nest-wrapper`}
                            testLogicKey={`${conditionKey}-nest`}
                            evaluator={conditionItem}
                            isGray={!isGrayCard}
                        />
                    ),
                )}
                {filteringCriteria && (
                    <Box
                        pt="4x"
                        borderColor="neutralBorderInitial"
                        borderWidth="borderWidth1"
                        borderPosition="top"
                    >
                        <KeyValuePair
                            label={t`Filtering Criteria`}
                            type="TEXT"
                            value={
                                filteringCriteria.mode === 'inclusion'
                                    ? t`Includes`
                                    : t`Excludes`
                            }
                        />

                        <LibraryTestLogicCardConditionComponent
                            testLogicKey={`${conditionKey}-filter`}
                            evaluator={filteringCriteria.evaluator}
                            isGray={!isGrayCard}
                        />
                    </Box>
                )}
            </Box>
        </Box>
    );
};
