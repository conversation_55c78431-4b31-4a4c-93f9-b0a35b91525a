import { Operator } from '@drata/enums';

export type SimpleValue = string | number | boolean | null;
export interface FactValue {
    fact: string;
    path?: string;
    params?: Record<string, unknown>;
    value?: SimpleValue;
}
export type Value = SimpleValue | FactValue;

export interface AllConditions<T = ConditionProperties> {
    all?: NestedCondition<T>[];
}
export interface AnyConditions<T = ConditionProperties> {
    any?: NestedCondition<T>[];
}
export interface ConditionProperties {
    fact: string;
    path?: string;
    params?: Record<string, unknown>;
    operator: Operator;
    value: Value;
}
export type TopLevelCondition<T = ConditionProperties> =
    | AllConditions<T>
    | AnyConditions<T>;
export type NestedCondition<T = ConditionProperties> = T | TopLevelCondition<T>;

export { Operator };
