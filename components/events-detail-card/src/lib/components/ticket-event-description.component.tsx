import { sharedEventsDetailsController } from '@controllers/events-details';
import { Text } from '@cosmos/components/text';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { EVENT_TYPE } from '../constants/events-details.constants';
import type { EventMetadata } from '../types/event-metadata.type';

export const TicketEventDescription = observer((): React.JSX.Element => {
    const { eventsDetailsData } = sharedEventsDetailsController;

    if (!eventsDetailsData) {
        return <EmptyValue label="-" />;
    }

    const { description, metadata, type } = eventsDetailsData;
    const { ticket } = metadata as EventMetadata;

    if (type === EVENT_TYPE.TICKET_DOWNLOAD || !ticket?.externalTicketId) {
        return <Text>{description}</Text>;
    }

    const { externalTicketId, url: ticketUrl } = ticket;
    const descriptionParts = description.split(externalTicketId);

    return (
        <Text data-testid="TicketingEventDescription" data-id="U_FjbQwL">
            {descriptionParts.shift()}
            <AppLink isExternal href={ticketUrl}>
                {externalTicketId}
            </AppLink>
            {descriptionParts.join(externalTicketId)}
        </Text>
    );
});
