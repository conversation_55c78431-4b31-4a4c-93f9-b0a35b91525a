import { sharedEventsDetailsController } from '@controllers/events-details';
import { Card } from '@cosmos/components/card';
import { Feedback } from '@cosmos/components/feedback';
import { CodeViewer } from '@cosmos-lab/components/code-viewer';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { AppButton } from '@ui/app-button';

export const EventsRawEvidenceCardComponent = observer(
    (): React.JSX.Element => {
        const {
            eventsDetailsCode,
            isEventDataTooLarge,
            eventsDetailsData,
            isLoading,
        } = sharedEventsDetailsController;

        const evidenceId = eventsDetailsData?.evidence?.id as number;
        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        if (!workspaceId) {
            throw new Error('Workspace ID is required');
        }

        return (
            <Card
                title={t`Raw event JSON`}
                size="md"
                data-id="EventsRawEvidenceCardComponent"
                actions={[
                    ...(evidenceId
                        ? [
                              {
                                  id: 'go-to-evidence-link',
                                  actionType:
                                      'cosmosUseWithCaution_customComponent' as const,
                                  typeProps: undefined as never,
                                  cosmosUseWithCaution_customComponent: (
                                      <AppButton
                                          label={t`View Evidence`}
                                          level="tertiary"
                                          href={`/workspaces/${workspaceId}/compliance/evidence/${evidenceId}`}
                                      />
                                  ),
                              },
                          ]
                        : []),
                    {
                        id: 'events-raw-evidence-card-dropdown',
                        actionType: 'dropdown',
                        typeProps: {
                            label: t`Download`,
                            level: 'secondary',
                            startIconName: 'Download',
                            align: 'end',
                            items: [
                                {
                                    id: `events-dropdown-download-pdf`,
                                    label: t`Evidence PDF`,
                                    description: t`Download a PDF of the event as evidence which includes the JSON and event details`,
                                    onSelect: action(() => {
                                        sharedEventsDetailsController.downloadPDF();
                                    }),
                                },
                                {
                                    id: `events-dropdown-download-txt`,
                                    label: t`Raw event TXT`,
                                    description: t`Download a TXT file of the raw event JSON only`,
                                    onSelect: action(() => {
                                        sharedEventsDetailsController.downloadTXT();
                                    }),
                                },
                            ],
                        },
                    },
                ]}
                body={
                    <>
                        {isEventDataTooLarge ? (
                            <Feedback
                                severity="warning"
                                title={t`The raw JSON for this event is too large to display.`}
                                description={t`Download the raw event JSON to review.`}
                                data-id="eventsRawEvidenceCard_Feedback"
                                data-testid="EventsRawEvidenceCardComponent"
                            />
                        ) : (
                            <CodeViewer
                                data-id="eventsRawEvidenceCard_CodeViewer"
                                isEditable={false}
                                isLoading={isLoading}
                                value={JSON.stringify(
                                    eventsDetailsCode,
                                    (_key: string, value: unknown): unknown =>
                                        value,
                                    '\t',
                                )}
                            />
                        )}
                    </>
                }
            />
        );
    },
);
