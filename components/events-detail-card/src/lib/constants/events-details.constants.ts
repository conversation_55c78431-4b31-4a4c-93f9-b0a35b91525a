import type { IconName } from '@cosmos/components/icon';
import type { ColorScheme } from '@cosmos/components/metadata';

export const CHECK_RESULT_STATUS_ENUM = {
    FAILED: 'FAILED',
    PASSED: 'PASSED',
    ERROR: 'ERROR',
    PREAUDIT: 'PREAUDIT',
    READY: 'READY',
};

export const CHECK_RESULT_STATUS_LABELS = {
    [CHECK_RESULT_STATUS_ENUM.ERROR]: 'Error',
    [CHECK_RESULT_STATUS_ENUM.PASSED]: 'Passed',
    [CHECK_RESULT_STATUS_ENUM.FAILED]: 'Failed',
    [CHECK_RESULT_STATUS_ENUM.PREAUDIT]: 'Pre-Audit To-Do',
    [CHECK_RESULT_STATUS_ENUM.READY]: 'Ready',
};

export const CHECK_RESULT_STATUS_ICONS: Record<string, IconName> = {
    [CHECK_RESULT_STATUS_ENUM.ERROR]: 'WarningTriangle',
    [CHECK_RESULT_STATUS_ENUM.PASSED]: 'CheckCircle',
    [CHECK_RESULT_STATUS_ENUM.FAILED]: 'NotReady',
    [CHECK_RESULT_STATUS_ENUM.PREAUDIT]: 'QuickStart',
    [CHECK_RESULT_STATUS_ENUM.READY]: 'Task',
};

export const CHECK_RESULT__STATUS_COLOR: Record<string, ColorScheme> = {
    [CHECK_RESULT_STATUS_ENUM.PASSED]: 'success',
    [CHECK_RESULT_STATUS_ENUM.FAILED]: 'critical',
    [CHECK_RESULT_STATUS_ENUM.ERROR]: 'warning',
    [CHECK_RESULT_STATUS_ENUM.PREAUDIT]: 'warning',
    [CHECK_RESULT_STATUS_ENUM.READY]: 'primary',
};

export const EVENT_TYPE = {
    TICKET_DOWNLOAD: 'TICKET_DOWNLOAD',
} as const;
