import { constant, isEmpty } from 'lodash-es';
import { z } from 'zod';
import {
    sharedCreateRiskMutationController,
    sharedRiskCategoriesController,
} from '@controllers/risk';
import { sharedUsersInfiniteController } from '@controllers/users';
import { sharedVendorsRisksMutationController } from '@controllers/vendors';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, toJS } from '@globals/mobx';
import { UPLOAD_FILES_ERROR_CODE_MESSAGES } from '@helpers/upload-file';
import type { FormSchema } from '@ui/forms';
import { RISK_SUPPORT_DOCUMENTS_ALLOWED_TYPES } from '../constants/create-risk-wizard.constants';

export class RiskDetailsFormModel {
    internalRiskType: 'VENDOR_RISK' | 'RISK' | undefined = undefined;

    constructor() {
        makeAutoObservable(this);
    }

    setRiskType = (riskType: 'VENDOR_RISK' | 'RISK'): void => {
        this.internalRiskType = riskType;
    };

    get usersOptions(): ListBoxItemData[] {
        return sharedUsersInfiniteController.usersList.map(
            ({ id, firstName, lastName, email, avatarUrl }) => ({
                id: String(id),
                label: `${firstName} ${lastName} - ${email}`,
                avatar: {
                    imgAlt: t`${firstName} ${lastName} image`,
                    imgSrc: avatarUrl ?? undefined,
                },
            }),
        );
    }

    get riskType(): 'VENDOR_RISK' | 'RISK' | undefined {
        return this.internalRiskType;
    }

    get categoriesOptions(): ListBoxItemData[] {
        return sharedRiskCategoriesController.categories.map((category) => ({
            id: category.id.toString(),
            label: category.name,
        }));
    }

    get getFormSchema(): FormSchema {
        const storedValues =
            this.riskType === 'VENDOR_RISK'
                ? sharedVendorsRisksMutationController.getMutatedStateForStep(
                      'details',
                  ).riskDetailsGroup
                : sharedCreateRiskMutationController.getMutatedStateForStep(
                      'details',
                  ).riskDetailsGroup;

        return {
            riskDetailsGroup: {
                type: 'group',
                header: t`Add your risk details`,
                showDivider: false,
                fields: {
                    title: {
                        type: 'text',
                        initialValue: toJS(storedValues?.title) ?? '',
                        label: t`Title`,
                        validator: z
                            .string()
                            .min(1, {
                                message: t`Title is required`,
                            })
                            .and(
                                z
                                    .string()
                                    .refine((val) => !isEmpty(val.trim()), {
                                        message: t`Title cannot be just whitespace`,
                                    }),
                            ),
                    },
                    description: {
                        type: 'textarea',
                        maxCharacters: 400,
                        initialValue: toJS(storedValues?.description) ?? '',
                        label: t`Description`,
                        validator: z
                            .string()
                            .min(1, {
                                message: t`Description is required`,
                            })
                            .and(
                                z
                                    .string()
                                    .refine((val) => !isEmpty(val.trim()), {
                                        message: t`Description cannot be just whitespace`,
                                    }),
                            ),
                    },
                    identifiedAt: {
                        type: 'date',
                        initialValue: toJS(
                            storedValues?.identifiedAt as
                                | TDateISODate
                                | undefined,
                        ),
                        label: t`Risk identified date`,
                        isOptional: true,
                    },
                    categories: {
                        type: 'combobox',
                        isMultiSelect: true,
                        getSearchEmptyState: constant(t`No categories found`),
                        options: this.categoriesOptions,
                        label: t`Risk categories`,
                        loaderLabel: t`Loading risk categories options`,
                        isOptional: true,
                        hasMore: sharedRiskCategoriesController.hasNextPage,
                        isLoading: sharedRiskCategoriesController.isLoading,
                        onFetchOptions:
                            sharedRiskCategoriesController.onFetchCategories,
                        ...(storedValues?.categories && {
                            initialValue: toJS(storedValues.categories),
                        }),
                    },
                    owners: {
                        type: 'combobox',
                        isMultiSelect: true,
                        getSearchEmptyState: constant(t`No users found`),
                        options: this.usersOptions,
                        label: t`Risk owners`,
                        loaderLabel: t`Loading risk owner options`,
                        isOptional: true,
                        hasMore: sharedUsersInfiniteController.hasNextPage,
                        isLoading: sharedUsersInfiniteController.isLoading,
                        onFetchOptions:
                            sharedUsersInfiniteController.onFetchUsers,
                        ...(storedValues?.owners && {
                            initialValue: toJS(storedValues.owners),
                        }),
                    },
                    documents: {
                        type: 'file',
                        label: t`Documents`,
                        isOptional: true,
                        acceptedFormats: RISK_SUPPORT_DOCUMENTS_ALLOWED_TYPES,
                        errorCodeMessages: UPLOAD_FILES_ERROR_CODE_MESSAGES,
                        innerLabel: t`Or drop files here`,
                        isMulti: true,
                        removeButtonText: t`Remove file`,
                        selectButtonText: t`Upload files`,
                        // Note: Documents are complex objects and may need special handling for initial values
                    },
                },
            },
        };
    }
}

export const sharedRiskDetailsFormModel = new RiskDetailsFormModel();
