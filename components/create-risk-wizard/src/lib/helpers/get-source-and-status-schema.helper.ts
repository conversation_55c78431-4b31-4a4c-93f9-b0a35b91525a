import type { CreateRiskCustomMutationType } from '@controllers/risk';
import { t } from '@globals/i18n/macro';
import { action, toJS } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';
import { sharedRiskDetailsFormModel } from '../models/risk-details-form.model';
import { getRiskStatusOptions } from './create-risk-options.helpers';
import { getRadioFieldOptions } from './get-radio-field-options.helper';

export const getRiskSourceAndStatusSchema = action(
    (
        vendorId?: number,
        isVendorRiskManagementProEnabled?: boolean,
        storedValues?: Partial<CreateRiskCustomMutationType>,
    ): FormSchema => {
        // Create base schema structure
        const schema: FormSchema = {};
        const { riskType } = sharedRiskDetailsFormModel;

        // Always add riskSourceGroup first (for order preservation)
        // but only include it when VRM Pro is enabled
        if (isVendorRiskManagementProEnabled) {
            const storedRiskSource = storedValues?.riskSourceGroup?.riskSource;
            const defaultRiskSource =
                riskType === 'RISK'
                    ? getRadioFieldOptions(vendorId)[0].value
                    : undefined;
            const initialValue = storedRiskSource?.value ?? defaultRiskSource;

            schema.riskSourceGroup = {
                type: 'group',
                header: t`Choose risk source`,
                fields: {
                    riskSource: {
                        type: 'choiceCardGroup',
                        options: getRadioFieldOptions(vendorId),
                        choiceCardInputType: 'radio',
                        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                        // TODO: make sure validation works after https://drata.atlassian.net/browse/ENG-67794
                        ...(vendorId && {
                            isOptional: true, // TODO: remove this line after https://drata.atlassian.net/browse/ENG-67794
                            initialValue,
                        }),
                        ...(initialValue &&
                            !vendorId && { initialValue: toJS(initialValue) }),
                    },
                },
            };
        }

        // Always add statusGroup second (for order preservation)
        const riskStatusOptions = getRiskStatusOptions();
        const storedStatus = storedValues?.statusGroup?.status;
        const defaultStatus = vendorId ? riskStatusOptions[0] : undefined;
        const statusInitialValue = storedStatus ?? defaultStatus;

        schema.statusGroup = {
            type: 'group',
            header: t`Risk Status`,
            showDivider: false,
            fields: {
                status: {
                    type: 'select',
                    label: t`Risk Status`,
                    shouldHideLabel: true,
                    options: riskStatusOptions,
                    ...(statusInitialValue && {
                        initialValue: toJS(statusInitialValue),
                    }),
                },
            },
        };

        return schema;
    },
);
