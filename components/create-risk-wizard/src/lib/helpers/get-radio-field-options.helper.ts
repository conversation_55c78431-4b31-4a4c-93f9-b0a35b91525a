import type { ChoiceCardOption } from '@cosmos-lab/components/choice-card-group';
import { t } from '@globals/i18n/macro';

export const getRadioFieldOptions = (vendorId?: number): ChoiceCardOption[] => {
    return [
        {
            helpText: t`Risks within your organization`,
            label: t`Internal risk`,
            value: 'INTERNAL',
            readOnly: <PERSON><PERSON><PERSON>(vendorId),
        },
        {
            helpText: t`Risks due to a third party e.g. Vendors, subcontractors.`,
            label: t`External risk`,
            value: 'EXTERNAL',
            readOnly: <PERSON><PERSON><PERSON>(vendorId),
        },
    ] as const satisfies ChoiceCardOption[];
};
