import { isNil } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedMonitorFindingsController } from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { DisabledCard } from './disabled-card';
import { FixCard } from './fix-card/fix-card';
import { ManageCard } from './manage-card';
import { TrackCard } from './track-card';

export const NextStepsBody = observer((): React.JSX.Element => {
    const { ticketingConnectionWithWriteAccess } = sharedConnectionsController;
    const { isMonitorFailinWithgNoFindingsAndWithFixNowPath, status } =
        sharedMonitoringTestDetailsController;
    const { failingResources } = sharedMonitorFindingsController;
    const { checkResultStatus } = sharedMonitoringTestDetailsController;

    if (status === 'DISABLED') {
        return <DisabledCard />;
    }

    if (isMonitorFailinWithgNoFindingsAndWithFixNowPath) {
        return (
            <Stack gap="lg" direction="column">
                <FixCard />
                <Stack gap="lg" direction="row">
                    <TrackCard />
                    {ticketingConnectionWithWriteAccess && <ManageCard />}
                </Stack>
            </Stack>
        );
    }

    return (
        <Stack gap="lg" direction="row" data-id="wI0fCYig">
            <TrackCard />
            {(isNil(failingResources) || checkResultStatus === 'FAILED') && (
                <FixCard />
            )}
            {ticketingConnectionWithWriteAccess && <ManageCard />}
        </Stack>
    );
});
