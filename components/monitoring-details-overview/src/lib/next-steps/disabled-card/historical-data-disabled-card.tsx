import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { Box } from '@cosmos/components/box';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { DateTime } from '@cosmos-lab/components/date-time';
import { Divider } from '@cosmos-lab/components/divider';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getTimeDiff } from '@helpers/date-time';
import { getInitials } from '@helpers/formatters';

export const HistoricalDataDisabledCard = observer((): React.JSX.Element => {
    const { testDetails } = sharedMonitoringTestDetailsController;

    const daysDisabled: number | null = testDetails?.disabledAt
        ? getTimeDiff(testDetails.disabledAt, new Date(), 'days')
        : null;

    return (
        <Box
            data-id="HistoricalDataDisabledCard"
            borderRadius="borderRadiusLg"
            borderColor="neutralBorderFaded"
            borderWidth="borderWidth1"
            p="lg"
        >
            <Stack direction="column" gap="lg">
                <Text type="title" size="200">
                    {t`Enable`}
                </Text>
                <Stack direction="column" gap="lg">
                    <StatBlock
                        title={t`Days disabled`}
                        statIcon="Calendar"
                        statIconColor="neutral"
                        statValueColor="neutral"
                        data-id="DaysDisabledStatBlock"
                        statValue={
                            daysDisabled !== null && daysDisabled > 0
                                ? daysDisabled.toString()
                                : '–'
                        }
                    />

                    <Divider />

                    <Stack direction="column" gap="lg">
                        <Stack direction="row" gap="6x">
                            <KeyValuePair
                                label={t`Disabled on`}
                                type="REACT_NODE"
                                value={
                                    testDetails?.disabledAt ? (
                                        <DateTime
                                            date={testDetails.disabledAt}
                                            format="field"
                                        />
                                    ) : (
                                        <Text
                                            type="body"
                                            size="200"
                                            colorScheme="neutral"
                                        >
                                            {t`Unknown`}
                                        </Text>
                                    )
                                }
                            />
                            <KeyValuePair
                                label={t`Disabled by`}
                                type="REACT_NODE"
                                value={
                                    testDetails?.disablingUser ? (
                                        <AvatarIdentity
                                            primaryLabel={`${testDetails.disablingUser.firstName} ${testDetails.disablingUser.lastName}`}
                                            fallbackText={getInitials(
                                                `${testDetails.disablingUser.firstName} ${testDetails.disablingUser.lastName}`,
                                            )}
                                            imgSrc={
                                                testDetails.disablingUser
                                                    .avatarUrl ?? undefined
                                            }
                                        />
                                    ) : (
                                        <Text
                                            type="body"
                                            size="200"
                                            colorScheme="neutral"
                                        >
                                            {t`Unknown`}
                                        </Text>
                                    )
                                }
                            />
                        </Stack>

                        <KeyValuePair
                            label={t`Rationale for disabling test`}
                            type="TEXT"
                            value={
                                testDetails?.disabledMessage ||
                                t`No reason provided.`
                            }
                        />
                    </Stack>
                </Stack>
            </Stack>
        </Box>
    );
});
