import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { observer } from '@globals/mobx';
import { HistoricalDataDisabledCard } from './historical-data-disabled-card';
import { NoHistoricalDataDisabledCard } from './no-historical-data-disabled-card';

export const DisabledCard = observer((): React.JSX.Element => {
    const { testDetails } = sharedMonitoringTestDetailsController;

    const hasHistoricalData =
        Boolean(testDetails?.lastCheck) &&
        Boolean(testDetails?.checkResultStatus);

    if (!hasHistoricalData) {
        return <NoHistoricalDataDisabledCard />;
    }

    return <HistoricalDataDisabledCard />;
});
