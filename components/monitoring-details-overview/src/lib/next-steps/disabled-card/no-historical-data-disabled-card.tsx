import { Box } from '@cosmos/components/box';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const NoHistoricalDataDisabledCard = observer((): React.JSX.Element => {
    return (
        <Box
            data-id="NoHistoricalDataDisabledCard"
            borderRadius="borderRadiusLg"
            borderColor="neutralBorderFaded"
            borderWidth="borderWidth1"
            p="lg"
        >
            <Stack direction="column" gap="lg">
                <Text type="title" size="200">
                    {t`Enable`}
                </Text>
                <Stack direction="column" gap="md">
                    <Icon name="Megaphone" size="300" colorScheme="education" />
                    <Text type="body" size="200" colorScheme="neutral">
                        {t`Tests help you to gather evidence and automate control monitoring.`}
                    </Text>
                </Stack>
            </Stack>
        </Box>
    );
});
