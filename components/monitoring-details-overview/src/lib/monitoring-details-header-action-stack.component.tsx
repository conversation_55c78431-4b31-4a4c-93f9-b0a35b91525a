import { isEmpty } from 'lodash-es';
import { sharedMonitoringDetailsActionsController } from '@controllers/monitoring-details';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import type { ButtonLevel } from '@cosmos/components/button';
import { dimensionSm } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const MonitoringDetailsHeaderActionStack = observer(
    (): React.JSX.Element => {
        const config =
            sharedMonitoringDetailsActionsController.getActionConfiguration();
        const actions: Action[] = [];

        if (!isEmpty(config.horizontalMenuOptions)) {
            actions.push({
                actionType: 'dropdown',
                id: 'monitoring-details-horizontal-menu',
                typeProps: {
                    label: t`More actions`,
                    isIconOnly: true,
                    level: 'tertiary' as ButtonLevel,
                    startIconName: 'HorizontalMenu',
                    items: config.horizontalMenuOptions,
                },
            });
        }

        // add header actions
        actions.push(...config.headerActions);

        return (
            <ActionStack
                data-id="monitoring-details-header-action-stack"
                gap={dimensionSm}
                data-testid="MonitoringDetailsHeaderActionStack"
                actions={actions}
            />
        );
    },
);
