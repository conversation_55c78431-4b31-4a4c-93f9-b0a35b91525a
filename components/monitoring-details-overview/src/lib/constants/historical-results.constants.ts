import {
    dataCategorizeGreen2,
    dataCategorizeRed2,
    dataCategorizeYellow2,
} from '@cosmos/constants/tokens';

/**
 * Chart dimensions and styling constants.
 */
export const CHART_HEIGHT = 350;
export const CHART_BAR_SIZE = 20;

/**
 * Chart legend configuration for historical results.
 * Maps result types to their display properties.
 */
export const HISTORICAL_RESULTS_LEGEND_ITEMS = [
    {
        key: 'passed' as const,
        color: dataCategorizeGreen2,
    },
    {
        key: 'failed' as const,
        color: dataCategorizeRed2,
    },
    {
        key: 'error' as const,
        color: dataCategorizeYellow2,
    },
] as const;

/**
 * Type for legend item keys to ensure type safety.
 */
export type HistoricalResultsLegendKey =
    (typeof HISTORICAL_RESULTS_LEGEND_ITEMS)[number]['key'];
