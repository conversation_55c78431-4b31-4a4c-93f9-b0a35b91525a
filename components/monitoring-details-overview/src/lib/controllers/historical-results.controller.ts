import { isEmpty, isNil, isString } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import {
    monitorsControllerGenerateSummariesCsvMutation,
    monitorsControllerGetControlTestHistoryOptions,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { downloadBlob } from '@helpers/download-file';

/**
 * Type guard to check if the response is a valid CSV string.
 */
const isCsvResponse = (response: unknown): response is string => {
    return isString(response) && !isEmpty(response);
};

class HistoricalResultsController {
    constructor() {
        makeAutoObservable(this);
    }

    historicalResultsQuery = new ObservedQuery(
        monitorsControllerGetControlTestHistoryOptions,
    );

    csvGenerationMutation = new ObservedMutation(
        monitorsControllerGenerateSummariesCsvMutation,
        {
            onSuccess: (response) => {
                if (!isCsvResponse(response)) {
                    logger.error('Invalid CSV response received');

                    return;
                }

                const blob = new Blob([response], {
                    type: 'text/csv',
                });

                // Use more descriptive filename with date
                const timestamp = new Date().toISOString().split('T')[0];
                const filename = `historical-results-${timestamp}.csv`;

                downloadBlob(blob, filename);
            },
        },
    );

    loadHistoricalResults = (testId: number): void => {
        if (isNil(testId)) {
            throw new Error('Test ID is required');
        }

        when(
            () => !sharedWorkspacesController.isLoading,
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                if (!currentWorkspace) {
                    throw new Error('Workspace not found');
                }

                this.historicalResultsQuery.load({
                    path: {
                        xProductId: currentWorkspace.id,
                    },
                    query: {
                        testId,
                        reportInterval: 'MONTHLY',
                    },
                });
            },
        );
    };

    downloadHistoricalResults = (
        testId: number,
        format: 'CSV' | 'PNG' | 'SVG',
    ): void => {
        if (isNil(testId)) {
            throw new Error('Test ID is required');
        }

        switch (format) {
            case 'CSV': {
                this.downloadCsv(testId);
                break;
            }
            case 'PNG':
            case 'SVG': {
                logger.warn(`${format} download not yet implemented`);

                // Show user feedback for unsupported formats
                snackbarController.addSnackbar({
                    id: 'chart-download-not-implemented',
                    props: {
                        title: t`${format} download coming soon`,
                        description: t`This feature is not yet available. Please use CSV download for now.`,
                        severity: 'warning',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                break;
            }
            default: {
                throw new Error(
                    `Unsupported download format: ${format as string}`,
                );
            }
        }
    };

    downloadCsv = (testId: number): void => {
        this.csvGenerationMutation.mutate({
            path: { testId },
        });
    };

    getHistoryUrl = (testId: number | null): string => {
        if (isNil(testId)) {
            return '#';
        }

        return `/monitoring/${testId}/history`;
    };

    navigateToHistory = (testId: number): void => {
        if (isNil(testId)) {
            throw new Error('Test ID is required');
        }

        const historyUrl = this.getHistoryUrl(testId);

        sharedProgrammaticNavigationController.navigateTo(historyUrl);
    };
}

export const sharedHistoricalResultsController =
    new HistoricalResultsController();
