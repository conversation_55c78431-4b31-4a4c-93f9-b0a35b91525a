import { isEmpty, noop } from 'lodash-es';
import { useCallback, useState } from 'react';
import { z } from 'zod';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { Card } from '@cosmos/components/card';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { ToggleGroup } from '@cosmos/components/toggle-group';
import { libraryTestTemplateControllerGetControlTestInstanceByNameOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { ObservedQuery, observer, when } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';
import { sharedDetailsCardModel } from './models/details-card.model';

export const DetailsCard = observer((): React.JSX.Element => {
    const model = sharedDetailsCardModel;
    const [isEditing, setIsEditing] = useState(false);
    const { formRef, triggerSubmit } = useFormSubmit();

    const { getMonitorArticleUrl } = sharedMonitoringTestDetailsController;

    const nameValidationQuery = new ObservedQuery(
        libraryTestTemplateControllerGetControlTestInstanceByNameOptions,
    );

    const formSchema: FormSchema = {
        name: {
            type: 'text',
            label: t`Name`,
            initialValue: model.name,
            validator: z
                .string()
                .min(1, t`Name cannot be empty`)
                .max(191, t`Name must contain at most 191 characters`)
                .refine((val) => !isEmpty(val.trim()), {
                    message: t`Name cannot be empty`,
                })
                .refine(
                    async (value) => {
                        if (!value.trim()) {
                            return false;
                        }

                        try {
                            nameValidationQuery.load({
                                body: { name: value.trim() },
                            });

                            await when(() => !nameValidationQuery.isLoading);

                            if (
                                nameValidationQuery.hasError ||
                                !nameValidationQuery.data
                            ) {
                                console.warn(
                                    'Name validation failed:',
                                    nameValidationQuery.error,
                                );

                                return true;
                            }

                            const result = nameValidationQuery.data;

                            if (result.valid) {
                                return true;
                            }

                            const currentName = model.name.trim();
                            const newName = value.trim();

                            if (newName === currentName) {
                                return true;
                            }

                            const existingTests = result.controlTestInstances;

                            return (
                                existingTests.length === 1 &&
                                existingTests[0]?.id === model.testId
                            );
                        } catch (error) {
                            console.warn('Name validation failed:', error);

                            return true;
                        }
                    },
                    {
                        message: t`Test name already in use. Names must be unique.`,
                    },
                ),
        },
        description: {
            type: 'textarea',
            label: t`Description`,
            initialValue: model.description,
            isOptional: true,
            validator: z
                .string()
                .max(
                    30000,
                    t`Description must contain at most 30000 characters`,
                )
                .optional(),
            maxCharacters: 30000,
        },
    };

    const handleSubmit = useCallback(
        (values: FormValues) => {
            const description = values.description as string;

            model.handleSubmit(
                {
                    name: values.name as string,
                    description:
                        description.trim() === '' ? undefined : description,
                },
                () => {
                    setIsEditing(false);
                },
            );
        },
        [model],
    );

    const handleEdit = useCallback(() => {
        setIsEditing(true);
    }, []);

    const handleCancel = useCallback(() => {
        setIsEditing(false);
    }, []);

    if (!model.hasData && !model.isLoading) {
        return (
            <Card
                data-id="overview-details-monitoring-card"
                title={t`Details`}
                data-testid="DetailsCard"
                body={<div>{t`No data available`}</div>}
            />
        );
    }

    if (model.isLoading) {
        return (
            <Card
                data-id="overview-details-monitoring-card"
                title={t`Details`}
                data-testid="DetailsCard"
                body={<div>{t`Loading...`}</div>}
            />
        );
    }

    const renderEditMode = () => (
        <Stack
            gap="lg"
            direction="column"
            data-testid="renderEditMode"
            data-id="jTQnR3G8"
        >
            <Form
                hasExternalSubmitButton
                ref={formRef}
                formId="monitoring-details-edit-form"
                data-id="monitoring-details-edit-form"
                schema={formSchema}
                onSubmit={handleSubmit}
            />
            <KeyValuePair
                label={t`Test type`}
                type="TEXT"
                value={model.source}
            />
            <KeyValuePair
                label={t`Status`}
                type="REACT_NODE"
                value={
                    <ToggleGroup
                        orientation="horizontal"
                        initialSelectedOption={model.checkStatus}
                        options={[
                            {
                                label: t`Enabled`,
                                value: 'ENABLED',
                            },
                            {
                                label: t`Disabled`,
                                value: 'DISABLED',
                            },
                        ]}
                        onChange={noop}
                    />
                }
            />
            {model.showHelpArticle && (
                <AppLink
                    isExternal
                    href={getMonitorArticleUrl ?? '#'}
                    size="sm"
                >
                    {t`View help article`}
                </AppLink>
            )}
        </Stack>
    );

    const renderViewMode = () => (
        <Stack
            gap="lg"
            direction="column"
            data-testid="renderViewMode"
            data-id="yC3cK-SK"
        >
            {model.isCustomDraftOrPublished && (
                <KeyValuePair label={t`Name`} type="TEXT" value={model.name} />
            )}
            <KeyValuePair
                label={t`Description`}
                type="TEXT"
                value={model.description}
            />
            <KeyValuePair
                label={t`Test type`}
                type="TEXT"
                value={model.source}
            />
            <KeyValuePair
                label={t`Status`}
                type="REACT_NODE"
                value={
                    <ToggleGroup
                        orientation="horizontal"
                        initialSelectedOption={model.checkStatus}
                        options={[
                            {
                                label: t`Enabled`,
                                value: 'ENABLED',
                            },
                            {
                                label: t`Disabled`,
                                value: 'DISABLED',
                            },
                        ]}
                        onChange={noop}
                    />
                }
            />
            {model.showHelpArticle && (
                <AppLink
                    isExternal
                    href={getMonitorArticleUrl ?? '#'}
                    size="sm"
                >
                    {t`View help article`}
                </AppLink>
            )}
        </Stack>
    );

    return (
        <Card
            data-id="overview-details-monitoring-card"
            title={t`Details`}
            data-testid="DetailsCard"
            body={isEditing ? renderEditMode() : renderViewMode()}
            actions={model.getCardActions(
                isEditing,
                triggerSubmit,
                handleEdit,
                handleCancel,
            )}
        />
    );
});
