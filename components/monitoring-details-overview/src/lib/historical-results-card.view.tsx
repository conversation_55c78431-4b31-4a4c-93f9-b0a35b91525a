import type React from 'react';
import { Card } from '@cosmos/components/card';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { HistoricalResultsChart } from './components/historical-results-chart.component';
import { createEmptyStateCard } from './helpers/historical-results-card.helpers';
import { sharedHistoricalResultsModel } from './models/historical-results.model';

export const HistoricalResultsCardView = observer((): React.JSX.Element => {
    const {
        error,
        hasData,
        isLoading,
        feedbackMessage,
        chartDescription,
        chartData,
        legendData,
        cardActions,
    } = sharedHistoricalResultsModel;

    const renderFeedbackMessage = () => {
        if (!feedbackMessage) {
            return null;
        }

        return (
            <Stack
                gap="xs"
                direction="row"
                align="center"
                data-testid="renderFeedbackMessage"
                data-id="feedback-message"
            >
                <Icon name="Information" size="100" colorScheme="primary" />
                <Text type="body" size="100" colorScheme="primary">
                    {feedbackMessage}
                </Text>
            </Stack>
        );
    };

    if (error) {
        return createEmptyStateCard(
            t`Unable to load historical results`,
            t`There was an error loading the historical data. Please try again.`,
            'historical-results-error',
        );
    }

    if (!hasData && !isLoading) {
        return createEmptyStateCard(
            t`No historical data available`,
            t`Historical results will appear here once the test has run multiple times.`,
            'historical-results-empty',
        );
    }

    return (
        <Card
            data-id="overview-historical-results-card"
            title={t`Historical results`}
            data-testid="HistoricalResultsCardView"
            isLoading={isLoading}
            actions={cardActions}
            body={
                <Stack gap="lg" direction="column">
                    {renderFeedbackMessage()}

                    <Text type="body" size="100" colorScheme="neutral">
                        {chartDescription}
                    </Text>

                    <HistoricalResultsChart
                        showLegend
                        chartData={chartData}
                        legendData={legendData}
                    />
                </Stack>
            }
        />
    );
});
