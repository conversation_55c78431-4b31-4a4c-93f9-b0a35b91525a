import React from 'react';
import { observer } from '@globals/mobx';
import { useParams } from '@remix-run/react';
import { HistoricalResultsCardView } from './historical-results-card.view';
import { sharedHistoricalResultsModel } from './models/historical-results.model';

export const HistoricalResultsCardContainer = observer(
    (): React.JSX.Element => {
        const params = useParams();

        const testIdParam = params.testId || params.id || params.monitorId;
        const testId = testIdParam ? parseInt(testIdParam, 10) : null;

        React.useEffect(() => {
            sharedHistoricalResultsModel.setTestId(testId);
        }, [testId]);

        return <HistoricalResultsCardView data-id="XXmUyv3m" />;
    },
);
