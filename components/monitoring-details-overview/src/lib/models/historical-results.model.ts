import { isEmpty, isString } from 'lodash-es';
import type { Action } from '@cosmos/components/action-stack';
import type { ControlTestInstanceHistoryResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable } from '@globals/mobx';
import { getMonthLabel } from '@helpers/date-time';
import { HISTORICAL_RESULTS_LEGEND_ITEMS } from '../constants/historical-results.constants';
import { sharedHistoricalResultsController } from '../controllers/historical-results.controller';
import {
    getHistoricalResultsDownloadOptions,
    getLegendLabels,
} from '../helpers/historical-results.helper';

export interface HistoricalChartData {
    month: string;
    passed: number;
    failed: number;
    error: number;
}

export class HistoricalResultsModel {
    testId: number | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    setTestId = (testId: number | null): void => {
        // Validate testId is a positive integer
        if (testId !== null && (!Number.isInteger(testId) || testId <= 0)) {
            logger.error('Invalid testId provided: must be a positive integer');

            return;
        }

        this.testId = testId;
        if (testId && !isNaN(testId)) {
            sharedHistoricalResultsController.loadHistoricalResults(testId);
        }
    };

    get historicalData(): ControlTestInstanceHistoryResponseDto | null {
        return (
            sharedHistoricalResultsController.historicalResultsQuery.data ??
            null
        );
    }

    get isLoading(): boolean {
        return sharedHistoricalResultsController.historicalResultsQuery
            .isLoading;
    }

    get error(): Error | null {
        return sharedHistoricalResultsController.historicalResultsQuery.error;
    }

    get hasData(): boolean {
        return Boolean(
            this.historicalData?.labels && !isEmpty(this.historicalData.labels),
        );
    }

    get feedbackMessage(): string | null {
        if (this.hasData) {
            return t`This test is not actively running. You are viewing past data.`;
        }

        return null;
    }

    get chartDescription(): string {
        return t`The number of times a test ran, and the result of that run.`;
    }

    get chartData(): HistoricalChartData[] {
        if (!this.historicalData || !this.hasData) {
            return [];
        }

        const { labels, passed, failed, errored } = this.historicalData;

        return labels.map((label: string, index: number) => ({
            month: this.convertApiLabelToMonthName(label),
            passed: passed[index] ?? 0,
            failed: failed[index] ?? 0,
            error: errored[index] ?? 0,
        }));
    }

    private convertApiLabelToMonthName(apiLabel: string): string {
        // Handle edge cases gracefully
        if (!apiLabel || !isString(apiLabel)) {
            logger.warn('Invalid API label provided for month conversion');

            return 'Unknown';
        }

        if (apiLabel.startsWith('MONTH_')) {
            const monthNumber = parseInt(apiLabel.replace('MONTH_', ''), 10);

            if (isNaN(monthNumber) || monthNumber < 1 || monthNumber > 12) {
                logger.warn('Invalid month number in API label');

                return apiLabel; // Fallback to original label
            }

            try {
                return getMonthLabel(monthNumber, 'short');
            } catch {
                logger.error('Error converting month number to label');

                return apiLabel; // Fallback to original label
            }
        }

        return apiLabel;
    }

    get legendData(): { label: string; value: number; color: string }[] {
        const totals = { passed: 0, failed: 0, error: 0 };

        for (const data of this.chartData) {
            totals.passed = totals.passed + data.passed;
            totals.failed = totals.failed + data.failed;
            totals.error = totals.error + data.error;
        }

        const labels = getLegendLabels();

        return HISTORICAL_RESULTS_LEGEND_ITEMS.map((item) => ({
            label: labels[item.key],
            value: totals[item.key],
            color: item.color,
        }));
    }

    get downloadOptions(): { id: string; label: string }[] {
        return getHistoricalResultsDownloadOptions();
    }

    get shouldShowEmptyState(): boolean {
        return Boolean(this.error) || (!this.hasData && !this.isLoading);
    }

    get shouldShowErrorState(): boolean {
        return Boolean(this.error);
    }

    get shouldShowNoDataState(): boolean {
        return !this.hasData && !this.isLoading && !this.error;
    }

    handleDownload = (format: 'CSV' | 'PNG' | 'SVG'): void => {
        // Validate testId before proceeding with download
        if (
            !this.testId ||
            !Number.isInteger(this.testId) ||
            this.testId <= 0
        ) {
            logger.error('Invalid testId for download operation');

            return;
        }

        sharedHistoricalResultsController.downloadHistoricalResults(
            this.testId,
            format,
        );
    };

    handleViewHistory = (): void => {
        if (this.testId && !isNaN(this.testId)) {
            sharedHistoricalResultsController.navigateToHistory(this.testId);
        }
    };

    get cardActions(): Action[] {
        return [
            {
                id: 'download-dropdown',
                actionType: 'dropdown',
                typeProps: {
                    label: t`Download`,
                    level: 'secondary',
                    size: 'sm',
                    startIconName: 'Download',
                    items: this.downloadOptions.map((option) => ({
                        id: option.id,
                        type: 'item',
                        label: option.label,
                        onSelect: () => {
                            this.handleDownload(
                                option.id.toUpperCase() as
                                    | 'CSV'
                                    | 'PNG'
                                    | 'SVG',
                            );
                        },
                    })),
                },
            },
            {
                id: 'view-history-button',
                actionType: 'button',
                typeProps: {
                    label: t`View history`,
                    level: 'primary',
                    size: 'sm',
                    onClick: this.handleViewHistory,
                },
            },
        ];
    }
}

export const sharedHistoricalResultsModel = new HistoricalResultsModel();
