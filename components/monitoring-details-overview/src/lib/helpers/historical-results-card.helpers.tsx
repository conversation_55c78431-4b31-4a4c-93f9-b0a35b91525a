import type React from 'react';
import { Card } from '@cosmos/components/card';
import { EmptyState } from '@cosmos/components/empty-state';
import { t } from '@globals/i18n/macro';

/**
 * Helper function to create consistent card structure for simple states.
 */
export const createSimpleCard = (body: React.ReactNode): React.JSX.Element => (
    <Card
        data-id="overview-historical-results-card"
        title={t`Historical results`}
        data-testid="createSimpleCard"
        body={body}
    />
);

/**
 * Helper function to create EmptyState cards.
 */
export const createEmptyStateCard = (
    title: string,
    description: string,
    dataId: string,
): React.JSX.Element =>
    createSimpleCard(
        <EmptyState title={title} description={description} data-id={dataId} />,
    );
