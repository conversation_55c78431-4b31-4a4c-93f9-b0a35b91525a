import { t } from '@globals/i18n/macro';

/**
 * Get localized labels for legend items.
 * This function should be called at render time to ensure proper i18n.
 */
export const getLegendLabels = (): Record<
    'passed' | 'failed' | 'error',
    string
> => ({
    passed: t`Passed`,
    failed: t`Failed`,
    error: t`Error`,
});

/**
 * Available download format options for historical results.
 * Used in download dropdown menu.
 */
export const getHistoricalResultsDownloadOptions = (): {
    id: string;
    label: string;
}[] => [
    { id: 'csv', label: t`Download as CSV` },
    { id: 'png', label: t`Download as PNG` },
    { id: 'svg', label: t`Download as SVG` },
];
