import { describe, expect, test } from 'vitest';
import { getSourceLabel } from './source-label';

describe('getSourceLabel', () => {
    test('returns correct label for DRATA source', () => {
        expect(getSourceLabel('DRATA')).toBe('Drata');
    });

    test('returns correct label for ACORN source', () => {
        expect(getSourceLabel('ACORN')).toBe('Codebase');
    });

    test('returns correct label for CUSTOM source', () => {
        expect(getSourceLabel('CUSTOM')).toBe('Custom');
    });

    test('returns correct label for DRATA_LIBRARY source', () => {
        expect(getSourceLabel('DRATA_LIBRARY')).toBe('Drata custom');
    });

    test('returns "Unknown" for undefined source', () => {
        // @ts-expect-error Testing invalid input
        expect(getSourceLabel(undefined)).toBe('Unknown');
    });

    test('returns "Unknown" for unknown source', () => {
        expect(getSourceLabel('UNKNOWN_SOURCE')).toBe('Unknown');
    });
});
