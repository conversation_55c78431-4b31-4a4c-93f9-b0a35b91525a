import { Stack } from '@cosmos/components/stack';
import {
    dataCategorizeGreen2,
    dataCategorizeRed2,
    dataCategorizeYellow2,
} from '@cosmos/constants/tokens';
import { DataBar } from '@cosmos-lab/components/data-bar';
import { DataLegend } from '@cosmos-lab/components/data-legend';
import {
    CHART_BAR_SIZE,
    CHART_HEIGHT,
} from '../constants/historical-results.constants';
import type { HistoricalChartData } from '../models/historical-results.model';

export interface HistoricalResultsChartProps {
    chartData: HistoricalChartData[];
    legendData: { label: string; value: number; color: string }[];
    showLegend?: boolean;
}

export const HistoricalResultsChart = ({
    chartData,
    legendData,
    showLegend = false,
}: HistoricalResultsChartProps): React.JSX.Element => {
    const chartComponent = (
        <DataBar
            categoryKey="month"
            height={CHART_HEIGHT}
            data-id="historical-results-chart"
            data={chartData}
            bars={[
                {
                    dataKey: 'passed',
                    fill: dataCategorizeGreen2,
                    barSize: CHART_BAR_SIZE,
                },
                {
                    dataKey: 'failed',
                    fill: dataCategorizeRed2,
                    barSize: CHART_BAR_SIZE,
                },
                {
                    dataKey: 'error',
                    fill: dataCategorizeYellow2,
                    barSize: CHART_BAR_SIZE,
                },
            ]}
        />
    );

    if (!showLegend) {
        return chartComponent;
    }

    return (
        <Stack
            gap="lg"
            direction="column"
            data-testid="HistoricalResultsChart"
            data-id="qaG6dYnR"
        >
            <DataLegend
                shouldShowLegend
                data-id="historical-results-legend"
                direction="row"
                position="bottom"
                data-testid="HistoricalResultsLegend"
                data={legendData}
            >
                {' '}
            </DataLegend>
            {chartComponent}
        </Stack>
    );
};
