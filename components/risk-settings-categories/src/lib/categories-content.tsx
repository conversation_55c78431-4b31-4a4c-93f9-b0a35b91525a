import { Button } from '@cosmos/components/button';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { dimension2xl } from '@cosmos/constants/tokens';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { t } from '@globals/i18n/macro';

export interface CategoriesContentProps {
    categories: ListBoxItemData[];
    isLoading: boolean;
    hasError: boolean;
    isDeletingCategory: boolean;
    onDeleteCategory: (categoryId: string, categoryName: string) => void;
}

export const CategoriesContent = ({
    categories,
    isLoading,
    hasError,
    isDeletingCategory,
    onDeleteCategory,
}: CategoriesContentProps): React.JSX.Element => {
    if (isLoading) {
        return (
            <Stack direction="column" gap="md">
                <Skeleton barCount={1} width="100%" barHeight={dimension2xl} />
                <Skeleton barCount={1} width="100%" barHeight={dimension2xl} />
                <Skeleton barCount={1} width="100%" barHeight={dimension2xl} />
            </Stack>
        );
    }

    if (hasError) {
        return (
            <Stack direction="column" gap="md" align="center">
                <Text colorScheme="critical">{t`Failed to load categories`}</Text>
                <Text size="200" colorScheme="neutral">
                    {t`Please refresh the page to try again.`}
                </Text>
            </Stack>
        );
    }

    return (
        <StackedList
            data-id="risk-categories-list"
            aria-label={t`Risk categories`}
            data-testid="CategoriesContent"
        >
            {categories.map((category) => (
                <StackedListItem
                    key={category.id}
                    data-id="risk-category-item"
                    primaryColumn={<Text>{category.label}</Text>}
                    action={
                        <Button
                            isIconOnly
                            label={t`Delete category`}
                            level="tertiary"
                            startIconName="Trash"
                            colorScheme="danger"
                            isLoading={isDeletingCategory}
                            a11yLoadingLabel={t`Deleting category...`}
                            onClick={() => {
                                onDeleteCategory(category.id, category.label);
                            }}
                        />
                    }
                />
            ))}
        </StackedList>
    );
};
