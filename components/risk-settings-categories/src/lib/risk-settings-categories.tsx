import { Button } from '@cosmos/components/button';
import { Grid } from '@cosmos/components/grid';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Stack } from '@cosmos/components/stack';
import { TextField } from '@cosmos/components/text-field';
import { t } from '@globals/i18n/macro';
import { CategoriesContent } from './categories-content.tsx';

export interface RiskSettingsCategoriesProps {
    categories: ListBoxItemData[];
    newCategoryName: string;
    validationError: string | null;
    isCreatingCategory: boolean;
    isDeletingCategory: boolean;
    isLoading?: boolean;
    hasError?: boolean;
    onInputChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
    onAddCategory: () => void;
    onDeleteCategory: (categoryId: string, categoryName: string) => void;
    'data-id'?: string;
}

export const RiskSettingsCategories = ({
    categories,
    newCategoryName,
    validationError,
    isCreatingCategory,
    isDeletingCategory,
    isLoading = false,
    hasError = false,
    onInputChange,
    onAddCategory,
    onDeleteCategory,
    'data-id': dataId,
}: RiskSettingsCategoriesProps): React.JSX.Element => {
    return (
        <Stack
            direction="column"
            gap="6x"
            data-id={dataId || 'risk-settings-categories'}
            data-testid="RiskSettingsCategories"
        >
            <Grid columns="1fr auto" gap="md" align="end">
                <TextField
                    name="new-category"
                    label={t`New category`}
                    value={newCategoryName}
                    data-id="new-category-input"
                    formId="risk-categories-form"
                    feedback={
                        validationError
                            ? {
                                  type: 'error',
                                  message: validationError,
                              }
                            : undefined
                    }
                    onChange={onInputChange}
                />
                <Button
                    label={t`Add category`}
                    level="secondary"
                    data-id="add-category-button"
                    isLoading={isCreatingCategory}
                    a11yLoadingLabel={t`Creating category...`}
                    onClick={onAddCategory}
                />
            </Grid>
            <CategoriesContent
                categories={categories}
                isLoading={isLoading}
                hasError={hasError}
                isDeletingCategory={isDeletingCategory}
                onDeleteCategory={onDeleteCategory}
            />
        </Stack>
    );
};
