import type React from 'react';
import { sharedVendorsVrmAgentController } from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { observer } from '@globals/mobx';
import {
    UtilitiesVrmAgentChatComponent,
    type UtilitiesVrmAgentMessageData,
} from '.';

export interface UtilitiesVrmAgentBaseComponentProps {
    messages?: UtilitiesVrmAgentMessageData[];
    'data-id'?: string;
}

export const UtilitiesVrmAgentBaseComponent = observer(
    ({
        messages,
        'data-id': dataId,
    }: UtilitiesVrmAgentBaseComponentProps): React.JSX.Element => {
        const controller = sharedVendorsVrmAgentController;

        // Use messages from props or controller, fallback to mock data
        const displayMessages = messages ?? [];

        // Show loading state when controller is loading
        if (controller.isLoading && !controller.hasWorkflowMessages) {
            return (
                <Stack
                    direction="column"
                    height="100%"
                    width="100%"
                    justify="center"
                    align="center"
                    data-testid="UtilitiesVrmAgentBaseComponent"
                    data-id={dataId || 'vrm-agent-base'}
                >
                    <Loader
                        label="Loading VRM Agent..."
                        size="md"
                        colorScheme="primary"
                        data-id="vrm-agent-loader"
                    />
                </Stack>
            );
        }

        return (
            <Stack
                direction="column"
                height="100%"
                width="100%"
                data-testid="UtilitiesVrmAgentBaseComponent"
                data-id={dataId || 'vrm-agent-base'}
                overflowY="auto"
            >
                <Box
                    borderPosition="bottom"
                    borderWidth="borderWidthSm"
                    borderColor="neutralBorderFaded"
                    p="xl"
                >
                    <Text
                        allowBold
                        size="300"
                        type="title"
                        data-id="base-title"
                    >
                        VRM Agent
                    </Text>
                </Box>

                <Stack direction="column" gap="lg" p="xl">
                    {displayMessages.map((message) => (
                        <UtilitiesVrmAgentChatComponent
                            key={message.id}
                            message={message}
                            data-id={`base-message-${message.id}`}
                        />
                    ))}
                </Stack>
            </Stack>
        );
    },
);
