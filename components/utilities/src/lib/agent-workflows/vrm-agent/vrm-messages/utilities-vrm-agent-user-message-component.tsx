import type React from 'react';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import type { UtilitiesVrmAgentMessageAction } from './types';
import { UtilitiesVrmAgentActionButtonComponent } from './utilities-vrm-agent-action-button-component';

const DEFAULT_DATA_ID = 'vrm-agent-user-action';

interface UtilitiesVrmAgentUserMessageProps {
    actions?: UtilitiesVrmAgentMessageAction[];
    'data-id'?: string;
}

export const UtilitiesVrmAgentUserMessageComponent = observer(
    ({
        actions,
        'data-id': dataId,
    }: UtilitiesVrmAgentUserMessageProps): React.JSX.Element | null => {
        if (!Array.isArray(actions)) {
            return null;
        }

        return (
            <Stack
                direction="row"
                justify="end"
                width="100%"
                data-testid="UserAction"
                data-id={dataId || DEFAULT_DATA_ID}
            >
                {actions.map((action) => (
                    <UtilitiesVrmAgentActionButtonComponent
                        key={action.id}
                        action={action}
                        data-id={`${dataId || DEFAULT_DATA_ID}-${action.id}`}
                    />
                ))}
            </Stack>
        );
    },
);
