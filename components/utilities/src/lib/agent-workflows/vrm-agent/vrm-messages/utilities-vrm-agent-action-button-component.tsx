import { sharedVendorsVrmAgentController } from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Text } from '@cosmos/components/text';
import { observer } from '@globals/mobx';
import type { UtilitiesVrmAgentMessageAction } from './types';

const PRESSED_BUTTON_TYPE = 'pressedButton';
const DEFAULT_DATA_ID = 'user-action';

export interface UtilitiesVrmActionButtonProps {
    action: UtilitiesVrmAgentMessageAction;
    'data-id'?: string;
}

export const UtilitiesVrmAgentActionButtonComponent = observer(
    ({
        action,
        'data-id': dataId,
    }: UtilitiesVrmActionButtonProps): React.JSX.Element => {
        const controller = sharedVendorsVrmAgentController;
        const isEnabled = action.type !== PRESSED_BUTTON_TYPE;

        return (
            <Box
                p={isEnabled ? '0x' : 'md'}
                borderRadius="borderRadius2x"
                borderColor="primaryBorderInitial"
                borderWidth="borderWidthSm"
                maxWidth="70%"
                data-id={`${dataId || DEFAULT_DATA_ID}-content`}
                backgroundColor={
                    isEnabled
                        ? 'neutralBackgroundSurfaceInitial'
                        : 'primaryBackgroundModerate'
                }
            >
                {isEnabled ? (
                    <Button
                        label={action.text}
                        colorScheme="primary"
                        level="tertiary"
                        size="md"
                        width="auto"
                        isLoading={controller.isExecutingAction}
                        a11yLoadingLabel="Processing action..."
                        data-id={`${dataId || DEFAULT_DATA_ID}-button`}
                        onClick={() => {
                            controller.handleActionClick(action);
                        }}
                    />
                ) : (
                    <Text
                        type="body"
                        size="200"
                        colorScheme="primary"
                        data-id={`${dataId || DEFAULT_DATA_ID}-text`}
                    >
                        {action.text}
                    </Text>
                )}
            </Box>
        );
    },
);
