import type React from 'react';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import type { UtilitiesVrmAgentMessageBodyItem } from './types';

const DEFAULT_DATA_ID = 'agent-message-body';

export interface UtilitiesVrmAgentMessageBodyProps {
    items: UtilitiesVrmAgentMessageBodyItem[];
    'data-id'?: string;
}

export const UtilitiesVrmAgentMessageBodyComponent = observer(
    ({
        items,
        'data-id': dataId,
    }: UtilitiesVrmAgentMessageBodyProps): React.JSX.Element => {
        return (
            <Stack
                direction="column"
                gap="sm"
                align="center"
                data-testid="UtilitiesVrmAgentMessageBodyComponent"
                data-id={dataId || DEFAULT_DATA_ID}
            >
                {items.map((item, index) => (
                    <Stack
                        key={item.id}
                        direction="row"
                        gap="xs"
                        align="center"
                        data-id="zyuPzR81"
                    >
                        {item.icon && (
                            <Icon
                                name={item.icon}
                                size="200"
                                colorScheme="success"
                                data-id={`${dataId || DEFAULT_DATA_ID}-icon-${index}`}
                            />
                        )}
                        {item.text && (
                            <>
                                {item.ref ? (
                                    <AppLink
                                        href={`#${item.ref}`}
                                        colorScheme="primary"
                                        size="md"
                                        data-id={`${dataId || DEFAULT_DATA_ID}-link-${index}`}
                                    >
                                        {item.text}
                                    </AppLink>
                                ) : (
                                    <Text
                                        allowBold
                                        size="200"
                                        type="body"
                                        data-id={`${dataId || DEFAULT_DATA_ID}-text-${index}`}
                                    >
                                        {item.style === 'bold' ? (
                                            <strong>{item.text}</strong>
                                        ) : (
                                            item.text
                                        )}
                                    </Text>
                                )}
                            </>
                        )}
                    </Stack>
                ))}
            </Stack>
        );
    },
);
