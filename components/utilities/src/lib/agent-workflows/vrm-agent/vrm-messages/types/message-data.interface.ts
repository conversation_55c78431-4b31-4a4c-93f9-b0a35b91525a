import type { IconName } from '@cosmos/components/icon';

export interface UtilitiesVrmAgentMessageTitleItem {
    id: string;
    icon?: IconName;
    text?: string;
    ref?: string;
}

export interface UtilitiesVrmAgentMessageBodyItem {
    id: string;
    icon?: IconName;
    text: string;
    ref?: string;
    style?: 'bold' | 'normal';
}

export interface UtilitiesVrmAgentMessageAction {
    id: string;
    type: 'button' | 'link' | 'pressedButton' | 'action';
    text: string;
    action: string;
}

export interface UtilitiesVrmAgentMessageData {
    id: string;
    caller: 'AGENT' | 'USER';
    title?: UtilitiesVrmAgentMessageTitleItem[] | null;
    body?: UtilitiesVrmAgentMessageBodyItem[] | null;
    actions?: UtilitiesVrmAgentMessageAction[];
}
