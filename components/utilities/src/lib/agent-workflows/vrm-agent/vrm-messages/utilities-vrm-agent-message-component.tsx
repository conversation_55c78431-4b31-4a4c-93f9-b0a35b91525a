import { isEmpty } from 'lodash-es';
import type React from 'react';
import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import type { UtilitiesVrmAgentMessageProps } from './types';
import { UtilitiesVrmAgentMessageActionsComponent } from './utilities-vrm-agent-message-actions-component';
import { UtilitiesVrmAgentMessageBodyComponent } from './utilities-vrm-agent-message-body-component';
import { UtilitiesVrmAgentMessageTitleComponent } from './utilities-vrm-agent-message-title-component';

const DEFAULT_DATA_ID = 'agent-message';

export const UtilitiesVrmAgentMessageComponent = observer(
    ({
        message,
        'data-id': dataId,
    }: UtilitiesVrmAgentMessageProps): React.JSX.Element => {
        const messageId = dataId || DEFAULT_DATA_ID;

        return (
            <Stack
                direction="row"
                justify="start"
                width="100%"
                data-testid="UtilitiesVrmAgentMessage"
                data-id={messageId}
            >
                <Box
                    px="md"
                    py="lg"
                    backgroundColor="neutralBackgroundMild"
                    borderRadius="borderRadius2x"
                    maxWidth="70%"
                    data-id={`${messageId}-content`}
                >
                    <Stack direction="column" gap="md">
                        {message.title && !isEmpty(message.title) && (
                            <UtilitiesVrmAgentMessageTitleComponent
                                items={message.title}
                                data-id={`${messageId}-title`}
                            />
                        )}

                        {message.body && !isEmpty(message.body) && (
                            <UtilitiesVrmAgentMessageBodyComponent
                                items={message.body}
                                data-id={`${messageId}-body`}
                            />
                        )}

                        {message.actions && !isEmpty(message.actions) && (
                            <UtilitiesVrmAgentMessageActionsComponent
                                actions={message.actions}
                                data-id={`${messageId}-actions`}
                            />
                        )}
                    </Stack>
                </Box>
            </Stack>
        );
    },
);
