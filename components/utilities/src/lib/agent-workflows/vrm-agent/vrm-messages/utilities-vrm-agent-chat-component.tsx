import type React from 'react';
import { observer } from '@globals/mobx';
import type { UtilitiesVrmAgentMessageData } from './types';
import { UtilitiesVrmAgentMessageComponent } from './utilities-vrm-agent-message-component';
import { UtilitiesVrmAgentUserMessageComponent } from './utilities-vrm-agent-user-message-component';

const DEFAULT_DATA_ID = 'chat-message';

export interface UtilitiesVrmAgentChatProps {
    message: UtilitiesVrmAgentMessageData;
    'data-id'?: string;
}

export const UtilitiesVrmAgentChatComponent = observer(
    ({
        message,
        'data-id': dataId,
    }: UtilitiesVrmAgentChatProps): React.JSX.Element | null => {
        const messageId = dataId || DEFAULT_DATA_ID;

        switch (message.caller) {
            case 'AGENT': {
                return (
                    <UtilitiesVrmAgentMessageComponent
                        message={message}
                        data-id={messageId}
                    />
                );
            }
            case 'USER': {
                return (
                    <UtilitiesVrmAgentUserMessageComponent
                        actions={message.actions}
                        data-id={messageId}
                    />
                );
            }
            default: {
                return null;
            }
        }
    },
);
