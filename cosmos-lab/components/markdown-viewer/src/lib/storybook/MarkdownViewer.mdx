import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as MarkdownViewerStories from './MarkdownViewer.stories';

<Meta of={MarkdownViewerStories} />

<Title />

<Description />

<Primary />

<Controls of={MarkdownViewerStories.Playground} />

## Import

```jsx
import { MarkdownViewer } from '@cosmos-lab/components/markdown-viewer';
```

The MarkdownViewer component renders markdown content with custom styling and interactive elements like links and lists.

```jsx
<MarkdownViewer data-id="content-viewer">
  {markdownContent}
</MarkdownViewer>
```

## 🟢 When to use the component

- **Documentation display** - Render README files, help content, or user guides with proper formatting
- **Rich text content** - Display formatted text with headings, lists, links, and emphasis
- **User-generated content** - Show markdown from forms, comments, or content management systems
- **API response formatting** - Display markdown content returned from backend services
- **Mixed content rendering** - Handle content that may contain both markdown and HTML

## ❌ When not to use the component

- **Plain text display** - Use Text component for simple unformatted text
- **Complex HTML content** - Use dangerouslySetInnerHTML or dedicated HTML renderer for full HTML
- **Interactive forms** - Use form components for editable content rather than display-only markdown
- **Performance-critical lists** - For large lists of markdown content, consider virtualization

## 🛠️ How it works

The MarkdownViewer component uses markdown-to-jsx to parse and render markdown with custom component overrides and HTML parsing options.

### Usability

**Core functionality:**
- **Markdown parsing** - Converts markdown syntax to React components with custom styling
- **HTML support** - `allowParsingRawHTML` prop enables mixed markdown/HTML content rendering
- **Line break handling** - Automatically formats line breaks differently for markdown vs HTML content
- **Custom component mapping** - Uses `generateMarkdownOptions` to override default HTML elements with styled components
- **Stack layout** - Renders content in vertical Stack with consistent spacing

**Content processing:**
- **Smart formatting** - Detects HTML content and adjusts line break handling accordingly
- **HTML detection** - Uses `isHtml` helper to determine content type and apply appropriate formatting
- **Line break conversion** - Converts `\n` to markdown line breaks (`  \n`) for plain markdown, removes for HTML

### Content

- Use `allowParsingRawHTML={true}` only for trusted content to prevent XSS vulnerabilities
- Ensure markdown content follows standard syntax for proper rendering
- Consider content source when enabling HTML parsing - never allow for user-generated content
- Test mixed HTML/markdown content thoroughly when using HTML parsing

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper heading hierarchy through custom component overrides
- Screen reader compatibility with meaningful markup for lists, links, and emphasis
- Keyboard navigation support for interactive elements like links within the content
- Focus management that works with the underlying Stack component structure
- High contrast support through design system components used in markdown rendering

**Development responsibilities:**
- Ensure markdown content has proper heading hierarchy (h1, h2, h3, etc.) for screen readers
- Validate that links within markdown content have meaningful text and proper destinations
- Test complex markdown structures with assistive technology to verify proper navigation
- Handle potentially unsafe HTML content appropriately when using `allowParsingRawHTML`
- Provide context about the markdown content's purpose through surrounding interface elements

**Design responsibilities:**
- Maintain consistent typography hierarchy that matches the design system
- Ensure sufficient contrast for all text elements including links and emphasis
- Design spacing and layout that works with the Stack component's gap system
- Consider the visual impact of nested lists and complex markdown structures
- Test markdown rendering across different content types and lengths
