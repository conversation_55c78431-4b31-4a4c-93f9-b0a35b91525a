import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as WizardStories from './Wizard.stories';
import wizardPattern from './assets/wizard-pattern.png';
import wizardSubsteps from './assets/wizard-substeps.png';
import progressiveDisclosure from './assets/progressive-disclosure.png';
import wizardWhenToUse from './assets/wizard-when-to-use.png';

<Meta of={WizardStories} />

<Title />

<Description />

<Primary />

<Controls of={WizardStories.Playground} />

## Import

```jsx
import { Wizard } from '@cosmos-lab/components/wizard';
```

<img src={wizardPattern} alt="Wizard Pattern" />

## Components

### Steps

- Displays progress across the top of the wizard.
- Shows:
    - Step number or label
    - Visual indicator of completed, current, or upcoming steps
- Should be persistent across all screens of the wizard.

### Steps Content

- Main container for form fields or interactions per step.
- Can support **substeps**:
    - Optional substep title (use only if clarity is needed)
    - Use clear visual hierarchy to differentiate from main step titles

<img src={wizardSubsteps} alt="Wizard Substeps" />

### CTA Bar

- Fixed to the bottom of the screen for consistent action access.
- Left side:
    - **Back** (always visible after step 1)
    - **Cancel** (visible on step 1 only, exits the wizard)
- Right side:
    - **Skip** (optional, appears only when a step is skippable)
    - **Next** (or **Complete** on the last step)

## 🟢 When to use the component

- **Creating new platform objects** - For creating controls, frameworks, evidence, and other platform objects
- **Adding items to existing objects** - Occasionally, for adding items to existing objects **(must be reviewed by the Design team before adding)**
- **Multi-step decision processes** - When the process requires multiple steps or decisions, and users benefit from a guided flow
- **Conditional workflows** - When certain steps may be skippable based on user choices or data validation
- **Progress indication needed** - When users benefit from seeing their progress through a multi-step process
- **Step validation required** - When each step needs validation before allowing progression to the next step

## ❌ When not to use the component

- **Quick, one-step tasks** - Use a modal, drawer, or form instead for simple tasks
- **Editing existing objects** - Prefer inline editing patterns for modifying existing content
- **Simple forms** - When the entire form can comfortably fit on a single page without overwhelming users
- **Linear data entry** - When all information is required and there's no logical step division
- **Frequent workflows** - For tasks users perform repeatedly where the step-by-step guidance becomes cumbersome
- **Mobile-first experiences** - Consider alternative patterns for mobile-heavy usage where screen space is limited

## 🛠️ How it works

The Wizard component guides users through multi-step processes for creating new platform objects with clear progress indicators and step-by-step navigation.

**Component structure:**
- **Steps** - Left sidebar showing numbered steps with titles, subtitles, and completion states
- **Step content** - Main content area displaying the current step's form or information
- **CTA bar** - Fixed bottom navigation with Back/Cancel and Next/Skip/Complete buttons

**Step management:**
- **Progress tracking** - Visual indicators show completed, current, and upcoming steps
- **State validation** - Each step can validate before allowing progression via `onStepChange`
- **Flexible navigation** - Steps can be skippable, have custom button labels, or restrict forward/backward movement

**Navigation behavior:**
- **Automatic scrolling** - Scrolls to top when changing steps for consistent user experience
- **Button logic** - Shows appropriate buttons based on current step position and step configuration
- **Loading states** - Global loading state affects navigation elements during async operations

**Button positioning:**
- **Right side** - Skip (when applicable), Next/Complete buttons in a horizontal stack
- **Left side** - Back button (after step 1) or Cancel button (on step 1)
- **Conditional display** - Buttons appear/disappear based on step configuration (`canGoForward`, `canGoBack`, `isStepSkippable`)

**Custom labeling:**
- **Step-level overrides** - Each step can override button labels via `backButtonLabelOverride`, `forwardButtonLabelOverride`, `skipButtonLabelOverride`
- **Wizard-level defaults** - Global `completeButtonLabel` and `nextButtonLabel` props for consistent labeling
- **Fallback system** - Uses helper functions for default labels when no overrides are provided

### Usability

**Step progression:**
- **Validation-driven** - Steps should validate their content before allowing users to proceed
- **Clear feedback** - Provide immediate feedback when validation fails or succeeds
- **Logical flow** - Organize steps in a logical sequence that matches user mental models

**Navigation patterns:**
- **Consistent placement** - Navigation controls remain in fixed positions for predictable interaction
- **Appropriate labeling** - Use clear, action-oriented labels for navigation buttons
- **Skip functionality** - Only make steps skippable when truly optional to avoid confusion

### Content

**Step organization:**
- **Manageable chunks** - Break complex tasks into digestible steps that don't overwhelm users
- **Clear titles** - Use concise, descriptive titles that clearly indicate what each step accomplishes
- **Optional subtitles** - Add subtitles when additional context helps users understand the step's purpose

**Button labeling:**
- **Action-oriented** - Use specific action words rather than generic "Next" when appropriate
- **Context-aware** - Customize button labels to match the specific action being performed
- **Consistent terminology** - Maintain consistent language patterns across similar wizards

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper heading hierarchy and navigation landmarks
- Keyboard navigation support for moving between interactive elements within steps
- Screen reader announcements for step changes and progress updates
- Focus management that maintains logical tab order during step transitions
- High contrast support for step indicators and navigation elements

**Development responsibilities:**
- Ensure step content is properly structured with headings and form labels for screen readers
- Handle focus appropriately when steps change, especially for form fields with validation errors
- Provide clear error messages and associate them with relevant form fields
- Announce loading states and completion status to assistive technology users

**Design responsibilities:**
- Clearly distinguish between completed, current, and upcoming steps with sufficient visual contrast
- Provide clear focus indicators for all interactive elements that meet contrast requirements
- Design step content with proper spacing and visual hierarchy for screen reader navigation
- Ensure wizard remains usable across different screen sizes and orientations
