import { isEmpty } from 'lodash-es';
import {
    Bar,
    <PERSON>hart,
    CartesianGrid,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    <PERSON>Axis,
} from 'recharts';
import { TooltipContentComponent } from './components/tooltip-content.component';
import { AXIS_TICK_CONFIG } from './constants/axis-tick-config.constant';
import { DATA_BAR_CONFIG } from './constants/data-bar-config.constant';
import { TOOLTIP_WRAPPER_STYLE } from './constants/tooltip-wrapper-style.component';
import type { DataBarProps } from './types/data-bar-props.type';

/**
 * The DataBar component displays a bar chart with configurable data, tooltips, and styling options.
 *
 * Figma Plugin coming soon 🤞.
 */
export const DataBar = <T extends object>({
    'data-id': dataId = 'cosmos-lab-data-bar',
    width = '100%',
    height = 200,
    data,
    categoryKey,
    bars = [],
    tooltipComponent: TooltipComponent = TooltipContentComponent,
}: DataBarProps<T>): React.JSX.Element => {
    const calculateYAxisWidth = () => {
        if (isEmpty(data) || isEmpty(bars)) {
            return DATA_BAR_CONFIG.defaultWidth;
        }

        // Find the maximum value across all data points for all bars
        let maxWidth = 0;

        data.forEach((item) => {
            bars.forEach((bar) => {
                const value = Number(item[bar.dataKey]);

                if (!isNaN(value) && value > maxWidth) {
                    maxWidth = value;
                }
            });
        });

        // Calculate width based on number of digits
        const digitCount = Math.max(1, Math.floor(Math.log10(maxWidth)) + 1);

        if (digitCount <= DATA_BAR_CONFIG.defaultDigits) {
            return DATA_BAR_CONFIG.defaultWidth;
        }

        return (
            DATA_BAR_CONFIG.defaultWidth +
            (digitCount - DATA_BAR_CONFIG.defaultDigits) *
                DATA_BAR_CONFIG.minWidth
        );
    };

    const yAxisWidth = calculateYAxisWidth();

    return (
        <ResponsiveContainer
            width={width}
            height={height}
            data-testid="DataBar"
            data-id={dataId}
        >
            <BarChart data={data} layout="horizontal">
                <CartesianGrid horizontal syncWithTicks vertical={false} />
                <XAxis
                    dataKey={categoryKey}
                    axisLine={false}
                    tickLine={false}
                    tick={AXIS_TICK_CONFIG}
                />
                <YAxis
                    orientation="left"
                    width={yAxisWidth}
                    axisLine={false}
                    tickLine={false}
                    tick={AXIS_TICK_CONFIG}
                />
                <Tooltip
                    cursor={{ fill: 'transparent' }}
                    wrapperStyle={TOOLTIP_WRAPPER_STYLE}
                    content={TooltipComponent}
                />
                {bars.map((bar) => (
                    <Bar
                        key={bar.dataKey}
                        dataKey={bar.dataKey}
                        stackId={bar.stackId}
                        fill={bar.fill}
                        barSize={bar.barSize}
                        data-id="-vORKQGr"
                    />
                ))}
            </BarChart>
        </ResponsiveContainer>
    );
};
