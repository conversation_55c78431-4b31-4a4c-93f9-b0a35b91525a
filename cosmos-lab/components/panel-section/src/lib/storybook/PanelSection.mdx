import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as PanelSectionStories from './PanelSection.stories';

<Meta of={PanelSectionStories} />

<Title />

<Description />

<Primary />

<Controls of={PanelSectionStories.Playground} />

## Import

```jsx
import { PanelSection } from '@cosmos-lab/components/panel-section';
```

## 🟢 When to use the component

- **Panel content organization** - When you need to structure content within panels with clear sections, titles, and consistent layouts
- **Consistent panel layouts** - To maintain visual consistency across different panel implementations
- **Grouped information with actions** - For organizing related information under descriptive headings with associated actions like edit, delete, or expand functionality
- **Contextual sections** - When sections need tooltips, additional context, or custom start/end slots for icons, badges, or other elements

## ❌ When not to use the component

- **Simple content blocks** - Use basic layout components like Box or Stack for content that doesn't need section structure
- **Full-page layouts** - Use page-level components for main content areas outside of panels
## 🛠️ How it works

The PanelSection component provides a structured section within panels with title, body content, optional borders, and customizable slots for consistent panel layouts.

**Content structure:**
- **Title area** - Contains the main title with optional tooltip support and customizable start/end slots
- **Action area** - Optional ActionStack for section-specific actions displayed on the right
- **Body content** - Flexible ReactNode content area with consistent padding (3xl bottom)
- **Border control** - Optional bottom border for visual separation between sections

**Visual features:**
- **Tooltip support** - Title can include tooltip text for additional context
- **Action alignment** - Actions are right-aligned and properly spaced from title content
- **Content padding** - Body content uses consistent padding (3xl bottom) for visual rhythm

### Usability



**Layout patterns:**
- **Section stacking** - Multiple PanelSections can be stacked with visual separation between them
- **Action grouping** - Related actions are grouped together in the section header area
- **Content flexibility** - Body content can contain any React components or layouts

**Border usage patterns:**
- **Visual separation** - Use `showBorderBottom={true}` to create dividers between sections
- **Last section styling** - The final PanelSection in a stack should have `showBorderBottom={false}` to avoid unnecessary visual clutter at the bottom
- **Single section styling** - When using only one PanelSection, set `showBorderBottom={false}` as no separation is needed

### Content

**Title guidelines:**
- **Descriptive labels** - Use clear, concise titles that describe the section content
- **Consistent terminology** - Maintain consistent language across similar sections
- **Contextual tooltips** - Provide additional context through tooltips when section purpose isn't immediately clear

**Body content:**
- **Focused information** - Keep section content focused on a single topic or related group of information
- **Scannable format** - Organize content for quick comprehension within the panel context
- **Appropriate depth** - Balance detail level with panel space constraints

**Action design:**
- **Relevant actions** - Include only actions that directly relate to the section content
- **Clear labels** - Use action-oriented labels that clearly describe what will happen
- **Appropriate hierarchy** - Use button levels that reflect action importance within the section context

**Slot usage:**
- **Start slot** - Use for status indicators, icons, or badges that provide immediate context
- **End slot** - Use for secondary information, counts, or supplementary indicators
- **Consistent patterns** - Maintain consistent slot usage patterns across similar sections

### Accessibility

**What the design system provides:**
- Semantic structure with proper heading hierarchy and content organization for screen readers
- Keyboard navigation with full keyboard accessibility for all interactive elements including actions and tooltips
- Screen reader support through proper labeling and content structure
- Focus management with logical tab order through section elements

**Development responsibilities:**
- Ensure section titles maintain appropriate heading hierarchy within panel context
- Verify all section actions have appropriate labels and keyboard support
- Ensure tooltip content is accessible to screen readers
- Structure section content logically for assistive technology

**Design responsibilities:**
- Design clear visual relationships between title, actions, and body content
- Ensure sufficient contrast for all text and interactive elements
- Provide clear focus states for all interactive elements
- Design responsive behavior across different screen sizes

