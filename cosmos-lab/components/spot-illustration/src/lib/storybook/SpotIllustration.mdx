import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as SpotIllustrationStories from './SpotIllustration.stories';

<Meta of={SpotIllustrationStories} />

<Title />

<Description />

<Primary />

<Controls of={SpotIllustrationStories.Playground} />

## Import

```jsx
import { SpotIllustration } from '@cosmos-lab/components/spot-illustration';
```

## 🟢 When to use the component

- **Empty states** - Used within EmptyState component to illustrate empty content areas

## ❌ When not to use the component

- **Decorative purposes only** - Don't use without clear functional purpose
- **Critical information** - Don't rely solely on illustrations to convey important information
- **Interactive elements** - These are display-only illustrations

## 🛠️ How it works

The SpotIllustration component displays SVG illustrations with configurable sizing.

### Usability

**Core functionality:**
- **Illustration selection** - Choose from available illustrations via `name` prop
- **Flexible sizing** - Control dimensions with `size` prop
- **SVG rendering** - Vector graphics that scale cleanly

### Content

**Illustration selection:**
- **Empty states** - Choose illustrations that relate to the missing content type (e.g., `no-results` for search, `no-access` for permissions)
- **Contextual meaning** - Select illustrations that support the surrounding content's purpose and user context
- **Universal understanding** - Use illustrations that are easily recognizable across different cultures and user backgrounds
- **Appropriate sizing** - Choose size values that maintain visual hierarchy and don't overwhelm surrounding content

### Accessibility

**What the design system provides:**
- SVG format that works with browser zoom and accessibility tools
- Scalable graphics that maintain clarity at different sizes

**Development responsibilities:**
- Use illustrations as visual enhancement, not primary information delivery
- Provide alternative text or descriptions in surrounding content when illustrations convey meaning
- Choose appropriate sizing for the intended layout context

**Design responsibilities:**
- Maintain consistent visual style across illustrations
- Design illustrations that work well at different sizes
- Create illustrations that are universally understandable
