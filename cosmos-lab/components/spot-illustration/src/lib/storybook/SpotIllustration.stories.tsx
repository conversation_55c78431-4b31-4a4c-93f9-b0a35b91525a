import type { Meta } from '@storybook/react-vite';
import { SPOT_ILLUSTRATION_OPTIONS } from '../constants/spot-illustration-options.constant';
import { SpotIllustration } from '../spot-illustration-cosmos-lab';

const meta: Meta<typeof SpotIllustration> = {
    tags: ['Lab'],
    title: 'Media & Imagery/SpotIllustration',
    component: SpotIllustration,
    argTypes: {
        name: {
            control: 'select',
            options: SPOT_ILLUSTRATION_OPTIONS,
        },
    },
};

export default meta;

export { Playground } from './stories';
export { AllSpotIllustrations } from './stories';
