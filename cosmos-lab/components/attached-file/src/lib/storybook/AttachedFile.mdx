import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as AttachedFile from './AttachedFile.stories';

<Meta of={AttachedFile} />

<Title />

<Description />

<Primary />

<Controls of={AttachedFile.Playground} />

## Import

```jsx
import { AttachedFile } from '@cosmos-lab/components/attached-file';
```

The AttachedFile component displays file attachments with status indicators, optional links, and actions for document management workflows.

```jsx
<AttachedFile
    id="attachment-1"
    title="Document.pdf"
    status="added"
    link={{
        href: "https://example.com/document.pdf",
        label: "View document"
    }}
    actions={[
        {
            actionType: 'tooltipButton',
            id: 'edit-btn',
            typeProps: {
                tooltip: { text: 'Edit', isInteractive: true },
                button: {
                    size: 'sm',
                    label: 'Edit',
                    isIconOnly: true,
                    startIconName: 'Edit',
                    colorScheme: 'neutral',
                    level: 'tertiary',
                    onClick: handleEdit,
                }
            }
        }
    ]}
/>
```

## 🟢 When to use the component

- **File attachment lists** - Display uploaded documents with management actions
- **Document workflows** - Show files in various states (uploading, added, error) with appropriate actions
- **Form attachments** - Display files attached to forms with status feedback
- **File galleries** - Show collections of documents with consistent interaction patterns

## ❌ When not to use the component

- **File upload interface** - Use FileUpload component for drag-and-drop upload functionality
- **Simple file links** - Use Link component for basic file download links without status
- **Large file lists** - Consider virtualization for performance with many files
- **Complex file metadata** - Use DataTable or StackedList for files requiring detailed information display

## 🛠️ How it works

The AttachedFile component combines file status visualization, optional external links, and configurable actions in a horizontal layout using Stack components.

### Usability

**Core functionality:**
- **Status visualization** - Three states: 'added' (PolicyCenter icon), 'uploading' (spinner), 'error' (WarningDiamond icon)
- **File identification** - `title` displays the filename with consistent typography
- **External links** - Optional `link` prop renders external link below filename
- **Action management** - Flexible actions via ActionStack component with automatic undefined filtering
- **Consistent layout** - Horizontal Stack with space-between justification for predictable positioning

**Status indicators:**
- **Added state** - Shows PolicyCenter icon indicating successful attachment
- **Uploading state** - Displays Loader component with "loading file" label wrapped in StyledIconWrapperDiv
- **Error state** - Shows WarningDiamond icon with critical color scheme for failed uploads

**Link behavior:**
- **External links** - Uses Link component with `isExternal` prop for proper external link handling
- **Conditional rendering** - Link only appears when `link.href` is provided
- **Accessible labeling** - Uses `link.label` for link text display

### Content

- Use descriptive `title` text that clearly identifies the file content
- Provide meaningful `link.label` text that describes the link destination
- Choose appropriate actions for the file management context
- Use consistent action patterns across similar file management workflows

### Accessibility

**What the design system provides:**
- Semantic structure with proper heading hierarchy via Text component with "title" type
- Screen reader support for status indicators with meaningful icon names and spinner labels
- Keyboard navigation for interactive elements (links and action buttons)
- Focus management through ActionStack component's built-in accessibility features
- Testing attributes with consistent `data-id` patterns for automated testing

**Development responsibilities:**
- Ensure action callbacks provide appropriate feedback for file operations
- Provide meaningful tooltips for action buttons when using tooltipButton actionType
- Test file management workflows with keyboard navigation and screen readers
- Handle loading and error states appropriately with proper status updates
- Consider the context where files are displayed and provide appropriate surrounding labels

**Design responsibilities:**
- Maintain visual consistency between different file status states
- Ensure sufficient contrast for status icons across all color schemes
- Design action button layouts that work with varying numbers of actions (1-3)
- Consider the visual hierarchy between filename, link, and action elements
- Test file list layouts with different filename lengths and action combinations
