import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as StatusDotCosmosLabStories from './StatusDot.stories';

<Meta of={StatusDotCosmosLabStories} />

<Title />

<Description />

<Primary />

<Controls of={StatusDotCosmosLabStories.Playground} />

## Import

```jsx
import { StatusDot } from '@cosmos-lab/components/status-dot-cosmos-lab';
```

## Props

### `intent`

The displayed sentiment of the Status; controls the colors.

- **Type:** `IntentType`
- **Options:** `'positive'` | `'negative'`
- **Default:** `'positive'`

<Canvas of={StatusDotCosmosLabStories.VariantNegative} />

## 🟢 When to use the component

- **Utility status indicators** - To show 'new' or 'overdue' status on dock buttons and similar controls
- **Supplementary visual cues** - As an additional indicator positioned alongside buttons or other interface elements
- **Non-critical status** - For status that enhances understanding but isn't essential for task completion
- **Secondary status indicators** - When the status supports but doesn't replace primary interface elements

## ❌ When not to use the component

- **Primary status communication** - When status is the main information users need to act on
- **Complex status states** - When you need to communicate more than positive/negative binary states
- **Standalone status display** - When status needs to be understood without surrounding context
- **Detailed status information** - When users need explanatory text or additional status details

## 🛠️ How it works

The StatusDot component renders a small 8x8 pixel SVG with three concentric circles that create a layered visual effect. The component uses different fill colors based on the intent prop to communicate positive or negative states.

### Usability

**Visual Design:**
- **Size:** Fixed 8x8 pixel dimensions for consistent, compact display
- **Layered effect:** Three concentric circles with varying opacity (0.3, 0.6, and stroke)
- **Color coding:** Different fill colors based on intent (positive/negative)
- **Minimal footprint:** Designed to be unobtrusive while still visible

**Integration:**
- **Positioned alongside components:** Used as a separate visual indicator next to buttons or other interface elements
- **Context-dependent:** Relies on surrounding elements to provide meaning and context
- **Consistent sizing:** Fixed dimensions ensure alignment across different contexts

### Content

**Intent Values:**
- **'positive'** - Default value, used for 'new' status indicators and other positive states
- **'negative'** - Used for 'overdue' status indicators and other problematic states

**Visual Representation:**
- Component uses color alone to convey meaning
- No text or additional labels are included in the component itself
- Relies on context or surrounding elements for semantic meaning

### Accessibility

**What the design system provides:**
- Semantic SVG markup with proper viewBox dimensions
- Consistent visual styling through intent-based color system
- Fixed 8x8 pixel dimensions for predictable layout
- Testing attributes for automated accessibility testing

**Development responsibilities:**
- Provide semantic context by adding `aria-label` or `aria-describedby` when status information is critical
- Don't rely on color alone - pair with text labels or other indicators to convey status meaning
- Consider screen reader announcements by using `aria-live` regions if status changes dynamically
- Test with assistive technology to verify that status information is accessible to screen readers
- Implement proper labeling to ensure surrounding elements explain the status meaning clearly

**Design responsibilities:**
- Don't use color as the only indicator - always pair with text, icons, or other visual cues
- Provide clear context to ensure the status meaning is obvious from surrounding content
- Consider color contrast and verify status colors meet accessibility contrast requirements
- Document status meanings by clearly defining what positive and negative states represent
- Test with color blindness simulators to ensure status is distinguishable for users with color vision differences

**Note:** The StatusDot component provides minimal built-in accessibility features. Developers must implement proper ARIA labeling, screen reader support, and alternative access methods for status information.
