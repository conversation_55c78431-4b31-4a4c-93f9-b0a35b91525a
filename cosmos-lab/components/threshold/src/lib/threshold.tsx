import { type AriaAttributes, useMemo, useState } from 'react';
import { But<PERSON> } from '@cosmos/components/button';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import {
    MAX_THUMB_NUMBER,
    MIN_THUMB_NUMBER,
    THUMB_SPACING,
} from './constants/threshold.constants';
import { splitNumberIntoParts } from './helpers/threshold.helpers';
import { StyledContainer } from './styles/StyledContainer.style';
import { StyledStepMeterContainerDiv } from './styles/StyledMeterSection.style';
import { StyledSliderRoot } from './styles/StyledSlider.style';
import { StyledThumb } from './styles/StyledThumb.style';
import { StyledTrack } from './styles/StyledTrack.style';

export interface ThresholdProps {
    /**
     * Identifies the element (or elements) that describes this one.
     */
    ['aria-describedby']?: AriaAttributes['aria-describedby'];
    /**
     * Indicates the entered value does not conform to the format expected by the application.
     */
    ['aria-invalid']?: AriaAttributes['aria-invalid'];
    /**
     * Identifies the element (or elements) that labels this one.
     */
    ['aria-labelledby']: AriaAttributes['aria-labelledby'];
    /**
     * Unique testing ID for this element.
     */
    'data-id'?: string;
    /**
     * When true, prevents the user from interacting with the threshold.
     */
    disabled?: boolean;
    /**
     * A unique ID.
     */
    id: string;
    /**
     * The name of the threshold. Submitted with its owning form as part of a name/value pair.
     */
    name: string;
    /**
     * The controlled value of the threshold. Must be used in conjunction with onValueChange.
     */
    value?: number[];
    /**
     * The stepping interval.
     */
    step?: number;
    /**
     * The maximum value for the range.
     */
    max?: number;
    /**
     * The minimum value for the range.
     */
    min?: number;
    /**
     * Event handler called when the value changes at the end of an interaction. Useful when you only need to capture a final value e.g. To update a backend service.
     */
    onValueCommit?: (value: number[]) => void;
    /**
     * Event handler called when the value changes.
     */
    onValueChange?: (value: number[]) => void;
    /**
     * List of initial thumb values.
     */
    initialValues: number[];
    /**
     * Number of divided counts.
     */
    displaySplitCount?: number;
    /**
     * To display the add/remove thumb buttons.
     */
    addAddendsButtons?: boolean;
}

/**
 * The Threshold component provides interactive controls for setting multiple boundary values on a range slider with color-coded segments.
 *
 * [Threshold in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=46189-299030&t=oCQvDh35aqAC6r0M-4).
 */
export const Threshold = ({
    'aria-describedby': ariaDescribedby = undefined,
    'aria-invalid': ariaInvalid,
    'aria-labelledby': ariaLabelledby,
    'data-id': dataId = 'cosmos-threshold',
    disabled = false,
    id,
    max = 100,
    min = 0,
    name,
    onValueCommit = undefined,
    step = undefined,
    initialValues,
    onValueChange = undefined,
    displaySplitCount = 5,
    addAddendsButtons = false,
}: ThresholdProps): React.JSX.Element => {
    const splitCountLabels = splitNumberIntoParts(max, min, displaySplitCount);
    const [thumbValues, setThumbValues] = useState(initialValues);
    const thumbs = useMemo(
        () => thumbValues,
        // eslint-disable-next-line react-hooks/exhaustive-deps -- We only care about length changes because other prop changes of the thumb values are irrelevant the amount of thumbs.
        [thumbValues.length],
    );

    const handleValueCommit: ThresholdProps['onValueCommit'] = (range) => {
        onValueCommit?.(range);
    };

    const handleOnChange = (range: number[]) => {
        setThumbValues(range);
        onValueChange?.(range);
    };

    return (
        <StyledContainer data-testid="Threshold" data-id="2WoDjd4h">
            <StyledSliderRoot
                aria-describedby={ariaDescribedby}
                aria-invalid={ariaInvalid}
                aria-labelledby={ariaLabelledby}
                minStepsBetweenThumbs={1}
                data-id={dataId}
                disabled={disabled}
                id={id}
                max={max}
                min={min}
                name={name}
                step={step}
                value={thumbValues}
                onValueCommit={handleValueCommit}
                onValueChange={handleOnChange}
            >
                <StyledTrack values={thumbValues} max={max} min={min} />
                {thumbs.map((val, index) => {
                    return (
                        <Tooltip
                            isInteractive
                            key={`${val}-${index + 1}`}
                            text={`${thumbValues[index]}`}
                            data-id="j6HTYp9e"
                        >
                            <StyledThumb>
                                <Icon name="ReorderDotsVertical" />
                            </StyledThumb>
                        </Tooltip>
                    );
                })}
            </StyledSliderRoot>
            {displaySplitCount ? (
                <StyledStepMeterContainerDiv>
                    {splitCountLabels.reverse().map((val) => (
                        <Text size="100" key={val} data-id="kGXTW16t">
                            {val}
                        </Text>
                    ))}
                </StyledStepMeterContainerDiv>
            ) : null}
            {addAddendsButtons && (
                <Stack gap="1x">
                    <Button
                        label="add Thumb"
                        width="auto"
                        onClick={() => {
                            if (
                                thumbs.length === MAX_THUMB_NUMBER ||
                                disabled
                            ) {
                                return;
                            }
                            setThumbValues((prev) => [
                                ...prev,
                                prev[prev.length - 1] + THUMB_SPACING,
                            ]);
                        }}
                    />
                    <Button
                        label="remove Thumb"
                        onClick={() => {
                            if (
                                thumbs.length === MIN_THUMB_NUMBER ||
                                disabled
                            ) {
                                return;
                            }
                            setThumbValues((prev) =>
                                prev.slice(0, prev.length - 1),
                            );
                        }}
                    />
                </Stack>
            )}
        </StyledContainer>
    );
};
