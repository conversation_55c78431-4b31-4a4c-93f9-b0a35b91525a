import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as ThresholdStories from './Threshold.stories';

<Meta of={ThresholdStories} />

<Title />

<Description />

<Primary />

<Controls of={ThresholdStories.Playground} />

## Import

```jsx
import { Threshold } from '@cosmos-lab/components/threshold';
```

## 🟢 When to use the component

- **Risk level configuration** - Set multiple risk thresholds with color-coded severity levels

## ❌ When not to use the component

- **Single value selection** - Use standard Slider component for single threshold values
- **Static displays** - When threshold values are read-only and don't need adjustment
- **Complex multi-dimensional data** - For data requiring charts or advanced visualizations

## 🛠️ How it works

The Threshold component renders a multi-thumb range slider with color-coded track segments and optional scale labels, built on Radix Slider primitives.

### Usability

**Core functionality:**
- **Multi-thumb slider** - Supports multiple draggable thumbs for setting threshold boundaries
- **Color-coded segments** - Track displays different colors between threshold values using design system tokens
- **Value tooltips** - Each thumb shows current value in tooltip on hover/focus
- **Scale labels** - `displaySplitCount` generates evenly distributed reference labels below the slider
- **Dynamic thumb management** - `addAddendsButtons` enables add/remove thumb functionality

**Interaction patterns:**
- **Drag adjustment** - Thumbs can be dragged to adjust threshold values
- **Keyboard navigation** - Arrow keys adjust values with optional `step` increments
- **Boundary constraints** - Values automatically constrained within `min`/`max` range
- **Real-time feedback** - `onValueChange` fires during adjustment, `onValueCommit` on completion

### Content

- Provide descriptive `aria-labelledby` reference for screen readers
- Set appropriate `min`/`max` ranges that reflect real operational boundaries
- Use meaningful `name` values for form submission
- Consider `step` values that align with your data precision needs

### Accessibility

**What the design system provides:**
- Semantic slider structure with proper ARIA attributes via Radix Slider primitives
- Keyboard navigation with arrow keys, Home/End, and Page Up/Down support
- Focus management with visible focus indicators on active thumbs
- Screen reader support with value announcements and range information
- Touch target sizing meeting accessibility guidelines for drag interactions
- High contrast support through design system color tokens

**Development responsibilities:**
- Provide meaningful `aria-labelledby` reference describing the threshold purpose
- Ensure `onValueChange` and `onValueCommit` callbacks handle data persistence appropriately
- Test multi-thumb interactions with keyboard navigation and assistive technology
- Handle form integration properly using `name` and `id` props
- Provide context about what the threshold values represent in surrounding content

**Design responsibilities:**
- Ensure sufficient contrast between color-coded track segments
- Design thumb indicators that are easily identifiable and appropriately sized
- Consider color-blind accessibility by ensuring color coding has additional visual cues
- Provide clear visual hierarchy between thumbs, track, and scale labels
- Test color combinations across different threshold configurations

