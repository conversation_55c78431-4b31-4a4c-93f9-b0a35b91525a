import { styled } from 'styled-components';
import { dimension6x } from '@cosmos/constants/tokens';
import { Root } from '@radix-ui/react-slider';

export const StyledSliderRoot = styled(Root)`
    position: relative;
    display: flex;
    align-items: center;
    user-select: none;
    touch-action: none;
    width: 100%;
    height: ${dimension6x};

    // The first <span> represents the Slider's track,
    // so this targets the tooltip thumbs to apply the necessary CSS styles.
    > span:not(:first-of-type) {
        display: flex;
    }
`;
