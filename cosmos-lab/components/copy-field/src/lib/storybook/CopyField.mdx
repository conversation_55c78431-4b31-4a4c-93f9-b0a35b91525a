import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as CopyFieldStories from './CopyField.stories';

<Meta of={CopyFieldStories} />

<Title />

<Description />

<Primary />

<Controls of={CopyFieldStories.Playground} />

## Import

```jsx
import { CopyField } from '@cosmos-lab/components/copy-field';
```

## 🟢 When to use the component

- **Sharing values** - When users need to copy URLs, tokens, API keys, or configuration strings for use elsewhere
- **Read-only data display** - For displaying important values that users frequently need to reference or share
- **One-click copying** - When the primary action users want to take with displayed text is copying it

## ❌ When not to use the component

- **Editable content** - Use TextField or TextareaField when users need to modify the content
- **Simple text display** - Use Text component when copying functionality isn't needed
- **Sensitive information** - Consider security implications before allowing easy copying of sensitive data

## 🛠️ How it works

The CopyField component displays read-only text with an integrated copy-to-clipboard button, providing users with easy access to copy important values like URLs, tokens, or configuration strings.

**Component structure:**
- **TextField integration** - Uses TextField component in read-only mode for consistent form field styling
- **Copy button** - Integrated Button component with copy icon and feedback states
- **Clipboard API** - Uses modern `navigator.clipboard.writeText()` for secure copying
- **Visual feedback** - Button text and icon change to "Copied!" state with automatic reset

**Copy functionality:**
- **One-click copying** - Single button click copies the entire field value to clipboard
- **Error handling** - Graceful fallback with console error logging if clipboard access fails
- **Feedback timing** - "Copied!" state displays for 1 second before reverting to default
- **Accessibility** - Button includes descriptive `a11yLabelOverride` for screen readers

**Form integration:**
- **Form field structure** - Accepts `formId` and `name` props for proper form association
- **Read-only state** - TextField is always read-only with `onChange={noop}` to prevent editing
- **Label support** - Supports standard form field labeling for accessibility and context
- **Consistent styling** - Maintains design system consistency with other form components

### Usability

- Clear purpose with copy icon and label indicating functionality
- Immediate visual confirmation when copy action succeeds
- Error resilience handling clipboard API failures gracefully
- Full visibility of value in text field for user verification

**Interaction patterns:**
- **Single action** - One click/keypress copies the complete value
- **Visual confirmation** - Button state change provides clear success feedback
- **Quick reset** - Automatic return to default state allows for repeated copying
- **Touch-friendly** - Button sizing appropriate for touch interaction on mobile devices

### Content

- Use descriptive labels explaining what value is being displayed (e.g., "API Key", "Webhook URL")
- Display complete values that will be copied, avoiding truncation when possible
- Use consistent copy-related terminology ("Copy", "Copied!") across the application
- Ensure copied value matches exactly what users expect based on displayed content

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper form field relationships and button labeling
- Full keyboard navigation support including Tab navigation and Space/Enter activation for copy button
- Screen reader announcements for copy actions through descriptive button labels
- High contrast support and focus management with visible focus indicators
- Touch target sizing meeting accessibility guidelines with proper spacing

**Development responsibilities:**
- Provide descriptive labels that give clear context for what value is being copied
- Ensure `formId` and `name` props for proper form association when used within forms
- Handle copy failures gracefully without breaking user experience
- Consider security implications of values being made easily copyable

**Design responsibilities:**
- Provide sufficient color contrast for all field states and button states across themes
- Design clear visual hierarchy showing relationship between label, value, and copy action
- Ensure focus indicators are clearly visible and meet contrast requirements
- Create consistent visual patterns for copy fields across the application
- Design appropriate spacing and sizing for copy button that works with text field layout

