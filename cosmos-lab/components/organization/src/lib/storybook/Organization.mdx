import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as OrganizationCosmosLabStories from './Organization.stories';

<Meta of={OrganizationCosmosLabStories} />

<Title />

<Description />

<Primary />

<Controls of={OrganizationCosmosLabStories.Playground} />

## Import

```jsx
import { Organization } from '@cosmos-lab/components/organization';
```

## Props

### `fallbackText`

The `fallbackText` prop allows you to specify a default text or description that will be displayed if the organization image is not available or fails to load.

<Canvas of={OrganizationCosmosLabStories.FallbackText} />

### `imgSrc`

The `imgSrc` prop on the Organization component represents the source URL or path for the image that will be displayed as the organization. If an image is being rendered, it should always be accompanied by `alt` text, provided by the `imgAlt` prop.

<Canvas of={OrganizationCosmosLabStories.ImgSrc} />

### `size`

There are four options for the `size` prop, with a default of "md".

<Canvas of={OrganizationCosmosLabStories.Size} />

## 🟢 When to use the component

- **Organization identification** - Display company logos or initials in tables, lists, and cards
- **Vendor displays** - Show vendor/company logos in table cells and vendor profiles
- **Organization stacks** - Use with OrganizationStack for multiple organization displays
- **Workspace identification** - Display workspace logos and organization branding
- **Provider displays** - Show service provider logos in connection tables
- **Service provider displays** - Show third-party service logos in integration contexts

## ❌ When not to use the component

- Use [Avatar](https://cosmos.drata.com/?path=/docs/media-imagery-avatar--docs) for user representation
- Use [FrameworkBadge](https://cosmos.drata.com/?path=/docs/media-imagery-frameworkbadge--docs) for compliance framework logos

## 🛠️ How it works

The Organization component displays company logos in a square format with automatic fallback to text when images fail to load.

### Usability

**Core functionality:**
- **Image display** - Shows organization logo when `imgSrc` is provided and loads successfully
- **Automatic fallback** - Displays `fallbackText` when image is unavailable or fails to load
- **Error handling** - Uses `onError` handler to detect image load failures and switch to fallback state
- **Square design** - All organizations use consistent square/rounded rectangle shape
- **Responsive sizing** - Four size variants with corresponding text sizes for optimal readability

**Visual design:**
- **Fallback styling** - Dark background (`neutralBackgroundStrong`) with white text (`neutralTextInverted`)
- **Text formatting** - Uses Text component with "title" type and "inherit" colorScheme for fallback text
- **Size consistency** - Each size has corresponding text size for proportional display
- **State management** - Internal `hasError` state tracks image load failures

**Size guidelines:**
- **Extra small (`xs`)** - Used in OrganizationStack and compact displays
- **Small (`sm`)** - Used in OrganizationIdentity and table cells
- **Medium (`md`)** - Default size for general usage
- **Large (`lg`)** - Used for prominent company displays and profile sections

**Usage patterns:**
- **OrganizationIdentity component** - Most common usage with Identity component for company information displays
- **Vendor table cells** - Used in data tables for vendor/company identification
- **Workspace displays** - Used for workspace branding and identification
- **Provider connections** - Used in service provider and integration contexts

### Content

- Use 1-2 character `fallbackText` (typically company initials from `generateFallbackText()` helper)
- Provide descriptive `imgAlt` text that identifies the organization
- Choose appropriate `size` based on context and available space
- Ensure `imgSrc` URLs are accessible and properly authenticated
- Use helper functions like `generateFallbackText()` and `getAvatarFallbackText()` consistently

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper image and text elements
- Screen reader support with `imgAlt` text for images and readable fallback text
- High contrast fallback design with dark background and white text for visibility
- Scalable sizing that works at different zoom levels and screen sizes
- Focus management integration when used within interactive components

**Development responsibilities:**
- Always provide `imgAlt` when using `imgSrc` for screen reader accessibility
- Use meaningful `fallbackText` that identifies the organization (typically initials)
- Test organization displays across different company name formats and image availability
- Ensure fallback text remains readable at all size variants
- Consider the context where organizations appear and provide appropriate surrounding labels

**Design responsibilities:**
- Maintain sufficient contrast between fallback background and text colors
- Ensure organization sizing works within the broader interface layout
- Test organization appearance with various text lengths and character sets
- Consider the visual hierarchy when organizations appear alongside other company information
- Verify square shape consistency across all size variants and content types
