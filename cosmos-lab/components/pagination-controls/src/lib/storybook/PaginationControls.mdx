import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as PaginationControlsStories from './PaginationControls.stories';

<Meta of={PaginationControlsStories} />

<Title />

<Description />

<Primary />

<Controls of={PaginationControlsStories.Playground} />

## Import

```jsx
import { PaginationControls } from '@cosmos-lab/components/pagination-controls';
```

## 🟢 When to use the component

- **Large data sets** - Navigate through extensive lists of controls, policies, evidence, or audit findings
- **Data tables** - Provide navigation for tables with many rows of structured data
- **Stacked lists** - Provide navigation for large lists of items with complex content structures

## ❌ When not to use the component

- **Small data sets** - For fewer items that fit comfortably on screen
- **Infinite scroll contexts** - Use infinite scroll patterns for continuous browsing experiences
- **Step-by-step processes** - Use appropriate stepper or wizard components for guided workflows and multi-step forms
- **Tab navigation** - Use tab components for switching between different content sections
- **Panel navigation** - Use PanelPagination component for navigating through items within panels

## 🛠️ How it works

The PaginationControls component provides a complete pagination interface with page size selection, navigation buttons (first, previous, next, last), and current page indicators. It manages internal page state while allowing external control through callback functions for integration with data fetching and URL state management.

### Usability

**Navigation controls:**
- First page button (⏮) - Jump to the beginning of the data set
- Previous page button (◀) - Go back one page, disabled on first page
- Next page button (▶) - Go forward one page, disabled on last page
- Last page button (⏭) - Jump to the end of the data set
- Page size selector - Change how many items display per page

**User feedback:**
- Current page indicator shows "X-Y of Z" format (e.g., "1-10 of 100") to provide context about visible items
- Navigation buttons are disabled when not applicable (first/previous on page 1, next/last on final page)
- Page size changes reset to page 1 internally to avoid empty pages

**Performance considerations:**
- Larger page sizes reduce server requests but increase initial load time
- Smaller page sizes provide faster initial loads but require more navigation

#### Usage guidelines

- Place pagination controls at the bottom of data tables or content lists for easy access
- Use consistent page size options across similar data types in your application
- Provide clear feedback about total items and current page position to help users understand the data scope
- Maintain URL synchronization so users can bookmark or share specific pages


### Content

**Page size:**
Current page size defaults are limited to [10, 20, 50] items per page. In the future, ideally we would be able to support larger page sizes [25, 50, 100, 200] to accommodate for user needs.

**Loading and error states:**
- Show loading indicators during page transitions to provide feedback
- Handle errors gracefully with clear messages and retry options
- Maintain pagination state during temporary network issues
- Consider skeleton loading for consistent layout during data fetching

**Empty states:**
- Display appropriate empty state messages when no data is available
- Hide or disable pagination controls when there are no items to paginate

### Accessibility

**What the design system provides:**
- Semantic button elements with proper ARIA labels for all navigation controls
- Screen reader announcements for page changes and current position
- Keyboard navigation support for all interactive elements
- High contrast focus indicators for navigation buttons
- Proper labeling of page size selector with "rows per page" context
- ARIA live regions for dynamic page count updates

**Development responsibilities:**
- Implement proper focus management when page content changes
- Provide meaningful loading states that work with screen readers
- Ensure page changes are announced to assistive technology users
- Test keyboard navigation through all pagination controls
- Handle error states accessibly with clear messaging
- Consider reduced motion preferences for page transition animations

**Design responsibilities:**
- Ensure sufficient color contrast for all button states (enabled, disabled, focus)
- Design clear visual hierarchy between different navigation elements
- Provide adequate touch targets for mobile users (minimum 44px)
- Consider cognitive accessibility by keeping pagination controls simple and predictable
- Test usability across different screen sizes and orientations
- Design consistent pagination patterns across your application

