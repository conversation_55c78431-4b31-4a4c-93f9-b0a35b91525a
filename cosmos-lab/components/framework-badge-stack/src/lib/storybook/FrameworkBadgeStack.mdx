import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as FrameworkBadgeStackStories from './FrameworkBadgeStack.stories';

<Meta of={FrameworkBadgeStackStories} />

<Title />

<Description />

<Primary />

<Controls of={FrameworkBadgeStackStories.Playground} />

## Import

```jsx
import { FrameworkBadgeStack } from '@cosmos-lab/components/framework-badge-stack';
```

## 🟢 When to use the component

- When you need to show multiple frameworks in limited space

## ❌ When not to use the component

- Use FrameworkBadge for single framework displays

## 🛠️ How it works

The FrameworkBadgeStack component displays a horizontal stack of framework badges with overflow handling, showing additional count when exceeding the maximum visible items.

### Usability

**Core functionality:**
- **Overflow handling** - Automatically shows "+N" button when exceeding maxVisibleItems
- **Tooltip integration** - Each badge shows framework name on hover via Tooltip component
- **Popover expansion** - Clicking "+N" shows remaining frameworks with labels in popover
- **Fixed sizing** - Always uses `xs` (24px) framework badges for consistent compact display
- **Responsive overflow** - Adapts to different maxVisibleItems settings
- **Consistent spacing** - Uses `xs` gap between framework badges

**Visual design:**
- **Button styling** - "+N" button uses neutral colorScheme with tertiary level and small size
- **Stack layout** - Horizontal Stack with `xs` gap and `center` alignment
- **Consistent badges** - All badges use same `xs` size with framework logos and color schemes
- **Popover content** - Remaining frameworks displayed with badges and text labels in vertical Stack

**Overflow behavior:**
- **Automatic overflow** - Shows "+N" button when frameworkBadgesData length exceeds maxVisibleItems
- **Popover content** - Remaining frameworks displayed with badges and text labels in popover
- **Interactive expansion** - Users can click "+N" to see all remaining frameworks

### Content

- Use helper functions like `getFrameworkLabel()` for consistent framework names
- Handle empty states appropriately when no frameworks are provided
- Set appropriate maxVisibleItems based on context and available space
- Include customLabel for CUSTOM frameworks to provide meaningful names

### Accessibility

**What the design system provides:**
- Tooltip integration with accessible framework names for each badge via Tooltip component
- Popover content with full framework information for overflow badges
- Semantic structure using proper button and popover semantics for overflow
- High contrast badges inheriting FrameworkBadge component's accessibility features
- Keyboard navigation support for "+N" button and popover interaction

**Development responsibilities:**
- Always provide meaningful `badgeName` that maps to recognizable frameworks
- Use descriptive `customLabel` for CUSTOM frameworks that identifies the framework
- Test keyboard navigation across badge tooltips and overflow popover
- Ensure framework data is properly structured with required fields
- Consider the context where framework stacks appear and provide appropriate surrounding labels
- Verify tooltip and popover content is readable and provides sufficient framework identification

**Design responsibilities:**
- Maintain sufficient contrast between overflow button and background colors
- Ensure framework badge sizing works within the broader interface layout and constraints
- Test framework badge appearance with various framework types and custom labels
- Consider the visual hierarchy when framework stacks appear alongside other content
- Verify consistent spacing and alignment across different maxVisibleItems configurations
- Design clear visual distinction between individual badges and overflow button display