import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as AutocompleteStories from './Autocomplete.stories';

<Meta of={AutocompleteStories} />

<Title />

<Description />

<Primary />

<Controls of={AutocompleteStories.Playground} />

## Import

```jsx
import { Autocomplete } from '@cosmos-lab/components/autocomplete';
```

## 🟢 When to use the component

- **Single selection with search** - When users need to select one item from a large, searchable list
- **Async data loading** - When options need to be fetched from an API with search and pagination
- **Custom item creation** - When users should be able to create new items that don't exist in the list

## ❌ When not to use the component

- **Simple selection** - Use Select for small, static option lists
- **Multiple selection** - Use Combobox for multi-select scenarios
- **Static filtering** - Use Select when all options are available without API calls

## 🛠️ How it works

The Autocomplete component provides single-selection functionality with search, async data loading, and custom item creation using Downshift's useCombobox for accessibility and keyboard navigation.

**Core functionality:**
- **Search integration** - Built-in input field with configurable debouncing (default: 300ms)
- **Single selection** - Uses Downshift's useCombobox with custom `singleSelectStateReducer`
- **Async data loading** - Optional `onFetchOptions` with search terms and pagination support
- **Custom item creation** - Optional `onCreateCustomItem` creates items on blur when input doesn't match existing options

### Usability

**Search and selection:**
- Debounced input prevents excessive API calls with configurable delay
- Automatic fetching on mount unless `disableFetchOnMount` is true
- Optional focus fetching with `enableFetchOnFocus` for data refresh
- Lazy loading supports infinite scrolling with `hasMore` and `handleLazyLoad`

**Display options:**
- Rich content display with StructuredListItem via `transformSelectedItem`
- Optional clear button with `clearSelectedItemButtonLabel`
- Input initialization with `initializeInputWithSelectedValue` for custom items

### Content

- Provide meaningful `loaderLabel` for screen reader users during loading states
- Use descriptive `placeholderText` that indicates the type of content being searched
- Implement `getSearchEmptyState` to provide helpful feedback based on `inputValue`
- Structure options as ListBoxItems with `id`, `label`, `value`, and optional `description`, `startSlot`

### Accessibility

**What the design system provides:**
- Full Downshift useCombobox integration with proper ARIA attributes and keyboard navigation
- Screen reader support with `aria-busy`, `aria-live`, and state announcements
- Popover integration with focus management and portal rendering

**Development responsibilities:**
- Always provide `aria-labelledby` pointing to a visible label element
- Provide meaningful `loaderLabel` for loading state announcements
- Use descriptive `clearSelectedItemButtonLabel` when clear functionality is enabled
- Test keyboard navigation (arrows, Enter, Escape) and screen reader compatibility

**Design responsibilities:**
- Ensure sufficient contrast for all text and interactive elements
- Design clear visual hierarchy between input, selected item, and dropdown options
- Maintain consistent spacing and sizing with other form controls
- Provide clear visual feedback for loading, error, and empty states
- Test with longer option labels and descriptions to ensure layout stability