import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as PdfViewerCosmosLabStories from './PdfViewerCosmosLab.stories';

<Meta of={PdfViewerCosmosLabStories} />

<Title />

<Description />

<Primary />

<Controls of={PdfViewerCosmosLabStories.Playground} />

## Import

```jsx
import { PdfViewer } from '@cosmos-lab/components/pdf-viewer';
```

The PdfViewer component displays PDF documents in an embedded viewer with accessibility support and customizable labeling for document viewing within the application.

```jsx
<PdfViewer
    src="https://example.com/document.pdf"
    label="Financial Report Q3 2024"
    data-id="pdf-viewer"
/>
```

## 🟢 When to use the component

- **Document viewing** - Display PDF reports, forms, or documentation within the application
- **Embedded previews** - Show PDF content without requiring users to download files
- **Modal content** - Display PDF documents in modals for quick reference
- **Policy documents** - Show terms of service, privacy policies, or compliance documents
- **Report display** - Present generated reports or exported data in PDF format

## ❌ When not to use the component

- **Large file sizes** - Consider download links for very large PDFs that may impact performance
- **Mobile-first experiences** - PDF viewing can be challenging on small screens; consider alternatives
- **Interactive forms** - Use dedicated form components instead of PDF forms for better UX
- **Frequently updated content** - Use native web content for information that changes often
- **Accessibility-critical content** - Ensure PDF content is accessible or provide HTML alternatives

## 🛠️ How it works

The PdfViewer component renders PDF documents using a standard iframe with browser-native PDF viewing capabilities and accessibility attributes.

### Usability

**Core functionality:**
- **Iframe embedding** - Uses standard HTML iframe for cross-browser PDF display compatibility
- **Native PDF controls** - Leverages browser's built-in PDF viewer with zoom, navigation, and download controls
- **Lazy loading** - `loading="lazy"` attribute defers loading until the component is visible
- **Full-screen support** - `allowFullScreen` enables full-screen viewing when supported by browser
- **Responsive sizing** - Automatically fills container with 100% width and height

**Browser behavior:**
- **Native rendering** - Relies on browser's PDF plugin or built-in viewer for display
- **Download fallback** - Browsers without PDF support will prompt for download
- **Zoom and navigation** - Users get familiar browser PDF controls for interaction

### Content

- Ensure `src` URLs are accessible and properly authenticated if needed
- Use descriptive `label` text that clearly identifies the document content
- Verify PDF files are optimized for web viewing to ensure good performance
- Test PDF accessibility and consider providing HTML alternatives for complex documents

### Accessibility

**What the design system provides:**
- Semantic iframe structure with proper `title` attribute using the provided `label`
- Screen reader compatibility with meaningful document identification
- Keyboard navigation support through browser's native PDF viewer controls
- Focus management that integrates with the page's tab order
- Testing attributes with `data-testid="PdfViewer"` and configurable `data-id` for automated testing

**Development responsibilities:**
- Provide meaningful `label` text that describes the document content and purpose
- Ensure PDF documents themselves are accessible with proper structure and alt text
- Test PDF viewing across different browsers and devices for consistent experience
- Consider providing download links or HTML alternatives for users who cannot view embedded PDFs
- Handle loading errors gracefully when PDF sources are unavailable

**Design responsibilities:**
- Ensure the container provides appropriate sizing for PDF content viewing
- Consider the context where PDFs are embedded and provide adequate space
- Test PDF viewing experience across different screen sizes and orientations
- Provide clear visual context about what document is being displayed
