import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as StatBlockStories from './StatBlock.stories';

<Meta of={StatBlockStories} />

<Title />

<Description />

<Primary />

<Controls of={StatBlockStories.Playground} />

## Import

```jsx
import { StatBlock } from '@cosmos-lab/components/stat-block';
```

## Examples

### Basic StatBlock
The simplest implementation of a StatBlock may contain only a `title` and `statValue`.

<Canvas of={StatBlockStories.Basic} />

### Interactive StatBlock
If the StatBlock is interactive, set `isInteractive` to `true` and provide the appropriate `as` prop.

<Canvas of={StatBlockStories.Interactive} />

## 🟢 When to use the component
Use StatBlock to:
- Display **high-impact single values** with or without trend indicators
- Present metrics **when a single number says more than a graph**
- Create **dashboard cards** that highlight key performance indicators
- Provide **inline highlights** of important statistics within content
- Enable **interactive navigation** to detailed information or related data
- Show **trend information** alongside the primary statistic

**Specific use cases:**
- **Key performance indicators** on dashboards
- **Summary statistics** that link to detailed reports
- **Filter controls** that also display current counts or values
- **Achievement highlights** with trend information
- **Quick reference numbers** that support decision-making
- **Interactive buttons** that combine data display with navigation

## ❌ When not to use the component
Avoid StatBlock when:
- **Multiple related values** need comparison → use DataBar or tables instead
- **Trends over time** are more important than current value → use DataLine or DataBar
- **Proportional relationships** need to be shown → use DataDonut or DataPosture
- **Precise values in context** are needed → use tables for detailed data
- **Complex data relationships** require visualization → use appropriate charts
- **Space is extremely limited** → consider simpler text display
- **The statistic doesn't inspire action** → ensure every stat block has purpose

**Not suitable for:**
- **Data that requires detailed analysis** (use charts or tables)
- **Multiple metrics that should be compared** (use DataBar or tables)
- **Progress tracking** (use DataGauge or DataMeter)
- **Categorical breakdowns** (use DataDonut or DataPosture)
- **Time series visualization** (use DataLine)
- **Simple text that doesn't benefit from stat block styling**

## 🛠️ How it works

### Usability

**Key principles:**
- Always ask: **"What action does this visualization inspire?"**
- Ensure the **single number tells a clear story** without requiring additional context
- Use **trend indicators** to provide directional context when helpful
- Make **interactive stat blocks** clearly actionable with appropriate visual cues

**Design considerations:**
- **Prominent value display** that draws attention to the key metric
- **Clear hierarchy** between the main value and supporting information
- **Appropriate color schemes** that match the data's sentiment or importance
- **Icon usage** that reinforces the meaning or category of the statistic

**Interactive usage:**
- Set `isInteractive` to `true` for clickable stat blocks
- Use appropriate `as` prop (button, link) for the interaction type
- Provide clear visual feedback for interactive states
- Ensure the action is obvious from the context and styling

### Content

**Required Content:**
- `title`: The descriptive text that identifies what the statistic represents
- `statValue`: The numerical value being displayed

**Optional Content:**
- `statIcon`: Icon that reinforces the category or meaning of the statistic
- `statIconColor`: Color scheme for the icon (neutral, success, critical, etc.)
- `statValueColor`: Color scheme for the main value text
- `totalText`: Additional context text (e.g., "out of 343 total")
- `trendDirection`: Direction of trend (up or down)
- `trendPercentage`: Percentage change value
- `trendSentiment`: Whether the trend is positive or negative (affects color)
- `trendText`: Descriptive text for the trend period (e.g., "in the last 7 days")
- `isInteractive`: Makes the stat block clickable when true
- `as`: HTML element to render as (div, button, a, etc.)

**Content Guidelines:**
- Use clear, concise titles that immediately identify the metric
- Ensure trend information adds actionable insight rather than just decoration
- Provide meaningful context through `totalText` when the number alone isn't self-explanatory
- Choose appropriate color schemes that match the data's sentiment or importance

### Accessibility

**What the design system provides:**
- Semantic text components with proper heading hierarchy and color contrast
- Accessible icon integration with appropriate sizing and contrast ratios
- Interactive foundation with support for keyboard navigation and focus management when stat blocks are clickable
- Color system compliance with design tokens ensuring WCAG contrast requirements are met across all text and background combinations

**Development responsibilities:**
- Use semantic markup by implementing proper heading hierarchy and semantic HTML elements for the statistic structure
- Add descriptive labels using `aria-label` or `aria-describedby` to provide context about what the statistic represents
- Handle interactive states by implementing proper keyboard activation (Enter/Space keys) and focus management for clickable stat blocks
- Provide trend context by ensuring trend indicators and changes are announced by screen readers with appropriate ARIA attributes
- Add loading states by implementing accessible loading indicators when stat data is being fetched
- Test with assistive technology to verify all information is comprehensible with screen readers and keyboard navigation

**Design responsibilities:**
- Provide clear context by designing descriptive titles and labels that explain both the value and its significance
- Use meaningful icons by choosing icons that enhance understanding and provide alternative text for decorative elements
- Design clear interactions by providing clear visual and textual indication of what action will occur if stat blocks are clickable
- Consider information hierarchy by arranging statistics in logical order that supports scanning and comprehension
- Include trend explanations by designing content that explains what trend indicators mean and their significance
- Avoid color-only communication by ensuring trend direction and status are communicated through text and icons, not just color

