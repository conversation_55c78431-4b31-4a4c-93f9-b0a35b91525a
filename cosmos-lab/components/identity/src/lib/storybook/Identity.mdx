import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as IdentityStories from './Identity.stories';
import * as AvatarIdentityStories from './AvatarIdentity.stories';
import * as FrameworkBadgeIdentityStories from './FrameworkBadgeIdentity.stories';
import * as OrganizationIdentityStories from './OrganizationIdentity.stories';
import { Banner } from '@cosmos/components/banner';

<Meta of={IdentityStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="Avoid using Identity component directly from Cosmos. Instead, use our AvatarIdentity, FrameworkBadgeIdentity, and OrganizationIdentity presets for consistent integration."
/>

<Description />

<Primary />

<Controls of={IdentityStories.Playground} />

### Import

```jsx
import { Identity } from '@cosmos-lab/components/identity';
```

## Presets

### AvatarIdentity

The AvatarIdentity preset is a wrapper around the Identity component, specifically tailored to use the Avatar component as its figure.

<Canvas of={AvatarIdentityStories.AvatarIdentityPreset} />

<Controls of={AvatarIdentityStories.AvatarIdentityPreset} />

#### Import

```jsx
import { AvatarIdentity } from '@cosmos-lab/components/identity/presets/avatar-identity';
```

### OrganizationIdentity


The OrganizationIdentity preset is a wrapper around the Identity component, specifically tailored to use the Organization component as its figure.

<Canvas of={OrganizationIdentityStories.OrganizationIdentityPreset} />

<Controls of={OrganizationIdentityStories.OrganizationIdentityPreset} />

#### Import

```jsx
import { OrganizationIdentity } from '@cosmos-lab/components/identity/presets/organization-identity';
```

### FrameworkBadgeIdentity

The FrameworkBadgeIdentity preset is a wrapper around the Identity component, specifically tailored to use the FrameworkBadge component as its figure.

<Canvas of={FrameworkBadgeIdentityStories.FrameworkBadgeIdentityPreset} />

<Controls of={FrameworkBadgeIdentityStories.FrameworkBadgeIdentityPreset} />

#### Import

```jsx
import { FrameworkBadgeIdentity } from '@cosmos-lab/components/identity';
```

## 🟢 When to use the component

- **Entity identification** - Display users, organizations, or frameworks with descriptive labels

## ❌ When not to use the component

- **Direct usage** - Avoid using Identity component directly; use the provided AvatarIdentity, FrameworkBadgeIdentity, and OrganizationIdentity presets instead
- **Multiple entities** - Use stack components (AvatarStack, FrameworkBadgeStack) for multiple items
- **Simple displays** - Use individual components (Avatar, Organization, FrameworkBadge) when no caption is needed

## 🛠️ How it works

The Identity component provides a consistent figure + caption layout, automatically handling alignment and spacing between supported figure components and up to three text labels.

### Usability

**Layout behavior:**
- **Dynamic alignment** - Uses 'start' alignment when all three labels present, 'center' alignment otherwise
- **Consistent spacing** - 2x gap between figure and text, minimum 8x height container
- **Fixed text column** - Text content has minimum 20x width for consistent layout

**Size variants:**
- **xs** - Extra small, primary label uses size '100'
- **sm** - Small size (default), primary label uses size '200'

**Label hierarchy:**
- **Primary label** - Main identifier (required), uses title typography with size mapping
- **Secondary/Tertiary labels** - Optional context, both use body typography size '100'

### Content

- Keep labels concise and scannable
- Use primary label for entity name or main identifier
- Provide meaningful secondary/tertiary context when available

### Accessibility

**What the design system provides:**
- Semantic HTML with proper text hierarchy using Stack and Text components
- Consistent spacing that works across zoom levels
- Integration with figure component accessibility features

**Development responsibilities:**
- Provide meaningful, descriptive labels that identify the entity clearly
- Ensure labels make sense when read sequentially by screen readers
- Use presets for consistent accessibility patterns across similar contexts

**Design responsibilities:**
- Maintain sufficient contrast between text and background colors
- Ensure text hierarchy is visually clear with appropriate font weights and sizes
- Design layouts that work well at different zoom levels and screen sizes
- Consider text length variations and ensure layout remains stable with longer labels
- Verify figure components maintain their accessibility features within Identity layouts

