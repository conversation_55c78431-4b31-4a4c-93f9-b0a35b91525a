import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as ConfirmationStories from './Confirmation.stories';

<Meta of={ConfirmationStories} />

<Title />

<Description />

<Primary />

<Controls />

## Import

```jsx
import { Confirmation } from '@cosmos-lab/components/confirmation';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@cosmos-lab/helpers/temp-confirmation-modal';

// Using the helper function
openConfirmationModal({
    title: 'Confirm Delete',
    body: 'Are you sure you want to delete this item?',
    confirmText: 'Delete',
    cancelText: 'Cancel',
    type: 'danger',
    onConfirm: () => handleDelete(),
    onCancel: () => handleCancel(),
});

closeConfirmationModal();
```

## Examples

### Danger variant

Use the danger variant for destructive actions that need extra confirmation.

<Canvas of={ConfirmationStories.DangerVariant} />

### Custom labels

Customize the button labels to match your specific use case.

<Canvas of={ConfirmationStories.CustomLabels} />

## 🟢 When to use the component

- **High-impact decisions** - When actions have significant consequences that warrant explicit confirmation
- **Data loss prevention** - When operations might result in permanent loss of user work or content
- **Critical state changes** - When actions fundamentally alter system state or user settings
- **Bulk operations** - When actions affect multiple items and users should confirm the scope

## ❌ When not to use the component

- **Simple form submissions** - Use standard form validation and submission patterns instead
- **Low-impact operations** - When actions have minimal consequences or are easily correctable
- **Frequent actions** - When confirmations would create excessive interruption in common workflows
- **Complex decision making** - When users need additional information or context to make informed decisions

## 🛠️ How it works

The Confirmation component renders as modal content with a consistent three-part structure: header with title and close button, body with descriptive text, and footer with action buttons.

#### Best Practices

1. Use clear and specific messages that explain the consequences of the action
2. Use the danger variant for destructive actions
3. Make the confirm button text action-specific (e.g., "Delete" instead of just "Confirm")
4. Keep the body text concise and focused
5. Use the primary variant for non-destructive actions

### Usability

**Core functionality:**
- **Clear action confirmation** - Title and body text explain the action and its consequences
- **Dual action options** - Confirm and cancel buttons provide clear choice paths
- **Loading state support** - Confirm button shows loading state during async operations
- **Flexible dismissal** - Users can cancel via button, close button, or escape key
- **Type-based styling** - Primary and danger variants communicate action severity

**Interaction patterns:**
- **Focus management** - Modal traps focus and returns to triggering element on close
- **Button hierarchy** - Cancel button (secondary) and confirm button (primary/danger) with appropriate visual weight
- **Consistent placement** - Cancel on left, confirm on right following platform conventions
- **Loading feedback** - Confirm button becomes disabled and shows loading state during processing

### Content

**Required Content:**
- `title`: Clear, specific title describing the action (e.g., "Delete Account")
- `body`: Explanatory text describing consequences or asking for confirmation
- `confirmText`: Action-specific button text (e.g., "Delete" not "Confirm")
- `onConfirm`: Callback function executed when user confirms the action

**Optional Content:**
- `cancelText`: Custom cancel button text (defaults to "No, go back")
- `onCancel`: Callback function for cancel action (enables cancel button when provided)
- `type`: Visual variant - 'primary' for standard actions, 'danger' for destructive actions (defaults to 'primary')
- `isLoading`: Function returning loading state for async confirm operations
- `data-id`: Unique testing identifier

**Content Guidelines:**
- Use specific, descriptive titles that clearly identify the action being confirmed
- Write body text that explains consequences without being overly verbose
- Make confirm button text action-specific rather than generic
- Use danger variant for irreversible or destructive operations
- Keep messaging focused on the decision at hand

### Accessibility

**What the design system provides:**
- Modal structure with proper ARIA attributes including `role="dialog"`
- Focus management that traps focus within modal and restores focus on close
- Keyboard navigation with Enter/Space for button activation and Escape for dismissal
- Screen reader support with `aria-labelledby` pointing to title and `aria-describedby` for body content
- Semantic button elements with appropriate labels and disabled states
- High contrast support through design system color tokens

**Development responsibilities:**
- Provide clear, descriptive titles and body text that explain the action and consequences
- Ensure confirm button text is action-specific for better screen reader context
- Handle loading states with appropriate ARIA attributes during async operations
- Test with assistive technology to verify all content is properly announced
- Implement proper error handling and user feedback for failed operations

**Design responsibilities:**
- Create clear visual hierarchy between title, body text, and action buttons
- Ensure sufficient contrast for all text and interactive elements
- Design loading states that provide clear visual feedback during processing
- Consider content length and ensure readability across different screen sizes
- Provide clear visual distinction between primary and danger action types


#### Keyboard Support

<table>
    <tbody>
        <tr>
            <th>Key</th>
            <th>Function</th>
        </tr>
        <tr>
            <td>
                <kbd>Tab</kbd>
            </td>
            <td>Moves focus between buttons</td>
        </tr>
        <tr>
            <td>
                <kbd>Enter</kbd> or <kbd>Space</kbd>
            </td>
            <td>Activates the focused button</td>
        </tr>
        <tr>
            <td>
                <kbd>Esc</kbd>
            </td>
            <td>Closes the modal (triggers cancel action)</td>
        </tr>
    </tbody>
</table>

#### ARIA Attributes

- Modal has `role="dialog"`
- Modal has `aria-labelledby` pointing to the title
- Modal has `aria-describedby` pointing to the body text
- Close button has appropriate `aria-label`

