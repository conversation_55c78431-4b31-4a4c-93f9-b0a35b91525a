import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as StackedListStories from './StackedList.stories';

<Meta of={StackedListStories} />

<Title />

<Description of={StackedListStories} />

<Primary />

<Controls of={StackedListStories.Playground} />

## Import

```jsx
import { StackedList, StackedListItem } from '@cosmos-lab/components/stacked-list';
```

The only required prop for StackedListItem is `primaryColumn`, making it simple to create basic lists:

```jsx
<StackedList>
    <StackedListItem primaryColumn="Simple list item" />
    <StackedListItem primaryColumn="Another list item" />
    <StackedListItem primaryColumn="Third list item" />
</StackedList>
```

### Custom components

You can create custom components that render StackedListItem internally. This maintains semantic HTML structure while allowing for component composition:

```jsx
// ✅ Good: Custom component that renders StackedListItem
const TaskStackedListItem = ({ task, onEdit, onDelete }) => (
    <StackedListItem
        eyebrow={task.category}
        primaryColumn={task.title}
        secondaryColumn={task.dueDate}
        action={
            <ActionStack
                actions={[
                    {
                        id: 'edit',
                        actionType: 'button',
                        typeProps: { label: 'Edit', onClick: onEdit }
                    },
                    {
                        id: 'delete',
                        actionType: 'button',
                        typeProps: { label: 'Delete', onClick: onDelete }
                    }
                ]}
            />
        }
    />
);

// Usage maintains proper HTML structure
<StackedList>
    <TaskStackedListItem task={task1} onEdit={handleEdit} onDelete={handleDelete} />
    <TaskStackedListItem task={task2} onEdit={handleEdit} onDelete={handleDelete} />
</StackedList>
```

## Examples

### Avatar identity examples

Use StackedList to display user information with avatar components.

<Canvas of={StackedListStories.AvatarIdentityOnly} />

### Avatar identity with status

Show user information alongside approval status indicators.

<Canvas of={StackedListStories.AvatarIdentityWithApprovalStatus} />

### Avatar identity with actions

Display user information with action buttons for management tasks.

<Canvas of={StackedListStories.AvatarIdentityWithDeleteAction} />

### Clickable rows

Make entire rows clickable for navigation while maintaining accessibility.

<Canvas of={StackedListStories.ClickableRows} />

### Individual StackedListItem

Explore all props and configurations for the StackedListItem component.

<Canvas of={StackedListStories.StackedListItemStory} />

## 🟢 When to use the component

Use StackedList when you need to display structured content in a vertical list format without column headers. It's ideal for:

- **Lists of similar items** with consistent structure and layout patterns
- **User lists, settings, or configuration items** where each item may have different action requirements
- **Content with primary/secondary information hierarchy** that benefits from vertical scanning
- **Flat structure without nested hierarchy** where users need to quickly scan and potentially take action on individual items
- **Lists that may require pagination** for larger datasets (~3–20 items work best)

**Perfect for:**
- User directories with avatars, names, and management actions
- Settings panels with toggles, descriptions, and edit capabilities
- Content lists with metadata, status indicators, and quick actions
- Any scenario where you need consistent item presentation with optional interactions

## ❌ When not to use the component

- **Your data needs column headers for clarity** - Use DataTable instead
- **You need sortable, filterable, or bulk operations** - Use DataTable instead
- **Content has nested or collapsible details** - Use Accordion instead
- **You have simple labeled facts or metadata** - Use Key Value Pairs instead
- **Content is purely textual without structure** - Use the List component instead
- **You need complex data visualization or comparison features** - Consider chart components

## 🛠️ How it works

### Usability

StackedList follows a compound component pattern designed for optimal user experience:

- **StackedList** acts as the semantic container (`<ul>` element)
- **StackedListItem** represents individual items (`<li>` elements)
- **Consistent spacing and visual separation** between items for easy scanning
- **Right-aligned actions** for predictable interaction patterns
- **Responsive design** that adapts to different screen sizes

**Component Composition Pattern:**

While StackedList accepts ReactNode for flexibility, the recommended pattern is to create custom components that render StackedListItem internally. This approach:

- ✅ **Maintains proper semantic HTML structure** (`<ul><li>` not `<ul><div><li>`)
- ✅ **Allows for component reusability and abstraction** across different contexts
- ✅ **Preserves accessibility features** and screen reader compatibility
- ✅ **Enables custom logic** while following design system patterns
- ✅ **Supports consistent styling** and interaction behaviors

**Example:** `<TaskStackedListItem />` should render a `<StackedListItem />` component internally, not replace it.

### Content

**Required Content:**
- `primaryColumn`: The main content for each list item (required) - this is the primary identifier or title

**Optional Content:**
- `eyebrow`: Small text above the primary content for categorization or context (automatically styled with Text size="100" for strings)
- `secondaryColumn`: Supporting information displayed alongside primary content for additional details
- `action`: Interactive elements like buttons or links (best used with ActionStack, Button, or Link components)

**Automatic Text Wrapping:**
- String content in `primaryColumn` is automatically wrapped with Text size="200" for optimal readability
- String content in `secondaryColumn` is automatically wrapped with Text size="100" for supporting information hierarchy
- ReactNode content is rendered as-is without additional wrapping, giving you full control over styling

**Content Guidelines:**
- Keep primary content concise and scannable
- Use secondary content for supplementary details that aid decision-making
- Ensure action labels are clear and describe the specific action being performed
- Consider the information hierarchy - most important content should be in the primary column

#### Design Principles

- **Scannable layout** - Users can quickly identify and compare items
- **Consistent structure** - Each item follows the same visual pattern
- **Progressive disclosure** - Essential information is immediately visible, with actions available on demand
- **Flexible content** - Supports various content types while maintaining visual consistency
**Visual Hierarchy:**
- Primary content should be the most prominent element in each item
- Secondary content provides supporting context without competing for attention
- Actions should be discoverable but not dominate the visual space

**Content Strategy:**
- Keep primary content concise and scannable for quick identification
- Use secondary content for details that help users make decisions
- Group related actions together using ActionStack for better organization

**Responsive Behavior:**
- Content automatically adapts to available space
- Actions may stack or adjust based on screen size
- Maintain touch-friendly interaction targets on mobile devices

### Accessibility

**What the design system provides:**
- Semantic list structure with built-in `<ul>` and `<li>` elements for proper screen reader navigation and list comprehension
- Keyboard navigation support with full keyboard accessibility through all interactive elements and logical tab order
- Focus management with visual focus indicators that meet WCAG contrast requirements and proper focus handling for interactive elements
- Touch target sizing with interactive elements meeting accessibility guidelines with minimum 44px touch targets
- Screen reader optimization with list structure and item count announced properly and appropriate ARIA attributes
- Accessible color system with design system tokens ensuring WCAG 2.1 AA contrast compliance for all text and interactive elements
- Clickable row accessibility with hidden button elements for screen reader users when rows are clickable

**Development responsibilities:**
- Provide meaningful labels by always including descriptive `rowButtonLabel` text for clickable rows that explains the action
- Ensure action accessibility with action buttons that have clear, descriptive labels explaining their specific purpose
- Maintain content hierarchy by wrapping StackedList in containers with appropriate headings that describe the list's purpose
- Test with assistive technology to verify all interactive elements are accessible through screen readers and keyboard navigation
- Handle dynamic content by ensuring proper announcements when list content changes or updates
- Implement proper error states by providing accessible feedback for any validation errors or loading states
- Support keyboard shortcuts by ensuring <kbd>Escape</kbd> key properly closes any popovers or modals triggered by list actions

**Design responsibilities:**
- Create clear visual hierarchy by designing primary and secondary content with sufficient contrast and visual distinction
- Don't rely on color alone by using text, icons, and other visual cues alongside color to convey meaning and status
- Ensure sufficient spacing by maintaining adequate spacing between interactive elements to prevent accidental activation
- Design accessible states by creating clear visual states for hover, focus, active, and disabled that work for all users
- Consider content length by designing for variable content lengths while maintaining readability and accessibility
- Plan for responsive behavior by ensuring touch targets remain accessible across different screen sizes and orientations
