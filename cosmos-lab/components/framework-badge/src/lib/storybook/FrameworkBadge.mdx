import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as FrameworkBadgeStories from './FrameworkBadge.stories';

<Meta of={FrameworkBadgeStories} />

<Title />

<Description />

<Primary />

<Controls of={FrameworkBadgeStories.Playground} />

## Import

```jsx
import { FrameworkBadge } from '@cosmos-lab/components/framework-badge';
```

## Props

### `badgeName`

The `badgeName` prop specifies which compliance framework to display. It accepts framework identifiers like 'SOC_2', 'ISO_27001', 'GDPR', etc.

<Canvas of={FrameworkBadgeStories.AllFrameworkBadges} />

## 🟢 When to use the component

- **Framework identification** - Display compliance framework logos in cards, lists, and headers
- **Framework details pages** - Show framework identity in page headers and sections
- **Framework stacks** - Use with FrameworkBadgeStack for multiple framework displays
- **Compliance displays** - Show framework certifications and standards

**Current usage patterns:**
- **FrameworkBadgeStack integration** - Most common usage with `size="xs"` for compact displays
- **GalleryCard component** - Used in `imageSlot` for framework cards
- **Page headers** - Used for framework identification in page headers
- **Vendor profiles** - Used with `size="lg"` for prominent framework display

## ❌ When not to use the component

- **User representation** - Use [Avatar](https://cosmos.drata.com/?path=/docs/media-imagery-avatar--docs) component for users
- **Organization representation** - Use [Organization](https://cosmos.drata.com/?path=/docs/media-imagery-organization--docs) component for company/organization logos
- Use [Icons](https://cosmos.drata.com/?path=/docs/media-imagery-icon--docs) when representing objects, actions, or other communicative symbols.

## 🛠️ How it works

The FrameworkBadge component displays compliance framework logos with consistent styling and sizing across different contexts.

### Usability

**Core functionality:**
- **Framework display** - Shows framework logo when `badgeName` is provided
- **SVG-based logos** - Uses scalable vector graphics for crisp display at any size
- **Consistent sizing** - Four size variants with proportional logo scaling
- **Color scheme options** - Primary (blue) and neutral (gray) variants
- **Helper integration** - Works with `getFrameworkBadge()` helper for API data mapping

**Visual design:**
- **Logo styling** - Framework-specific SVG logos with consistent visual treatment
- **Size consistency** - Each size maintains proper proportions for framework logos
- **Color schemes** - Primary scheme for emphasis, neutral for secondary contexts
- **Circular container** - Consistent circular background for all framework logos

**Size guidelines:**
- **Extra small (`xs`)** - Used in FrameworkBadgeStack and compact displays
- **Small (`sm`)** - Used in constrained spaces
- **Medium (`md`)** - Default size for general usage
- **Large (`lg`)** - Used for prominent displays like vendor compliance sections

**Framework support:**
- **30+ frameworks** - Supports major compliance standards (SOC 2, ISO 27001, GDPR, HIPAA, etc.)
- **API integration** - Use `getFrameworkBadge()` helper to map API framework tags to display names
- **Custom frameworks** - `CUSTOM` badge for user-defined frameworks
- **Consistent logos** - Each framework has a standardized visual representation

### Content

- Use `getFrameworkBadge()` helper function for API data to ensure consistent mapping
- Choose appropriate `size` based on context and available space
- Use `primary` colorScheme for emphasis, `neutral` for secondary contexts
- Ensure framework identifiers match supported framework names

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper SVG elements and accessible markup
- High contrast logos designed for clear visibility across color schemes
- Scalable design that works at different zoom levels and screen sizes
- Consistent sizing with predictable dimensions for layout planning
- Focus management integration when used within interactive components

**Development responsibilities:**
- Use meaningful framework identifiers that map to recognizable compliance standards
- Test framework badge displays across different contexts and sizes
- Ensure framework logos remain clear and identifiable at all size variants
- Consider the context where framework badges appear and provide appropriate surrounding labels
- Verify color scheme choice maintains sufficient contrast with surrounding content

**Design responsibilities:**
- Maintain sufficient contrast between framework logos and background colors
- Ensure framework badge sizing works within the broader interface layout
- Test framework logo appearance across different size variants
- Consider the visual hierarchy when framework badges appear alongside other content
- Verify consistent circular shape and proportions across all frameworks and sizes
