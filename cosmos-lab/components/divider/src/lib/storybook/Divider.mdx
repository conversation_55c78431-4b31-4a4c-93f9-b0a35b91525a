import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as DividerStories from './Divider.stories';

<Meta of={DividerStories} />

<Title />

<Description />

<Primary />

<Controls of={DividerStories.Playground} />

## Import

```jsx
import { Divider } from '@cosmos-lab/components/divider';
```

## 🟢 When to use the component

- **Visual separation** - Create clear separation between content sections
- **Layout boundaries** - Separate different areas within cards, panels, or modals

## ❌ When not to use the component

- **Within tight layouts** - When spacing and typography already provide clear separation
- **Simple interfaces** - When content structure is already clear without visual aids
- **Layout components** - Use Stack, Grid, or Box components for structural separation

## 🛠️ How it works

The Divider component provides visual separation between content sections with support for different orientations and sizes.

**Size options:**
- **Small (`sm`)** - 1px thickness, default size
- **Medium (`md`)** - 2px thickness, for stronger visual emphasis

**Orientation variants:**
- **Horizontal (default)** - Used for separating stacked content sections
- **Vertical** - Used in horizontal layouts to separate adjacent elements

**Technical behavior:**
- Built on Radix UI Separator with proper ARIA semantics
- Renders as semantic HTML separator element
- Uses design system color tokens (`neutralBorderFaded`)

### Usability

**Usage patterns:**
- Default implementation is simply `<Divider />` with no props needed
- Parent components handle spacing around dividers
- Maintains visibility at different zoom levels and screen sizes

### Content

**Integration patterns:**
- **FormGroup component** - Includes built-in divider with `showDivider` prop (defaults to `true`)
- **Panel and card layouts** - Used to separate content sections within containers
- **Header layouts** - Used with vertical orientation to separate navigation elements

**Spacing guidelines:**
- **Automatic spacing** - Parent components handle appropriate spacing around dividers
- **No manual margins** - Avoid adding custom margins; rely on container spacing patterns
- **Consistent application** - Follow established patterns in similar interface contexts

**Content organization:**
- Use dividers to create logical content groupings
- Apply consistently within similar interface patterns
- Maintain clear content hierarchy with appropriate visual breaks

### Accessibility

**What the design system provides:**
- Semantic separator role from Radix UI Separator
- Proper ARIA attributes for screen reader compatibility
- High contrast support through design system tokens
- Scalable design that maintains visibility at different zoom levels

**Development responsibilities:**
- Use dividers to enhance content structure for screen readers
- Ensure dividers don't create confusion in content flow
- Test that dividers provide meaningful content organization

**Design responsibilities:**
- Ensure sufficient contrast ratios for divider visibility
- Design consistent divider usage patterns across similar interface contexts
