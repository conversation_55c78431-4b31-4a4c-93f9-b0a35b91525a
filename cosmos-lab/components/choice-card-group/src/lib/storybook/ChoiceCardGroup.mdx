import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as ChoiceCardGroupStories from './ChoiceCardGroup.stories';

<Meta of={ChoiceCardGroupStories} />

<Title />

<Description />

<Primary />

<Controls of={ChoiceCardGroupStories.Playground} />

## Import

```jsx
import { ChoiceCardGroup } from '@cosmos-lab/components/choice-card-group';
```

## 🟢 When to use the component

- **Related option sets** - When grouping multiple ChoiceCards that belong to the same decision or category
- **Form field replacement** - When standard radio or checkbox groups need enhanced visual presentation
- **Complex selection scenarios** - When users need to compare multiple options with detailed information

## ❌ When not to use the component

- **Simple form fields** - Use standard RadioFieldGroup or CheckboxFieldGroup for basic selections
- **Space-constrained layouts** - Groups require significant space
- **Unrelated options** - Don't group ChoiceCards that represent different decisions

## 🛠️ How it works

Groups multiple choice cards together with shared validation and selection behavior for radio or checkbox interactions. Automatically manages orientation, focus, and accessibility for the entire group.

**Key features:**
- **Selection modes** - Supports both single selection (radio) and multiple selection (checkbox) modes
- **Responsive layout** - Automatically switches to vertical layout when more than 3 options are provided
- **Form integration** - Provides automatic responsive layout switching between horizontal and vertical orientations
- **Accessibility** - Handles form validation and error states at the group level

### Usability

**Orientation behavior:**
- Automatically switches to vertical layout when more than 3 options are provided
- Horizontal layout used for 3 or fewer options when space allows
- Can be manually overridden using `cosmosUseWithCaution_forceOptionOrientation`
- Responsive behavior adapts to container width

**Selection management:**
- Handles value state for the entire group with onChange callback for all selected values
- Supports controlled and uncontrolled usage patterns
- Manages focus movement between options with keyboard navigation (Tab, Arrow keys, Space, Enter)

### Content

**Group labeling:**
- Use clear, descriptive labels for the entire group with help text to explain selection criteria
- Use parallel structure across all options in the group
- Keep individual option labels concise and scannable
- Order options logically (alphabetical, by importance, or by frequency of use)

### Accessibility

**What the design system provides:**
- Proper fieldset and legend structure for screen readers with ARIA attributes for group labeling
- Keyboard navigation support with arrow key movement and focus management within the group
- Screen reader announcements for selection changes and high contrast mode compatibility

**Development responsibilities:**
- Associate the group with form validation and error messaging
- Provide clear error feedback at the group level and ensure proper form submission handling
- Test keyboard navigation across different browsers and implement accessible loading states

**Design responsibilities:**
- Design clear visual grouping that works for screen readers with sufficient contrast for all elements
- Create accessible error states clearly associated with the group
- Don't rely solely on color to convey group state or validation
