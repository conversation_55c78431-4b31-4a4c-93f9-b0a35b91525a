import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as TruncationCosmosLabStories from './Truncation.stories';

<Meta of={TruncationCosmosLabStories} />

<Title />

<Description />

<Primary />

<Controls of={TruncationCosmosLabStories.Playground} />

## Import

```jsx
import { Truncation } from '@cosmos-lab/components/truncation';
```

## 🟢 When to use the component

- **Non-critical content with fallback access** - When content is not mission-critical to see in full immediately but should still be accessible if needed
- **With long text** - For displaying long text content in constrained space
- **Dynamic or variable-length content** - For content like names, file paths, or account IDs that may overflow their container
- **Multi-line text truncation** - When you need to limit text to a specific number of lines using `lineClamp`
- **Responsive text display** - When text needs to adapt to different container widths using `maxWidthPx`
container widths using `maxWidthPx`
- **Character limit enforcement** - When you need to limit text to a specific number of characters using `maxLength`

## ❌ When not to use the component

- **Critical information** - When the truncated content is essential for user understanding and cannot be hidden (error messages, validation messages)
- **Interactive text elements** - When users need to interact with specific parts of the text that might be truncated
- **Short text content** - When text is unlikely to exceed the available space
- **Accessibility-critical content** - When truncated text would prevent screen reader users from accessing important information
- **Real-time editing** - When users are actively editing the text content

## 🛠️ How it works

The Truncation component handles text overflow in constrained spaces with multiple truncation modes and sophisticated truncation detection.

**Truncation Modes:**

| Mode | Description | Use Case | Example Output |
|------|-------------|----------|----------------|
| `start` | Used at the beginning of a text string to indicate the text is continued from a previous location.	|File paths, URLs, IDs where the end is most important | `"...to/important/document.pdf"` |
| `middle` | Used when several text strings have different beginnings and/or endings but the exact same middle characters. Can also be used to shorten a phrase or text string when the end of a string cannot be truncated by an ellipsis.	| Account IDs, tokens, hashes where start and end matter | `"account_id_1234567890_very_l...ifier_end"` |
| `end` | Used at the end of a character string or paragraph to indicate that there is more content in another location, to show that the pattern in a sequence continues, or to shorten a long text string. | Descriptions, names, sentences where beginning is key | `"This is a very long description that exceeds th..."` |

The Truncation component uses two different approaches depending on the configuration:

#### CSS Truncation vs Manual Truncation

**CSS Truncation** (used when mode is 'end' with `lineClamp` > 1 or `maxWidthPx` without `maxLength`):
- Uses CSS properties like `text-overflow: ellipsis`, `-webkit-line-clamp`, and `overflow: hidden`
- More performant for simple end truncation scenarios
- Automatically responsive to container size changes

**Manual Truncation** (used for 'start', 'middle' modes, or when `maxLength` is specified):
- JavaScript-based text slicing using the `truncateText` function
- Allows for complex truncation patterns like start and middle truncation
- Uses a default max length of 50 characters when not specified

#### Truncation Detection

The component includes sophisticated truncation detection using:
- **ResizeObserver** - Monitors container size changes to detect when truncation occurs
- **Scroll measurements** - Compares `scrollHeight` vs `clientHeight` for multi-line, `scrollWidth` vs `clientWidth` for single-line
- **Character counting** - For manual truncation, compares text length against max length
- **Callback notifications** - Calls `onTruncate` when truncation status changes

### Usability

**Responsive Behavior:**
- Automatically adjusts to container size changes using ResizeObserver
- Falls back to window resize events when ResizeObserver is unavailable
- Supports both fixed character limits (`maxLength`) and responsive width constraints (`maxWidthPx`)
- Provides real-time truncation status updates through `onTruncate` callbacks

**Performance Considerations:**
- **CSS truncation** (more performant) used for 'end' mode with `lineClamp` > 1 or `maxWidthPx` without `maxLength`
- **Manual truncation** (JavaScript-based) used for 'start', 'middle' modes or when `maxLength` is specified
- Efficient truncation detection with minimal DOM measurements
- Cleanup of event listeners and ResizeObserver on unmount

### Content

**Text Processing:**
- Handles string content only - does not support rich text or JSX
- Preserves text integrity while applying truncation rules
- Uses ellipsis (`...`) as the truncation indicator for all modes

**Default Values:**
- Default mode is `'end'` for most common use case
- Default max length is `DEFAULT_MAX_LENGTH_TRUNCATION` (50 characters) when using manual truncation
- Default width is 100% of container

### Accessibility

**What the design system provides:**
- Basic HTML div structure for text container
- ResizeObserver integration for responsive truncation detection
- Testing attributes and `onTruncate` callback for status changes

**Development responsibilities:**
- **Provide full text access** - Always implement tooltips, expandable content, or other mechanisms when truncation occurs
- **Use onTruncate callback** - Handle truncation status changes to show/hide accessibility features
- **Add ARIA labels** - Include `aria-label`, `aria-describedby`, or `title` attributes for screen readers
- **Test with assistive technology** - Verify truncated content is accessible through alternative means

**Design responsibilities:**
- **Prioritize important content** - Ensure critical information appears before truncation points
- **Provide visual cues** - Design clear indicators that full content is available
- **Test with real content** - Verify truncation works with actual data lengths and patterns
- **Plan for internationalization** - Account for different text lengths in various languages

**Note:** The Truncation component provides minimal built-in accessibility features. Developers must implement proper ARIA labeling, screen reader support, and alternative access methods for truncated content.

