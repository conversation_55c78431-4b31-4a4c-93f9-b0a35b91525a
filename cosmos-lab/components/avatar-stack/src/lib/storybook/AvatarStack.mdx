import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as AvatarStackCosmosLabStories from './AvatarStack.stories';

<Meta of={AvatarStackCosmosLabStories} />

<Title />

<Description />

<Primary />

<Controls of={AvatarStackCosmosLabStories.Playground} />

## Import

```jsx
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
```

## 🟢 When to use the component

- When you need to show multiple users in limited space
- Use in table cells to represent users even when there will always only be one user when the user is metadata to the main entity

## ❌ When not to use the component

- Use Avatar for single user displays
- In table cells when there will always only be one user and the user is the main information, use AvatarIdentity instead


## 🛠️ How it works

The AvatarStack component displays a horizontal stack of user avatars with overflow handling, showing additional count when exceeding the maximum visible items.

**Table cell guidelines**
- **Use AvatarStack** - When users are metadata, even if there only will be one user (e.g., control owners where control is the primary information)
- **Use AvatarIdentity** - When there will only ever be one user and the user is the primary information

### Usability

**Core functionality:**
- **Overflow handling** - Automatically shows "+N" button when exceeding maxVisibleItems
- **Tooltip integration** - Each avatar shows user name on hover via Tooltip component
- **Popover expansion** - Clicking "+N" shows remaining users with PopoverContent format
- **Fixed sizing** - Always uses `xs` (24px) avatars for consistent compact display
- **Responsive overflow** - Adapts to different maxVisibleItems settings
- **Consistent spacing** - Uses `xs` gap between avatars in horizontal Stack

**Visual design:**
- **Button styling** - "+N" button uses neutral colorScheme with tertiary level and small size
- **Stack layout** - Horizontal Stack with `xs` gap and `center` alignment
- **Consistent avatars** - All avatars use same `xs` size with fallback text and optional images
- **Popover content** - Remaining users displayed with PopoverContent component in popover

**Size guidelines:**
- **Fixed size** - Always uses `xs` (24px) avatars for consistent compact display
- **Responsive overflow** - Adapts to different maxVisibleItems settings
- **Consistent spacing** - Uses `xs` gap between avatars

**Overflow behavior:**
- **Automatic overflow** - Shows "+N" button when avatarData length exceeds maxVisibleItems
- **Popover content** - Remaining users displayed with PopoverContent component in popover
- **Interactive expansion** - Users can click "+N" to see all remaining users

### Content

- Use helper functions like `getInitials()` and `getFullName()` consistently
- Handle empty states with `isEmpty()` check and show `EmptyValue` when no users assigned
- Set appropriate maxVisibleItems based on context and available space
- Ensure fallbackText is concise (1-2 characters) for proper avatar display

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper Stack layout and individual Avatar components
- Tooltip integration with accessible user names for each avatar via Tooltip component
- High contrast avatars inheriting Avatar component's accessibility features
- Consistent sizing with fixed `xs` size ensuring predictable layout
- Focus management integration when used within interactive components
- Screen reader support through individual Avatar tooltips and meaningful fallback text

**Development responsibilities:**
- Always provide meaningful `fallbackText` for accessibility (typically user initials)
- Use descriptive `primaryLabel` that identifies the user for tooltip display
- Test tooltip accessibility across different avatar displays
- Ensure empty state handling with appropriate fallback components like EmptyValue
- Consider the context where avatar stacks appear and provide appropriate surrounding labels
- Verify tooltip content is readable and provides sufficient user identification

**Design responsibilities:**
- Maintain sufficient contrast between overflow text and background colors
- Ensure avatar sizing works within the broader interface layout and table cell constraints
- Test avatar appearance with various text lengths and character sets for fallback text
- Consider the visual hierarchy when avatar stacks appear alongside other user information
- Verify consistent spacing and alignment across different maxVisibleItems configurations
- Design clear visual distinction between individual avatars and overflow count display
