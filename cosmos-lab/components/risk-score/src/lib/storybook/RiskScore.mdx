import { Controls, Description, Meta, Primary, Title } from '@storybook/addon-docs/blocks';
import * as RiskScoreStories from './RiskScore.stories';

<Meta of={RiskScoreStories} />

<Title />

<Description />

<Primary />

<Controls of={RiskScoreStories.Playground} />

## Import

```jsx
import { RiskScore } from '@cosmos-lab/components/risk-score';
```


## 🟢 When to use the component

- **Display risk assessment scores** - When you need to show calculated risk values with visual severity indicators
- **Risk management interfaces** - When building dashboards, tables, or panels that show risk data
- **Color-coded severity communication** - When users need to quickly identify risk levels through visual cues
- **Consistent risk presentation** - When you need uniform formatting of risk scores across your application

## ❌ When not to use the component

- **Non-risk numerical data** - When displaying scores or numbers that aren't related to risk assessment, or when numerical values don't have associated severity levels or color-coding requirements
- **Complex risk data** - When risk information requires detailed breakdowns, charts, or multi-dimensional analysis
- **Interactive risk editing** - When users need to directly edit or input risk values (use form components instead)
- **Large datasets** - When displaying many risk scores in dense layouts where the component's visual weight would be overwhelming

## 🛠️ How it works

The RiskScore component combines a numerical score with visual severity indicators using color-coded backgrounds and optional labels.

### Usability

- **Clear severity communication** - Color coding provides immediate visual feedback about risk levels
- **Consistent sizing** - Two size options (sm, md) accommodate different layout contexts
- **Intensity control** - Moderate and strong intensity options allow for visual hierarchy
- **Disabled state handling** - Automatically handles null scores with appropriate visual treatment
- **Label integration** - Optional labels provide additional context without cluttering the display
- **Responsive design** - Adapts to container constraints while maintaining readability

### Content

- **Severity levels** - Four severity options: low, moderate, high, critical
- **Score display** - Numerical values clearly presented within color-coded containers
- **Contextual labels** - Optional descriptive text that complements the numerical score
- **Null handling** - Graceful display when score data is unavailable

### Accessibility

**What the design system provides:**
- Semantic HTML structure with appropriate text hierarchy and color contrast
- Accessible color system ensuring WCAG 2.1 AA compliance across all severity levels
- Screen reader optimization with meaningful text announcements including both score and severity
- Consistent text sizing and spacing for optimal readability
- High contrast ratios maintained across all color schemes and intensity levels

**Developer responsibilities:**
- Ensure severity levels accurately reflect the actual risk assessment
- Provide meaningful labels that give context to the numerical scores
- Test color perception with users who have color vision differences
- Verify that risk information is understandable without relying solely on color
- Consider providing additional context through tooltips or surrounding content when needed

**Design responsibilities:**
- Design clear visual hierarchy that distinguishes between different severity levels
- Ensure sufficient contrast between text and background across all severity/intensity combinations
- Create consistent spacing and alignment when multiple RiskScore components are displayed together
- Plan for responsive behavior ensuring readability across different screen sizes
- Consider the visual weight of RiskScore components in relation to surrounding content

