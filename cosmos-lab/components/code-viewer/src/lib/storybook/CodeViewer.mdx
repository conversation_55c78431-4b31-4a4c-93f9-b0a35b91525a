import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as CodeViewerCosmosLabStories from './CodeViewer.stories';

<Meta of={CodeViewerCosmosLabStories} />

<Title />

<Description />

<Primary />

<Controls of={CodeViewerCosmosLabStories.Playground} />

## Import

```jsx
import { CodeViewerCosmosLab } from '@cosmos-lab/components/code-viewer-cosmos-lab';
```
## Examples

### Language

<Canvas of={CodeViewerCosmosLabStories.Language} />

### Information icon

<Canvas of={CodeViewerCosmosLabStories.InformationButton} />

### Editable

<Canvas of={CodeViewerCosmosLabStories.Editable} />

## 🟢 When to use the component

- **Code display** - Show configuration files, API responses, or code snippets with syntax highlighting
- **Editable code fields** - Allow users to modify JSON, YAML, or other structured data
- **Documentation examples** - Display code samples with copy functionality for easy reuse
- **Configuration management** - Edit and validate configuration files with error highlighting

## ❌ When not to use the component

- **Large file editing** - Use dedicated code editors for extensive development work
- **Simple text input** - Use TextArea for plain text without syntax requirements
- **Performance-critical contexts** - Monaco Editor has overhead; consider lighter alternatives for simple cases

## 🛠️ How it works

The CodeViewer component wraps Monaco Editor with custom theming, action controls, and validation feedback for code editing workflows.

### Usability

**Core functionality:**
- **Monaco Editor integration** - Full-featured code editor with syntax highlighting and IntelliSense
- **Language support** - Automatic syntax detection and highlighting for multiple programming languages
- **Edit mode toggle** - `isEditable` enables editing with save/cancel controls and validation
- **Copy functionality** - One-click copying with visual feedback and error handling
- **Info button** - Optional information button for contextual help via `onClickInfoButton`
- **Loading states** - `isLoading` shows skeleton placeholder during content loading

**Editing workflow:**
- **Read-only by default** - Safe viewing mode with copy and info actions
- **Edit activation** - Edit button switches to editable mode with Monaco Editor controls
- **Validation feedback** - Real-time error highlighting with error count display
- **Save/Cancel actions** - Save validates and commits changes; Cancel reverts to original value
- **Error prevention** - Save button disabled when validation errors exist

**Visual feedback:**
- **Action states** - Copy button shows "Copied!" confirmation for 1 second
- **Error display** - FieldFeedback shows validation errors with specific error counts
- **Copy errors** - Displays feedback when clipboard operations fail

### Content

- Choose appropriate `language` for accurate syntax highlighting
- Provide meaningful `onSave` callbacks that handle validation and persistence
- Use `isLoading` during initial content fetch or processing
- Set `data-id` for testing and debugging purposes

### Accessibility

**What the design system provides:**
- Monaco Editor's built-in accessibility features including keyboard navigation and screen reader support
- Semantic button controls with proper labels and ARIA attributes via ActionStack component
- Focus management that maintains context during edit mode transitions
- High contrast support through custom 'drata-editor' theme with accessible color combinations
- Error messaging with proper ARIA associations via FieldFeedback component

**Development responsibilities:**
- Ensure `onSave` callbacks provide appropriate success/error feedback to users
- Test edit workflows with keyboard navigation and screen readers
- Provide meaningful context about what code is being edited through surrounding content
- Handle loading and error states appropriately with proper announcements
- Consider the complexity of code being edited and provide appropriate user guidance

**Design responsibilities:**
- Maintain sufficient contrast in the custom Monaco theme across all syntax highlighting colors
- Ensure action buttons are clearly distinguishable and appropriately sized
- Design error states that clearly communicate validation issues without overwhelming the interface
- Consider the visual hierarchy between code content and control actions
- Test color combinations for accessibility across different code languages and themes