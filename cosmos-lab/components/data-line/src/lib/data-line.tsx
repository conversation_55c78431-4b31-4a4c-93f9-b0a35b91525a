import { isNumber } from 'lodash-es';
import {
    CartesianGrid,
    type DotProps,
    Line,
    LineChart,
    ResponsiveContainer,
    Tooltip,
    XAxis,
    <PERSON>Axis,
} from 'recharts';
import {
    borderWidthSm,
    dimension2x,
    neutralBackgroundNone,
} from '@cosmos/constants/tokens';
import { TooltipContentComponent } from './components/tooltip-content.component';
import { AXIS_TICK_CONFIG } from './constants/axis-tick-config.constant';
import { DATA_LINE_CONFIG } from './constants/data-line-config.constant';
import { TOOLTIP_WRAPPER_STYLE } from './constants/tooltip-wrapper-style.component';
import type { DataLineProps } from './types/data-line-props.type';

/**
 * Render function for active dot.
 */
const renderActiveDot = (
    stroke: string,
): ((props: DotProps) => React.ReactElement) => {
    /**
     * Dimension2x = 8px - inner colored square.
     */
    const size = Number(dimension2x);
    /**
     * BorderWidthSm = 1px.
     */
    const borderWidth = Number(borderWidthSm);
    /**
     * 4px to center the colored square on the line.
     */
    const offset = size / 2;

    // eslint-disable-next-line react/display-name -- Recharts expects a render function without display name
    return function (props: DotProps) {
        const { cx, cy } = props;

        // Only render if we have valid coordinates
        if (
            !isNumber(cx) ||
            !isNumber(cy) ||
            isNaN(cx) ||
            isNaN(cy) ||
            isNaN(size) ||
            isNaN(borderWidth) ||
            isNaN(offset)
        ) {
            return <g />;
        }

        // Additional safety check for calculated positions
        const x = cx - offset;
        const y = cy - offset;

        if (isNaN(x) || isNaN(y)) {
            return <g />;
        }

        return (
            <rect
                data-testid="ActiveDot"
                data-id="active-dot"
                x={cx - offset}
                y={cy - offset}
                width={size}
                height={size}
                fill={stroke}
                stroke={neutralBackgroundNone}
                strokeWidth={borderWidth}
            />
        );
    };
};

export const DataLine = <T extends object>({
    'data-id': dataId,
    width = '100%',
    height = 200,
    data,
    categoryKey,
    lines = [],
    tooltipComponent: TooltipComponent = TooltipContentComponent,
}: DataLineProps<T>): React.JSX.Element => {
    return (
        <ResponsiveContainer
            width={width}
            height={height}
            data-testid="DataLine"
            data-id={dataId}
        >
            <LineChart data={data}>
                <CartesianGrid horizontal syncWithTicks vertical={false} />
                <XAxis
                    dataKey={categoryKey}
                    axisLine={false}
                    tickLine={false}
                    tick={AXIS_TICK_CONFIG}
                />
                <YAxis
                    orientation="left"
                    width={DATA_LINE_CONFIG.width}
                    axisLine={false}
                    tickLine={false}
                    tick={AXIS_TICK_CONFIG}
                />
                <Tooltip
                    cursor={{ fill: 'transparent' }}
                    wrapperStyle={TOOLTIP_WRAPPER_STYLE}
                    content={TooltipComponent}
                />
                {lines.map((line) => (
                    <Line
                        key={line.dataKey}
                        dataKey={line.dataKey}
                        stroke={line.stroke}
                        strokeWidth={line.strokeWidth ?? 2}
                        type={line.type ?? 'monotone'}
                        dot={line.dot ?? false}
                        data-id={`${dataId}-line-${line.dataKey}`}
                        activeDot={
                            line.activeDot === false
                                ? false
                                : renderActiveDot(line.stroke)
                        }
                    />
                ))}
            </LineChart>
        </ResponsiveContainer>
    );
};
