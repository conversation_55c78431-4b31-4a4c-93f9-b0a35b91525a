import { Text } from '@cosmos/components/text';
import type { Meta } from '@storybook/react-vite';
import { Highlight } from '../highlight';

const meta: Meta<typeof Highlight> = {
    tags: ['Lab'],
    title: 'Typography & Content/Highlight',
    component: Highlight,
    args: {
        children: <Text>This is a highlighted text</Text>,
    },
};

export default meta;

// Playground should be first and have all controls enabled so they are accessible on Docs page
export { Playground, SearchResults } from './stories';
