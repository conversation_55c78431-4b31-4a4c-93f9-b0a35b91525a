import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as HighlightStories from './HighlightCosmosLab.stories';

<Meta of={HighlightStories} />

<Title />

<Description />

<Primary />

<Controls of={HighlightStories.Playground} />

## Import

```jsx
import { Highlight } from '@cosmos-lab/components/highlight';
```

## 🟢 When to use the component

- **Search result emphasis** - Highlight matching terms in search results
- **Content scanning** - Help users identify relevant information in text blocks
- **Status indication** - Use color variants for different contexts

## ❌ When not to use the component

- **Interactive selection** - Don't use for user-selectable content requiring interaction
- **Overuse in content** - Don't highlight too much text as it reduces effectiveness

## 🛠️ How it works

The Highlight component wraps content in a `<mark>` element with colored background and border styling.

### Usability

**Core functionality:**
- **Three color options** - Green, yellow (default), and red color schemes using design tokens
- **Flexible content** - Accepts any ReactNode content for highlighting
- **Inline styling** - Integrates within text flow without layout disruption

### Content

**Highlighting guidelines:**
- **Search results** - Highlight matching search terms to help users identify relevant content
- **Key information** - Emphasize important data points, status indicators, or critical values
- **Color meaning** - Use green for positive/success, yellow for attention/neutral, red for warnings/errors
- **Selective use** - Highlight only the most relevant portions to maintain effectiveness and readability

### Accessibility

**What the design system provides:**
- Semantic `<mark>` element for screen reader support
- Color contrast through design tokens
- Border styling for additional visual distinction

**Development responsibilities:**
- Don't rely solely on color to convey meaning
- Use highlighting sparingly to avoid cognitive overload
- Test with screen readers to verify proper announcements

**Design responsibilities:**
- Ensure highlight colors work well with surrounding interface elements
- Design consistent color usage patterns across the application
- Consider colorblind users when choosing highlight color meanings
