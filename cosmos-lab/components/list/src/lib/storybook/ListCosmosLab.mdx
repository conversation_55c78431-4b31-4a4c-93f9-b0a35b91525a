import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as ListStories from './ListCosmosLab.stories';

<Meta of={ListStories} />

<Title />

<Description />

<Primary />

<Controls of={ListStories.Playground} />

## Import

```jsx
import { List } from '@cosmos-lab/components/list';
// or
import { ListRoot, ListItem } from '@cosmos-lab/components/list';
```

## Examples

### Compound component pattern

The List component can be used as compound components, which gives you more control over the rendering of each item. This pattern also allows for creating nested lists, as shown in the example below.

<Canvas of={ListStories.CompoundComponent} />

### React nodes

The List component can accept React nodes as list items, allowing you to include interactive elements like links within list items.

<Canvas of={ListStories.ReactNodes} />

## 🟢 When to use the component

- **Simple collections** - When you need to display a straightforward list of related items without complex structure
- **Semantic content** - For content that benefits from proper HTML list semantics (`<ul>`, `<ol>`, `<li>`)
- **Mixed content types** - When list items contain a combination of text, links, buttons, or other React components
- **Hierarchical information** - For nested lists that show relationships between different levels of content
- **Consistent styling** - When you need uniform typography and spacing across list items

## ❌ When not to use the component

- **Tabular data** - Use DataTable for data that requires column headers, sorting, or filtering
- **User management interfaces** - Use StackedList for structured data with avatars, metadata, and actions
- **Expandable content sections** - Use Accordion when items need to show/hide detailed information
- **Key-value information** - Use Key Value Pairs for displaying labeled facts or metadata

## 🛠️ How it works

The List component renders semantic HTML lists with consistent styling, supporting both simple array-based and compound component patterns.

**List types:**
- **Unordered** - Renders as `<ul>` with bullet points for general collections
- **Ordered** - Renders as `<ol>` with numbers for sequential content
- **None** - Renders as `<ul>` with `list-style: none` for custom styling

**Content and styling:**
- **ReactNode items** - Accepts any valid React content
- **Size and color** - Uses Text component sizes and color schemes
- **Compound components** - ListRoot for containers, ListItem for individual items
- **Nested support** - Supports hierarchical content by nesting components

### Usability

**Usage patterns:**
- **Consistent content** - Use similar item types within a single list
- **Appropriate nesting** - Use nested lists sparingly for clear hierarchy
- **Semantic choice** - Choose list type based on content meaning (ordered vs unordered)

### Content

**Content guidelines:**
- **Related items** - Ensure all items are logically related
- **List type selection** - Use ordered for sequential content, unordered for collections, none for custom styling
- **Nested structure** - Keep nesting shallow and ensure clear parent-child relationships

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper list elements for screen reader navigation
- High contrast support and scalable typography
- Proper color contrast ratios across all color schemes

**Development responsibilities:**
- Organize list items logically for screen readers
- Use appropriate semantic markup based on content meaning
- Ensure interactive elements within list items are properly accessible

**Design responsibilities:**
- Visual hierarchy that supports the semantic structure with appropriate sizing and spacing
- Sufficient contrast for all list content and styling across color schemes
- Responsive typography that maintains readability and list structure across different screen sizes
- Focus indicators for any interactive content within list items

