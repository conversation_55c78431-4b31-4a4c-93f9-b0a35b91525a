import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as OrganizationStackCosmosLabStories from './OrganizationStack.stories';

<Meta of={OrganizationStackCosmosLabStories} />

<Title />

<Description />

<Primary />

<Controls of={OrganizationStackCosmosLabStories.Playground} />

## Import

```jsx
import { OrganizationStack } from '@cosmos-lab/components/organization-stack';
```

## 🟢 When to use the component

- When you need to show multiple organizations in limited space

## ❌ When not to use the component

- Use Organization component for single organization displays

## 🛠️ How it works

The OrganizationStack component displays a horizontal stack of organization logos with overflow handling, showing additional count when exceeding the maximum visible items.

### Usability

**Core functionality:**
- **Overflow handling** - Automatically shows "+N" button when exceeding maxVisibleItems
- **Tooltip integration** - Each organization shows company name on hover via Tooltip component
- **Popover expansion** - Clicking "+N" shows remaining organizations with OrganizationIdentity format
- **Fixed sizing** - Always uses `xs` (24px) organization components for consistent compact display
- **Responsive overflow** - Adapts to different maxVisibleItems settings
- **Consistent spacing** - Uses `xs` gap between organization components

**Visual design:**
- **Button styling** - "+N" button uses neutral colorScheme with tertiary level and small size
- **Stack layout** - Horizontal Stack with `xs` gap and `center` alignment
- **Consistent organizations** - All organizations use same `xs` size with fallback text and optional images
- **Popover content** - Remaining organizations displayed as OrganizationIdentity components in vertical Stack

**Overflow behavior:**
- **Automatic overflow** - Shows "+N" button when organizationData length exceeds maxVisibleItems
- **Popover content** - Remaining organizations displayed as OrganizationIdentity components in popover
- **Interactive expansion** - Users can click "+N" to see all remaining organizations

### Content

- Use helper functions like `generateFallbackText()` for consistent organization initials
- Handle empty states with `isEmpty()` check and show `EmptyValue` when no organizations assigned
- Set appropriate maxVisibleItems based on context and available space
- Ensure fallbackText is concise (1-2 characters) for proper organization display

### Accessibility

**What the design system provides:**
- Tooltip integration with accessible organization names for each organization via Tooltip component
- Popover content with full OrganizationIdentity information for overflow organizations
- Semantic structure using proper button and popover semantics for overflow
- High contrast organizations inheriting Organization component's accessibility features
- Keyboard navigation support for "+N" button and popover interaction

**Development responsibilities:**
- Always provide meaningful `primaryLabel` that identifies the organization
- Use descriptive `fallbackText` that represents the organization (typically initials)
- Test keyboard navigation across organization tooltips and overflow popover
- Ensure organization data is properly structured with required fields
- Consider the context where organization stacks appear and provide appropriate surrounding labels
- Verify tooltip and popover content is readable and provides sufficient organization identification

**Design responsibilities:**
- Maintain sufficient contrast between overflow button and background colors
- Ensure organization sizing works within the broader interface layout and constraints
- Test organization appearance with various company names and logo availability
- Consider the visual hierarchy when organization stacks appear alongside other content
- Verify consistent spacing and alignment across different maxVisibleItems configurations
- Design clear visual distinction between individual organizations and overflow button display
