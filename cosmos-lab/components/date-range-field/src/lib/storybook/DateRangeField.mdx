import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as DateRangeFieldStories from './DateRangeField.stories';
import { Banner } from '@cosmos/components/banner';

<Meta of={DateRangeFieldStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="Date range validation (ensuring start date is before end date) only works when using controlled component values."
/>

<Description />

<Primary />

<Controls of={DateRangeFieldStories.Playground} />

## Import

```jsx
import { DateRangeField } from '@cosmos-lab/components/date-range-field';
```

## Examples with pre-filled values

The DateRangeField component is fully controlled and supports pre-filling date values using the `value` prop. This is useful for editing existing date ranges or providing suggested date ranges to users.

### With pre-filled start date

You can pre-fill just the start date, leaving the end date for the user to select:

<Canvas of={DateRangeFieldStories.WithPrefilledStartDate} />

### With pre-filled date range

You can pre-fill both the start and end dates:

<Canvas of={DateRangeFieldStories.WithPrefilledDateRange} />

## 🟢 When to use the component

- **Date period selection** - When users need to select a start and end date for reports, filters, or time-based queries
- **Validation with constraints** - When date ranges need validation (start before end) and unavailable date handling

## ❌ When not to use the component

- **Single date selection** - Use DatePickerField for selecting individual dates
- **Read-only date display** - Use DateTime component for displaying existing date ranges without selection
- Time-based selections requiring hour/minute precision
- When immediate validation between dates isn't required
- When dates must be selected in reverse order (end before start)

## 🛠️ How it works

The DateRangeField component provides dual date picker inputs for selecting start and end dates with validation, accessibility features, and unavailable date handling.

**Core functionality:**
- **Dual DatePickerField inputs** - Separate start and end date pickers with coordinated behavior
- **Range validation** - Built-in validation ensuring start date is before or equal to end date (controlled values only)
- **Value management** - Uses `value` prop with `{ start: TDateISODate | null, end: TDateISODate | null }` structure
- **Unavailable date handling** - Separate `getIsDateUnavailableStart` and `getIsDateUnavailableEnd` functions with custom messages

### Usability

- Provides two date pickers in a stack layout
- Auto-validates that end date is after start date
- If Helptext is enabled it appears for both fields. If it’s disabled, it disappears for both fields too. Each field can have its own copy.
- Supports keyboard navigation and screen readers
- Shows validation feedback for invalid selections
- Handles localization through `locale` prop
- Integrates with form libraries via `name` props

**States**

- Default: Both fields enabled and empty
- Filled: One or both dates selected
- Error: Invalid date selection
- Disabled: Fields non-interactive
- ReadOnly: Values displayed but not editable

### Content

- Use clear labels that explain the date range purpose (e.g., "Audit Period", "Report Range")
- Use `a11yHiddenLabel` to provide specific context for start and end fields
- Help text should explain date constraints, validation rules, or business logic
- Error messages should clearly explain range validation requirements

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper form field relationships and dual input coordination
- Full keyboard navigation support including Tab order between start/end fields and within calendars
- Screen reader announcements for date selection, validation errors, and range completion
- High contrast support and focus management with visible focus indicators

**Development responsibilities:**
- Provide descriptive `label` and `a11yHiddenLabel` props that give clear context for the date range purpose
- Use meaningful help text that explains date constraints, business rules, or validation requirements
- Implement proper validation with clear, actionable error messages for range validation failures
- Ensure `getIsDateUnavailableStart` and `getIsDateUnavailableEnd` functions have logical, explainable constraints

**Design responsibilities:**
- Provide sufficient color contrast for all field states, calendar elements, and validation feedback across themes
- Design clear visual hierarchy that shows the relationship between start/end fields and overall range selection
- Ensure focus indicators are clearly visible and meet contrast requirements for both date inputs and calendar navigation
- Create consistent visual patterns for date range fields across the application

