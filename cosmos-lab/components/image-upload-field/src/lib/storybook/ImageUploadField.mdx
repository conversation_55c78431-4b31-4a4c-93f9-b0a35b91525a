import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as ImageUploadFieldStories from './ImageUploadField.stories';

<Meta of={ImageUploadFieldStories} />

<Title />

<Description />

<Primary />

<Controls of={ImageUploadFieldStories.Playground} />

## Import

```jsx
import { ImageUploadField } from '@cosmos-lab/components/image-upload-field';
```

## 🟢 When to use the component

- **Image documentation** - For uploading screenshots, certificates, or visual evidence that benefits from preview functionality
- **Single image uploads** - When only one image file is needed with integrated preview and validation
- **Form-based image input** - When image upload is part of a larger form with validation and submission requirements

## ❌ When not to use the component

- **Multiple image uploads** - Use FileUploadField with image formats when multiple images are needed
- **Non-image files** - Use FileUploadField for documents, PDFs, or other non-image file types
- **Temporary image handling** - Use simpler patterns when images don't need persistent storage
- **Read-only image display** - Use Image component for displaying existing images without upload functionality

## 🛠️ How it works

The ImageUploadField component provides image upload functionality with preview capabilities, drag-and-drop support, and form field integration specifically optimized for single image uploads.

**Core functionality:**
- **FormField wrapper** - Provides consistent labeling, help text, validation feedback, and form integration
- **Preview container** - Fixed-size preview area (16x dimension) with rounded corners and border styling
- **FileUpload integration** - Uses FileUpload component configured for single image files with hidden file list
- **Horizontal layout** - Side-by-side arrangement of preview and upload controls for optimal user experience

**Image handling:**
- **Single image only** - Configured with `isMulti={false}` for one image at a time
- **Accepted formats** - Supports common image formats (jpg, jpeg, png, gif, svg) defined in `ACCEPTED_FORMATS`
- **Size validation** - Default 25MB limit configurable via `maxFileSizeInBytes` prop
- **Preview generation** - Automatic preview using `URL.createObjectURL()` for immediate visual feedback
- **Memory management** - Proper cleanup of object URLs to prevent memory leaks

### Usability

- **Visual confirmation** - Immediate preview provides clear confirmation of selected image
- **Multiple input methods** - Users can drag-and-drop, click to browse, or use keyboard navigation
- **Clear expectations** - Button text and inner label communicate upload functionality
- **Error recovery** - Validation errors provide guidance for resolution
- **Replace functionality** - Easy to replace image by selecting a new one

### Content

- Use clear labels that explain the image purpose (e.g., "Profile Photo", "Company Logo")
- Button text should clearly indicate upload action ("Upload image", "Select photo")
- Inner label should provide helpful context ("Or drop image here")
- Use help text to explain image quality, dimension requirements, or business context
- Error messages should clearly explain format, size, or validation issues with recovery guidance

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper form field relationships and image preview labeling
- Full keyboard navigation support including Tab navigation and Space/Enter activation for upload controls
- Screen reader announcements for image selection, preview updates, and validation errors
- High contrast support that works with system accessibility preferences and meets WCAG guidelines
- Focus management with visible focus indicators and logical tab order through upload interface
- Touch target sizing that meets accessibility guidelines with proper spacing for mobile interaction

**Development responsibilities:**
- Provide descriptive labels that give clear context for what image is being collected and its purpose
- Use meaningful help text that explains image requirements, size limits, format restrictions, and business context
- Implement proper error handling with clear, actionable messages for validation scenarios
- Ensure preview images have appropriate alt text that describes their purpose in the form context
- Handle loading states appropriately when image processing or validation is occurring
- Coordinate with form validation systems to provide consistent error handling and user feedback

**Design responsibilities:**
- Provide sufficient color contrast for preview container, upload controls, and validation feedback across themes
- Design clear visual hierarchy that shows the relationship between preview, upload area, and form labels
- Ensure focus indicators are clearly visible and meet contrast requirements for all interactive elements
- Create consistent visual patterns for image upload fields across the application
- Design appropriate sizing for preview container and upload controls that work across different screen sizes
- Ensure drag-and-drop visual feedback and validation states provide clear visual cues that complement screen reader announcements
