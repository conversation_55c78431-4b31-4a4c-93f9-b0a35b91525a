import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as TextEditorCosmosLabStories from './TextEditorCosmosLab.stories';

<Meta of={TextEditorCosmosLabStories} />

<Title />

<Description />

<Primary />

<Controls of={TextEditorCosmosLabStories.Playground} />

## Import

```jsx
import { TextEditorCosmosLab } from '@cosmos-lab/components/text-editor-cosmos-lab';
```

## Props

### `isAutoSaveMode`

<Canvas of={TextEditorCosmosLabStories.Playground} />

You can optionally include a `<Controls />` block and limit which props are shown.

<Controls of={TextEditorCosmosLabStories.Playground} exclude={['prop']} />

## 🟢 When to use the component

- **Rich text editing** - When users need formatting capabilities (bold, italic, links, lists, tables) beyond plain text
- **Auto-save scenarios** - When content needs to be automatically saved during editing to prevent data loss
- **Template-based content** - For content creation using predefined templates and structured formats (pending premium license)

## ❌ When not to use the component

- **Simple text input** - Use TextField or TextareaField for basic text entry without formatting needs
- **Code editing** - Use specialized code editors for syntax highlighting and code-specific features
- **Single-line input** - Use TextField for short, single-line text entries
- **Read-only content display** - Use Text component for displaying formatted content without editing capabilities
- **Collaborative editing needs** - Comments and collaborative features are currently disabled pending premium license

## 🛠️ How it works

The TextEditorCosmosLab component provides rich text editing capabilities powered by CKEditor with auto-save functionality and flexible annotation systems.

**Core functionality:**
- **Rich text editing** - Full CKEditor integration with toolbar for formatting (bold, italic, headings, lists, tables, links)
- **Auto-save support** - Optional automatic saving with configurable debounce timing (defaults to 6000ms) and visual status feedback
- **Annotation system** - Flexible display with inline or sidebar modes (narrow/wide sidebar options)
- **Word and character counting** - Real-time tracking with `onWordCountUpdate` callback providing both character and word statistics

**Component architecture:**
- **MVC pattern** - TextEditorController handles business logic, TextEditorModel manages state, both integrated with MobX observer pattern
- **CKEditor integration** - Custom Editor class extending ClassicEditor with configured plugins and toolbar
- **Layout components** - StyledEditorContainerDiv with optional StyledSidebarDiv for annotations, TextEditorStatus for save state display
- **Plugin system** - Extensible architecture supporting autosave, word count, and future premium features

**Configuration and state:**
- **License management** - Currently uses GPL license; premium features (comments, templates) require license upgrade
- **Callback system** - Supports onChange, onSave, and onWordCountUpdate through controller methods (`setCallbacks`, `setAutoSaveConfig`)
- **Reactive updates** - MobX integration ensures UI updates when model state changes (content, busy state, character counts)

**Temporarily disabled features:**
- **Comments and collaborative editing** - Full implementation exists but completely disabled with console warning when props provided
- **Template support** - Template creation and insertion features await premium license activation

### Usability

**Editing experience:**
- **Familiar interface** - Standard rich text editor toolbar with common formatting options
- **Keyboard shortcuts** - Full keyboard support for formatting and navigation
- **Flexible input** - Supports both mouse and keyboard interactions for all editing functions

**Content management:**
- **Real-time feedback** - Immediate visual updates for formatting changes and content modifications
- **Status indicators** - Clear save state display (saving/saved) when auto-save is enabled
- **Flexible layout** - Choose annotation display style based on available screen space and workflow needs

### Content

**Editor setup:**
- **Initial content** - Use `data` prop to provide initial HTML content for the editor
- **Auto-save configuration** - Enable `isAutoSaveMode` with appropriate `autoSaveDebounceTime` for content preservation
- **Annotation style** - Choose between `inline`, `narrowSidebar`, or `wideSidebar` based on layout needs
- **Callback integration** - Implement `onWordCountUpdate` for content statistics and `onSave` for persistence handling

**Content guidelines:**
- **Structured content** - Encourage use of headings, lists, and proper document structure
- **Accessible formatting** - Use semantic formatting options rather than purely visual styling

### Accessibility

**What the design system provides:**
- CKEditor accessibility features including keyboard navigation, screen reader support, and ARIA attributes
- Semantic HTML output with proper heading hierarchy and list structures
- Focus management with logical tab order through toolbar and content areas
- High contrast support maintaining usability across different visual accessibility settings
- Status announcements for auto-save states and editor changes

**Development responsibilities:**
- Provide meaningful content through `data` prop with proper HTML structure
- Implement appropriate callback handlers for `onChange`, `onSave`, and `onWordCountUpdate`
- Configure auto-save settings based on content importance and user workflow needs
- Choose appropriate `annotationStyle` that works with the overall page layout

**Design responsibilities:**
- Ensure sufficient contrast ratios for editor content, toolbar elements, and status indicators
- Design annotation displays that integrate well with the overall page layout
- Maintain consistent spacing and alignment between editor, sidebar, and surrounding content
- Ensure responsive behavior that maintains usability across different screen sizes
- Design auto-save status indicators that provide clear feedback without being intrusive
