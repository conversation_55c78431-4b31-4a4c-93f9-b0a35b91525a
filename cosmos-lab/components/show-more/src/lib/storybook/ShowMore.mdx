import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as ShowMoreStories from './ShowMore.stories';

<Meta of={ShowMoreStories} />

<Title />

<Description />

<Primary />

<Controls of={ShowMoreStories.Playground} />

## Import

```jsx
import { ShowMore } from '@cosmos-lab/components/show-more';
```

## 🟢 When to use the component

- **Long content sections** - When text or content exceeds comfortable reading length and needs progressive disclosure
- **Space-constrained layouts** - When you need to preserve vertical space while keeping all content accessible
- **Content previews** - For showing truncated content with the option to expand for full details
- **Overflow management** - When content naturally exceeds the available display area

## ❌ When not to use the component

- **Critical information** - Don't hide essential information that users need to see immediately
- **Short content** - Avoid using for content that fits comfortably within the truncation height
- **Navigation or form elements** - Not appropriate for interactive elements that need constant access
- **Error messages or alerts** - Critical feedback should always be immediately visible

## 🛠️ How it works

The ShowMore component provides expandable content functionality with a gradient fade effect and accessible toggle controls for managing long content sections.

**Content management:**
- **Flexible content** - Accepts any ReactNode content with fixed height truncation
- **Internal state** - Manages expanded/collapsed state with optional `isOpen` prop
- **Gradient overlay** - Visual fade effect with positioned toggle button

**Interaction:**
- **Toggle functionality** - Single button switches between "Show more" and "Show less" states
- **Accessible controls** - Includes proper ARIA attributes for screen readers
- **Chevron indicators** - Icons indicate expand/collapse direction

### Usability

**Content considerations:**
- **Meaningful previews** - Ensure truncated content provides valuable context
- **Consistent behavior** - Use similar patterns across the application
- **Touch-friendly controls** - Adequate touch targets for mobile interaction

### Content

**Content guidelines:**
- **Complete information** - Expanded content should be self-contained and related to preview
- **Fixed height truncation** - Content is cut off at consistent height regardless of structure
- **Logical flow** - Maintain readable content even when truncated

**Technical considerations:**
- **ReactNode flexibility** - Component accepts any valid React content including text, elements, and components
- **Height constraints** - Content is truncated using `overflow-y: hidden` with design token-based max-height
- **Gradient positioning** - Visual fade effect uses absolute positioning and linear gradient background

### Accessibility

**What the design system provides:**
- Semantic button structure with proper ARIA attributes for screen reader compatibility
- Keyboard navigation support with standard button focus management
- Descriptive button labels that clearly indicate functionality

**Development responsibilities:**
- Ensure logical reading order for screen readers during state changes
- Maintain clear connections between truncated and full content
- Provide meaningful context within the fixed height constraint

**Design responsibilities:**
- Visual hierarchy with sufficient contrast for gradient overlays and button elements against various background colors
- Responsive behavior that maintains usability and accessibility across different screen sizes and orientations
- Focus indicators that provide clear visual feedback for keyboard navigation and button interaction

