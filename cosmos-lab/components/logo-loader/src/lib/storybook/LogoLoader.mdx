import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as LogoLoaderStories from './LogoLoader.stories';

<Meta of={LogoLoaderStories} />

<Title />

<Description />

<Primary />

<Controls of={LogoLoaderStories.Playground} />

## Import

```jsx
import { LogoLoader } from '@cosmos-lab/components/logo-loader';
```

## Props

### `fadeOut`

Whether the loader should fade out.

### `ariaLabel`

The label to use for the aria-label attribute.


## 🟢 When to use the component

- **Initial page load** - Use only when there is a full page loading experience, such as initial page load and logging in.

## 🔴 When not to use the component

- **Partial page loading** - Use the [Loader](https://cosmos.drata.com/?path=/story/components-loader--playground) component when there is a partial page loading experience, such as loading data
- If loading is triggered by an action, consider using the [IsLoading Button](https://cosmos.drata.com/?path=/docs/actions-button--docs#isloading)

## 🛠️ How it works

The LogoLoader component provides a branded full-page loading experience using the Drata logo with animated rotating circles and smooth fade transitions.

### Usability

**Core functionality:**
- **Branded animation** - Displays Drata logo with pulsing animation and rotating circle groups
- **Fade transitions** - Smooth opacity and scaling transitions when `fadeOut` is enabled
- **Full viewport coverage** - Takes up entire screen space (100vw/100vh) for immersive loading
- **Accessibility announcements** - Uses `aria-busy` and `aria-live` for screen reader communication

**Animation behavior:**
- **Logo pulse** - Central logo scales between 1.0 and 1.2 with 2s ease-in-out alternate animation
- **Rotating circles** - Four circle groups rotate at different speeds and directions:
  - Group 1: 2.1s clockwise rotation
  - Group 2: 2.6s clockwise rotation
  - Group 3: 4s counter-clockwise rotation
  - Group 4: 1.3s counter-clockwise rotation
- **Fade out effect** - When enabled, circles scale by different amounts (0.5x to 2x) while overall opacity fades to 0
- **Responsive scaling** - SVG maintains aspect ratio with max-width of 600px

**Visual design:**
- **Consistent branding** - Uses official Drata logo path and brand colors (#276FFF primary, various blue gradients for circles)
- **Smooth animations** - CSS transitions (0.5s ease-in-out) and linear rotations for professional appearance
- **Centered layout** - Flexbox centering ensures logo stays centered across screen sizes

### Content

- Use descriptive `ariaLabel` that indicates what is loading (e.g., "Loading application", "Signing in")
- Keep aria labels concise but informative for screen reader users
- Trigger `fadeOut` when loading is complete to provide smooth transition

### Accessibility

**What the design system provides:**
- Will inform the user that the page is loading via the `aria-label` and `aria-busy` attributes.
- Semantic loading state communication with proper ARIA attributes
- Scalable SVG graphics that work across zoom levels and screen sizes
- High contrast logo design with blue color scheme

**Development responsibilities:**
- Provide meaningful `ariaLabel` that describes the loading context
- Trigger `fadeOut` appropriately when loading completes
- Ensure LogoLoader is removed from DOM after fade-out completes
- Test with screen readers to verify loading announcements are clear

**Design responsibilities:**
- Maintain brand consistency with official Drata logo and colors
- Ensure animations are smooth and professional across different devices
- Test logo visibility across various screen sizes and orientations
- Consider loading duration - animations should feel appropriate for expected load times
- Verify sufficient contrast for logo elements against background


