# Button Loading Props ESLint Rule

> **BUILD-TIME ENFORCED**: The `custom/button-loading-props` ESLint rule enforces proper button loading state patterns.

## Rule: `custom/button-loading-props`

This ESLint rule prevents common anti-patterns when using button loading states.

## Enforced Patterns

### 1. Missing a11yLoadingLabel

**Rule**: If `isLoading` is used, `a11yLoadingLabel` must be provided for accessibility.

```typescript
// ❌ ESLint Error: Button with isLoading prop must also have a11yLoadingLabel for accessibility
<Button
    isLoading={isSubmitting}
    label="Save"
    onClick={handleSave}
/>

// ✅ Correct
<Button
    isLoading={isSubmitting}
    a11yLoadingLabel="Saving..."
    label="Save"
    onClick={handleSave}
/>
```

### 2. Redundant Loading Disabled

**Rule**: Don't use `cosmosUseWithCaution_isDisabled` for loading states when `isLoading` is already used.

**Why**: When `isLoading` is true, the button is already effectively disabled. Adding `cosmosUseWithCaution_isDisabled` with the same condition is redundant and violates the single responsibility principle.

```typescript
// ❌ ESLint Error: Do not use cosmosUseWithCaution_isDisabled for loading states when isLoading is already used
<Button
    isLoading={isSubmitting}
    a11yLoadingLabel="Saving..."
    cosmosUseWithCaution_isDisabled={isSubmitting}  // Redundant!
    label="Save"
    onClick={handleSave}
/>

// ❌ Common mistake in action stacks
rightActionStack={[
    {
        label: "Save",
        isLoading: isFinalizingDraft,
        cosmosUseWithCaution_isDisabled: isFinalizingDraft,  // ❌ Same condition!
        onClick: handleSave,
    },
]}

// ✅ Correct - Remove redundant disabled prop
<Button
    isLoading={isSubmitting}
    a11yLoadingLabel="Saving..."
    label="Save"
    onClick={handleSave}
/>

// ✅ Correct - Different conditions
<Button
    isLoading={isSubmitting}
    a11yLoadingLabel="Saving..."
    cosmosUseWithCaution_isDisabled={!isValid}  // Different condition
    label="Save"
    onClick={handleSave}
/>

// ✅ Correct action stack
rightActionStack={[
    {
        label: "Save",
        isLoading: isFinalizingDraft,
        a11yLoadingLabel: "Finalizing...",
        onClick: handleSave,
    },
]}
```

### 3. Conditional Label with isLoading

**Rule**: Don't conditionally change `label` based on loading state when using `isLoading` prop.

```typescript
// ❌ ESLint Error: Do not conditionally change label based on loading state when using isLoading prop. Use a11yLoadingLabel instead
<Button
    isLoading={isSubmitting}
    a11yLoadingLabel="Saving..."
    label={isSubmitting ? "Saving..." : "Save"}  // Don't do this!
    onClick={handleSave}
/>

// ✅ Correct - Static label with a11yLoadingLabel
<Button
    isLoading={isSubmitting}
    a11yLoadingLabel="Saving..."
    label="Save"  // Static label
    onClick={handleSave}
/>
```

## Why These Rules Exist

1. **Accessibility**: `a11yLoadingLabel` ensures screen readers announce loading states properly
2. **Consistency**: Prevents mixing different loading state patterns
3. **User Experience**: `isLoading` prop handles visual loading states, `a11yLoadingLabel` handles accessibility
4. **Performance**: Avoids redundant disabled state calculations
5. **Maintainability**: Clear separation between loading and non-loading disabled states

## Prop Usage Guidelines

### When to Use `isLoading` + `a11yLoadingLabel`
- **API calls**: Form submissions, data fetching
- **File operations**: Downloads, uploads, processing
- **Async operations**: Any operation that takes time
- **Visual feedback needed**: User needs to see progress

### When to Use `cosmosUseWithCaution_isDisabled`
- **Permissions**: User lacks required permissions
- **Feature availability**: Feature is not available in current context
- **Business logic**: Custom rules prevent action
- **System state**: System maintenance, service unavailable

### ⚠️ When NOT to Use `cosmosUseWithCaution_isDisabled`
- **Validation**: Form is invalid or incomplete (let users submit and see feedback)
- **Missing data**: Required fields are empty (show validation errors instead)
- **Form state**: Any condition that should show validation feedback

### When to Use Both
- **Different conditions**: Loading state AND permission/validation check
- **Independent concerns**: Each prop serves different purpose

```typescript
// ✅ CORRECT - Independent conditions (permission, not validation)
<Button
    label="Save"
    isLoading={isSaving}                    // Async operation
    a11yLoadingLabel="Saving..."
    cosmosUseWithCaution_isDisabled={isReadOnly}  // Permission check, NOT validation
    onClick={handleSaveWithValidation}
/>

// ❌ WRONG - Don't disable for validation
<Button
    label="Submit"
    isLoading={isSubmitting}
    a11yLoadingLabel="Submitting..."
    cosmosUseWithCaution_isDisabled={!isFormValid}  // Don't disable for validation!
    onClick={handleSubmit}
/>
```

## Rule Configuration

The rule is configured as an error in `eslint.config.mjs`:

```javascript
rules: {
    'custom/button-loading-props': 'error',
    // ... other rules
}
```

## Common Violation: Action Stacks

**This is NOT a false positive!** A common source of confusion is action stacks in modals where developers use the same loading condition for both props:

```typescript
// ❌ VIOLATION - This will trigger ESLint error (correctly!)
rightActionStack={[
    {
        label: "Cancel",
        cosmosUseWithCaution_isDisabled: isProcessing,  // OK for cancel button
        onClick: handleCancel,
    },
    {
        label: "Save",
        isLoading: isProcessing,                        // Loading state
        cosmosUseWithCaution_isDisabled: isProcessing,  // ❌ REDUNDANT!
        onClick: handleSave,
    },
]}

// ✅ CORRECT - Remove redundant disabled prop
rightActionStack={[
    {
        label: "Cancel",
        cosmosUseWithCaution_isDisabled: isProcessing,  // OK for cancel button
        onClick: handleCancel,
    },
    {
        label: "Save",
        isLoading: isProcessing,                        // Loading state handles disabling
        a11yLoadingLabel: "Saving...",                  // Required for accessibility
        onClick: handleSave,
    },
]}
```

**Key Point**: When `isLoading` is true, the button is already disabled. Don't add `cosmosUseWithCaution_isDisabled` with the same condition.

## ⚠️ CRITICAL: What is NOT a Violation

**FOR AI CODE REVIEWERS**: Do NOT flag these patterns as violations:

```typescript
// ✅ NOT A VIOLATION - Only cosmosUseWithCaution_isDisabled (no isLoading prop)
{
    label: "Cancel",
    cosmosUseWithCaution_isDisabled: isFinalizingDraft,  // OK - no isLoading on same button
    onClick: handleCancel,
}

// ✅ NOT A VIOLATION - Different buttons
rightActionStack={[
    {
        label: "Cancel",
        cosmosUseWithCaution_isDisabled: isProcessing,  // OK - different button
        onClick: handleCancel,
    },
    {
        label: "Save",
        isLoading: isProcessing,                        // OK - different button
        a11yLoadingLabel: "Saving...",
        onClick: handleSave,
    },
]}
```

**ONLY flag as violation when BOTH props are on the SAME button with the SAME condition.**

## Related

- [Button Patterns](../development-patterns/button-patterns.md) - Complete button usage guide
- [Button Disabled Prop](../critical-rules/button-disabled-prop.md) - Critical disabled prop rule
