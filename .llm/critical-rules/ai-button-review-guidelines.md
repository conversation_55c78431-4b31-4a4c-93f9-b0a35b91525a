# AI Code Review Guidelines - <PERSON><PERSON> Props

> **FOR AI CODE REVIEWERS**: This document clarifies when button prop usage is a violation vs. correct usage.

## 🚨 CRITICAL: False Positive Prevention

**ONLY flag as violation when BOTH `isLoading` AND `cosmosUseWithCaution_isDisabled` are present on the SAME button/object.**

**If there is NO `isLoading` property → This is NOT a violation, regardless of variable names.**

## ❌ ACTUAL VIOLATIONS (Flag These)

### 1. Same Button with Both Props and Same Condition

```typescript
// ❌ VIOLATION - Same button, same condition
<Button
    label="Save"
    isLoading={isSubmitting}
    cosmosUseWithCaution_isDisabled={isSubmitting}  // Same condition!
    onClick={handleSave}
/>

// ❌ VIOLATION - Action stack, same button, same condition
{
    label: "Save",
    isLoading: isFinalizingDraft,
    cosmosUseWithCaution_isDisabled: isFinalizingDraft,  // Same condition!
    onClick: handleSave,
}

// ❌ VIOLATION - Incomplete property (missing value)
{
    label: "Save",
    isLoading: isProcessing,
    a11yLoadingLabel: "Saving...",
    cosmosUseWithCaution_isDisabled:  // INCOMPLETE - should be removed entirely
    onClick: handleSave,
}
```

**REQUIRED FIX**: Remove the `cosmosUseWithCaution_isDisabled` property entirely. Do NOT suggest adding `false` or any other value.

**CORRECT RESOLUTION EXAMPLES**:

```typescript
// ✅ CORRECT FIX - Remove redundant disabled prop
{
    label: "Save",
    isLoading: isProcessing,
    a11yLoadingLabel: "Saving...",
    // cosmosUseWithCaution_isDisabled: isProcessing,  // REMOVED - redundant
    onClick: handleSave,
}

// ✅ CORRECT FIX - Keep only isLoading for loading states
<Button
    label="Save"
    isLoading={isSubmitting}
    a11yLoadingLabel="Saving..."
    // cosmosUseWithCaution_isDisabled={isSubmitting}  // REMOVED - redundant
    onClick={handleSave}
/>
```

### 2. Missing a11yLoadingLabel

```typescript
// ❌ VIOLATION - isLoading without a11yLoadingLabel
<Button
    label="Save"
    isLoading={isSubmitting}  // Missing a11yLoadingLabel
    onClick={handleSave}
/>
```

## ✅ CORRECT USAGE (Do NOT Flag These)

### 1. Only cosmosUseWithCaution_isDisabled (No isLoading)

```typescript
// ✅ CORRECT - Only disabled prop, no isLoading
<Button
    label="Cancel"
    cosmosUseWithCaution_isDisabled={isFinalizingDraft}  // OK - no isLoading
    onClick={handleCancel}
/>

// ✅ CORRECT - Action stack with only disabled
{
    label: "Cancel",
    cosmosUseWithCaution_isDisabled: isProcessing,  // OK - no isLoading
    onClick: handleCancel,
}
```

### 2. Different Buttons in Action Stack

```typescript
// ✅ CORRECT - Different buttons, different purposes
rightActionStack={[
    {
        label: "Cancel",
        cosmosUseWithCaution_isDisabled: isProcessing,  // OK - cancel button
        onClick: handleCancel,
    },
    {
        label: "Save",
        isLoading: isProcessing,                        // OK - save button
        a11yLoadingLabel: "Saving...",
        onClick: handleSave,
    },
]}
```

### 3. Same Button, Different Conditions

```typescript
// ✅ CORRECT - Different conditions (permission vs loading)
<Button
    label="Save"
    isLoading={isSaving}                            // Loading state
    a11yLoadingLabel="Saving..."
    cosmosUseWithCaution_isDisabled={isReadOnly}    // Permission check
    onClick={handleSave}
/>
```

## 🔍 How to Identify Violations

1. **Check if BOTH props are on the SAME button/action object**
2. **Check if they use the SAME condition/variable**
3. **Only flag if BOTH conditions are true**

## 🚫 Common False Positives to Avoid

### ❌ DO NOT FLAG: `cosmosUseWithCaution_isDisabled` without `isLoading`

**CRITICAL**: If there is NO `isLoading` property on the same button/object, this is NOT a violation:

```typescript
// ✅ NOT A VIOLATION - No isLoading property present
{
    label: "Cancel",
    cosmosUseWithCaution_isDisabled: isProcessing,  // OK - no isLoading on this object
    onClick: handleCancel,
}

// ✅ NOT A VIOLATION - Even with loading-related variable names
{
    label: "Cancel",
    cosmosUseWithCaution_isDisabled: isFinalizingDraft,  // OK - no isLoading on this object
    onClick: handleCancel,
}

// ✅ NOT A VIOLATION - Property with value on next line (common formatting)
{
    label: "Cancel",
    cosmosUseWithCaution_isDisabled:
        sharedController.isProcessing,  // OK - no isLoading on this object
    onClick: handleCancel,
}

// ✅ NOT A VIOLATION - Multi-line property value
{
    label: "Cancel",
    cosmosUseWithCaution_isDisabled:
        sharedPolicyHeaderNeedsApprovalActions
            .overrideApprovalMutation.isPending,  // OK - no isLoading on this object
    onClick: handleCancel,
}
```

### Other False Positives:
- Different buttons in the same action stack using related loading states
- Cancel buttons being disabled during loading operations
- **Multi-line property formatting** - Properties split across lines for readability are still valid properties

## ✅ Review Checklist

**STEP 1: Check for BOTH properties**
- [ ] Is there BOTH `isLoading` AND `cosmosUseWithCaution_isDisabled` on the SAME button/object?
- [ ] **If NO `isLoading` property exists → STOP → This is NOT a violation**

**STEP 2: If both properties exist, check conditions**
- [ ] Do they use the SAME condition/variable?
- [ ] If `isLoading` is present, is `a11yLoadingLabel` also present?

**Only flag as violation if ALL conditions are met.**

### 🚨 CRITICAL: Must Have BOTH Properties

```typescript
// ❌ VIOLATION - Has BOTH isLoading AND cosmosUseWithCaution_isDisabled with same condition
{
    label: "Save",
    isLoading: isProcessing,                        // ← HAS isLoading
    cosmosUseWithCaution_isDisabled: isProcessing,  // ← HAS disabled with SAME condition
    onClick: handleSave,
}

// ✅ NOT A VIOLATION - Only has cosmosUseWithCaution_isDisabled
{
    label: "Cancel",
    cosmosUseWithCaution_isDisabled: isProcessing,  // ← NO isLoading property = NOT A VIOLATION
    onClick: handleCancel,
}
```
