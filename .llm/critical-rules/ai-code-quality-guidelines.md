# AI Agent Code Quality Guidelines

## §1.0 Core Operating Principles

### §1.1 Decision Confidence Rule
**Be confident in your technical decisions.** When you make a recommendation based on best practices, maintenance concerns, or code quality, stand by it. Only reconsider if presented with factual errors or overlooked context. Flip-flopping undermines trust and creates confusion.

**Example**: If you identify fragmented documentation as a maintenance problem, that insight is valid. Don't backtrack just because someone questions it - explain why the principle matters.

### §1.2 Pattern Propagation Rule
**Any code pattern you introduce will be copied throughout the codebase.** Design every solution assuming it will be replicated 50+ times by other developers who may not understand the original context.

### §1.3 Investigative Code Review Principle
**Every change tells a story - understand that story completely.** Approach code reviews with the paranoid mindset of a detective. No change is too small to question. If you don't understand why a change was made, why this specific approach was chosen, and what could go wrong, don't approve it.

**Reference**: See [ai-code-review-investigation-methodology.md](./ai-code-review-investigation-methodology.md) and [ai-subtle-change-detection.md](./ai-subtle-change-detection.md) for detailed investigation techniques.

## §2.0 Quality Framework

### §2.1 Quality Gates
- [ ] **Correctness**: Does it solve the problem completely?
- [ ] **Consistency**: Does it follow established codebase patterns?
- [ ] **Maintainability**: Can it be understood and modified easily?
- [ ] **Testability**: Can it be thoroughly tested?
- [ ] **Performance**: Is it efficient for current and projected usage?
- [ ] **Copy-Safety**: Will it work correctly when copied without full context?

### §2.2 Pre-Implementation Checklist

#### Research Phase
- [ ] Identify existing patterns for similar problems in the codebase
- [ ] Locate and study established conventions (check `.llm/PATTERN_INDEX.md` first)
- [ ] Find 2-3 examples of how similar problems are currently solved
- [ ] Verify the approach aligns with architectural decisions
- [ ] Check for existing utilities or abstractions that should be used

#### Design Validation
- [ ] Solution follows the principle of least surprise
- [ ] Implementation can be understood without extensive comments
- [ ] Error cases are explicitly handled
- [ ] Dependencies are minimal and justified
- [ ] Pattern is reusable and extensible

## §3.0 Code Quality Enforcement

### §3.1 Critical Red Flags - Auto-Reject

#### §3.1.1 Immediate Rejection Criteria
- [ ] **ESLint disable comments** (`eslint-disable`) - NEVER acceptable, fix the underlying issue
- [ ] **TypeScript safety bypasses** (`@ts-ignore`, `as any`) - Use proper type guards instead
- [ ] Code that works "by accident" or through unclear mechanisms
- [ ] Copy-pasted code with minor modifications
- [ ] Temporary solutions without clear removal timeline
- [ ] Complex solutions to simple problems
- [ ] Code requiring extensive comments to explain basic functionality
- [ ] Patterns that deviate from conventions without documented justification
- [ ] Missing error handling for obvious failure cases
- [ ] Hardcoded values that should be configurable

#### §3.1.2 React-Specific Anti-patterns (Auto-Reject)
- [ ] **Side effects during render** - Mutating external state in hook/component body
- [ ] **Duplicate function implementations** - Same logic in multiple places
- [ ] **Hardcoded style values** - Magic numbers instead of design tokens
- [ ] **Inconsistent error handling** - Different patterns for similar operations
- [ ] **Functions attached to external objects** - Without proper useEffect cleanup
- [ ] **Type assertions for external APIs** - `as unknown as` indicates architectural problems
- [ ] Direct DOM manipulation (`document.getElementById`, `element.setAttribute`, `element.focus`)
- [ ] Manual event creation (`new Event()`, `element.dispatchEvent()`)
- [ ] Imperative DOM updates instead of declarative React patterns
- [ ] Complex hooks with multiple unrelated responsibilities
- [ ] Type assertions without proper type guards (`as`, `!`)
- [ ] Render functions or component variables inside components

#### §3.1.3 Copy-Paste Vulnerability Indicators
- [ ] Code that's "convenient" to copy but fragile
- [ ] Inconsistent implementations of the same concept across files
- [ ] Solutions that work only in specific contexts but appear general
- [ ] Code with hidden dependencies or assumptions
- [ ] Patterns that fail silently when misused

### §3.2 Type Safety Requirements
- [ ] No `any` types (exceptions require explicit justification)
- [ ] **No type assertions** (`as`, `!`) - See `.llm/critical-rules/no-type-assertions.md` for detailed guidance
- [ ] All function parameters and return types are explicitly typed
- [ ] Null/undefined cases are handled with proper type guards
- [ ] Generic types are constrained appropriately

### §3.3 Maintainability Standards
- [ ] Functions have single, clear responsibilities
- [ ] Variable and function names are descriptive and unambiguous
- [ ] Complex logic is broken into small, focused functions
- [ ] Magic numbers/strings are replaced with named constants
- [ ] Dependencies are injected rather than hardcoded

### §3.4 Consistency Enforcement
- [ ] Import/export patterns match codebase standards
- [ ] File naming follows established conventions
- [ ] Error handling patterns are consistent with existing code
- [ ] Logging and monitoring follow established patterns
- [ ] Code organization matches project structure

## §4.0 Decision Trees

### §4.1 When to Create New Abstractions
```
Is there existing code that solves this problem?
├─ YES: Use existing solution, extend if necessary
└─ NO: Is this problem likely to occur again?
   ├─ YES: Create reusable abstraction
   └─ NO: Implement simple, direct solution
```

### §4.2 When to Deviate from Patterns
```
Does existing pattern solve the problem adequately?
├─ YES: Use existing pattern
└─ NO: Is the limitation fundamental or contextual?
   ├─ FUNDAMENTAL: Propose pattern evolution with team
   └─ CONTEXTUAL: Document deviation and create isolated solution
```

### §4.3 Error Handling Strategy
```
Can this operation fail?
├─ YES: What are the failure modes?
│   ├─ EXPECTED: Handle gracefully with user feedback
│   ├─ UNEXPECTED: Log error, provide fallback
│   └─ CRITICAL: Fail fast with clear error message
└─ NO: Proceed with implementation
```

## §5.0 Implementation Standards

### §5.1 Function Design
- [ ] Maximum 20 lines per function (exceptions require justification)
- [ ] Single return type (avoid union types when possible)
- [ ] Pure functions when possible (no side effects)
- [ ] Explicit error handling (no silent failures)
- [ ] Clear parameter validation

### §5.2 File Organization
- [ ] One primary export per file
- [ ] Related utilities in same file or dedicated utils file
- [ ] Clear separation of concerns
- [ ] Consistent import ordering
- [ ] Minimal file dependencies

## §6.0 Testing Requirements

### §6.1 Test Coverage
- [ ] Unit tests for all public functions
- [ ] Edge case coverage (null, undefined, empty, boundary values)
- [ ] Error condition testing
- [ ] Integration tests for complex interactions
- [ ] Performance tests for critical paths

### §6.2 Quality Metrics
- [ ] Cyclomatic complexity < 10 per function
- [ ] Test coverage > 80% for new code
- [ ] No linting violations
- [ ] No type errors
- [ ] Performance within acceptable bounds

## §7.0 Communication Standards

### §7.1 Code Review Feedback Format
```
**Issue**: [Specific problem]
**Impact**: [Why this matters for maintainability/correctness]
**Solution**: [Concrete suggestion]
**Example**: [Reference to existing good pattern if available]
```

### §7.2 Pattern Deviation Documentation
```
**Deviation**: [What pattern is being changed]
**Justification**: [Why existing pattern is insufficient]
**Scope**: [Where this deviation applies]
**Future**: [Plan for pattern evolution or isolation]
```

### §7.3 Critical Violation Format
```
CRITICAL VIOLATION: [Specific violation]
GUIDELINE REFERENCE: [Reference to specific section, e.g., §3.1.2]
REQUIRED FIX: [Concrete solution]
PATTERN REFERENCE: [Link to existing good example if available]
```

## §8.0 Automated Quality Checks

### §8.1 Automated Verification
- [ ] **Pattern Consistency**: Compare with similar existing code
- [ ] **Type Safety**: Verify no `any` types or unsafe assertions
- [ ] **Error Handling**: Ensure all failure paths are addressed
- [ ] **Performance**: Check for obvious inefficiencies
- [ ] **Documentation**: Verify complex logic is explained
- [ ] **Testing**: Confirm adequate test coverage
- [ ] **DOM Manipulation**: Check for direct DOM access or manipulation
- [ ] **React Patterns**: Verify declarative patterns over imperative code
- [ ] **Hook Complexity**: Ensure hooks have single, focused responsibilities

## §9.0 Enforcement Actions

### §9.1 When Code Doesn't Meet Standards
- [ ] **Minor Issues**: Provide specific feedback with examples
- [ ] **Pattern Violations**: Reference existing correct patterns
- [ ] **Safety Issues**: Require fixes before approval
- [ ] **Architecture Violations**: Escalate for design review

### §9.2 When Patterns Need Evolution
- [ ] Document current pattern limitations
- [ ] Propose specific improvements
- [ ] Create migration plan for existing code
- [ ] Update documentation and examples

## §10.0 Success Criteria

### §10.1 Code Quality Indicators
- [ ] New engineers can understand and modify code quickly
- [ ] Similar problems are solved consistently across the codebase
- [ ] Bugs are caught by automated systems before production
- [ ] Performance remains stable as features are added
- [ ] Technical debt decreases over time

### §10.2 Pattern Health Metrics
- [ ] Consistency score across similar implementations
- [ ] Time to understand and modify existing code
- [ ] Frequency of bugs in copied patterns
- [ ] Developer satisfaction with existing abstractions

## §11.0 Core Principles Summary

Your goal is to ensure every piece of code you approve or create will:
- [ ] **Work correctly** when copied to other contexts
- [ ] **Maintain consistency** with established patterns
- [ ] **Be easily understood** by future developers
- [ ] **Scale appropriately** with system growth
- [ ] **Fail obviously** when misused rather than silently

Focus on creating code that survives the reality of software development: it will be copied, modified, and maintained by people who weren't involved in the original design decisions.

---

**Remember**: Any code pattern you introduce will be copied throughout the codebase. Design every solution assuming it will be replicated 50+ times by other developers.
