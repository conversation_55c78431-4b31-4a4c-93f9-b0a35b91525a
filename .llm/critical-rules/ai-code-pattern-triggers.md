# AI Code Review Pattern Recognition Guide

## 🎯 Purpose

Train AI agents to recognize **conceptual code patterns** in diffs that should trigger loading specific investigation methodologies. Focus on understanding the nature of changes rather than matching exact syntax.

## 🔍 Pattern Recognition Framework

### **React Component and Hook Patterns** → Load `ai-react-antipatterns.md`

**Trigger when you detect:**
- React hook usage (useState, useEffect, useMemo, useCallback, etc.)
- JSX syntax and component rendering
- Component function definitions and exports
- Custom hook definitions and implementations
- React-specific patterns like props, state, and lifecycle management
- Component composition and rendering logic

### **Type Assertion Patterns** → Load `no-type-assertions.md`

**Trigger when you detect:**
- Type assertions using the 'as' keyword
- Double type assertions (as unknown as)
- Non-null assertion operator (!) usage
- Type casting that bypasses TypeScript's type checking
- Any pattern that forces TypeScript to accept a different type
- Unsafe type conversions without proper validation

### **Interface and Type Constraint Changes** → Load `ai-subtle-change-detection.md`

**Trigger when you detect:**
- Required properties becoming optional (adding ? to interface properties)
- Type constraints being widened (strict types becoming unions)
- Union types gaining additional members (especially null/undefined)
- Generic type constraints being relaxed
- Type definitions becoming more permissive
- Interface properties changing from specific to general types

### **Function Contract Modifications** → Load `ai-subtle-change-detection.md`

**Trigger when you detect:**
- Synchronous functions becoming asynchronous
- Function parameter lists being modified (additions, removals, type changes)
- Return type changes (especially adding Promise wrappers)
- Error handling patterns changing within function signatures
- Function visibility or access modifiers changing
- Breaking changes to established function contracts

### **Configuration and Default Value Drift** → Load `ai-subtle-change-detection.md`

**Trigger when you detect:**
- Timeout values being increased significantly
- Retry limits or attempt counts being raised
- Default values changing to different states or behaviors
- Buffer sizes or memory allocations growing
- Rate limits being relaxed or increased
- Configuration constants becoming more permissive
- Threshold values being adjusted upward

### **Error Handling Strategy Changes** → Load `ai-subtle-change-detection.md`

**Trigger when you detect:**
- Try-catch blocks being removed or simplified
- Error logging being removed from error handling
- Functions changing from throwing errors to returning null/undefined
- Error types changing (specific errors becoming generic)
- Silent failure patterns being introduced
- Error handling becoming less comprehensive
- Changes in how errors are propagated or reported

### **Performance and Efficiency Degradation** → Load `ai-subtle-change-detection.md`

**Trigger when you detect:**
- Caching mechanisms being removed or disabled
- Efficient data structures being replaced with less efficient ones
- O(1) operations becoming O(n) operations
- Memoization being removed from expensive computations
- Batch operations being converted to individual operations
- Optimized algorithms being replaced with simpler but slower ones
- Resource usage patterns becoming less efficient

### **Side Effect and Mutation Patterns** → Load `ai-react-antipatterns.md`

**Trigger when you detect:**
- Mutations happening in hook or component body (outside useEffect)
- External object properties being modified during render
- Global state updates happening during component execution
- Direct DOM manipulation outside of useEffect
- Functions being attached to external objects during render
- Any side effects occurring outside of proper React lifecycle hooks
- Imperative operations happening in declarative contexts

### **Hardcoded Values and Magic Numbers** → Load `ai-react-antipatterns.md`

**Trigger when you detect:**
- Magic numbers in CSS properties (z-index, spacing, sizing)
- Hardcoded color values instead of design tokens
- Arbitrary measurements that bypass design system
- Inline style values that don't reference established patterns
- Magic numbers in business logic or configuration
- Hardcoded strings that should be constants or configuration
- Values that appear to be arbitrary rather than systematic

### **Code Duplication and Redundancy** → Load `ai-react-antipatterns.md`

**Trigger when you detect:**
- Identical or nearly identical function implementations
- Same logic appearing in multiple places within a component/module
- Functions being both attached to external objects and returned separately
- Copy-pasted code blocks with minimal variations
- Redundant implementations that serve the same purpose
- Multiple functions that perform identical operations

## 🚨 Complex Pattern Recognition

### **Multiple Risk Indicators** → Load `ai-code-review-investigation-methodology.md`

**Trigger when you detect combinations of:**
- Type assertions combined with side effects
- Function signature changes alongside error handling modifications
- Default value changes paired with performance pattern changes
- Multiple interface changes within the same change set
- Hardcoded values appearing with type assertions
- Any combination of 3+ individual risk patterns

### **Inconsistency and Mixed Patterns** → Load `ai-code-review-investigation-methodology.md`

**Trigger when you detect:**
- Different error handling strategies for similar functions within the same module
- Mixed synchronous and asynchronous patterns without clear reasoning
- Inconsistent return types for similar operations
- Varying approaches to the same problem within a single change
- Architectural patterns that don't align with established conventions
- Multiple ways of accomplishing the same goal in related code

## 🎯 Pattern Recognition Methodology

### **Recognition Process:**
1. **Analyze the nature of changes** rather than matching exact syntax
2. **Identify conceptual patterns** that indicate potential risks
3. **Load relevant investigation methodologies** based on pattern categories
4. **Apply principled investigation** using the loaded frameworks

### **Focus On Conceptual Patterns:**
- **Structural changes** that affect system behavior
- **Constraint relaxation** that makes systems more permissive
- **Architectural deviations** from established patterns
- **Risk-introducing modifications** that could cause issues
- **Consistency violations** within modules or systems

### **Avoid Literal Pattern Matching:**
- Don't rely on exact syntax matching
- Don't trigger on comments or documentation mentions
- Don't look for specific variable names or abstract concepts
- Focus on the nature and impact of changes rather than specific code

### **Multi-Pattern Recognition:**
When multiple pattern categories are detected:
- **React patterns + Type assertions** → Load both React antipatterns AND type assertion methodologies
- **Interface changes + Function modifications** → Load subtle change detection methodology
- **Multiple risk indicators** → Also load comprehensive investigation methodology

### **Core Principle:**
Recognize **conceptual risk patterns** that indicate potential architectural, performance, or maintainability issues, rather than trying to enumerate all possible code variations.
