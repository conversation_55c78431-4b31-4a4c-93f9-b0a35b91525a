# AI Subtle Change Detection Patterns

## 🎯 Purpose

Train AI code review agents to detect subtle but significant changes that often hide major issues. These changes look innocuous but can have cascading effects throughout the codebase.

## 🧠 Detection Mindset

**Core Principle**: The most dangerous bugs hide in changes that look harmless.

Develop pattern recognition for changes that:
- Seem too small to matter (but often matter most)
- Break established patterns without obvious justification
- Relax constraints without clear business need
- Add complexity to previously simple code
- Change behavior in subtle ways

## 🔍 High-Risk Change Patterns

### Type System Erosion & Type Safety Violations
**Detection Patterns:**
- Required props becoming optional (adding `?:` to interface properties)
- Type constraints being widened (specific types becoming unions with null/undefined)
- Type assertions appearing (`as`, `as unknown as`)
- Type guards being removed from validation logic
- Strict types becoming loose unions

**Investigation Questions:**
- What changed in the business logic that makes this optional?
- Are all call sites handling the undefined case?
- Is this a temporary workaround becoming permanent?
- **For type assertions**: What's the actual type? Why can't we use proper typing?

**Red Flags:**
- Required → Optional without clear business justification
- Strict types → Union types with null/undefined
- Specific types → Generic any/unknown
- Type guards being removed
- Validation being weakened
- **Type assertions (`as`, `as unknown as`)** - Usually indicates architectural problems

**Guidance for Fixes:**
- Investigate the root cause requiring the type change
- Ensure proper type guards are in place for optional types
- Consider if the API design needs to change instead of the types
- Look for proper typing solutions instead of assertions

### Function Contract Violations
**Detection Patterns:**
- Synchronous functions becoming asynchronous
- Return types changing (especially adding Promise wrappers)
- Parameter lists being modified (additions, removals, type changes)
- Error handling patterns changing within function signatures

**Investigation Questions:**
- What operation now requires async behavior?
- Have all callers been updated to handle Promises?
- Is this adding unnecessary complexity?

**Red Flags:**
- Sync → Async without clear I/O need
- Return type changes without caller updates
- Parameter additions without migration strategy
- Error handling patterns changing

**Guidance for Fixes:**
- Ensure all callers are updated when function contracts change
- Consider if the change indicates a design problem
- Look for proper migration strategies for breaking changes
- Verify that async is actually needed for the operation

### Default Value Drift
**Detection Patterns:**
- Timeout values being increased significantly
- Buffer sizes growing without clear justification
- Rate limits being relaxed
- Validation thresholds becoming more permissive
- Configuration defaults changing to be less restrictive

**Investigation Questions:**
- What operations are timing out that weren't before?
- Is this masking a performance problem?
- What's the impact on user experience?

**Red Flags:**
- Timeouts being increased
- Retry counts being bumped up
- Buffer sizes growing
- Rate limits being relaxed
- Validation thresholds changing

**Guidance for Fixes:**
- Investigate the root cause of why defaults need to change
- Consider if this indicates a performance or design issue
- Ensure the new defaults are appropriate for all use cases
- Document the reasoning for default value changes

### Error Handling Degradation
**Detection Patterns:**
- Try-catch blocks being removed or simplified
- Error logging being removed
- Errors being silenced (returning null/undefined instead of throwing)
- Error types changing without clear justification
- Early returns that skip error handling

**Investigation Questions:**
- Why is this error no longer worth reporting?
- What downstream code expects this to throw?
- Is this hiding a real problem?

**Red Flags:**
- Try-catch blocks being removed
- Errors being silenced
- Error types changing
- Logging being removed
- Early returns skipping error handling

**Guidance for Fixes:**
- Ensure errors are properly logged and reported
- Consider if silent failures are appropriate for the use case
- Verify that downstream code can handle the new error behavior
- Look for proper error recovery strategies instead of silencing

### Performance Characteristic Changes
**Detection Patterns:**
- Efficient data structures being replaced with less efficient ones
- O(1) operations becoming O(n) operations
- Indexed access being replaced with linear searches
- Caching being disabled or removed
- Batch operations being split into individual calls

**Investigation Questions:**
- Why was the efficient data structure abandoned?
- What changed that requires this less efficient approach?
- What's the performance impact at scale?

**Red Flags:**
- Efficient algorithms → Inefficient ones
- Indexed lookups → Linear searches
- Memoization being removed
- Caching being disabled
- Batch operations becoming individual calls

### Security Boundary Changes
**Detection Patterns:**
- Permission checks being removed from validation logic
- Authentication requirements being relaxed
- Input validation being weakened or bypassed
- Access control logic being simplified
- CORS policies becoming more permissive

**Investigation Questions:**
- Why is this permission check no longer needed?
- What changed in the security model?
- Are there other paths that need similar updates?

**Red Flags:**
- Permission checks being removed
- Input validation being weakened
- Authentication bypasses
- Data exposure increasing
- CORS policies being relaxed

### State Management Mutations
**Detection Patterns:**
- Immutable update patterns being replaced with direct mutations
- State shape changes without migration strategies
- Reducer logic being modified to include side effects
- Update timing changes that could affect rendering
- Pure functions gaining side effects

**Investigation Questions:**
- Why was immutability abandoned?
- What problems might this cause with React rendering?
- Are there other components that depend on immutable updates?

**Red Flags:**
- Immutable → Mutable patterns
- State shape changes
- Update timing changes
- Reducer logic modifications
- Side effects in pure functions

## 🚨 Investigation Triggers

### Constraint Relaxation Indicators
Watch for any change that makes the system more permissive:
- Validation rules becoming looser
- Type constraints being widened
- Rate limits being increased
- Timeouts being extended
- Buffer sizes growing
- Permission requirements being reduced

### Pattern Deviation Indicators
Look for changes that break established conventions:
- Different error handling in similar functions
- Inconsistent naming patterns
- Mixed architectural approaches
- New ways of doing existing things
- Bypassing established abstractions

### Complexity Introduction Indicators
Notice when simple things become complicated:
- Special cases being added to general functions
- Configuration options for hardcoded values
- Multiple code paths for same outcome
- Wrapper functions around existing functionality
- Conditional logic based on magic values

## 🔬 Investigation Methodology

### Context Analysis
For every suspicious change:

1. **Historical Context**
   - What other changes have been made in this area?
   - Are there patterns of instability?
   - Do commit messages suggest uncertainty?

2. **Architectural Context**
   - How does this fit with system design?
   - Does this respect existing boundaries?
   - Are there ripple effects to other systems?

3. **Business Context**
   - What requirement is driving this change?
   - Is this a temporary workaround?
   - What's the urgency behind this?

### Risk Assessment Framework
Evaluate each change for:

1. **Immediate Risks**
   - What could break right now?
   - How would we detect problems?
   - How easy is rollback?

2. **Maintenance Risks**
   - Will future developers understand this?
   - How will this age as the system evolves?
   - What happens when someone copies this pattern?

3. **Business Risks**
   - What's the impact if this fails?
   - Are there compliance implications?
   - What's the cost of getting this wrong?

## 🎯 Investigation Process Framework

When detecting high-risk patterns, AI agents should follow this process:

1. **Pattern Recognition**: Identify which high-risk pattern the change matches
2. **Context Analysis**: Examine surrounding code and related changes
3. **Investigation Questions**: Ask specific probing questions about motivation
4. **Risk Assessment**: Evaluate potential impact and failure modes
5. **Approval Decision**: Determine if change needs justification or rejection

**Key Principles:**
- Focus on understanding WHY the change is needed
- Ask about alternatives that were considered
- Investigate ripple effects and downstream impacts
- Require clear business justification for constraint relaxation
- Consider long-term maintenance implications

## 🎓 Training Outcomes

After training on these patterns, AI agents should:

1. **Automatically flag** changes that match high-risk patterns
2. **Ask probing questions** about motivation and alternatives
3. **Assess ripple effects** of seemingly small changes
4. **Require justification** for constraint relaxation
5. **Understand business context** behind technical changes
6. **Evaluate long-term impact** of pattern deviations

The goal is developing an AI agent with the paranoid instincts of an experienced staff engineer who knows that the devil is in the details.
