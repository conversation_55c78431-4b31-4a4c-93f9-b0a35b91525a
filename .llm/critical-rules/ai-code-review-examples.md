# AI Code Review Investigation Methodology

## 🎯 Purpose

Train AI agents to investigate critical violations through systematic questioning and risk assessment. Focus on understanding WHY problematic patterns exist and HOW to guide developers toward better solutions.

## 🚨 Investigation Scenario 1: Side Effects During Render (CRITICAL)

### **Pattern Recognition - Look For:**
- Functions being attached to external objects in hook/component body
- Direct mutations of refs or external state during render
- Any side effects happening outside of useEffect/useLayoutEffect
- Object property assignments in the main execution flow

### **Critical Risk Indicators:**
- **CRITICAL**: Mutating external state during hook execution
- **CRITICAL**: Will cause issues in StrictMode and concurrent rendering
- **CRITICAL**: Potential memory leaks when component unmounts
- **CRITICAL**: Race conditions in concurrent rendering scenarios

### **Investigation Questions to Ask:**
- Why is this function being attached during render instead of in useEffect?
- What happens when this hook unmounts? Does the function become stale?
- How will this behave in <PERSON><PERSON>'s StrictMode (which calls hooks twice)?
- Is this creating memory leaks or race conditions?
- What external systems depend on this mutation?
- Are there cleanup requirements that aren't being handled?

### **Guidance Principles:**
- Move all side effects into useEffect or useLayoutEffect hooks
- Ensure proper cleanup functions are provided for external mutations
- Use useCallback for functions that need to be attached to external objects
- Consider if the external mutation is actually necessary
- Verify that the timing of the side effect is appropriate for the use case

## 🚨 Investigation Scenario 2: Type Assertion Anti-Patterns

### **Pattern Recognition - Look For:**
- Type assertions using `as` or `as unknown as`
- Double type assertions that bypass TypeScript's safety
- Vague or generic type assertions like `Record<string, unknown>`
- Type assertions without accompanying type guards or validation

### **Critical Risk Indicators:**
- **CRITICAL**: Type assertion indicates architectural problems
- **CRITICAL**: Using `as unknown as` bypasses TypeScript's type safety
- **CRITICAL**: Suggests misunderstanding of the actual data structure
- **CRITICAL**: Creates potential runtime errors that TypeScript can't catch

### **Investigation Questions to Ask:**
- What's the real type of the object being asserted?
- Why can't we use proper typing here?
- Is this indicating that we're using a library API incorrectly?
- Should we extend the type properly instead of asserting?
- What runtime validation exists to ensure the assertion is safe?
- Are we missing type definitions that should be added?

### **Guidance Principles:**
- Investigate the actual type structure and create proper interfaces
- Use type guards for runtime validation when types are uncertain
- Consider extending library types properly rather than asserting
- Look for missing type definitions that could be added
- Ensure any type assertions are accompanied by runtime validation

## 🚨 Investigation Scenario 3: Duplicate Function Implementation

### **Pattern Recognition - Look For:**
- Identical function logic implemented in multiple places
- Functions with the same name and similar signatures
- Copy-pasted function implementations with minor variations
- Functions that perform the same operation but are defined separately

### **Critical Risk Indicators:**
- **VIOLATION**: Identical function logic in multiple places
- **VIOLATION**: Maintenance burden - multiple places to update
- **VIOLATION**: Architectural confusion - unclear which function is authoritative
- **VIOLATION**: High risk of implementations diverging over time

### **Investigation Questions to Ask:**
- Why do we need the same function in two places?
- Which implementation is the "source of truth"?
- What happens if these implementations diverge over time?
- Is this indicating unclear API design requirements?
- Could one function call the other to eliminate duplication?
- Are there different use cases that justify separate implementations?

### **Guidance Principles:**
- Identify the single source of truth for the function logic
- Consider if one function can call the other to eliminate duplication
- Evaluate whether the duplication serves different architectural purposes
- Look for opportunities to create shared utility functions
- Ensure clear ownership and responsibility for each function

## 🚨 Investigation Scenario 4: Hardcoded Magic Values

### **Pattern Recognition - Look For:**
- Hardcoded z-index values without clear reasoning
- Magic numbers in CSS properties (borders, shadows, spacing)
- Inline color values instead of design tokens
- Arbitrary measurements that bypass design system
- Style values that don't reference established patterns

### **Critical Risk Indicators:**
- **VIOLATION**: Hardcoded z-index values create conflict risk
- **VIOLATION**: Magic numbers make maintenance difficult
- **VIOLATION**: Not using design system tokens breaks consistency
- **VIOLATION**: Values that won't adapt to theme changes

### **Investigation Questions to Ask:**
- Why aren't design tokens being used for these values?
- How do these z-index values relate to other components?
- What happens when the design system changes?
- Will these values work across different themes?
- Are there established patterns for these types of styles?
- What's the reasoning behind these specific numeric values?

### **Guidance Principles:**
- Import and use design tokens from the established design system
- Use named z-index values instead of arbitrary numbers
- Reference spacing scales for consistent measurements
- Use design system shadows, colors, and other visual properties
- Ensure styles are maintainable and consistent across components
- Document any exceptions to design system usage with clear reasoning

## 🚨 Investigation Scenario 5: Inconsistent Error Handling

### **Pattern Recognition - Look For:**
- Similar functions that handle errors differently (boolean vs void vs throwing)
- Inconsistent return types for error conditions across related functions
- Mixed error handling strategies within the same module
- Functions that sometimes return error indicators and sometimes throw
- Unclear error handling contracts that make calling code unpredictable

### **Critical Risk Indicators:**
- **VIOLATION**: Inconsistent return types for similar error conditions
- **VIOLATION**: API confusion - callers won't know what to expect
- **VIOLATION**: Some functions return boolean, others void for same error
- **VIOLATION**: Error handling patterns that don't follow module conventions

### **Investigation Questions to Ask:**
- Why do similar functions handle the same error differently?
- What's the intended error handling strategy for this module?
- How do callers know which error pattern to expect?
- Should these be consistent or is the difference intentional?
- What are the downstream implications of each error handling approach?
- Are there established patterns in the codebase for this type of error?

### **Guidance Principles:**
- Establish consistent error handling patterns within modules
- Ensure error handling contracts are clear and predictable
- Consider if errors should be thrown, returned as values, or handled silently
- Document the reasoning for different error handling approaches
- Look for opportunities to standardize error handling across similar functions

## 🚨 Investigation Scenario 6: Questionable Business Logic

### **Pattern Recognition - Look For:**
- Functions that return "success" for conditions that might indicate programming errors
- Business logic that treats duplicate operations as successful
- Functions that mask potential bugs by returning positive results
- Unclear success/failure semantics in business operations
- Logic that makes debugging harder by hiding actual problems

### **Critical Risk Indicators:**
- **VIOLATION**: Treating "already in desired state" as success
- **VIOLATION**: This might hide programming errors
- **VIOLATION**: Unclear semantics - did the operation do anything?
- **VIOLATION**: Makes it difficult to distinguish between action and no-op

### **Investigation Questions to Ask:**
- Is "already pinned" really a success condition?
- Should attempting to pin an already-pinned column be an error?
- How do callers distinguish between "did something" vs "was already done"?
- What's the business requirement that drives this behavior?
- Could this pattern hide bugs where the same operation is called multiple times?
- What are the user experience implications of this behavior?

### **Guidance Principles:**
- Clarify the business requirements for duplicate operations
- Consider if the operation should be idempotent or should error
- Ensure the return value clearly communicates what happened
- Look for ways to make the operation's behavior more explicit
- Consider returning more detailed information about the operation result

## 🚨 Investigation Scenario 7: Performance Anti-Patterns

### **Pattern Recognition - Look For:**
- Inefficient data access patterns when better APIs exist
- O(n) operations when O(1) alternatives might be available
- Filtering large datasets when specific query methods exist
- Manual data processing that duplicates library functionality
- Unnecessary computation in memoized values

### **Critical Risk Indicators:**
- **VIOLATION**: Getting all data then filtering (O(n) operation)
- **VIOLATION**: Library likely has more efficient APIs
- **VIOLATION**: Performance will degrade with large datasets
- **VIOLATION**: Not using library APIs as intended

### **Investigation Questions to Ask:**
- Why filter all data instead of using specific APIs?
- Does the library provide more efficient methods for this use case?
- What's the performance impact with large datasets?
- Is this the intended way to use the library?
- Are there built-in methods that accomplish the same goal?
- What happens to performance as the data size grows?

### **Guidance Principles:**
- Research library documentation for more efficient APIs
- Consider the algorithmic complexity of the current approach
- Look for built-in methods that accomplish the same goal
- Evaluate the performance implications at scale
- Ensure the approach aligns with library best practices

## 🎯 Investigation Methodology Framework

### **Step 1: Pattern Recognition**
Scan for these critical patterns in order of severity:

**Immediate Red Flags (Require Deep Investigation):**
- [ ] Side effects during render (mutations in hook/component body)
- [ ] Type assertions (`as`, `as unknown as`) without justification
- [ ] Duplicate function implementations
- [ ] Hardcoded style values (z-index, shadows, spacing)
- [ ] Inconsistent error handling patterns
- [ ] Functions attached to external objects without cleanup

**Investigation Triggers (Require Questioning):**
- [ ] Magic numbers in styles or logic
- [ ] Performance anti-patterns (inefficient data access)
- [ ] Questionable business logic (hiding errors as success)
- [ ] Disabled ESLint rules without strong justification
- [ ] Complex dependency arrays in hooks

### **Step 2: Investigation Process**
For each pattern detected, follow this methodology:

1. **Context Analysis**: Understand the surrounding code and business requirements
2. **Risk Assessment**: Evaluate immediate and long-term risks
3. **Alternative Exploration**: Ask about other approaches considered
4. **Impact Evaluation**: Consider what happens when this pattern is copied
5. **Guidance Provision**: Offer principles rather than specific code solutions

### **Step 3: Required Understanding**
Before approving any change, ensure clarity on:
- [ ] Why was this specific approach chosen?
- [ ] What problem is this solving?
- [ ] How will this behave in edge cases?
- [ ] What happens when this pattern is copied?
- [ ] Are there better alternatives available?

### **Core Principle**
Approach every piece of code with the assumption that it will be copied 50+ times throughout the codebase. Ask whether that outcome would be acceptable and guide developers toward patterns that scale well.
