# AI Code Review Workflow

## 🚨 MANDATORY: Start Here

**FIRST:** Apply core standards from [ai-code-quality-guidelines.md](./ai-code-quality-guidelines.md)

**SECOND:** Use [ai-code-pattern-triggers.md](./ai-code-pattern-triggers.md) to recognize code patterns that need investigation

## 📋 STREAMLINED REVIEW PROCESS

### STEP 1: Pattern-Based Rule Loading
Based on what you see in the code, load ONLY the relevant files:

**React hooks/components (`use`, `useState`, `useEffect`, JSX)?** → Load [ai-react-antipatterns.md](./ai-react-antipatterns.md)
**Type assertions (`as`, `as unknown as`, `!`)?** → Load [no-type-assertions.md](./no-type-assertions.md)
**Props changing (`?:` added/removed, types widened/narrowed)?** → Load [ai-subtle-change-detection.md](./ai-subtle-change-detection.md)
**Function signatures changing (params, return types, async)?** → Load [ai-subtle-change-detection.md](./ai-subtle-change-detection.md)
**Default values changing (timeouts, limits, validation)?** → Load [ai-subtle-change-detection.md](./ai-subtle-change-detection.md)
**Error handling changes (try/catch, throw, return patterns)?** → Load [ai-subtle-change-detection.md](./ai-subtle-change-detection.md)
**Need investigation methodology?** → Load [ai-code-review-investigation-methodology.md](./ai-code-review-investigation-methodology.md)
**Something feels wrong but unsure why?** → Load [ai-investigative-code-review.md](./ai-investigative-code-review.md)

### STEP 2: Apply Loaded Rules
- Focus ONLY on the rules from the files you loaded
- Don't try to remember everything - load what you need when you need it
- Use [PATTERN_INDEX.md](../PATTERN_INDEX.md) to find additional relevant files

### STEP 3: Investigation & Approval
- If you don't understand WHY a change was made, investigate using [ai-investigative-code-review.md](./ai-investigative-code-review.md)
- Only approve when you understand the complete story behind the change

## 🎯 Quick Reference

**Don't memorize everything.** Load specific files based on what you encounter:

### Code Pattern → File Mapping:
- **React hooks/components (`use*`, JSX)** → [ai-react-antipatterns.md](./ai-react-antipatterns.md)
- **Type assertions (`as`, `as unknown as`)** → [no-type-assertions.md](./no-type-assertions.md)
- **Interface/type changes (`?:`, union types)** → [ai-subtle-change-detection.md](./ai-subtle-change-detection.md)
- **Function signature changes (params, async, returns)** → [ai-subtle-change-detection.md](./ai-subtle-change-detection.md)
- **Constants/defaults changing (timeouts, limits)** → [ai-subtle-change-detection.md](./ai-subtle-change-detection.md)
- **Error handling changes (try/catch patterns)** → [ai-subtle-change-detection.md](./ai-subtle-change-detection.md)
- **Button components (`Button`, `disabled`, `isLoading`, `cosmosUseWithCaution_isDisabled`)** → [ai-button-review-guidelines.md](./ai-button-review-guidelines.md)
- **DOM queries (`getElementById`, `querySelector`)** → [no-direct-dom-manipulation.md](./no-direct-dom-manipulation.md)
- **Need examples of what to flag** → [ai-code-review-examples.md](./ai-code-review-examples.md)
- **Unsure what's wrong but something feels off** → [ai-investigative-code-review.md](./ai-investigative-code-review.md)

### Process:
1. **Scan code for keywords**
2. **Load relevant file(s) from above**
3. **Apply rules from loaded files**
4. **Investigate anything suspicious**
5. **Only approve when you understand the complete story**

**Remember:** Every change will be copied 50+ times. Design for that reality.
