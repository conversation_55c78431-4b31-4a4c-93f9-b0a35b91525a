# AI React Anti-Pattern Detection

## 🚨 CRITICAL: React Violations That Must Be Caught

These patterns violate React's fundamental principles and will cause production issues. The AI agent must flag these immediately and understand why they're dangerous.

## 🔥 Side Effects During Render (CRITICAL VIOLATION)

### **Pattern Detection:**
- Mutating ref properties during component render
- Modifying external objects in hook/component body
- Attaching functions to external objects during render
- Any side effects happening outside of useEffect/useLayoutEffect
- Direct DOM manipulation during render

### **Why This Is Dangerous:**
- **Concurrent Rendering**: <PERSON>act may call render multiple times, causing multiple mutations
- **StrictMode Issues**: Development mode calls render twice, doubling side effects
- **Memory Leaks**: Functions attached to external objects may not be cleaned up
- **Race Conditions**: Unpredictable timing of when mutations occur
- **Debugging Nightmares**: Side effects make component behavior unpredictable

### **Investigation Questions:**
- Why is this mutation happening during render instead of in an effect?
- What external state is being modified and why?
- How will this behave in StrictMode or concurrent rendering?
- What happens when the component unmounts?
- Is this creating memory leaks or stale closures?

### **Guidance for Fixes:**
- Move all side effects into useEffect or useLayoutEffect hooks
- Ensure proper cleanup functions are provided for external mutations
- Use useCallback for functions that need to be attached to external objects
- Consider if the external mutation is actually necessary or if there's a better React pattern
- Verify that the timing of the side effect is appropriate for the use case

## 🔄 Duplicate Function Implementations

### **Pattern Detection:**
- Same function logic implemented in multiple places within a component/hook
- Functions attached to external objects that duplicate returned functions
- Copy-pasted function implementations with identical logic
- Multiple functions that perform the same operation with different signatures

### **Why This Is Problematic:**
- **Maintenance Burden**: Two places to update the same logic
- **Inconsistency Risk**: Functions may diverge over time
- **Architectural Confusion**: Unclear which function should be used
- **Copy-Paste Vulnerability**: Others will copy this confusing pattern

### **Investigation Questions:**
- Why do we need the same function in two places?
- Which function is the "source of truth"?
- What happens if these implementations diverge?
- Is this indicating unclear API design?

## 🎨 Hardcoded Style Values

### **Pattern Detection:**
- Magic numbers for z-index values without clear reasoning
- Hardcoded pixel values for spacing, borders, shadows
- Color values defined inline instead of using design tokens
- Style values that don't reference the design system
- Arbitrary measurements that bypass established spacing scales

### **Why This Is Problematic:**
- **Design System Violations**: Bypasses established design tokens
- **Maintenance Issues**: Hard to update when design changes
- **Inconsistency**: Different components use different values
- **Z-Index Conflicts**: Hardcoded z-index values cause stacking issues
- **Copy-Paste Propagation**: These magic numbers spread throughout codebase

### **Investigation Questions:**
- Why aren't design tokens being used?
- How do these values relate to the design system?
- What happens when the design system changes?
- Are there z-index conflicts with other components?
- Will these values work in different themes?

### **Guidance for Fixes:**
- Import and use design tokens from the established design system
- Use named z-index values instead of arbitrary numbers
- Reference spacing scales for consistent measurements
- Use design system shadows, colors, and other visual properties
- Ensure styles are maintainable and consistent across components

## ❌ Inconsistent Error Handling

### **Pattern Detection:**
- Similar functions that handle errors differently (boolean vs void vs throwing)
- Inconsistent return types for error conditions across related functions
- Mixed error handling strategies within the same module or component
- Functions that sometimes return error indicators and sometimes throw
- Unclear error handling contracts that make calling code unpredictable

### **Why This Is Problematic:**
- **API Confusion**: Callers don't know what to expect
- **Error Handling Bugs**: Inconsistent patterns lead to missed error cases
- **Copy-Paste Issues**: Developers copy inconsistent patterns
- **Maintenance Burden**: Multiple error handling strategies to maintain

### **Investigation Questions:**
- Why do similar functions handle errors differently?
- What's the intended error handling strategy for this module?
- How do callers know which error pattern to expect?
- Is this inconsistency intentional or accidental?

## 🔧 Questionable Business Logic

### **Pattern Detection:**
- Functions that return "success" for conditions that might indicate programming errors
- Business logic that treats duplicate operations as successful
- Functions that mask potential bugs by returning positive results
- Unclear success/failure semantics in business operations
- Logic that makes debugging harder by hiding actual problems

### **Why This Is Problematic:**
- **Masks Programming Errors**: Calling pin on already-pinned column might be a bug
- **Unclear Semantics**: Is "already pinned" success or error?
- **Debugging Issues**: Makes it harder to find actual problems
- **API Confusion**: Callers don't know if operation actually did anything

### **Investigation Questions:**
- Is "already in desired state" really success?
- Should this be an error condition instead?
- How do callers distinguish between "did something" vs "was already done"?
- What's the business requirement that drives this behavior?

## 🐌 Performance Anti-Patterns

### **Pattern Detection:**
- Using inefficient data access patterns when better APIs exist
- Filtering large datasets when specific query methods are available
- O(n) operations when O(1) alternatives exist in the library
- Manual data processing that duplicates library functionality
- Unnecessary computation in memoized values

### **Why This Is Problematic:**
- **Unnecessary Work**: Filtering all columns when specific APIs exist
- **Performance Impact**: O(n) operation when O(1) might be available
- **API Misuse**: Not using library APIs as intended
- **Scalability Issues**: Performance degrades with more columns

### **Investigation Questions:**
- Why filter all columns instead of using specific APIs?
- What's the performance impact with large datasets?
- Are there more efficient ways to get this data?
- Does the library provide better APIs for this use case?

## 🔍 Dependency Array Violations

### **Pattern Detection:**
- ESLint disable comments for react-hooks/exhaustive-deps without clear justification
- Missing dependencies in useEffect, useMemo, or useCallback dependency arrays
- Vague or generic explanations for disabling the exhaustive deps rule
- Dependencies that are intentionally omitted without explaining why
- Dependency arrays that don't match the actual dependencies used in the hook

### **Why This Is Problematic:**
- **Stale Closures**: Missing dependencies can cause bugs
- **Performance Issues**: Wrong dependencies cause unnecessary recalculations
- **Maintenance Risk**: Changes to function body may not update dependencies
- **Rule Exists for Reason**: Exhaustive deps rule prevents common bugs

### **Investigation Questions:**
- Why is the exhaustive deps rule being disabled?
- What specific dependencies are being intentionally omitted?
- Is the comment explanation sufficient and accurate?
- Could the effect be restructured to avoid disabling the rule?

## 🎯 AI Agent Training Checklist

When reviewing React code, the AI agent must check for:

- [ ] **Side effects during render** - Any mutation of external state in hook/component body
- [ ] **Duplicate function implementations** - Same logic in multiple places
- [ ] **Hardcoded style values** - Magic numbers instead of design tokens
- [ ] **Inconsistent error handling** - Different error patterns in similar functions
- [ ] **Questionable business logic** - Success conditions that might hide errors
- [ ] **Performance anti-patterns** - Inefficient data access or computation
- [ ] **Dependency array issues** - Disabled rules without proper justification
- [ ] **Type assertion usage** - `as` or `as unknown as` indicating architectural problems
- [ ] **Memory leak potential** - Functions attached to external objects without cleanup
- [ ] **StrictMode compatibility** - Code that breaks in development mode

Each violation should trigger deep investigation into the underlying architectural decisions and business requirements that led to the problematic code.
