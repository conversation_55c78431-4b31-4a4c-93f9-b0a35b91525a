# Button Disabled Prop - CRITICAL RULE

> **CRITICAL**: The `disabled` prop is **FORBIDDEN** on all button components. This rule is enforced at the TypeScript level.

## Rule

**NEVER use the `disabled` prop on button components. Use `cosmosUseWithCaution_isDisabled` instead.**

## Enforcement

This rule is enforced by TypeScript - the button types explicitly prevent the `disabled` prop:

```typescript
interface BaseButtonProps {
    disabled?: never; // TypeScript error if you try to use this
    cosmosUseWithCaution_isDisabled?: boolean; // Use this instead
}
```

## Examples

### ❌ WRONG - Will cause TypeScript error

```typescript
<Button
    label="Save"
    disabled={isLoading}  // ❌ TypeScript error!
    onClick={handleSave}
/>
```

### ✅ CORRECT - For Loading States

```typescript
<Button
    label="Save"
    isLoading={isLoading}
    a11yLoadingLabel="Saving..."
    onClick={handleSave}
/>
```

### ✅ CORRECT - For Non-Loading Disabled States

```typescript
<Button
    label="Save"
    cosmosUseWithCaution_isDisabled={isReadOnly}
    onClick={handleSave}
/>
```

### ✅ CORRECT - Combining Both

```typescript
<Button
    label="Save"
    isLoading={isSaving}
    a11yLoadingLabel="Saving..."
    cosmosUseWithCaution_isDisabled={isReadOnly}  // Different condition
    onClick={handleSave}
/>
```

## Why This Rule Exists

1. **Accessibility**: Disabled buttons create problems for screen readers
2. **User Experience**: Users should get feedback about why they can't perform an action
3. **Consistency**: Enforces consistent handling of button states across the application

## Migration

When you encounter `disabled` prop usage:

1. Replace `disabled={condition}` with `cosmosUseWithCaution_isDisabled={condition}`
2. Add loading states with `isLoading` prop when appropriate
3. Consider providing user feedback instead of just disabling

## ESLint Enforcement

The `custom/button-loading-props` ESLint rule enforces proper loading state patterns:

- **Error**: `isLoading` without `a11yLoadingLabel`
- **Error**: Using both `isLoading` and `cosmosUseWithCaution_isDisabled` for the same loading state **ON THE SAME BUTTON**
- **Error**: Conditional `label` prop when `isLoading` is used

**IMPORTANT**: The rule only triggers when BOTH props are present on the same button. Using `cosmosUseWithCaution_isDisabled` alone (even with loading-related variable names) is NOT a violation.

## What is NOT a Violation

These patterns are **CORRECT** and should NOT be flagged:

```typescript
// ✅ CORRECT - Only cosmosUseWithCaution_isDisabled (no isLoading)
<Button
    label="Cancel"
    cosmosUseWithCaution_isDisabled={isFinalizingDraft}  // OK - no isLoading prop
    onClick={handleCancel}
/>

// ✅ CORRECT - Different buttons in action stack
rightActionStack={[
    {
        label: "Cancel",
        cosmosUseWithCaution_isDisabled: isProcessing,  // OK - different button
        onClick: handleCancel,
    },
    {
        label: "Save",
        isLoading: isProcessing,                        // OK - different button
        a11yLoadingLabel: "Saving...",
        onClick: handleSave,
    },
]}

// ✅ CORRECT - Different conditions
<Button
    label="Save"
    isLoading={isSaving}
    a11yLoadingLabel="Saving..."
    cosmosUseWithCaution_isDisabled={isReadOnly}  // OK - different condition
    onClick={handleSave}
/>
```

## Related

- [Button Patterns](../development-patterns/button-patterns.md) - Complete button usage guide
- [Component Design](../development-patterns/component-design.md) - General component patterns
