# Button Patterns

## Overview

This document covers proper usage of button components in the Multiverse codebase, including accessibility guidelines and the disabled prop restrictions.

## Core Rules

### ❌ NEVER Use `disabled` Prop

The `disabled` prop is **explicitly forbidden** on all button components for accessibility reasons.

```typescript
// ❌ WRONG - disabled prop is not allowed
<Button
    label="Save"
    disabled={isLoading}
    onClick={handleSave}
/>

// ✅ CORRECT - Use isLoading + a11yLoadingLabel for loading states
<Button
    label="Save"
    isLoading={isLoading}
    a11yLoadingLabel="Saving..."
    onClick={handleSave}
/>
```

### Why `disabled` is Forbidden

Drata follows accessibility guidelines that buttons should never be "disabled". The rationale is:

1. **User Feedback**: If a user tries to interact with a button but the action can't be taken, we should provide feedback explaining why
2. **Screen Reader Issues**: Disabled buttons create accessibility problems for screen readers
3. **Better UX**: Instead of disabling, show loading states or provide clear feedback

## When to Use Each Prop

### `isLoading` + `a11yLoadingLabel`
**Use for**: Async operations that take time (API calls, file operations, etc.)

```typescript
// ✅ Form submissions
<Button
    label="Save"
    isLoading={controller.isSubmitting}
    a11yLoadingLabel="Saving..."
    onClick={handleSubmit}
/>

// ✅ File downloads
<Button
    label="Download Report"
    isLoading={isDownloading}
    a11yLoadingLabel="Downloading..."
    onClick={handleDownload}
/>

// ✅ Processing operations
<Button
    label="Process Data"
    isLoading={isProcessing}
    a11yLoadingLabel="Processing..."
    onClick={handleProcess}
/>
```

### `cosmosUseWithCaution_isDisabled`
**Use for**: Non-loading conditions that prevent action (permissions, feature availability, etc.)

**⚠️ IMPORTANT**: Do NOT disable submit buttons for validation issues. Users should be allowed to submit and receive proper feedback.

```typescript
// ✅ Permission-based disabling
<Button
    label="Edit"
    cosmosUseWithCaution_isDisabled={isReadOnly}
    onClick={handleEdit}
/>

// ❌ WRONG - Don't disable submit buttons for validation
<Button
    label="Submit"
    cosmosUseWithCaution_isDisabled={!isFormValid}  // Don't do this!
    onClick={handleSubmit}
/>

// ✅ CORRECT - Allow submit, provide feedback in handler
<Button
    label="Submit"
    onClick={handleSubmitWithValidation}  // Handle validation inside
/>

// ✅ Feature availability
<Button
    label="Advanced Feature"
    cosmosUseWithCaution_isDisabled={!hasPermission}
    onClick={handleAdvancedFeature}
/>
```

### Combining Both Props
**Use when**: You have both loading AND non-loading disable conditions (NOT validation)

```typescript
// ✅ CORRECT - Loading + Permission check
<Button
    label="Save"
    isLoading={isSaving}
    a11yLoadingLabel="Saving..."
    cosmosUseWithCaution_isDisabled={isReadOnly}  // Permission, not validation
    onClick={handleSave}
/>

// ❌ WRONG - Same condition
<Button
    label="Save"
    isLoading={isSaving}
    a11yLoadingLabel="Saving..."
    cosmosUseWithCaution_isDisabled={isSaving}  // Redundant!
    onClick={handleSave}
/>

// ❌ WRONG - Validation-based disabling
<Button
    label="Submit"
    isLoading={isSubmitting}
    a11yLoadingLabel="Submitting..."
    cosmosUseWithCaution_isDisabled={!isFormValid}  // Don't disable for validation!
    onClick={handleSubmit}
/>
```

## Button Component Usage

### Basic Button

```typescript
import { Button } from '@cosmos/components/button';

// ✅ CORRECT - Basic button usage
<Button
    label="Save Policy"
    level="primary"
    onClick={handleSave}
/>
```

### Loading States

```typescript
// ✅ CORRECT - Use isLoading with a11yLoadingLabel for loading states
<Button
    label="Save"
    level="primary"
    isLoading={isLoading}
    a11yLoadingLabel="Saving..."
    onClick={handleSave}
/>

// ❌ WRONG - Don't use both isLoading and cosmosUseWithCaution_isDisabled for same condition
<Button
    label="Save"
    level="primary"
    isLoading={isLoading}
    cosmosUseWithCaution_isDisabled={isLoading}  // Redundant!
    onClick={handleSave}
/>

// ❌ WRONG - Don't conditionally change label when using isLoading
<Button
    label={isLoading ? "Saving..." : "Save"}  // Don't do this!
    level="primary"
    isLoading={isLoading}
    a11yLoadingLabel="Saving..."
    onClick={handleSave}
/>
```

### Form Submission Pattern

```typescript
// ✅ CORRECT - Always allow submit, handle validation in onClick
const handleSubmit = () => {
    if (!isFormValid) {
        // Show validation feedback - don't prevent submission
        snackbarController.showError(t`Please fill in all required fields`);
        return;
    }
    // Proceed with submission
    submitForm();
};

<Button
    label="Submit"
    level="primary"
    onClick={handleSubmit}
    isLoading={isSubmitting}
    a11yLoadingLabel="Submitting..."
    // Only disable for loading, NOT for validation
/>

// ❌ WRONG - Don't disable submit buttons for validation
<Button
    label="Submit"
    cosmosUseWithCaution_isDisabled={!isFormValid}  // Don't do this!
    onClick={handleSubmit}
/>
```

### Read-Only States

```typescript
// ✅ CORRECT - Handle read-only scenarios
<Button
    label="Edit"
    level="secondary"
    onClick={isReadOnly ? undefined : handleEdit}
    cosmosUseWithCaution_isDisabled={isReadOnly}
/>

// ✅ BETTER - Conditional rendering for read-only
{!isReadOnly && (
    <Button
        label="Edit"
        level="secondary"
        onClick={handleEdit}
    />
)}
```

## Action Stack Usage

When using buttons in action stacks (like modal footers), the same rules apply:

```typescript
// ✅ CORRECT - Action stack with proper disabled handling
rightActionStack={[
    {
        label: t`Cancel`,
        level: 'secondary',
        cosmosUseWithCaution_isDisabled: isSaving,  // Disable cancel during save
        onClick: handleCancel,
    },
    {
        label: t`Save`,
        level: 'primary',
        isLoading: isSaving,                        // Loading state handles disabling
        a11yLoadingLabel: t`Saving...`,             // Required for accessibility
        onClick: handleSave,
    },
]}

// ❌ WRONG - Redundant disabled prop (ESLint will catch this)
rightActionStack={[
    {
        label: t`Save`,
        level: 'primary',
        isLoading: isSaving,
        cosmosUseWithCaution_isDisabled: isSaving,  // ❌ Redundant!
        onClick: handleSave,
    },
]}
```

## Type Safety

The button types are configured to prevent the `disabled` prop at compile time:

```typescript
// This will cause a TypeScript error:
interface BaseButtonProps {
    disabled?: never; // Explicitly prevents disabled prop
    cosmosUseWithCaution_isDisabled?: boolean; // Use this instead
}
```

## ESLint Enforcement

The `custom/button-loading-props` ESLint rule enforces proper loading state usage:

1. **Missing a11yLoadingLabel**: If `isLoading` is used, `a11yLoadingLabel` must be provided
2. **Redundant loading disabled**: Don't use `cosmosUseWithCaution_isDisabled` for loading states when `isLoading` is already used
3. **Conditional label with isLoading**: Don't conditionally change `label` based on loading state when using `isLoading`

```typescript
// ❌ ESLint errors
<Button
    isLoading={isSubmitting}  // Error: missing a11yLoadingLabel
    label="Save"
/>

<Button
    isLoading={isSubmitting}
    a11yLoadingLabel="Saving..."
    cosmosUseWithCaution_isDisabled={isSubmitting}  // Error: redundant
    label="Save"
/>

<Button
    isLoading={isSubmitting}
    a11yLoadingLabel="Saving..."
    label={isSubmitting ? "Saving..." : "Save"}  // Error: conditional label
/>

// ✅ Correct
<Button
    isLoading={isSubmitting}
    a11yLoadingLabel="Saving..."
    cosmosUseWithCaution_isDisabled={isReadOnly}  // OK: permission, not validation
    label="Save"
/>
```

## Migration from `disabled`

When migrating existing code:

1. **Replace `disabled` with `cosmosUseWithCaution_isDisabled`**
2. **Add proper loading states** with `isLoading` prop
3. **Provide user feedback** for why actions can't be taken
4. **Consider conditional rendering** instead of disabling

```typescript
// ❌ OLD - Just disabled the button
<Button
    label="Submit"
    disabled={!isValid || isLoading}
    onClick={handleSubmit}
/>

// ✅ NEW - Better UX with feedback, no validation disabling
<Button
    label="Submit"
    isLoading={isLoading}
    a11yLoadingLabel="Submitting..."
    onClick={handleSubmitWithValidation}
/>

const handleSubmitWithValidation = () => {
    if (!isValid) {
        snackbarController.showError(t`Please fix validation errors before submitting`);
        return;
    }
    handleSubmit();
};
```

## Common Patterns

### Form Submission

```typescript
// ✅ CORRECT - Form submission pattern (no validation disabling)
<Button
    label="Save"
    level="primary"
    isLoading={controller.isSubmitting}
    a11yLoadingLabel="Saving..."
    onClick={handleSubmitWithValidation}
/>

const handleSubmitWithValidation = () => {
    if (!isFormValid) {
        // Show validation errors, don't prevent submission
        showValidationErrors();
        return;
    }
    handleSubmit();
};
```

### Icon-Only Buttons

```typescript
// ✅ CORRECT - Icon-only buttons with disabled state
<Button
    isIconOnly
    startIconName="Copy"
    label="Copy to clipboard"
    level="tertiary"
    cosmosUseWithCaution_isDisabled={isReadOnly}
    onClick={handleCopy}
/>
```

### Multi-line Props

When using `cosmosUseWithCaution_isDisabled` with complex conditions, place it at the end:

```typescript
// ✅ CORRECT - Multi-line props with disabled at end
<Button
    label="Process Data"
    level="primary"
    isLoading={isProcessing}
    onClick={handleProcess}
    cosmosUseWithCaution_isDisabled={
        isProcessing || !hasPermission || isReadOnly
    }
/>
```

## Technical Implementation Details

### How Props Work Together

The button component internally handles the combination of these props:

```typescript
// Internal button implementation (simplified)
aria-disabled={isLoading || cosmosUseWithCaution_isDisabled}
```

This means:
- **`isLoading`**: Automatically disables the button AND shows loading UI
- **`cosmosUseWithCaution_isDisabled`**: Only disables the button (no loading UI)
- **Both together**: Button is disabled if EITHER condition is true

### Accessibility Considerations

```typescript
// ✅ CORRECT - Screen readers get proper feedback
<Button
    label="Save"                    // Always visible label
    isLoading={isSaving}           // Visual loading indicator
    a11yLoadingLabel="Saving..."   // Screen reader announcement
    onClick={handleSave}
/>
```

**Why this pattern works:**
1. **Visual users** see the loading spinner and static label
2. **Screen reader users** hear "Saving..." announcement
3. **Button remains focusable** but disabled during loading
4. **Consistent experience** across all user types

### Common Anti-Patterns to Avoid

```typescript
// ❌ WRONG - Missing accessibility
<Button
    label="Save"
    isLoading={isSaving}  // Missing a11yLoadingLabel
    onClick={handleSave}
/>

// ❌ WRONG - Redundant loading disabled
<Button
    label="Save"
    isLoading={isSaving}
    cosmosUseWithCaution_isDisabled={isSaving}  // Same condition!
    onClick={handleSave}
/>

// ❌ WRONG - Conditional label with isLoading
<Button
    label={isSaving ? "Saving..." : "Save"}  // Don't change label
    isLoading={isSaving}
    a11yLoadingLabel="Saving..."
    onClick={handleSave}
/>

// ❌ WRONG - Using disabled prop
<Button
    label="Save"
    disabled={isSaving}  // Forbidden! Use cosmosUseWithCaution_isDisabled
    onClick={handleSave}
/>
```

## Real-World Examples

### Form Submission
```typescript
// ✅ Complete form submission pattern (no validation disabling)
<Button
    label="Save Policy"
    level="primary"
    isLoading={controller.isSubmitting}
    a11yLoadingLabel="Saving policy..."
    cosmosUseWithCaution_isDisabled={isReadOnly}  // Permission only, not validation
    onClick={handleSubmitWithValidation}
/>

const handleSubmitWithValidation = () => {
    if (!isFormValid) {
        // Show validation feedback, don't prevent submission
        showFormValidationErrors();
        return;
    }
    handleSubmit();
};
```

### File Operations
```typescript
// ✅ Download button
<Button
    label="Download Report"
    startIconName="Download"
    isLoading={downloadController.isDownloading}
    a11yLoadingLabel="Downloading report..."
    onClick={handleDownload}
/>

// ✅ Upload button
<Button
    label="Upload File"
    isLoading={uploadController.isUploading}
    a11yLoadingLabel="Uploading file..."
    cosmosUseWithCaution_isDisabled={!hasUploadPermission}
    onClick={handleUpload}
/>
```

### Modal Actions
```typescript
// ✅ Modal action buttons
rightActionStack={[
    {
        label: "Cancel",
        level: "secondary",
        // No loading state needed for cancel
        onClick: handleCancel,
    },
    {
        label: "Confirm",
        level: "primary",
        isLoading: isProcessing,
        a11yLoadingLabel: "Processing...",
        onClick: handleConfirm,
    },
]}
```

## Related Patterns

- [Navigation Patterns](./navigation-patterns.md) - For AppButton with href
- [Component Design](./component-design.md) - General component patterns
- [Form System](./form-system.md) - Form-specific button usage
