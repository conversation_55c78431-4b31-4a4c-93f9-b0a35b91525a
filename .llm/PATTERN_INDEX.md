**DO NOT EDIT THIS INDEX FILE** to add detailed information. This is only a quick reference!

# Pattern Index - Your Starting Point

**Last Updated**: 2025-08-12 | **Version**: 3.0

**For LLM Agents**: This is your main reference. When coding and you need to know:
- How to import something → Check "Import Violations" below
- How to name a file → Check "Creating a New File" below
- Any other pattern → Search this file for keywords

You do NOT need to read other files unless this index points you there.

## 🚨 FIRST: AI Agent Behavior

**BEFORE doing ANYTHING**, read: [critical-rules/ai-agent-behavior.md](./critical-rules/ai-agent-behavior.md)

## 📊 PRIORITY LEGEND

- 🔴 **CRITICAL** - Will break build or cause runtime errors
- 🟡 **IMPORTANT** - Common patterns you should follow
- 🟢 **REFERENCE** - Detailed docs to read when needed

## 🚨 SECOND: AI Code Quality Guidelines

**BEFORE implementing ANY code**, read: [critical-rules/ai-code-quality-guidelines.md](./critical-rules/ai-code-quality-guidelines.md)

## 🚨 THIRD: AI Agent Personas

**WHEN adopting specific communication styles or roles**, check: [personas/PERSONA_INDEX.md](./personas/PERSONA_INDEX.md)

**QUICK PERSONA REFERENCE**:
- **Code Reviews**: [personas/staff-engineer-persona.md](./personas/staff-engineer-persona.md)
<!-- - **General Development**: [personas/collaborative-developer.md](./personas/collaborative-developer.md)
- **Investigation**: [personas/technical-investigator.md](./personas/technical-investigator.md)
- **Documentation**: [personas/documentation-specialist.md](./personas/documentation-specialist.md) -->

## 🚨 CRITICAL: Found Incorrect Documentation?

**If you discover any inaccuracy in .llm files:**
1. **NOTIFY THE USER IMMEDIATELY** - Don't silently work around it
2. **Create a todo** to fix the documentation
3. **Provide specific details**: file path, line number, what's wrong, suggested fix
4. **Continue with the correct approach** but ensure the docs get updated

## 🚨 IMPORTANT: Adding New Documentation

📖 **See [DOCUMENTATION_GUIDELINES.md](./DOCUMENTATION_GUIDELINES.md) for where to add new content**

When adding new patterns or documentation:
1. Check if a relevant pattern file already exists in `.llm/development-patterns/`, `.llm/critical-rules/`, or `.llm/build-time-rules/`
2. Add your detailed documentation to the appropriate existing file
3. Only update this index with a brief pointer to that documentation
4. If no appropriate file exists, create a new one in the correct directory

## 🚨 CRITICAL BUILD-BREAKING RULES

These violations will cause ESLint errors or runtime failures:

### 🔴 Import Violations
**Keywords**: `import`, `mobx`, `lingui`, `@tanstack`, `react-table`
- **Rule**: Use global modules for restricted imports
- **File**: [pitfalls.md#import-restrictions](./pitfalls.md)

### 🔴 Button Components
**Keywords**: `Button`, `disabled`, `cosmosUseWithCaution_isDisabled`, `isLoading`, `a11yLoadingLabel`, `accessibility`
- **Rule**: NEVER use `disabled` prop - use proper loading/disabled patterns
- **ESLint**: `custom/button-loading-props` enforces proper loading state patterns
- **⚠️ AI Reviewers**: Check [ai-button-review-guidelines.md](./critical-rules/ai-button-review-guidelines.md) to avoid false positives
- **🚨 CRITICAL FIX**: When both `isLoading` and `cosmosUseWithCaution_isDisabled` use same condition, REMOVE the disabled prop entirely (don't add `false`)
- **Files**:
  - [development-patterns/button-prop-decision-tree.md](./development-patterns/button-prop-decision-tree.md) - Decision tree for choosing props
  - [critical-rules/button-disabled-prop.md](./critical-rules/button-disabled-prop.md) - CRITICAL: disabled prop forbidden
  - [critical-rules/ai-button-review-guidelines.md](./critical-rules/ai-button-review-guidelines.md) - AI reviewer guidelines (prevents false positives)
  - [build-time-rules/button-loading-eslint.md](./build-time-rules/button-loading-eslint.md) - ESLint rule enforcement
  - [development-patterns/button-patterns.md](./development-patterns/button-patterns.md) - Complete button usage guide

### 🔴 Non-Interactive Event Handlers
**Keywords**: `onClick on div`, `Box onClick`, `Stack onClick`, `keyboard`, `role`, `tabIndex`
- **Rule**: Never add event handlers to non-interactive elements (div, span, Box, Stack). Use AppLink/AppButton instead.
- **File**: [critical-rules/noninteractive-event-handlers.md](./critical-rules/noninteractive-event-handlers.md)

### Direct DOM Manipulation
**Keywords**: `document.getElementById`, `element.focus`, `setAttribute`, `dispatchEvent`, `new Event`, `DOM manipulation`
- **Rule**: NEVER directly manipulate the DOM - use React patterns instead
- **ESLint**: `custom/no-direct-dom-manipulation` enforces this rule (build-breaking)
- **Files**:
  - [critical-rules/no-direct-dom-manipulation.md](./critical-rules/no-direct-dom-manipulation.md) - CRITICAL: Complete DOM manipulation guide
  - [build-time-rules/dom-manipulation-eslint.md](./build-time-rules/dom-manipulation-eslint.md) - ESLint rule enforcement
- **Quick Fix**:
  - **DOM queries** → Use `useRef` and ref callbacks
  - **Focus management** → `useRef` + `useEffect`
  - **Event dispatching** → Controlled components + `onChange`
  - **Style changes** → CSS modules + conditional classes
  - **Complex hooks** → Split into focused, single-purpose hooks

### 🔴 Navigation Components
**Keywords**: `Link`, `navigate`, `href`, `routing`, `redirect`
- **Rule**: Use AppLink/AppButton for declarative navigation, useNavigate for programmatic
- **Files**:
  - [development-patterns/navigation-patterns.md](./development-patterns/navigation-patterns.md) - Complete navigation guide
  - [critical-rules/applink-usage.md](./critical-rules/applink-usage.md) - AppLink details
  - [development-patterns/appbutton-href-usage.md](./development-patterns/appbutton-href-usage.md) - AppButton navigation

### 🔴 Data Tables
**Keywords**: `table`, `datatable`, `grid`, `rows`, `row actions`, `rowActionsProps`
- **Rule**: Always use AppDatatable, never legacy Datatable
- **Files**:
  - [critical-rules/appdatatable-usage.md](./critical-rules/appdatatable-usage.md)
  - [critical-rules/appdatatable-cells.md](./critical-rules/appdatatable-cells.md)
  - [development-patterns/datatable-row-actions.md](./development-patterns/datatable-row-actions.md)

### 🔴 Export Rules
**Keywords**: `export`, `default`, `module`
- **Rule**: Named exports only (except routes)
- **File**: [critical-rules/export-patterns.md](./critical-rules/export-patterns.md)

### 🟡 Test Import Handling
**Keywords**: `test import`, `module not found`, `await outside async`, `TDD imports`
- **Rule**: Handle non-existent imports gracefully in tests
- **File**: [critical-rules/test-import-handling.md](./critical-rules/test-import-handling.md)

### 🔴 Render Props Anti-Pattern
**Keywords**: `render*`, `renderAction`, `renderItem`, `renderRow`, `render function`, `component inside function`, `const someComponent =`
- **Rule**: NEVER create render functions OR component variables inside components - breaks React reconciliation
- **Pattern**: Any function starting with "render" that returns JSX, OR `const someComponent = <Component />` inside render
- **File**: [critical-rules/no-render-props-antipattern.md](./critical-rules/no-render-props-antipattern.md)

### Hook Design Violations
**Keywords**: `complex hook`, `multiple responsibilities`, `hook testing`, `useEffect DOM`, `hook composition`
- **Rule**: Each hook must have ONE clear responsibility - split complex hooks into focused utilities
- **Files**:
  - [development-patterns/hook-design-patterns.md](./development-patterns/hook-design-patterns.md) - Complete hook design guide
  - [critical-rules/ai-code-review-checklist.md](./critical-rules/ai-code-review-checklist.md) - AI review checklist
- **Quick Fix**: Split into single-purpose hooks that compose well together

### 🔴 AI Code Review & Investigation
**Keywords**: `code review`, `investigation`, `subtle changes`, `why questions`, `detective`, `suspicious`, `constraint relaxation`
- **Rule**: Every change must be investigated and understood before approval - no change is too small to question
- **Files**:
  - [critical-rules/ai-code-review-checklist.md](./critical-rules/ai-code-review-checklist.md) - Complete review workflow
  - [critical-rules/ai-code-review-investigation-methodology.md](./critical-rules/ai-code-review-investigation-methodology.md) - Investigation framework and methodology
  - [critical-rules/ai-subtle-change-detection.md](./critical-rules/ai-subtle-change-detection.md) - Pattern recognition for dangerous changes
  - [critical-rules/ai-react-antipatterns.md](./critical-rules/ai-react-antipatterns.md) - React-specific violations and anti-patterns
  - [critical-rules/ai-code-pattern-triggers.md](./critical-rules/ai-code-pattern-triggers.md) - Code patterns that trigger investigation
  - [critical-rules/ai-code-quality-guidelines.md](./critical-rules/ai-code-quality-guidelines.md) - Core quality standards
- **Investigation Triggers**: Constraint relaxation, behavioral inconsistencies, complexity creep, resource changes, security modifications
- **Required Understanding**: Root cause, business justification, technical rationale, risk mitigation, future impact

## 📋 PATTERN LOOKUP BY TASK

### 🟡 Creating a New File
**Keywords**: `new file`, `create`, `naming`, `test`, `spec`
1. **File Naming**: [build-time-rules/file-naming.md](./build-time-rules/file-naming.md)
   - Format: `export-name.modifier.extension`
   - Use kebab-case only
   - Test files use `.spec.ts` or `.spec.tsx` (NOT `.test.ts`)
2. **Single Export**: [build-time-rules/single-export-files.md](./build-time-rules/single-export-files.md)
   - One export per file
   - Filename matches export name

### 🟡 Testing & TDD
**Keywords**: `test`, `tdd`, `spec`, `vitest`, `testing-library`, `red phase`, `failing test`
1. **TDD Patterns**: [development-patterns/tdd-testing-patterns.md](./development-patterns/tdd-testing-patterns.md)
   - Writing tests before implementation
   - Handling non-existent imports
   - Proper test structure
   - Common TDD mistakes to avoid
2. **Testing Pitfalls**: [development-patterns/testing-pitfalls.md](./development-patterns/testing-pitfalls.md)
   - Common mistakes and how to avoid them
   - Pre-submission checklist
   - Real examples from the codebase
3. **Test Naming**: Use `.spec.ts` or `.spec.tsx` extension
4. **Test Structure**: Lowercase describe blocks, proper spacing

### 🟡 Building Components
**Keywords**: `component`, `react`, `UI`, `card`, `ViewEditCardComponent`
1. **Design Pattern**: [development-patterns/component-design.md](./development-patterns/component-design.md)
   - Separation of concerns
   - Composition over configuration
2. **ViewEditCardComponent**: [development-patterns/view-edit-card-pattern.md](./development-patterns/view-edit-card-pattern.md)
   - Use for cards with view/edit modes
   - Automatic state management
   - ~40% code reduction
3. **Styling**: [development-patterns/styling.md](./development-patterns/styling.md)
   - CSS modules + design tokens
   - No child selectors
4. **Data Attributes**: [development-patterns/conventions.md#component-requirements](./development-patterns/conventions.md)
   - Add `data-testid` and `data-id`

### 🟡 State Management
**Keywords**: `state`, `mobx`, `controller`, `model`, `useState`
1. **MobX Pattern**: [development-patterns/state-management.md](./development-patterns/state-management.md)
   - Use Controllers + Models
   - Avoid React state for business logic
2. **Mutations**: [critical-rules/mobx-mutations.md](./critical-rules/mobx-mutations.md)
   - ObservedMutation + onSuccess + when()
3. **Data Fetching**: [development-patterns/data-fetching.md](./development-patterns/data-fetching.md)
   - ObservedQuery for fetching
   - `.load()` and `.invalidate()`

### 🟡 API SDK (Auto-Generated)
**Keywords**: `api`, `sdk`, `openapi`, `generated`, `fetch`, `endpoint`
1. **Complete Guide**: [development-patterns/api-sdk-patterns.md](./development-patterns/api-sdk-patterns.md)
2. **Quick Check**: API files are in `globals/api-sdk/src/lib/`
   - Generated files: `globals/api-sdk/src/lib/generated/`
   - **Endpoint Index**: `globals/api-sdk/src/lib/generated/api-endpoints.json` (auto-generated)
3. **Quick Usage**: Import from `@globals/api-sdk`
   - **NEVER** edit files in `@globals/api-sdk` - they're auto-generated!
   - Use `pnpm run update-api-sdk` to sync with backend
4. **Finding Endpoints**:
   - Check the auto-generated index: `globals/api-sdk/src/lib/generated/api-endpoints.json`
   - This file contains all controllers, queries, and mutations
   - Updated automatically when you run `pnpm run update-api-sdk`
   - See [Finding Available Endpoints](./development-patterns/api-sdk-patterns.md#finding-available-endpoints) for the complete workflow
5. **Can't Find an Endpoint?**
   - If endpoint doesn't exist in `api-endpoints.json`, it hasn't been added to backend yet
   - Check with backend team or create a ticket for the new endpoint
   - Once backend adds it, run `pnpm run update-api-sdk`
6. **Query Pattern**: See [Data Fetching with ObservedQuery](./development-patterns/api-sdk-patterns.md#data-fetching-with-observedquery)
7. **Mutation Pattern**: See [Data Mutations with ObservedMutation](./development-patterns/api-sdk-patterns.md#data-mutations-with-observedmutation)

### 🟡 Creating Controllers
**Keywords**: `controller`, `new controller`, `create controller`, `mobx controller`
- **Complete Guide**: [Controller Patterns](./development-patterns/controller-patterns.md)
- **Quick Info**: Controllers live in `controllers/[feature-name]/src/lib/[feature-name].controller.ts`
- **Key Concepts**: ObservedQuery for fetching, ObservedMutation for actions
- **State Management**: [State Management Patterns](./development-patterns/state-management.md)

### 🔴 Forms
**Keywords**: `form`, `input`, `validation`, `zod`, `submit`
**CRITICAL**: NEVER disable submit buttons for validation - let users submit and show feedback
1. **Form Submission**: [development-patterns/form-submission-patterns.md](./development-patterns/form-submission-patterns.md)
   - CRITICAL: Submit button patterns and validation handling
2. **Form System**: [development-patterns/form-system.md](./development-patterns/form-system.md)
   - Schema-based with Zod
3. **Form Fields**: [development-patterns/form-fields.md](./development-patterns/form-fields.md)
   - Field types reference
4. **Validation**: [development-patterns/form-validation.md](./development-patterns/form-validation.md)
   - Zod schemas and patterns

### 🟡 Wizard Component
**Keywords**: `wizard`, `multi-step`, `onStepChange`, `onComplete`, `step`
**CRITICAL**: `onStepChange` is NOT called for the last step - handle it in `onComplete`
1. **Wizard Patterns**: [development-patterns/wizard-patterns.md](./development-patterns/wizard-patterns.md)
   - Last step behavior, conditional steps, validation patterns

### 🔴 TypeScript
**Keywords**: `type`, `interface`, `satisfies`, `as`, `array`, `[]`, `assertion`, `type safety`
**🚨 CRITICAL**: Never use type assertions (`as`, `!`) as quick fixes - they bypass type safety
1. **No Type Assertions**: [critical-rules/no-type-assertions.md](./critical-rules/no-type-assertions.md)
   - **CRITICAL**: Why type assertions are dangerous and proper alternatives
2. **TypeScript Patterns**: [build-time-rules/typescript-patterns.md](./build-time-rules/typescript-patterns.md)
   - Use `satisfies` operator
   - Detailed type assertion guidance
   - Use bracket syntax `[]` for array types (not `Array<>`)
3. **Data Mapping**: [development-patterns/data-mapping-patterns.md](./development-patterns/data-mapping-patterns.md)
   - Switch statements over objects

### 🟢 ESLint & Code Quality
**Keywords**: `eslint`, `lint`, `formatting`, `code style`, `import order`
1. **ESLint & TypeScript Rules**: [build-time-rules/eslint-typescript-rules.md](./build-time-rules/eslint-typescript-rules.md)
   - Comprehensive ESLint rules guide
   - TypeScript configuration and common issues
   - Auto-fix commands and IDE integration
2. **Testing Pitfalls**: [development-patterns/testing-pitfalls.md](./development-patterns/testing-pitfalls.md)
   - Common testing mistakes to avoid

### 🟡 Internationalization
**Keywords**: `i18n`, `translate`, `t`, `Trans`, `locale`
1. **i18n Patterns**: [development-patterns/internationalization.md](./development-patterns/internationalization.md)
   - Function-based translations
   - Avoid constants with t``
   - Import from `@globals/i18n/macro`

### 🟢 Error Handling
**Keywords**: `error`, `exception`, `api error`, `logging`, `snackbar`, `user message`
1. **Error Handling**: [development-patterns/error-handling.md](./development-patterns/error-handling.md)
   - Two-step process: log for engineering, help the customer
   - User-friendly messages with internationalization
   - Never expose internal system details

### 🟡 Permissions & Access
**Keywords**: `permission`, `access`, `auth`, `feature flag`
1. **Access Control**: [development-patterns/permissions-and-access.md](./development-patterns/permissions-and-access.md)
   - Always use FeatureAccessModel
   - Never direct permission checks

### 🟢 Package/Module Creation
**Keywords**: `package`, `barrel`, `export`, `public API`
1. **Package Exports**: [build-time-rules/package-exports.md](./build-time-rules/package-exports.md)
   - Explicit barrel exports
   - No `export *`

### 🟢 File Organization
**Keywords**: `structure`, `folder`, `organize`, `directory`
1. **Organization**: [development-patterns/file-organization.md](./development-patterns/file-organization.md)
   - Domain-driven structure
   - Co-location principles

## 🔍 QUICK VIOLATION CHECKER

Before committing code, check for these common violations:

### 🔴 Commit Message Format
**Keywords**: `commit`, `git`, `conventional`, `changelog`
📖 **Complete Documentation**: [critical-rules/conventional-commits.md](./critical-rules/conventional-commits.md)

## 🎯 CONTEXT TRIGGERS

These keywords should trigger loading specific documentation:

- **"import"** → Check import restrictions
- **"export"** → Check export patterns
- **"state"** → Load state management patterns
- **"form"** → Load form system docs
- **"table"** → Load AppDatatable patterns
- **"row actions"/"rowActionsProps"** → Load datatable row actions guide
- **"button"/"disabled"** → Load button patterns (NEVER use disabled prop!)
- **"link"/"navigate"/"redirect"** → Load navigation patterns
- **"permission"/"access"** → Load FeatureAccessModel docs
- **"style"/"css"** → Load styling patterns
- **"controller"/"model"** → Load MobX patterns & Creating Controllers
- **"translate"/"i18n"** → Load internationalization
- **"api"/"sdk"/"fetch"** → Load API SDK patterns
- **"error"/"exception"/"logging"/"snackbar"** → Load error handling patterns
- **"create controller"/"new controller"** → Load Creating Controllers section
- **"endpoint"/"openapi"** → Load API SDK patterns
- **"test"/"tdd"/"spec"/"vitest"** → Load TDD testing patterns
- **"red phase"/"failing test"** → Load TDD patterns for tests before implementation
- **"testing mistake"/"test error"/"lint error"** → Load testing pitfalls
- **"wizard"/"multi-step"/"onStepChange"/"onComplete"** → Load wizard patterns
- **"eslint"/"lint"/"formatting"/"code style"/"import order"** → Load ESLint & TypeScript rules
- **"type error"/"typescript error"** → Load ESLint & TypeScript rules
- **"type assertion"/"as"/"!"/"type safety"** → Load TypeScript section & No Type Assertions rule
- **"card"/"edit mode"/"view mode"/"ViewEditCardComponent"** → Load ViewEditCardComponent pattern
- **"commit"/"git"/"conventional"/"changelog"** → Load conventional commits format
- **"document"/"getElementById"/"querySelector"/"DOM"/"focus"/"setAttribute"** → Load DOM manipulation critical rule
- **"dispatchEvent"/"new Event"/"manual event"** → Load DOM manipulation critical rule
- **"element.style"/"classList"/"innerHTML"/"direct manipulation"** → Load DOM manipulation critical rule
- **"complex hook"/"hook responsibility"/"useEffect DOM"** → Load DOM manipulation and hook patterns
- **"code review"/"review checklist"/"ai review"/"quality check"** → Load AI code review checklist
- **"?:" prop changes/"interface" changes/"type" widening** → Load AI subtle change detection
- **"async" added/"return type" changed/"function signature" modified** → Load AI subtle change detection
- **"timeout"/"retry"/"limit" value changes/"default" value changes** → Load AI subtle change detection
- **"try"/"catch" removed/"error handling" changed/"throw" patterns** → Load AI subtle change detection
- **"side effects"/"render mutation"/"external state"/"duplicate functions"** → Load React anti-patterns
- **"magic numbers"/"z-index"/"hardcoded"/"design tokens"** → Load React anti-patterns
- **"investigation"/"something feels wrong"/"unsure why"** → Load AI investigative code review
- **"staff engineer"/"code quality"/"copy paste"/"maintainability"** → Load staff engineer persona
- **"hook design"/"hook testing"/"hook composition"** → Load hook design patterns
- **"persona"/"communication style"/"tone"/"code review style"** → Load persona index
<!-- - **"collaborative"/"helpful"/"constructive"/"teammate"** → Load collaborative developer persona
- **"investigation"/"detective"/"suspicious"/"methodical"** → Load technical investigator persona
- **"documentation"/"explain"/"teaching"/"clarity"** → Load documentation specialist persona -->

## 📍 NAVIGATION SHORTCUTS

- **Project Setup**: [README.md](./README.md) → [project-docs/overview.md](./project-docs/overview.md)
- **Code Style**: [development-patterns/conventions.md](./development-patterns/conventions.md) → [project-docs/tooling.md](./project-docs/tooling.md)
- **Common Issues**: [development-patterns/pitfalls.md](./development-patterns/pitfalls.md)
- **Contributing**: [project-docs/contribution.md](./project-docs/contribution.md)
- **Development Patterns**: [development-patterns/](./development-patterns/)
- **Critical Rules**: [critical-rules/](./critical-rules/)
- **Build-Time Rules**: [build-time-rules/](./build-time-rules/)
