import { isEqual, isNil, last } from 'lodash-es';
import {
    sharedControlAvailableWorkspacesController,
    sharedControlDetailsController,
    sharedControlLinkedWorkspacesController,
    sharedControlLinkedWorkspacesMutationController,
} from '@controllers/controls';
import { routeController } from '@controllers/route';
import type { Action } from '@cosmos/components/action-stack';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, reaction, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import type { FormSchema, FormValues } from '@ui/forms';
import { LinkedWorkspacesComboboxField } from '../components/linked-workspaces-combobox-field.component';

class ControlLinkedWorkspacesCardModel {
    isEditing = false;
    shouldScrollIntoView = false;
    triggerSubmit: () => Promise<boolean> | undefined = () => undefined;
    currentValue: ListBoxItemData[] = [];
    routeReactionDisposer?: () => void;

    constructor() {
        makeAutoObservable(this);
    }

    get isUpdating(): boolean {
        return sharedControlLinkedWorkspacesMutationController.isUpdating;
    }

    get hasEditPermission(): boolean {
        const {
            hasWriteControlPermission,
            isControlManagerWithRestrictedView,
        } = sharedFeatureAccessModel;

        return !hasWriteControlPermission || isControlManagerWithRestrictedView;
    }

    get actions(): Action[] {
        if (this.hasEditPermission) {
            return [];
        }

        if (this.isEditing) {
            return [
                {
                    id: 'save-button',
                    actionType: 'button',
                    typeProps: {
                        label: t`Save`,
                        level: 'primary',
                        size: 'md',
                        isLoading: this.isUpdating,
                        onClick: this.handleSaveClick,
                    },
                },

                {
                    id: 'cancel-button',
                    actionType: 'button',
                    typeProps: {
                        label: t`Cancel`,
                        level: 'secondary',
                        size: 'md',
                        cosmosUseWithCaution_isDisabled: this.isUpdating,
                        onClick: this.handleCancelClick,
                    },
                },
            ];
        }

        return [
            {
                id: 'edit-button',
                actionType: 'button',
                typeProps: {
                    label: t`Manage`,
                    level: 'secondary',
                    size: 'md',
                    onClick: this.handleEditClick,
                },
            },
        ];
    }

    get linkedWorkspacesInitialValue(): ListBoxItemData[] {
        const { controlLinkedWorkspacesGroup, controlLinkedWorkspacesOptions } =
            sharedControlLinkedWorkspacesController;
        const { currentWorkspace } = sharedWorkspacesController;

        if (!isNil(controlLinkedWorkspacesGroup?.id)) {
            return controlLinkedWorkspacesOptions;
        }

        if (!currentWorkspace) {
            return [];
        }

        return [
            {
                id: String(currentWorkspace.id),
                label: currentWorkspace.name,
            },
        ];
    }

    get formSchema(): FormSchema {
        const { controlAvailableWorkspacesOptions } =
            sharedControlAvailableWorkspacesController;

        return {
            linkedWorkspaces: {
                type: 'custom',
                render: LinkedWorkspacesComboboxField,
                validateWithDefault: 'combobox',
                label: t`Linked workspaces`,
                isMultiSelect: true,
                options: controlAvailableWorkspacesOptions,
                initialValue: this.linkedWorkspacesInitialValue,
                isOptional: true,
                loaderLabel: t`Loading workspaces`,
                removeAllSelectedItemsLabel: t`Clear all`,
            },
        };
    }

    handleSubmit = (formValues: FormValues): void => {
        const { controlLinkedWorkspacesGroup } =
            sharedControlLinkedWorkspacesController;
        const { controlId } = sharedControlDetailsController;
        const { currentWorkspace } = sharedWorkspacesController;
        const typedValues = formValues as {
            linkedWorkspaces: ListBoxItemData[];
        };

        const existWorkspaceGroup = !isNil(controlLinkedWorkspacesGroup?.id);

        const shouldCreateWorkspaceGroup =
            (!existWorkspaceGroup &&
                !typedValues.linkedWorkspaces.some(
                    (ws) => ws.id === String(currentWorkspace?.id),
                )) ||
            (!existWorkspaceGroup && typedValues.linkedWorkspaces.length < 2);

        if (
            !existWorkspaceGroup &&
            !isNil(controlId) &&
            !shouldCreateWorkspaceGroup
        ) {
            sharedControlLinkedWorkspacesMutationController.createLinkedWorkspaceGroup(
                controlId,
                typedValues.linkedWorkspaces.map(({ id }) => Number(id)),
            );
        }

        if (!isNil(controlId) && !isNil(controlLinkedWorkspacesGroup?.id)) {
            sharedControlLinkedWorkspacesMutationController.updateLinkedWorkspaceGroup(
                controlId,
                controlLinkedWorkspacesGroup.id,
                {
                    workspaceIds: typedValues.linkedWorkspaces.map(({ id }) =>
                        Number(id),
                    ),
                },
            );
        }

        when(
            () => !this.isUpdating,
            () => {
                this.isEditing = false;
                this.routeReactionDisposer?.();
            },
        );
    };

    handleEditClick = (): void => {
        const { controlDetails } = sharedControlDetailsController;

        if (!controlDetails || this.hasEditPermission) {
            return;
        }

        this.shouldScrollIntoView = true;

        sharedControlAvailableWorkspacesController.load(controlDetails.code);

        when(
            () => !sharedControlAvailableWorkspacesController.isLoading,
            () => {
                this.setupRouteReaction();
                this.isEditing = true;
            },
        );
    };

    handleCancelClick = (): void => {
        if (!this.isEditing) {
            return;
        }

        if (isEqual(this.currentValue, this.linkedWorkspacesInitialValue)) {
            this.shouldScrollIntoView = false;
            this.isEditing = false;
            this.routeReactionDisposer?.();

            return;
        }

        openConfirmationModal({
            title: t`Discard changes?`,
            body: t`Changes will not be saved.`,
            confirmText: t`Ok`,
            cancelText: t`Cancel`,
            type: 'primary',
            onConfirm: () => {
                this.shouldScrollIntoView = false;
                this.isEditing = false;
                closeConfirmationModal();
            },
            onCancel: closeConfirmationModal,
        });
    };

    handleSaveClick = (): void => {
        this.shouldScrollIntoView = false;
        this.triggerSubmit()?.catch(() => {
            console.error('Failed to submit form');
        });
    };

    /**
     * Reaction for when the user navigates away from the overview page, close the edit mode.
     */
    private setupRouteReaction(): void {
        this.routeReactionDisposer = reaction(
            () => routeController.matches,
            () => {
                const match = last(routeController.matches);

                if (!match?.pathname.includes('overview')) {
                    this.handleCancelClick();
                }
            },
            {
                fireImmediately: false,
            },
        );
    }
}

export const controlLinkedWorkspacesCardModel =
    new ControlLinkedWorkspacesCardModel();
