import { isEmpty, isNil, last } from 'lodash-es';
import {
    sharedControlDetailsController,
    sharedControlLinkedWorkspacesController,
    sharedControlLinkedWorkspacesMutationController,
    sharedControlOwnersController,
    sharedControlOwnersMutationController,
} from '@controllers/controls';
import { routeController } from '@controllers/route';
import { sharedUsersInfiniteController } from '@controllers/users';
import type { Action } from '@cosmos/components/action-stack';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, reaction, when } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import type { FormSchema, FormValues } from '@ui/forms';

class ControlOwnersCardModel {
    isEditing = false;
    shouldScrollIntoView = false;
    triggerSubmit: (() => Promise<boolean>) | undefined;
    routeReactionDisposer?: () => void;

    constructor() {
        makeAutoObservable(this);
    }

    handleOnSubmitOwners = (values: FormValues): void => {
        const typedValues = values as {
            owners: ListBoxItemData[];
            hasIndependentControlOwners?: boolean;
        };

        const ownerIds = isEmpty(typedValues.owners)
            ? []
            : typedValues.owners.map((owner) => Number(owner.id));

        sharedControlOwnersMutationController.updateControlOwners(ownerIds);
        this.updateControlLinkedWorkspacesGroup(
            typedValues.hasIndependentControlOwners,
        );

        when(
            () =>
                !sharedControlOwnersMutationController.isUpdating &&
                !sharedControlLinkedWorkspacesMutationController.isUpdating,
            () => {
                this.isEditing = false;
                this.routeReactionDisposer?.();
            },
        );
    };

    updateControlLinkedWorkspacesGroup = (
        hasIndependentControlOwners: boolean | undefined,
    ): void => {
        if (isNil(hasIndependentControlOwners)) {
            return;
        }

        const { controlId } = sharedControlDetailsController;
        const { controlLinkedWorkspacesGroup } =
            sharedControlLinkedWorkspacesController;

        if (isNil(controlId) || isNil(controlLinkedWorkspacesGroup?.id)) {
            return;
        }

        sharedControlLinkedWorkspacesMutationController.updateLinkedWorkspaceGroup(
            controlId,
            controlLinkedWorkspacesGroup.id,
            {
                hasIndependentControlOwners,
                workspaceIds: controlLinkedWorkspacesGroup.linkedWorkspaces.map(
                    (workspace) => workspace.workspace.id,
                ),
            },
            { skipSnackbar: true },
        );
    };

    get hasIndependentControlOwnersInitialValue(): boolean {
        const { controlId } = sharedControlDetailsController;

        if (isNil(controlId)) {
            return false;
        }

        const controlLinkedWorkspaceGroup =
            sharedControlLinkedWorkspacesController.getControlWorkspaceGroupByControlId(
                controlId,
            );

        return Boolean(
            controlLinkedWorkspaceGroup?.hasIndependentControlOwners,
        );
    }

    get formSchema(): FormSchema {
        const { options, hasNextPage, isFetching, isLoading, onFetchUsers } =
            sharedUsersInfiniteController;
        const { controlOwners } = sharedControlOwnersController;
        const { controlLinkedWorkspacesGroup } =
            sharedControlLinkedWorkspacesController;

        return {
            owners: {
                type: 'combobox',
                label: t`Control owners`,
                loaderLabel: t`Loading results`,
                removeAllSelectedItemsLabel: t`Clear all`,
                getSearchEmptyState: () => t`No users found`,
                isMultiSelect: true,
                options,
                hasMore: hasNextPage,
                isLoading: isFetching && isLoading,
                onFetchOptions: (params) => {
                    onFetchUsers({
                        ...params,
                        roles: [
                            'ADMIN',
                            'TECHGOV',
                            'WORKSPACE_ADMINISTRATOR',
                            'CONTROL_MANAGER',
                        ],
                        excludeReadOnlyUsers: true,
                    });
                },
                isOptional: true,
                initialValue: controlOwners.map((owner) => ({
                    id: owner.id.toString(),
                    label: getFullName(owner.firstName, owner.lastName),
                    value: owner.id.toString(),
                })),
            },
            ...(!isNil(controlLinkedWorkspacesGroup?.id) && {
                hasIndependentControlOwners: {
                    type: 'checkbox',
                    label: t`Control owners are unique per workspace`,
                    initialValue: this.hasIndependentControlOwnersInitialValue,
                },
            }),
        };
    }

    get hasEditPermission(): boolean {
        const {
            hasWriteControlPermission,
            isControlManagerWithRestrictedView,
        } = sharedFeatureAccessModel;

        return !hasWriteControlPermission || isControlManagerWithRestrictedView;
    }

    get actions(): Action[] {
        if (this.hasEditPermission) {
            return [];
        }

        if (this.isEditing) {
            return [
                {
                    id: 'save-button',
                    actionType: 'button' as const,
                    typeProps: {
                        label: t`Save`,
                        level: 'primary' as const,
                        size: 'md' as const,
                        isLoading:
                            sharedControlOwnersMutationController.isUpdating,
                        onClick: this.handleSaveClick,
                    },
                },
                {
                    id: 'cancel-button',
                    actionType: 'button',
                    typeProps: {
                        label: t`Cancel`,
                        level: 'secondary',
                        size: 'md',
                        cosmosUseWithCaution_isDisabled:
                            sharedControlOwnersMutationController.isUpdating,
                        onClick: this.handleCancelClick,
                    },
                },
            ];
        }

        return [
            {
                id: 'edit-button',
                actionType: 'button',
                typeProps: {
                    label: t`Assign`,
                    level: 'secondary',
                    size: 'md',
                    onClick: this.handleEditClick,
                },
            },
        ];
    }

    handleEditClick = (): void => {
        if (this.hasEditPermission) {
            return;
        }

        this.shouldScrollIntoView = true;
        this.setupRouteReaction();
        this.isEditing = true;
    };

    handleCancelClick = (): void => {
        this.shouldScrollIntoView = false;
        this.isEditing = false;
        this.routeReactionDisposer?.();
    };

    handleSaveClick = (): void => {
        this.shouldScrollIntoView = false;
        this.triggerSubmit?.().catch(() => {
            logger.error('Failed to submit form');
        });
    };

    /**
     * Reaction for when the user navigates away from the overview page, close the edit mode.
     */
    private setupRouteReaction(): void {
        this.routeReactionDisposer = reaction(
            () => routeController.matches,
            () => {
                const match = last(routeController.matches);

                if (!match?.pathname.includes('overview')) {
                    this.handleCancelClick();
                }
            },
            {
                fireImmediately: false,
            },
        );
    }
}

export const controlOwnersCardModel = new ControlOwnersCardModel();
