import { sharedControlOwnersController } from '@controllers/controls';
import { Banner } from '@cosmos/components/banner';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { controlReviewCardModel } from '@models/controls';
import { Form } from '@ui/forms';

export const ControlApprovalsEditComponent = observer(
    ({
        formRef,
    }: {
        formRef: React.ForwardedRef<HTMLFormElement>;
    }): React.JSX.Element => {
        const { formSchema, handleSubmit } = controlReviewCardModel;
        const { controlHasOwners } = sharedControlOwnersController;

        return (
            <Stack
                data-testid="ControlApprovalsEditComponent"
                direction="column"
                gap="xl"
                data-id="XgwM4pIx"
            >
                <Text size="200" type="subheadline">
                    {t`Require specific people to approve the control before it's considered "Ready". This is in addition to existing readiness criteria, such as passing tests or evidence renewal dates.`}
                </Text>
                {!controlHasOwners && (
                    <Banner
                        severity="critical"
                        title={t`This control needs an owner for required approvals. Assign a new owner to stay on track with this control.`}
                    />
                )}
                {controlHasOwners && (
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        formId="control-approvals-form"
                        data-id="control-approvals-form"
                        schema={formSchema}
                        onSubmit={handleSubmit}
                    />
                )}
            </Stack>
        );
    },
);
