import {
    ControlApprovalsReadinessCard,
    ControlEvidenceReadinessCard,
    ControlMonitoringReadinessCard,
    ControlPoliciesReadinessCard,
} from '@components/controls';
import { sharedControlDetailsController } from '@controllers/controls';
import { Grid } from '@cosmos/components/grid';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { observer } from '@globals/mobx';

export const ControlMetricsGridComponent = observer((): React.JSX.Element => {
    const { controlDetails } = sharedControlDetailsController;
    const { isMapControlsTestsEnabled } = sharedEntitlementFlagController;
    const showMonitoringCard =
        (controlDetails?.isMonitored ?? false) || isMapControlsTestsEnabled;

    return (
        <Grid
            columns={showMonitoringCard ? '4' : '3'}
            gap="4x"
            data-id="fKs-1LpC"
        >
            <ControlEvidenceReadinessCard />
            {showMonitoringCard && <ControlMonitoringReadinessCard />}
            <ControlPoliciesReadinessCard />
            <ControlApprovalsReadinessCard />
        </Grid>
    );
});
