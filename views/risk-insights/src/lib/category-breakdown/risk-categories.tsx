import { isEmpty } from 'lodash-es';
import { Stack } from '@cosmos/components/stack';
import { neutralBackgroundMild } from '@cosmos/constants/tokens';
import {
    DataPosture,
    type DataPostureBox,
} from '@cosmos-lab/components/data-posture';
import { AppLink } from '@ui/app-link';

export const RiskCategories = ({
    boxesPerCategory,
    currentWorkspace,
}: {
    boxesPerCategory: {
        category: string;
        boxes: DataPostureBox[];
    }[];
    currentWorkspace: string;
}): React.JSX.Element => {
    if (isEmpty(boxesPerCategory)) {
        const singlePlaceholderBox: DataPostureBox[] = [
            {
                id: 'placeholder-box',
                value: 1,
                color: neutralBackgroundMild,
            },
        ];

        return (
            <Stack direction="column" gap="xl">
                {Array.from({ length: 3 }, (_, index) => (
                    <Stack
                        key={`placeholder-category-${index}`}
                        direction="column"
                        gap="sm"
                        data-id="ri-JARNE"
                    >
                        <DataPosture
                            isEmptyData
                            legend={false}
                            size="lg"
                            boxes={singlePlaceholderBox}
                            data-id="Cx1ET0qR"
                        />
                    </Stack>
                ))}
            </Stack>
        );
    }

    return (
        <Stack
            direction="column"
            gap="xl"
            data-testid="RiskCategories"
            data-id="SYGUO_qu"
        >
            {boxesPerCategory.map(({ category, boxes }) => (
                <Stack
                    key={category}
                    direction="column"
                    gap="sm"
                    data-id="ri-JARNE"
                >
                    <AppLink
                        size="sm"
                        /*
                         * TODO: this navigation has to go to /risk/management/registers/[register_id]/register-risks/[task-id]
                         * That created a problem because we don't have the register_id outside of /risk/management/registers/[register_id].
                         *
                         * At the moment it was decided to hardcode the register_id to 1 since that's all tenants will have.
                         *
                         * See: https://drata.atlassian.net/browse/ENG-72803
                         */
                        href={`${currentWorkspace}/risk/management/registers/1/register-risks`}
                    >
                        {category}
                    </AppLink>
                    <DataPosture
                        legend={false}
                        size="lg"
                        boxes={boxes}
                        data-id="Cx1ET0qR"
                    />
                </Stack>
            ))}
        </Stack>
    );
};
