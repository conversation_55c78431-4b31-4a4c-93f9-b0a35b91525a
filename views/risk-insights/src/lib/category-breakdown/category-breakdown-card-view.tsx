import { isEmpty } from 'lodash-es';
import { renderToStaticMarkup } from 'react-dom/server';
import {
    sharedRiskInsightsController,
    sharedRiskInsightsDownloadController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import {
    DATA_POSTURE_BACKGROUND_COLORS,
    DataPosture,
    type DataPostureBox,
} from '@cosmos-lab/components/data-posture';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { calculateThresholdLevelsHelper } from '@helpers/calculate-threshold-levels';
import { sharedCategoryBreakdownPaginationModel } from '@models/risk-insights-category-breakdown';
import { generateCategoryBreakdownBoxes } from '../helper/category-breakdown.helper';
import { CategoryBreakdownCardView } from './risk-insights-category-breakdown-card';

class CategoryBreakdownCardModel {
    constructor() {
        makeAutoObservable(this);
    }

    get boxesPerCategory(): {
        category: string;
        boxes: DataPostureBox[];
    }[] {
        const { riskSettings } = sharedRiskSettingsController;
        const { paginatedCategories } = sharedCategoryBreakdownPaginationModel;

        return generateCategoryBreakdownBoxes(
            paginatedCategories,
            riskSettings?.thresholds ?? [],
        );
    }

    get allCategories(): {
        category: string;
        boxes: DataPostureBox[];
    }[] {
        const { riskSettings } = sharedRiskSettingsController;
        const { allCategories } = sharedCategoryBreakdownPaginationModel;

        return generateCategoryBreakdownBoxes(
            allCategories,
            riskSettings?.thresholds ?? [],
        );
    }

    get legendData(): {
        label: string;
        value: string;
        color: string;
    }[] {
        const { riskSettings } = sharedRiskSettingsController;
        const { thresholds = [] } = riskSettings ?? {};
        const thresholdLevels = calculateThresholdLevelsHelper(thresholds);

        if (isEmpty(this.boxesPerCategory)) {
            return [];
        }

        return thresholdLevels.map((threshold) => ({
            label: threshold.name,
            value: '',
            color: threshold.level
                ? DATA_POSTURE_BACKGROUND_COLORS[
                      threshold.level as keyof typeof DATA_POSTURE_BACKGROUND_COLORS
                  ]
                : '',
        }));
    }
}

export const CategoryBreakdownCard = observer((): React.JSX.Element => {
    const { isLoading } = sharedRiskInsightsController;
    const currentWorkspace = `/workspaces/${sharedWorkspacesController.currentWorkspace?.id}`;

    const { legendData, allCategories, boxesPerCategory } =
        new CategoryBreakdownCardModel();

    const {
        totalCategories,
        defaultCategoryBreakdownPageSize,
        categoryBreakdownPaginationOnPageChange,
    } = sharedCategoryBreakdownPaginationModel;

    const handleDownloadCategoryBreakdown = () => {
        // eslint-disable-next-line custom/no-direct-dom-manipulation -- Required for creating temporary DOM element for download
        const el = document.createElement('div');

        const breakdownHtml = renderToStaticMarkup(
            allCategories.map(({ category, boxes }) => {
                return (
                    <Stack
                        key={category}
                        direction="column"
                        gap="sm"
                        data-id="ri-JARNE"
                        data-category={category}
                    >
                        <Box>
                            <DataPosture
                                legend={false}
                                size="lg"
                                boxes={boxes}
                                data-id="Cx1ET0qR"
                            />
                        </Box>
                    </Stack>
                );
            }),
        );

        el.innerHTML = breakdownHtml;
        document.body.appendChild(el);

        sharedRiskInsightsDownloadController.downloadRiskInsightsReportFromHtml(
            el,
        );
    };

    return (
        <CategoryBreakdownCardView
            title={t`Category Breakdown`}
            tooltipText={t`The category breakdown shows how different parts of your business affect your risk posture.`}
            isLoading={isLoading}
            legendData={legendData}
            boxesPerCategory={boxesPerCategory}
            currentWorkspace={currentWorkspace}
            totalCategories={totalCategories}
            pageSize={defaultCategoryBreakdownPageSize}
            data-id="qUl7ERX_"
            onPageChange={categoryBreakdownPaginationOnPageChange}
            onDownload={handleDownloadCategoryBreakdown}
        />
    );
});
