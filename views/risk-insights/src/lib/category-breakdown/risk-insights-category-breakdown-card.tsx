import { isEmpty } from 'lodash-es';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { DataPostureBox } from '@cosmos-lab/components/data-posture';
import { PaginationControls } from '@cosmos-lab/components/pagination-controls';
import { t } from '@globals/i18n/macro';
import { RiskCategories } from './risk-categories';

interface CategoryBreakdownCardViewProps {
    title: string;
    tooltipText: string;
    isLoading: boolean;
    legendData: {
        label: string;
        value: string;
        color: string;
    }[];
    boxesPerCategory: {
        category: string;
        boxes: DataPostureBox[];
    }[];
    currentWorkspace: string;
    totalCategories: number;
    pageSize: number;
    onPageChange: (page: number) => void;
    onDownload: () => void;
}

export const CategoryBreakdownCardView = ({
    title,
    tooltipText,
    isLoading,
    legendData,
    boxesPerCategory,
    currentWorkspace,
    totalCategories,
    pageSize,
    onPageChange,
    onDownload,
}: CategoryBreakdownCardViewProps): React.JSX.Element => {
    return (
        <Card
            title={title}
            tooltipText={tooltipText}
            data-testid="CategoryBreakdownCardView"
            data-id="SuFqJEr7"
            body={
                isLoading ? (
                    <Skeleton barCount={5} />
                ) : (
                    <Stack
                        direction="column"
                        gap="lg"
                        data-testid="renderBody"
                        data-id="wzz55cPJ"
                    >
                        <Stack
                            direction="row"
                            gap="md"
                            data-testid="renderLegend"
                            data-id="bE8gm_4N"
                        >
                            {legendData.map((item) => (
                                <Stack
                                    key={item.label}
                                    direction="row"
                                    gap="xs"
                                    align="center"
                                    data-id="du2AX4ri"
                                >
                                    <Box
                                        width="3x"
                                        height="3x"
                                        style={{ backgroundColor: item.color }}
                                    />
                                    <Text>{item.label}</Text>
                                </Stack>
                            ))}
                        </Stack>
                        <RiskCategories
                            boxesPerCategory={boxesPerCategory}
                            currentWorkspace={currentWorkspace}
                        />
                        {isEmpty(boxesPerCategory) ? null : (
                            <PaginationControls
                                hidePageSizeOptions
                                total={totalCategories}
                                pageSize={pageSize}
                                data-id="category-breakdown-pagination-controls"
                                onPageChange={onPageChange}
                            />
                        )}
                    </Stack>
                )
            }
            actions={[
                {
                    actionType: 'button',
                    id: 'download-category-breakdown-button',
                    typeProps: {
                        isIconOnly: true,
                        label: t`Download`,
                        startIconName: 'Download',
                        level: 'tertiary',
                        cosmosUseWithCaution_isDisabled:
                            isLoading || isEmpty(boxesPerCategory),
                        onClick: onDownload,
                    },
                },
            ]}
        />
    );
};
