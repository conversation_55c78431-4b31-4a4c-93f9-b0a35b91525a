import { isEmpty } from 'lodash-es';
import { EmptyState } from '@cosmos/components/empty-state';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import {
    DataPosture,
    type DataPostureBox,
} from '@cosmos-lab/components/data-posture';
import { t } from '@globals/i18n/macro';

export const CardBody = ({
    boxes,
    isLoading,
}: {
    boxes: DataPostureBox[];
    isLoading: boolean;
}): React.JSX.Element => {
    if (isLoading) {
        return <Skeleton barCount={5} />;
    }

    if (isEmpty(boxes)) {
        return (
            <Stack align="start" justify="center">
                <EmptyState
                    isInline
                    title={t`No risk data available`}
                    description={t`Once risks are assessed, you'll see a breakdown of your organization's risk posture here.`}
                />
            </Stack>
        );
    }

    return (
        <DataPosture
            legend
            size="lg"
            boxes={boxes}
            data-testid="CardBody"
            data-id="nTFOqq3N"
        />
    );
};
