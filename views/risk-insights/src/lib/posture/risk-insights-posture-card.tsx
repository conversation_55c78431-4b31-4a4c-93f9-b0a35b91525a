import { isEmpty } from 'lodash-es';
import { useEffect, useMemo, useRef } from 'react';
import {
    sharedRiskInsightsController,
    sharedRiskInsightsDownloadController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { generateRiskPostureBoxes } from '../helper/risk-posture.helper';
import { CardBody } from './card-body';

export const PostureCard = observer((): React.JSX.Element => {
    const captureRef = useRef<HTMLDivElement>(null);
    const { riskInsights, isLoading } = sharedRiskInsightsController;
    const { riskSettings } = sharedRiskSettingsController;
    const { currentWorkspaceId } = sharedWorkspacesController;
    const { downloadRiskInsightsReportFromReference } =
        sharedRiskInsightsDownloadController;
    const navigate = useNavigate();

    const { riskPosture = {} } = riskInsights ?? {};
    const { thresholds = [] } = riskSettings ?? {};

    const handleDownload = () => {
        downloadRiskInsightsReportFromReference(captureRef);
    };

    useEffect(() => {
        runInAction(() => {
            sharedRiskInsightsDownloadController.lastPostureRef = captureRef;
        });
    }, [captureRef]);

    const boxes = useMemo(() => {
        const generatedBoxes = generateRiskPostureBoxes(
            riskPosture,
            thresholds,
        );

        const sortedThresholds = [...thresholds].sort(
            (a, b) => a.minThreshold - b.minThreshold,
        );

        return sortedThresholds
            .map((threshold) => {
                const foundBox = generatedBoxes.find((box) =>
                    box.id.includes(`-${threshold.id}`),
                );

                return foundBox
                    ? {
                          ...foundBox,
                          legendLabel: threshold.name,
                          onClick: () => {
                              if (!currentWorkspaceId) {
                                  return;
                              }
                              /*
                               * TODO: this navigation has to go to /risk/management/registers/[register_id]/register-risks/[task-id]
                               * That created a problem because we don't have the register_id outside of /risk/management/registers/[register_id].
                               *
                               * At the moment it was decided to hardcode the register_id to 1 since that's all tenants will have.
                               *
                               * See: https://drata.atlassian.net/browse/ENG-72803
                               */
                              navigate(
                                  `/workspaces/${currentWorkspaceId}/risk/management/registers/1/register-risks`,
                              );
                          },
                      }
                    : null;
            })
            .filter((box): box is NonNullable<typeof box> => box !== null);
    }, [riskPosture, thresholds, currentWorkspaceId, navigate]);

    return (
        <Card
            title={t`Posture`}
            tooltipText={t`Your risk posture shows your organization's overall preparedness against cyber attacks and other security threats.`}
            data-testid="PostureCard"
            data-id="MjCPGeLV"
            body={<CardBody boxes={boxes} isLoading={isLoading} />}
            actions={[
                {
                    actionType: 'button',
                    id: 'download-posture-button',
                    typeProps: {
                        cosmosUseWithCaution_isDisabled: isEmpty(boxes),
                        isIconOnly: true,
                        label: 'Download',
                        startIconName: 'Download',
                        level: 'tertiary',
                        onClick: handleDownload,
                    },
                },
            ]}
        />
    );
});
