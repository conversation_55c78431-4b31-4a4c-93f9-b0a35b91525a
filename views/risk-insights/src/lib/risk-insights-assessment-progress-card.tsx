import { sharedRiskInsightsDownloadController } from '@controllers/risk';
import { Card } from '@cosmos/components/card';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { DataGauge } from '@cosmos-lab/components/data-gauge';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedRiskInsightsAssessmentProgressModel } from '@models/risk-insights';

export const AssessmentProgressCard = observer((): React.JSX.Element => {
    const { downloadRiskInsightsReport } = sharedRiskInsightsDownloadController;
    const { assessmentScored, isLoading, downloadDisabled } =
        sharedRiskInsightsAssessmentProgressModel;

    const handleDownloadClick = () => {
        downloadRiskInsightsReport('ASSESSMENT_PROGRESS');
    };

    return (
        <Card
            title={t`Assessment progress`}
            data-testid="AssessmentProgressCard"
            data-id="w8sTXvqB"
            actions={[
                {
                    actionType: 'button',
                    id: 'download-assessment-progress-button',
                    typeProps: {
                        cosmosUseWithCaution_isDisabled: downloadDisabled,
                        isIconOnly: true,
                        label: 'Download',
                        startIconName: 'Download',
                        level: 'tertiary',
                        onClick: handleDownloadClick,
                    },
                },
            ]}
            body={
                isLoading ? (
                    <Stack direction="column" align="center" gap="3x" p="4x">
                        <Skeleton
                            barCount={1}
                            width="96px"
                            barHeight="96px"
                            data-id="assessment-progress-gauge-skeleton"
                        />
                        <Skeleton
                            barCount={1}
                            width="60px"
                            barHeight="16px"
                            data-id="assessment-progress-label-skeleton"
                        />
                    </Stack>
                ) : (
                    <DataGauge
                        hasRainbow
                        value={assessmentScored}
                        unit={t`Scored`}
                    />
                )
            }
        />
    );
});
