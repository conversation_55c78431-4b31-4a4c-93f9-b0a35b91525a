import { RiskInsightsOverTimeChart } from '@components/risk-insights-over-time-chart';
import { Box } from '@cosmos/components/box';
import { EmptyState } from '@cosmos/components/empty-state';
import { Skeleton } from '@cosmos/components/skeleton';
import { t } from '@globals/i18n/macro';

export const RiskOverTimeCardBody = ({
    isLoading,
    isEmptyData,
    captureAreaRef,
}: {
    isLoading: boolean;
    isEmptyData: boolean;
    captureAreaRef: React.RefObject<HTMLDivElement>;
}): React.JSX.Element => {
    if (isLoading) {
        return <Skeleton barCount={2} />;
    }

    if (isEmptyData) {
        return <EmptyState isInline title={t`No risk data`} />;
    }

    return (
        <Box
            ref={captureAreaRef}
            data-testid="RiskOverTimeCardBody"
            data-id="XUWuWYiC"
        >
            <RiskInsightsOverTimeChart />
        </Box>
    );
};
