import { useCallback, useMemo, useRef } from 'react';
import { sharedRiskInsightsDownloadController } from '@controllers/risk';
import {
    getPeriodLabel,
    type PeriodValue,
} from '@controllers/risk-insights-over-time';
import type { Action } from '@cosmos/components/action-stack';
import { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedRiskInsightsOverTimeModel } from '@models/risk-insights-over-time';
import { RiskOverTimeCardBody } from './risk-over-time-card-body';

interface RisksOverTimeCardViewProps {
    title: string;
    tooltipText: string;
    isLoading: boolean;
    isEmptyData: boolean;
    currentPeriod: PeriodValue;
    periodOptions: {
        id: string;
        label: string;
        value: PeriodValue;
    }[];
    onPeriodChange: (value: string) => void;
}

export const RisksOverTimeCardView = observer(
    ({
        title,
        tooltipText,
        isLoading,
        isEmptyData,
        currentPeriod,
        onPeriodChange,
    }: RisksOverTimeCardViewProps): React.JSX.Element => {
        const captureAreaRef = useRef<HTMLDivElement>(null);
        const { downloadRiskInsightsReportFromReference } =
            sharedRiskInsightsDownloadController;
        const { controller, periodOptions } = sharedRiskInsightsOverTimeModel;

        const handleDownload = useCallback(() => {
            const { data: chartData } = controller;

            if (!chartData) {
                return;
            }
            downloadRiskInsightsReportFromReference(captureAreaRef);
        }, [controller, downloadRiskInsightsReportFromReference]);

        const actions = useMemo(() => {
            const actionsToReturn: Action[] = [
                {
                    actionType: 'button',
                    id: 'download-risk-over-time-button',
                    typeProps: {
                        isIconOnly: true,
                        label: t`Download`,
                        startIconName: 'Download',
                        level: 'tertiary',
                        cosmosUseWithCaution_isDisabled: isEmptyData,
                        onClick: handleDownload,
                    },
                },
            ];

            if (!isEmptyData) {
                actionsToReturn.push({
                    actionType: 'dropdown',
                    id: 'period-selector-dropdown',
                    typeProps: {
                        items: [
                            {
                                cosmosUseWithCaution_isDisabled: isEmptyData,
                                id: 'period-options-group',
                                type: 'group',
                                label: '',
                                items: periodOptions.map((option) => ({
                                    id: option.id,
                                    label: option.label,
                                    type: 'item' as const,
                                    value: option.value,
                                })),
                            },
                        ],
                        label: getPeriodLabel(currentPeriod),
                        level: 'secondary',
                        endIconName: 'ChevronDown',
                        onSelectGlobalOverride: (item) => {
                            const itemValue = (item as { value?: string })
                                .value;
                            const itemId = item.id;

                            if (itemValue) {
                                onPeriodChange(itemValue);
                            } else if (itemId) {
                                const option = periodOptions.find(
                                    (opt) => opt.id === itemId,
                                );

                                if (option) {
                                    onPeriodChange(option.value);
                                }
                            }
                        },
                    },
                });
            }

            return actionsToReturn;
        }, [
            isEmptyData,
            currentPeriod,
            periodOptions,
            onPeriodChange,
            handleDownload,
        ]);

        return (
            <Card
                title={title}
                tooltipText={tooltipText}
                data-testid="RisksOverTimeCardView"
                data-id="irCH46rU"
                actions={actions}
                body={
                    <RiskOverTimeCardBody
                        isLoading={isLoading}
                        isEmptyData={isEmptyData}
                        captureAreaRef={captureAreaRef}
                    />
                }
            />
        );
    },
);
