import { isEmpty } from 'lodash-es';
import { sharedRiskInsightsController } from '@controllers/risk';
import { sharedRiskInsightsOverTimeController } from '@controllers/risk-insights-over-time';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { sharedRiskInsightsOverTimeModel } from '@models/risk-insights-over-time';
import { RisksOverTimeCardView } from './risk-insights-risks-over-time-card';

export const RisksOverTimeCard = observer((): React.JSX.Element => {
    const { controller, periodOptions, isValidPeriod } =
        sharedRiskInsightsOverTimeModel;
    const { data } = sharedRiskInsightsOverTimeController;
    const { isLoading } = sharedRiskInsightsController;

    const isEmptyData = isEmpty(data?.data);

    const handlePeriodChange = (value: string) => {
        if (isValidPeriod(value)) {
            runInAction(() => {
                controller.setPeriod(value);
            });
        }
    };

    return (
        <RisksOverTimeCardView
            title={t`Risks Over Time`}
            tooltipText={t`Risks over time`}
            isLoading={isLoading}
            isEmptyData={isEmptyData}
            currentPeriod={controller.period}
            periodOptions={periodOptions}
            data-id="t63vPna-"
            onPeriodChange={handlePeriodChange}
        />
    );
});
