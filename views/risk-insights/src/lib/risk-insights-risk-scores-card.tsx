import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedRiskScoresCardModel } from '@models/risk-insights';
import { CategoryBreakdownCard } from './category-breakdown/category-breakdown-card-view';
import { RisksOverTimeCard } from './over-time/over-time-card-view';
import { PostureCard } from './posture/risk-insights-posture-card';
import { HeatmapCard } from './risk-insights-heatmap-card';

export const RiskScoresCard = observer((): React.JSX.Element => {
    const { actions } = sharedRiskScoresCardModel;

    return (
        <Card
            title={t`Risk Scores`}
            tooltipText={t`Risk scores`}
            data-testid="RiskScoresCard"
            data-id="ziH9WCGy"
            actions={actions}
            body={
                <Grid
                    areas='"posture" "risksOverTime" "gridArea"'
                    columns="1"
                    gapX="2xl"
                    gapY="3xl"
                >
                    <Box gridArea="posture">
                        <PostureCard />
                    </Box>
                    <Box gridArea="risksOverTime">
                        <RisksOverTimeCard />
                    </Box>
                    <Grid
                        areas='"heatmap categoryBreakdown"'
                        columns="2"
                        gapX="2xl"
                        gapY="3xl"
                        gridArea="gridArea"
                    >
                        <Box gridArea="heatmap">
                            <HeatmapCard />
                        </Box>
                        <Box gridArea="categoryBreakdown">
                            <CategoryBreakdownCard />
                        </Box>
                    </Grid>
                </Grid>
            }
        />
    );
});
