import { openUnmapObjectFromControlModal } from '@components/controls';
import { openAddArtifactModal } from '@components/evidence-library';
import {
    sharedControlDetailsController,
    sharedControlsExternalEvidenceMutationController,
} from '@controllers/controls';
import { sharedEvidenceDetailsController } from '@controllers/evidence-library';
import type {
    SchemaDropdownItemData,
    SchemaDropdownItems,
} from '@cosmos/components/schema-dropdown';
import type {
    EvidenceUnionResponseDto,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable, when } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedEvidenceDetailsModel } from '@models/evidence-library-details';

type ArtifactType = 'URL' | 'FILE' | 'TICKET_PROVIDER';

export class ControlsEvidenceViewTableActionCellModel {
    constructor() {
        makeAutoObservable(this);
    }

    handleAddArtifact = (
        evidenceData: EvidenceUnionResponseDto,
        artifactType: ArtifactType,
    ): void => {
        sharedEvidenceDetailsController.loadEvidenceDetails(
            evidenceData.evidenceId,
        );

        when(
            () => !sharedEvidenceDetailsController.isLoading,
            () => {
                if (!sharedEvidenceDetailsController.evidenceDetailsData) {
                    return;
                }

                openAddArtifactModal(
                    artifactType,
                    {
                        id: evidenceData.evidenceId,
                        name: evidenceData.name,
                        controlsIds:
                            sharedEvidenceDetailsController.evidenceDetailsData.controls.map(
                                ({ id }) => id,
                            ),
                        user: evidenceData.owner as UserResponseDto,
                    },
                    'controls-evidence',
                );
            },
        );
    };

    handleUnmapEvidence = (evidenceData: EvidenceUnionResponseDto): void => {
        const { controlId } = sharedControlDetailsController;

        if (!controlId) {
            return;
        }

        if (evidenceData.sourceTable === 'EXTERNAL_EVIDENCE') {
            openConfirmationModal({
                title: t`Unmap evidence?`,
                body: t`The evidence will no longer be mapped to this control.`,
                confirmText: t`Unmap`,
                cancelText: t`Cancel`,
                size: 'md',
                type: 'danger',
                isLoading: () =>
                    sharedControlsExternalEvidenceMutationController.isUnmapPending,
                onConfirm: () => {
                    sharedControlsExternalEvidenceMutationController.unmapControlEvidence(
                        {
                            controlId,
                            externalEvidenceId: evidenceData.evidenceId,
                        },
                    );

                    when(
                        () =>
                            !sharedControlsExternalEvidenceMutationController.isUnmapPending,
                        () => {
                            closeConfirmationModal();
                        },
                    );
                },
                onCancel: closeConfirmationModal,
            });

            return;
        }

        openUnmapObjectFromControlModal({
            controlId,
            objectId: evidenceData.evidenceId,
            objectType: 'EVIDENCE',
        });
    };

    getUrlArtifactItem = (
        evidenceData: EvidenceUnionResponseDto,
    ): SchemaDropdownItems[0] => ({
        id: 'add-new-artifact-url',
        label: t`URL`,
        onSelect: () => {
            this.handleAddArtifact(evidenceData, 'URL');
        },
    });

    getFileArtifactItem = (
        evidenceData: EvidenceUnionResponseDto,
    ): SchemaDropdownItemData => ({
        id: 'add-new-artifact-file',
        label: t`File`,
        onSelect: () => {
            this.handleAddArtifact(evidenceData, 'FILE');
        },
    });

    getTicketProviderArtifactItem = (
        evidenceData: EvidenceUnionResponseDto,
    ): SchemaDropdownItemData => ({
        id: 'add-new-artifact-ticket-provider',
        label: t`Ticket provider`,
        onSelect: () => {
            this.handleAddArtifact(evidenceData, 'TICKET_PROVIDER');
        },
    });

    getAddNewArtifactSubmenu = (
        evidenceData: EvidenceUnionResponseDto,
    ): SchemaDropdownItemData => {
        const { showTicketProviderOption } = sharedEvidenceDetailsModel;

        const subMenuItems: SchemaDropdownItems = [
            this.getUrlArtifactItem(evidenceData),
            ...(showTicketProviderOption
                ? [this.getTicketProviderArtifactItem(evidenceData)]
                : []),
            this.getFileArtifactItem(evidenceData),
        ];

        return {
            id: 'add-new-artifact',
            value: 'newArtifact',
            label: t`Add new artifact`,
            type: 'subMenu',
            items: subMenuItems,
        };
    };

    getUnmapEvidenceItem = (
        evidenceData: EvidenceUnionResponseDto,
    ): SchemaDropdownItemData => ({
        id: 'unmap-evidence-option',
        label: t`Unmap evidence`,
        type: 'item',
        colorScheme: 'critical',
        onSelect: action(() => {
            this.handleUnmapEvidence(evidenceData);
        }),
    });

    getDropdownItems = (
        evidenceData: EvidenceUnionResponseDto,
    ): SchemaDropdownItems => {
        if (evidenceData.sourceTable === 'EXTERNAL_EVIDENCE') {
            return [this.getUnmapEvidenceItem(evidenceData)];
        }

        return [
            this.getAddNewArtifactSubmenu(evidenceData),
            this.getUnmapEvidenceItem(evidenceData),
        ];
    };
}

export const sharedControlsEvidenceViewTableActionCellModel =
    new ControlsEvidenceViewTableActionCellModel();
