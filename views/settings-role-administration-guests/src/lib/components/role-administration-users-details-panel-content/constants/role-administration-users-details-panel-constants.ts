import { noop } from 'lodash-es';
import type { Stack } from '@cosmos/components/action-stack';

export const ASSIGNED_ROLES_STACKS: Stack[] = [
    {
        actions: [
            {
                actionType: 'button',
                id: 'role-administration-user-details-assigned-roles-remove-button',
                typeProps: {
                    label: 'Remove all',
                    colorScheme: 'danger',
                    level: 'tertiary',
                    onClick: noop,
                },
            },
            {
                actionType: 'button',
                id: 'role-administration-user-details-assigned-roles-add-button',
                typeProps: {
                    label: 'Add role',
                    level: 'secondary',
                    onClick: noop,
                },
            },
        ],
        id: 'role-administration-user-details-assigned-roles-stack',
    },
];

export const ASSIGNED_CONTROLS_STACKS: Stack[] = [
    {
        actions: [
            {
                actionType: 'button',
                id: 'role-administration-user-details-assigned-controls-go-to-button',
                typeProps: {
                    label: 'Go to Controls',
                    level: 'tertiary',
                    href: '/compliance/controls',
                },
            },
        ],
        id: 'role-administration-user-details-assigned-controls-stack',
    },
];

export const ASSIGNED_POLICIES_STACKS: Stack[] = [
    {
        actions: [
            {
                actionType: 'button',
                id: 'role-administration-user-details-assigned-policies-go-to-button',
                typeProps: {
                    label: 'Go to Policies',
                    level: 'tertiary',
                    href: '/governance/policies/active',
                },
            },
        ],
        id: 'role-administration-user-details-assigned-policies-stack',
    },
];

export const ASSIGNED_VENDORS_STACKS: Stack[] = [
    {
        actions: [
            {
                actionType: 'button',
                id: 'role-administration-user-details-assigned-vendors-go-to-button',
                typeProps: {
                    label: 'Go to Vendors',
                    level: 'tertiary',
                    href: '/vendors/current',
                },
            },
        ],
        id: 'role-administration-user-details-assigned-vendors-stack',
    },
];
