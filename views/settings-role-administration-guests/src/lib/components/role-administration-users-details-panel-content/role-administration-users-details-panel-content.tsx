import { isNil } from 'lodash-es';
import { sharedUserDetailsController } from '@controllers/user-details';
import { sharedUserRisksController } from '@controllers/user-risks';
import {
    PanelBody,
    PanelControls,
    PanelHeader,
} from '@cosmos/components/panel';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AssignedControlsAccordionComponent } from './components/assigned-controls-accordion.component';
import { AssignedPoliciesAccordionComponent } from './components/assigned-policies-accordion.component';
import { AssignedRisksAccordionComponent } from './components/assigned-risks-accordion.component';
import { AssignedRolesAccordionComponent } from './components/assigned-roles-accordion.component';
import { AssignedVendorsComponent } from './components/assigned-vendors.component';
import { RoleAdministrationAccordionComponent } from './components/role-administration-accordion.component';
import {
    ASSIGNED_CONTROLS_STACKS,
    ASSIGNED_POLICIES_STACKS,
    ASSIGNED_ROLES_STACKS,
    ASSIGNED_VENDORS_STACKS,
} from './constants/role-administration-users-details-panel-constants';

interface Props {
    onClose: () => void;
}

export const RoleAdministrationUsersDetailsPanelContent = observer(
    ({ onClose }: Props): React.JSX.Element | null => {
        const {
            userOwnedControls,
            userOwnedPolicies,
            userOwnedVendors,
            userAssignedRoles,
            userDetails,
        } = sharedUserDetailsController;

        const { userRisks } = sharedUserRisksController;

        const isRoleModifiable = !userAssignedRoles
            .flatMap((roles) => roles.roles)
            .some(
                (role) =>
                    role === 'WORKSPACE_ADMINISTRATOR' ||
                    role === 'SERVICE_USER',
            );

        const userFullName =
            isNil(userDetails?.firstName) || isNil(userDetails.lastName)
                ? 'Pending name setup'
                : `${userDetails.firstName} ${userDetails.lastName}`;

        return (
            <Stack
                direction="column"
                data-testid="RoleAdministrationUsersDetailsPanelContent"
                data-id="LLlG_ACU"
            >
                <PanelControls closeButtonLabel="Close" onClose={onClose} />

                <PanelHeader title={userFullName} />

                <PanelBody>
                    <Stack direction="column" gap="2xl">
                        <RoleAdministrationAccordionComponent
                            title="Assigned roles"
                            headerStacks={
                                isRoleModifiable ? ASSIGNED_ROLES_STACKS : []
                            }
                            quantity={
                                userAssignedRoles.flatMap(
                                    (roles) => roles.roles,
                                ).length
                            }
                        >
                            <AssignedRolesAccordionComponent
                                roles={userAssignedRoles.flatMap(
                                    (roles) => roles.roles,
                                )}
                            />
                        </RoleAdministrationAccordionComponent>

                        <Divider />

                        <RoleAdministrationAccordionComponent
                            title="Assigned controls"
                            quantity={userOwnedControls.length}
                            headerStacks={ASSIGNED_CONTROLS_STACKS}
                        >
                            <AssignedControlsAccordionComponent
                                controls={userOwnedControls}
                            />
                        </RoleAdministrationAccordionComponent>

                        <Divider />

                        <RoleAdministrationAccordionComponent
                            title="Assigned policies"
                            quantity={userOwnedPolicies.length}
                            headerStacks={ASSIGNED_POLICIES_STACKS}
                        >
                            <AssignedPoliciesAccordionComponent
                                policies={userOwnedPolicies}
                            />
                        </RoleAdministrationAccordionComponent>

                        <Divider />

                        <RoleAdministrationAccordionComponent
                            title="Assigned risks"
                            quantity={userRisks.length}
                            headerStacks={[
                                {
                                    actions: [
                                        {
                                            actionType: 'button',
                                            id: 'role-administration-user-details-assigned-risks-go-to-button',
                                            typeProps: {
                                                label: t`Go to Risks`,
                                                level: 'tertiary',
                                                /*
                                                 * TODO: this navigation has to go to /risk/management/registers/[register_id]/register-risks/[task-id]
                                                 * That created a problem because we don't have the register_id outside of /risk/management/registers/[register_id].
                                                 *
                                                 * At the moment it was decided to hardcode the register_id to 1 since that's all tenants will have.
                                                 *
                                                 * See: https://drata.atlassian.net/browse/ENG-72803
                                                 */
                                                href: `/risk/management/registers/1/register-risks`,
                                            },
                                        },
                                    ],
                                    id: 'role-administration-user-details-assigned-risks-stack',
                                },
                            ]}
                        >
                            <AssignedRisksAccordionComponent
                                risks={userRisks}
                            />
                        </RoleAdministrationAccordionComponent>

                        <Divider />

                        <RoleAdministrationAccordionComponent
                            title="Assigned vendors"
                            quantity={userOwnedVendors.length}
                            headerStacks={ASSIGNED_VENDORS_STACKS}
                        >
                            <AssignedVendorsComponent
                                vendors={userOwnedVendors}
                            />
                        </RoleAdministrationAccordionComponent>
                    </Stack>
                </PanelBody>
            </Stack>
        );
    },
);
