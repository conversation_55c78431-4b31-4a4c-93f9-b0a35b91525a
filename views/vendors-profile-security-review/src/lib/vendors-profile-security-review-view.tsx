import { type default as React, useEffect } from 'react';
import { routeController } from '@controllers/route';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import { Card } from '@cosmos/components/card';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate, useParams } from '@remix-run/react';
import { VendorsSecurityReviewAiGetStartedCallout } from './components/vendors-security-review-ai-get-started-callout';
import { VendorsSecurityReviewFilesComponent } from './components/vendors-security-review-files-component';
import { VendorsSecurityReviewQuestionnairesComponent } from './components/vendors-security-review-questionnaires-component';
import {
    getVendorsProfileSecurityReviewAddFilesActions,
    getVendorsProfileSecurityReviewAddQuestionnaireActions,
} from './constants/vendors-profile-security-review.constants';

export const VendorsProfileSecurityReviewView = observer(
    (): React.JSX.Element => {
        const { workspaceId, vendorId, securityReviewId } = useParams();
        const navigate = useNavigate();

        const { files = [], questionnaires = [] } =
            sharedVendorsSecurityReviewDocumentsController;

        const { securityReviewDetails, isLoading: isSecurityReviewLoading } =
            sharedVendorsSecurityReviewDetailsController;

        const { isProspectiveVendor } = sharedVendorsDetailsController;
        const { userPartOfUrl } = routeController;

        useEffect(() => {
            // If security review is not completed or still loading, do nothing
            if (
                securityReviewDetails?.status !== 'COMPLETED' ||
                isSecurityReviewLoading
            ) {
                return;
            }

            // Redirect if security review is completed
            const vendorType = isProspectiveVendor ? 'prospective' : 'current';

            navigate(
                `${userPartOfUrl}/vendors/${vendorType}/${vendorId}/security-reviews/${securityReviewId}/completed`,
            );
        }, [
            securityReviewDetails?.status,
            isSecurityReviewLoading,
            navigate,
            workspaceId,
            vendorId,
            securityReviewId,
            isProspectiveVendor,
            userPartOfUrl,
        ]);

        return (
            <Stack
                gap="xl"
                direction="column"
                data-testid="VendorsProfileSecurityReviewView"
                data-id="2kElRmor"
            >
                <VendorsSecurityReviewAiGetStartedCallout />
                <Card
                    data-id="vendors-profile-security-review-add-files-card"
                    title={t`Files`}
                    actions={getVendorsProfileSecurityReviewAddFilesActions()}
                    body={
                        <VendorsSecurityReviewFilesComponent
                            data-id="U4puK0vk"
                            data-testid="VendorsSecurityReviewFilesComponent"
                            data={files}
                        />
                    }
                />
                <Card
                    data-id="vendors-profile-security-review-add-questionnaires-card"
                    title={t`Questionnaires`}
                    actions={getVendorsProfileSecurityReviewAddQuestionnaireActions()}
                    body={
                        <VendorsSecurityReviewQuestionnairesComponent
                            data-testid="VendorsSecurityReviewQuestionnairesComponent"
                            data-id="WwQyGcrv"
                            data={questionnaires}
                        />
                    }
                />
            </Stack>
        );
    },
);
