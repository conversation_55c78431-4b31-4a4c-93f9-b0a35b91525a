import { sendQuestionnaireModalFormSchema } from '@components/vendors-prospective-add';
import { snackbarController } from '@controllers/snackbar';
import {
    sharedVendorsDetailsController,
    sharedVendorsTypeformQuestionnairesController,
} from '@controllers/vendors';
import { But<PERSON> } from '@cosmos/components/button';
import { Modal } from '@cosmos/components/modal';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { observer } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import { useLocation, useNavigate } from '@remix-run/react';
import { Form, useFormSubmit } from '@ui/forms';
import { sharedVendorsSecurityReviewQuestionnairesController } from '../controllers/vendors-security-review-questionnaires-controller';
import { closeSendQuestionnaireModal } from '../helpers/open-send-questionnaire-modal.helper';

const FORM_ID = 'security-review-send-questionnaire-modal-form-id';

export const SendQuestionnaireModal = observer((): React.JSX.Element => {
    const { allVendorsQuestionnaires, isLoading } =
        sharedVendorsTypeformQuestionnairesController;
    const { saveSecurityReviewQuestionnaire, isSendingQuestionnaire } =
        sharedVendorsSecurityReviewQuestionnairesController;
    const { vendorDetails } = sharedVendorsDetailsController;
    const location = useLocation();
    const navigate = useNavigate();
    const parentRoute = getParentRoute(location.pathname, 4);
    const { formRef, triggerSubmit, getFormValues } = useFormSubmit();

    const handlePreviewClick = (): void => {
        const formValues = getFormValues();

        if (!formValues) {
            logger.error({
                message: 'Form values not available for preview',
            });

            return;
        }

        const selectedQuestionnaire = formValues.questionnaires as {
            id: string;
            label: string;
            value: string;
        } | null;

        if (!selectedQuestionnaire?.value) {
            snackbarController.addSnackbar({
                id: 'questionnaire-required-error',
                props: {
                    title: t`A questionnaire is required`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const questionnaireId = selectedQuestionnaire.value;
        const previewUrl = `/vendor-hub-questionnaire/preview/${questionnaireId}`;

        navigate(previewUrl);
    };

    return (
        <>
            <Modal.Header
                title={t`Send via Drata`}
                closeButtonAriaLabel={t`Close send questionnaire modal`}
                onClose={closeSendQuestionnaireModal}
            />
            <Modal.Body>
                {isLoading ? (
                    <Skeleton barCount={10} />
                ) : (
                    <Stack gap="xl" direction="column">
                        <Form
                            hasExternalSubmitButton
                            ref={formRef}
                            formId={FORM_ID}
                            data-id="security-review-send-questionnaire-modal-form-id"
                            isReadOnly={isSendingQuestionnaire}
                            schema={sendQuestionnaireModalFormSchema(
                                allVendorsQuestionnaires,
                                vendorDetails?.contactsEmail,
                            )}
                            onSubmit={(values) => {
                                saveSecurityReviewQuestionnaire(values);
                            }}
                        />
                        <Stack direction="row" justify="start">
                            <Button
                                label={t`Set follow-up reminders in settings`}
                                size="sm"
                                level="tertiary"
                                href={`${parentRoute}/settings`}
                            />
                        </Stack>
                    </Stack>
                )}
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Preview`,
                        level: 'secondary',
                        cosmosUseWithCaution_isDisabled: isSendingQuestionnaire,
                        onClick: handlePreviewClick,
                    },
                    {
                        label: t`Confirm`,
                        level: 'primary',
                        colorScheme: 'primary',
                        isLoading: isSendingQuestionnaire,
                        onClick: () => {
                            triggerSubmit().catch(() => {
                                logger.error({
                                    message: 'Failed to submit form',
                                    additionalInfo: {
                                        vendorDetails,
                                        allVendorsQuestionnaires,
                                    },
                                });
                            });
                        },
                    },
                ]}
            />
        </>
    );
});
