import type { default as React } from 'react';
import { sharedFeatureAnnouncementDismissalsController } from '@controllers/feature-announcement-dismissals';
import { Banner } from '@cosmos/components/banner';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedVendorsSecurityReviewAiGetStartedController } from '../controllers/vendors-security-review-ai-get-started-controller';
import { VendorsSecurityReviewAiGetStartedBodyComponent } from './vendors-security-review-ai-get-started-body-component';

const handleDismissal = (): void => {
    sharedFeatureAnnouncementDismissalsController.dismissAiGetStartedAnnouncement();
};

export const VendorsSecurityReviewAiGetStartedCallout = observer(
    (): React.JSX.Element | null => {
        // Don't render if conditions are not met
        if (
            !sharedVendorsSecurityReviewAiGetStartedController.shouldDisplayBanner
        ) {
            return null;
        }

        return (
            <Banner
                title={t`Work through vendor reviews with AI`}
                severity="ai"
                displayMode="section"
                data-id="vendors-security-review-ai-get-started-callout"
                data-testid="VendorsSecurityReviewAiGetStartedCallout"
                closeButtonAriaLabel={t`Close AI get started banner`}
                body={<VendorsSecurityReviewAiGetStartedBodyComponent />}
                onClose={handleDismissal}
            />
        );
    },
);
