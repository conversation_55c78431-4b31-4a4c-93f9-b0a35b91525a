import { isNumber } from 'lodash-es';
import { routeController } from '@controllers/route';
import {
    type QuestionnaireFiles,
    sharedVendorsDetailsController,
    sharedVendorsProfileReportsAndDocumentsMutationController,
    sharedVendorsSecurityReviewFilesMutationController,
} from '@controllers/vendors';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate, useParams } from '@remix-run/react';

interface QuestionnaireActionsCellComponentProps {
    questionnaireData: QuestionnaireFiles;
}

export const SecurityReviewQuestionnaireActionsCellComponent = observer(
    ({
        questionnaireData,
    }: QuestionnaireActionsCellComponentProps): React.JSX.Element => {
        const navigate = useNavigate();
        const params = useParams();
        const { customData } = questionnaireData;
        const {
            isCompleted = false,
            isManualUpload = false,
            responseId,
            questionnaireId,
            securityReviewDocumentId,
        } = customData;

        const { isQuestionnaireDownloadPending, downloadQuestionnaire } =
            sharedVendorsProfileReportsAndDocumentsMutationController;

        const {
            isDeletePending,
            isSendReminderPending,
            openDeleteQuestionnaireConfirmationModal,
            sendReminder,
        } = sharedVendorsSecurityReviewFilesMutationController;

        const { isVendorEditable, isVendorRiskManagementProEnabled } =
            sharedFeatureAccessModel;
        const canView = isCompleted && isVendorRiskManagementProEnabled;

        const { currentWorkspace } = sharedWorkspacesController;
        const { vendorDetails } = sharedVendorsDetailsController;
        const currentWorkspaceId = currentWorkspace?.id;
        const { userPartOfUrl } = routeController;

        const handleDownloadQuestionnaire = () => {
            if (isQuestionnaireDownloadPending || !responseId) {
                return;
            }

            downloadQuestionnaire(responseId);
        };

        const handleDeleteQuestionnaire = () => {
            if (isDeletePending) {
                return;
            }

            if (!securityReviewDocumentId) {
                return;
            }

            openDeleteQuestionnaireConfirmationModal(securityReviewDocumentId);
        };

        const handleSendReminder = () => {
            if (isSendReminderPending || !isNumber(questionnaireId)) {
                return;
            }

            sendReminder(questionnaireId);
        };

        const handleViewQuestionnaire = () => {
            if (!responseId) {
                return;
            }

            const { workspaceId, vendorId, securityReviewId } = params;

            if (!workspaceId || !vendorId || !currentWorkspaceId) {
                return;
            }

            const isProspectiveVendor = vendorDetails?.status === 'PROSPECTIVE';
            const vendorType = isProspectiveVendor ? 'prospective' : 'current';

            let navigationRoute: string;

            if (securityReviewId) {
                // We're in a security review context
                navigationRoute = `${userPartOfUrl}/vendors/${vendorType}/${vendorId}/security-reviews/${securityReviewId}/questionnaires/${responseId}`;
            } else {
                // We're in a reports and documents context
                navigationRoute = `${userPartOfUrl}/vendors/${vendorType}/${vendorId}/reports-and-documents/questionnaires/${responseId}`;
            }

            navigate(navigationRoute);
        };

        return (
            <Stack
                gap="1x"
                data-testid="SecurityReviewQuestionnaireActionsCellComponent"
                data-id="7xbJM0NT"
                align="start"
                justify="end"
            >
                {!isCompleted && !isManualUpload && (
                    <Button
                        id="vendors-current-security-review-questionnaire-actions-cell-component-send-reminder-button"
                        label={t`Send reminder`}
                        level="tertiary"
                        cosmosUseWithCaution_isDisabled={isSendReminderPending}
                        onClick={handleSendReminder}
                    />
                )}
                {canView && (
                    <Button
                        id="vendors-current-security-review-questionnaire-actions-cell-component-view-button"
                        label={t`View`}
                        level="tertiary"
                        onClick={handleViewQuestionnaire}
                    />
                )}
                {(isCompleted || isManualUpload) && (
                    <Button
                        isIconOnly
                        label={t`Download`}
                        id="vendors-current-security-review-questionnaire-actions-cell-component-download-button"
                        startIconName="Download"
                        level="tertiary"
                        cosmosUseWithCaution_isDisabled={
                            isQuestionnaireDownloadPending
                        }
                        onClick={handleDownloadQuestionnaire}
                    />
                )}
                {isVendorEditable && (
                    <Button
                        isIconOnly
                        label={t`Delete`}
                        colorScheme="danger"
                        startIconName="Trash"
                        level="tertiary"
                        cosmosUseWithCaution_isDisabled={isDeletePending}
                        onClick={handleDeleteQuestionnaire}
                    />
                )}
            </Stack>
        );
    },
);
