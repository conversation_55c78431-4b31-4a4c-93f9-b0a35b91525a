import type React from 'react';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const AISettingsDescription = observer((): React.JSX.Element => {
    return (
        <Stack direction="column" gap="lg" data-id="_-vYUhjg">
            <Text type="body" size="200" colorScheme="neutral">
                {t`When you opt in to our AI, you get access to a number of different thoughtful ways to enhance and streamline your work across the application.`}
            </Text>

            <Text type="body" size="200" colorScheme="neutral">
                {t`<PERSON><PERSON> is committed to an approach to AI that safeguards your data.`}
            </Text>

            <Stack direction="column" gap="lg">
                <Stack
                    direction="column"
                    gap="xs"
                    data-id="strict-data-separation"
                >
                    <Text type="title" size="200" colorScheme="neutral">
                        {t`Strict data separation.`}
                    </Text>
                    <Text type="body" size="200" colorScheme="neutral">
                        {t`We enforce strict data separation across all of our customers to ensure your information is kept safe.`}
                    </Text>
                </Stack>

                <Stack
                    direction="column"
                    gap="xs"
                    data-id="responsible-governance"
                >
                    <Text type="title" size="200" colorScheme="neutral">
                        {t`Responsible governance.`}
                    </Text>
                    <Text type="body" size="200" colorScheme="neutral">
                        {t`We designed our AI approach with fairness, inclusivity, safety, reliability, and privacy in mind.`}
                    </Text>
                </Stack>

                <Stack direction="column" gap="xs" data-id="content-moderation">
                    <Text type="title" size="200" colorScheme="neutral">
                        {t`Content moderation.`}
                    </Text>
                    <Text type="body" size="200" colorScheme="neutral">
                        {t`Our team conducts internal reviews and uses automated tools to ensure data quality.`}
                    </Text>
                </Stack>

                <Stack direction="column" gap="xs" data-id="user-control">
                    <Text type="title" size="200" colorScheme="neutral">
                        {t`You're in control.`}
                    </Text>
                    <Text type="body" size="200" colorScheme="neutral">
                        {t`You can always change your preferences for AI features in settings.`}
                    </Text>
                </Stack>
            </Stack>
        </Stack>
    );
});
