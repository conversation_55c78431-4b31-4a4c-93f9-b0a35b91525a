import type React from 'react';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { openActiveQuestionnaireAIModal } from '../helpers/open-active-questionnaire-ai-modal.helper';

export const VendorsSecurityReviewAiGetStartedBodyComponent = observer(
    (): React.JSX.Element => {
        return (
            <Stack
                direction="column"
                align="start"
                gap="lg"
                pt="lg"
                data-id="vendors-security-review-ai-get-started-body"
                data-testid="VendorsSecurityReviewAiGetStartedBodyComponent"
            >
                <Text type="body" size="200" colorScheme="neutral">
                    {t`AI summaries will quickly help create summaries of SOC reports and vendor questionnaires so that you can spend more time taking action.`}
                </Text>
                <Text type="body" size="200" colorScheme="neutral">
                    {t`<PERSON><PERSON> is committed to an approach to generative AI that safeguards your data.`}{' '}
                    <AppLink
                        isExternal
                        href="https://drata.com/blog/our-ai-philosophy"
                        data-id="ai-documentation-link"
                        data-testid="ai-documentation-link"
                    >
                        {t`Read our AI documentation`}
                    </AppLink>
                </Text>
                <Button
                    level="secondary"
                    size="sm"
                    type="button"
                    width="auto"
                    label={t`Get started`}
                    data-id="get-started-ai-button"
                    onClick={openActiveQuestionnaireAIModal}
                />
            </Stack>
        );
    },
);
