import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import { ACTIVE_QUESTIONNAIRE_AI_MODAL_ID } from '../constants/active-questionnaire-ai-modal.constant';
import { QuestionnaireActiveAIModal } from '../modals/questionnaire-active-ai-modal.component';

export const openActiveQuestionnaireAIModal = action((): void => {
    modalController.openModal({
        id: ACTIVE_QUESTIONNAIRE_AI_MODAL_ID,
        content: () => <QuestionnaireActiveAIModal data-id="DmJLREO5" />,
        centered: true,
        disableClickOutsideToClose: true,
        size: 'lg',
    });
});

export const closeActiveQuestionnaireAIModal = action((): void => {
    modalController.closeModal(ACTIVE_QUESTIONNAIRE_AI_MODAL_ID);
});
