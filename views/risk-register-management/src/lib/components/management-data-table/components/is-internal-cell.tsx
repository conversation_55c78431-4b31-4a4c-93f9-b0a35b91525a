import { isNil } from 'lodash-es';
import { Text } from '@cosmos/components/text';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { AppLink } from '@ui/app-link';

export const IsInternalCell = ({
    row: { original },
}: {
    row: { original: RiskWithCustomFieldsResponseDto };
}): React.JSX.Element => {
    const { type, vendor } = original;

    if (type === 'EXTERNAL') {
        return isNil(vendor) ? (
            <EmptyValue label="-" />
        ) : (
            <AppLink
                isExternal
                colorScheme="primary"
                data-id="test-link"
                href={`/vendors/profile/${vendor.id}/risks`}
                label="External"
                size="sm"
            />
        );
    }

    return (
        <Text data-testid="IsInternalCell" data-id="9wp2EUta">
            Internal
        </Text>
    );
};
