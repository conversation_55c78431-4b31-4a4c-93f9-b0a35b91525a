import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { DateTime } from '@cosmos-lab/components/date-time';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';

export const TargetedCompletionDateCell = ({
    row: { original },
}: {
    row: { original: RiskWithCustomFieldsResponseDto };
}): React.JSX.Element => {
    const { anticipatedCompletionDate } = original;

    if (!anticipatedCompletionDate) {
        return <EmptyValue label="-" />;
    }

    return (
        <Stack
            direction="row"
            gap="md"
            data-id="k4jA817n"
            data-testid="TargetedCompletionDateCell"
        >
            <DateTime date={anticipatedCompletionDate} format="table" />
            <Icon
                backgroundType="minimal"
                colorScheme="neutral"
                data-id="helpCircle-icon"
                name="Edit"
                size="100"
            />
        </Stack>
    );
};
