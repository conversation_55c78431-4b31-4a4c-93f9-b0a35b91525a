import { capitalize } from 'lodash-es';
import { useMemo } from 'react';
import { sharedRiskSettingsController } from '@controllers/risk';
import { Box } from '@cosmos/components/box';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { RiskScore } from '@cosmos-lab/components/risk-score';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { calculateRiskMetrics } from '@views/risk-register-overview';

export const ResidualScoreCell = observer(
    ({
        row: { original },
    }: {
        row: { original: RiskWithCustomFieldsResponseDto };
    }): React.JSX.Element => {
        const { residualScore } = original;

        const { riskSettings } = sharedRiskSettingsController;

        const residualMetrics = useMemo(
            () => calculateRiskMetrics(residualScore ?? 0, riskSettings),
            [riskSettings, residualScore],
        );

        if (!riskSettings) {
            return <EmptyValue label="-" />;
        }

        return (
            <Box pt="1x" data-id="cnBNnERx" data-testid="ResidualScoreCellBox">
                <RiskScore
                    intensity="strong"
                    severity={residualMetrics.severity}
                    scoreNumber={residualScore}
                    data-id="mvaKLz-T"
                    data-testid="ResidualScoreCell"
                    label={capitalize(residualMetrics.threshold?.name)}
                />
            </Box>
        );
    },
);
