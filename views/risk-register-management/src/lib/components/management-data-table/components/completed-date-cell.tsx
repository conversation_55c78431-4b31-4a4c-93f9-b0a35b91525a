import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { DateTime } from '@cosmos-lab/components/date-time';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';

export const CompletedDateCell = ({
    row: { original },
}: {
    row: { original: RiskWithCustomFieldsResponseDto };
}): React.JSX.Element => {
    const { completionDate } = original;

    if (!completionDate) {
        return <EmptyValue label="-" />;
    }

    return (
        <Stack
            direction="row"
            gap="md"
            data-id="2dGeayuH"
            data-testid="CompletedDateCell"
        >
            <DateTime date={completionDate} format="table" />
            <Icon
                backgroundType="minimal"
                colorScheme="neutral"
                data-id="helpCircle-icon"
                name="Edit"
                size="100"
            />
        </Stack>
    );
};
