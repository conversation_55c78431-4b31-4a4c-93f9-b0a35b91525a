import { Metadata } from '@cosmos/components/metadata';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';

export const RiskCodeCell = ({
    row: { original },
}: {
    row: { original: RiskWithCustomFieldsResponseDto };
}): React.JSX.Element => {
    const { riskId: code } = original;

    return (
        <Metadata
            data-testid="RiskCodeCell"
            colorScheme="neutral"
            label={code}
            type="tag"
            data-id="Qm7esNXd"
        />
    );
};
