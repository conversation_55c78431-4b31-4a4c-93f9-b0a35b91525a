import { noop } from 'lodash-es';
import { sharedRiskSettingsController } from '@controllers/risk';
import { SelectField } from '@cosmos/components/select-field';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import {
    DEFAULT_SCORING_OPTION,
    scoringValueOptions,
} from '../../../helpers/risk-scoring.helper';

export const ResidualImpactCell = observer(
    ({
        row: { original },
    }: {
        row: { original: RiskWithCustomFieldsResponseDto };
    }): React.JSX.Element => {
        const { riskSettings } = sharedRiskSettingsController;

        const { residualImpact = 0 } = original;
        const residualImpactOptions = scoringValueOptions(
            riskSettings?.impact ?? 0,
        );

        return (
            <SelectField
                shouldHideLabel
                formId="risk-management-form"
                label="Treatment"
                loaderLabel="Loading"
                name={'treatment'}
                options={residualImpactOptions}
                data-id="eZVBfzUS"
                data-testid="ResidualImpactCell"
                value={
                    residualImpactOptions.find(
                        (o) => o.value === residualImpact?.toString(),
                    ) ?? DEFAULT_SCORING_OPTION
                }
                onChange={noop}
            />
        );
    },
);
