import { isEmpty } from 'lodash-es';
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { getFullName, getInitials } from '@helpers/formatters';

const MAX_OWNERS_TO_SHOW = 6;

export const RiskOwnerCell = ({
    row: { original },
}: {
    row: { original: RiskWithCustomFieldsResponseDto };
}): React.JSX.Element => {
    const { owners } = original;

    if (isEmpty(owners)) {
        return <EmptyValue label="-" />;
    }

    return (
        <AvatarStack
            data-id="ecJ4vcxR"
            maxVisibleItems={MAX_OWNERS_TO_SHOW}
            data-testid="RiskOwnerCell"
            avatarData={owners.map((owner) => ({
                fallbackText: getInitials(
                    getFullName(owner.firstName, owner.lastName),
                ),
                primaryLabel: getFull<PERSON><PERSON>(owner.firstName, owner.lastName),
                secondaryLabel: owner.email,
                imgSrc: owner.avatarUrl,
            }))}
        />
    );
};
