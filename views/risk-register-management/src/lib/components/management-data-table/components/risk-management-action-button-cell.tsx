import { noop } from 'lodash-es';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';

export const RiskManagementActionButtonCell = (): React.ReactNode => {
    return (
        <SchemaDropdown
            isIconOnly
            level="tertiary"
            label="Risk row actions"
            startIconName="HorizontalMenu"
            colorScheme="danger"
            size="sm"
            data-id="VONMacOx"
            data-testid="RiskManagementActionButtonCell"
            items={[
                {
                    id: 'tbd-option',
                    label: 'TBD',
                    type: 'item',
                    value: 'tbd',
                    startIconName: 'Microsoft',
                },
            ]}
            onSelectGlobalOverride={noop}
        />
    );
};
