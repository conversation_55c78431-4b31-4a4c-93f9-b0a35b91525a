import { noop } from 'lodash-es';
import { sharedRiskSettingsController } from '@controllers/risk';
import { SelectField } from '@cosmos/components/select-field';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import {
    DEFAULT_SCORING_OPTION,
    scoringValueOptions,
} from '../../../helpers/risk-scoring.helper';

export const InherentLikelihoodCell = observer(
    ({
        row: { original },
    }: {
        row: { original: RiskWithCustomFieldsResponseDto };
    }): React.JSX.Element => {
        const { riskSettings } = sharedRiskSettingsController;

        const { likelihood } = original;
        const inherentLikelihoodOptions = scoringValueOptions(
            riskSettings?.likelihood ?? 0,
        );

        return (
            <SelectField
                shouldHideLabel
                formId="risk-management-form"
                label="Treatment"
                loaderLabel="Loading"
                name={'treatment'}
                options={inherentLikelihoodOptions}
                data-id="UNTsewHI"
                data-testid="InherentLikelihoodCell"
                value={
                    inherentLikelihoodOptions.find(
                        (o) => o.value === likelihood?.toString(),
                    ) ?? DEFAULT_SCORING_OPTION
                }
                onChange={noop}
            />
        );
    },
);
