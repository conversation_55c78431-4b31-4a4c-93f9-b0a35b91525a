import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';

export const FirstUseEmptyState = observer((): JSX.Element => {
    if (sharedFeatureAccessModel.isRiskManagementEnabled) {
        return (
            <EmptyState
                title={t`Stay on top of your organization's risk posture with risk assessment`}
                description={t`Evaluate potential security risks by analyzing their impact and assigning scores based on their severity. This process helps you understand your vulnerabilities and ensures you can proactively manage any potential threats. `}
                illustrationName="AddCircle"
                imageSize="md"
                data-testid="FirstUseEmptyState"
                data-id="Ryj36TpX"
                leftAction={
                    <Button
                        label={t`Create risk`}
                        level="primary"
                        onClick={() => {
                            // TODO: redirect to Create risk
                            alert('Create risk not implemented');
                        }}
                    />
                }
                rightAction={
                    <AppLink isExternal href="#">
                        [Describe link destination]
                    </AppLink>
                }
            />
        );
    }

    return (
        <EmptyState
            title={t`Stay on top of your organization's risk posture with risk assessment`}
            description={t`Get started building your risk register by adding a risk from our pre-populated Risk Library, adding a custom risk or with our get started guide.`}
            illustrationName="AddCircle"
            imageSize="md"
            data-testid="FirstUseEmptyState"
            data-id="Ryj36TpX"
            leftAction={
                <Button
                    label={t`Get started`}
                    level="primary"
                    onClick={() => {
                        // TODO: Implement Get started
                        alert('Get started not implemented');
                    }}
                />
            }
            rightAction={
                <AppLink isExternal href="#">
                    [Describe link destination]
                </AppLink>
            }
        />
    );
});
