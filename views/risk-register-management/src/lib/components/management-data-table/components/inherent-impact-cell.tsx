import { noop } from 'lodash-es';
import { sharedRiskSettingsController } from '@controllers/risk';
import { SelectField } from '@cosmos/components/select-field';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import {
    DEFAULT_SCORING_OPTION,
    scoringValueOptions,
} from '../../../helpers/risk-scoring.helper';

export const InherentImpactCell = observer(
    ({
        row: { original },
    }: {
        row: { original: RiskWithCustomFieldsResponseDto };
    }): React.JSX.Element => {
        const { riskSettings } = sharedRiskSettingsController;

        const { impact } = original;
        const inherentImpactOptions = scoringValueOptions(
            riskSettings?.impact ?? 0,
        );

        return (
            <SelectField
                shouldHideLabel
                formId="risk-management-form"
                label="Treatment"
                loaderLabel="Loading"
                name={'treatment'}
                options={inherentImpactOptions}
                data-id="4yaKWuvy"
                data-testid="InherentImpactCell"
                value={
                    inherentImpactOptions.find(
                        (o) => o.value === impact?.toString(),
                    ) ?? DEFAULT_SCORING_OPTION
                }
                onChange={noop}
            />
        );
    },
);
