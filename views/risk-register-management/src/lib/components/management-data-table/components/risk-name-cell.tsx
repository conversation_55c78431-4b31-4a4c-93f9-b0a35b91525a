import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';

export const RiskNameCell = ({
    row: { original },
}: {
    row: { original: RiskWithCustomFieldsResponseDto };
}): React.JSX.Element => {
    const { title, description } = original;

    return (
        <Stack direction="column" data-id="JF3pgBh2" data-testid="RiskNameCell">
            <Text type="title">{title}</Text>
            <Text size="100">{description}</Text>
        </Stack>
    );
};
