import { noop } from 'lodash-es';
import { SelectField } from '@cosmos/components/select-field';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';

export const RiskTreatmentCell = ({
    row: { original },
}: {
    row: { original: RiskWithCustomFieldsResponseDto };
}): React.JSX.Element => {
    const { treatmentPlan } = original;

    const treatmentOptions = [
        {
            id: 'untreated-option',
            label: 'Untreated',
            value: 'UNTREATED',
        },
        {
            id: 'accepted-option',
            label: 'Accept',
            value: 'ACCEPT',
        },
        {
            id: 'transferred-option',
            label: 'Transfer',
            value: 'TRANSFER',
        },
        {
            id: 'avoided-option',
            label: 'Avoid',
            value: 'AVOID',
        },
        {
            id: 'mitigated-option',
            label: 'Mitigate',
            value: 'MITIGATE',
        },
    ];

    return (
        <SelectField
            shouldHideLabel
            formId="risk-management-form"
            label="Treatment"
            loaderLabel="Loading"
            name={'treatment'}
            options={treatmentOptions}
            data-id="Vt1QF_Xa"
            data-testid="RiskTreatmentCell"
            value={treatmentOptions.find(
                (o) => o.value.toLowerCase() === treatmentPlan.toLowerCase(),
            )}
            onChange={noop} // TODO: For implementation checkout vendors profile risks table at VendorsRisksCellTreatmentSelect
        />
    );
};
