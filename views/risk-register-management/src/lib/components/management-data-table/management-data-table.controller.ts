import { RiskCategoryCell } from '@components/risk-register';
import { sharedConnectionsController } from '@controllers/connections';
import {
    FlatfileEntityEnum,
    sharedFlatfileController,
} from '@controllers/flatfile';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { sharedRiskManagementController } from '@controllers/risk';
import { routeController } from '@controllers/route';
import type { DatatableProps, FilterProps } from '@cosmos/components/datatable';
import type { ListBoxItems } from '@cosmos/components/list-box';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import type { riskManagementControllerGetRisksListOptions } from '@globals/api-sdk/queries';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { getFullName } from '@helpers/formatters';
import { CompletedDateCell } from './components/completed-date-cell';
import { InherentImpactCell } from './components/inherent-impact-cell';
import { InherentLikelihoodCell } from './components/inherent-likelihood-cell';
import { InherentScoreCell } from './components/inherent-score-cell';
import { IsInternalCell } from './components/is-internal-cell';
import { MitigatingControlsCell } from './components/mitigating-controls-cell';
import { ResidualImpactCell } from './components/residual-impact-cell';
import { ResidualLikelihoodCell } from './components/residual-likelihood-cell';
import { ResidualScoreCell } from './components/residual-score-cell';
import { RiskCodeCell } from './components/risk-code-cell';
import { RiskManagementActionButtonCell } from './components/risk-management-action-button-cell';
import { RiskNameCell } from './components/risk-name-cell';
import { RiskOwnerCell } from './components/risk-owner-cell';
import { RiskTreatmentCell } from './components/risk-treatment-cell';
import { TargetedCompletionDateCell } from './components/targeted-completion-date-cell';

export type RiskManagementDatatableQuery = Required<
    Parameters<typeof riskManagementControllerGetRisksListOptions>
>[0]['query'];

class ManagementDatatableController {
    tableId = 'datatable-risk-management';

    constructor() {
        makeAutoObservable(this);
    }

    load = (query: RiskManagementDatatableQuery): void => {
        sharedRiskManagementController.riskManagementListQuery.load({ query });
    };

    get data(): RiskWithCustomFieldsResponseDto[] {
        return sharedRiskManagementController.risks;
    }

    get isLoading(): boolean {
        return sharedRiskManagementController.isLoading;
    }

    get total(): number {
        return sharedRiskManagementController.total;
    }

    get isFirstUse(): boolean {
        return sharedRiskManagementController.statistics?.totalRisks === 0;
    }

    loadOwnersIdsOptions = ({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            sharedRiskManagementController.risksOwnersLoadNextPage();
        } else {
            sharedRiskManagementController.risksOwnersLoadFirstPage(search);
        }
    };

    get ownersIdsOptions(): ListBoxItems {
        return sharedRiskManagementController.risksOwners.map((owner) => {
            return {
                id: owner.id.toString(),
                label: getFullName(owner.firstName, owner.lastName),
                value: owner.id.toString(),
            };
        });
    }

    loadCategoriesIdsOptions = ({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            sharedRiskManagementController.riskCategoriesLoadNextPage();
        } else {
            sharedRiskManagementController.riskCategoriesLoadFirstPage(search);
        }
    };

    get categoriesIdsOptions(): ListBoxItems {
        return sharedRiskManagementController.riskCategories.map((category) => {
            return {
                id: category.id.toString(),
                label: category.name,
                value: category.id.toString(),
            };
        });
    }

    get filterProps() {
        const filterProps = {
            clearAllButtonLabel: t`Reset`,
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'status[]',
                    label: t`Risk status`,
                    options: [
                        {
                            label: t`Active`,
                            value: 'ACTIVE',
                        },
                        {
                            label: t`Closed`,
                            value: 'CLOSED',
                        },
                        {
                            label: t`Archived`,
                            value: 'ARCHIVED',
                        },
                    ],
                },
                {
                    filterType: 'radio',
                    id: 'isScored',
                    label: t`Assessment`,
                    options: [
                        {
                            label: t`Not scored`,
                            value: 'false',
                        },
                        {
                            label: t`Scored`,
                            value: 'true',
                        },
                    ],
                },
                {
                    filterType: 'checkbox',
                    id: 'treatmentPlans[]',
                    label: t`Treatment`,
                    options: [
                        {
                            label: t`Accept`,
                            value: 'ACCEPT',
                        },
                        {
                            label: t`Mitigate`,
                            value: 'MITIGATE',
                        },
                        {
                            label: t`Avoid`,
                            value: 'AVOID',
                        },
                        {
                            label: t`Transfer`,
                            value: 'TRANSFER',
                        },
                        {
                            label: t`Untreated`,
                            value: 'UNTREATED',
                        },
                    ],
                },
                {
                    filterType: 'radio',
                    id: 'riskFilter',
                    label: t`Risks`,
                    options: [
                        {
                            label: t`Needs attention`,
                            value: 'NEEDS_ATTENTION',
                        },
                        {
                            label: t`Custom risks`,
                            value: 'CUSTOM_ONLY',
                        },
                        ...(sharedFeatureAccessModel.isVendorRiskManagementProEnabled
                            ? [
                                  {
                                      label: t`Internal risks`,
                                      value: 'INTERNAL_ONLY',
                                  },
                                  {
                                      label: t`External risks`,
                                      value: 'EXTERNAL_ONLY',
                                  },
                              ]
                            : []),
                    ],
                },
                {
                    filterType: 'radio',
                    id: 'hasTicketsDone',
                    label: t`Tickets`,
                    options: [
                        {
                            label: t`In progress`,
                            value: 'true',
                        },
                        {
                            label: t`Done`,
                            value: 'false',
                        },
                    ],
                },
                {
                    filterType: 'radio',
                    id: 'isOwned',
                    label: t`Risk owners`,
                    options: [
                        {
                            label: t`Owners assigned`,
                            value: 'true',
                        },
                        {
                            label: t`No owners assigned`,
                            value: 'false',
                        },
                    ],
                },
                {
                    isMultiSelect: true,
                    filterType: 'combobox',
                    clearSelectedItemButtonLabel: t`Remove all`,
                    id: 'ownersIds',
                    label: t`Owners`,
                    placeholder: 'Search',
                    onFetchOptions: this.loadOwnersIdsOptions,
                    options: this.ownersIdsOptions,
                    hasMore:
                        sharedRiskManagementController.risksOwnersHasNextPage,
                },
                {
                    isMultiSelect: true,
                    filterType: 'combobox',
                    id: 'categoriesIds',
                    label: t`Categories`,
                    placeholder: 'Search',
                    onFetchOptions: this.loadCategoriesIdsOptions,
                    options: this.categoriesIdsOptions,
                    hasMore:
                        sharedRiskManagementController.riskCategoriesHasNextPage,
                },
            ],
        } as const satisfies FilterProps;

        const filtersToExclude = new Set<string>();

        if (sharedConnectionsController.hasTicketingConnectionWithWriteAccess) {
            filtersToExclude.add('hasTicketsDone');
        }

        if (sharedFeatureAccessModel.isRiskManagerWithRestrictedView) {
            filtersToExclude.add('isOwned');
            filtersToExclude.add('ownersIds');
        }

        return {
            ...filterProps,
            filters: filterProps.filters.filter(
                (filter) => !filtersToExclude.has(filter.id),
            ),
        };
    }

    get columns(): DatatableProps<RiskWithCustomFieldsResponseDto>['columns'] {
        return [
            {
                id: 'risk-management-action-button',
                isActionColumn: true,
                cell: RiskManagementActionButtonCell,
            },
            {
                accessorKey: 'riskId',
                header: 'Risk code',
                id: 'code',
                enableSorting: true,
                cell: RiskCodeCell,
            },
            {
                accessorKey: 'title',
                header: 'Name',
                id: 'name',
                enableSorting: true,
                cell: RiskNameCell,
            },
            {
                accessorKey: 'controls',
                header: 'Mitigating controls',
                id: 'mitigatingControls',
                enableSorting: false,
                cell: MitigatingControlsCell,
            },
            {
                accessorKey: 'treatmentPlan',
                header: 'Treatment',
                id: 'treatment',
                enableSorting: true,
                cell: RiskTreatmentCell,
            },
            {
                accessorKey: 'impact',
                header: 'Inherent impact',
                id: 'inherentImpact',
                enableSorting: true,
                cell: InherentImpactCell,
            },
            {
                accessorKey: 'likelihood',
                header: 'Inherent likelihood',
                id: 'inherentLikelihood',
                enableSorting: true,
                cell: InherentLikelihoodCell,
            },
            {
                accessorKey: 'score',
                header: 'Inherent score',
                id: 'inherentScore',
                enableSorting: true,
                cell: InherentScoreCell,
            },
            {
                accessorKey: 'residualImpact',
                header: 'Residual impact',
                id: 'residualImpact',
                enableSorting: true,
                cell: ResidualImpactCell,
            },
            {
                accessorKey: 'residualLikelihood',
                header: 'Residual likelihood',
                id: 'residualLikelihood',
                enableSorting: true,
                cell: ResidualLikelihoodCell,
            },
            {
                accessorKey: 'residualScore',
                header: 'Residual score',
                id: 'residualScore',
                enableSorting: true,
                cell: ResidualScoreCell,
            },
            {
                accessorKey: 'owners',
                header: 'Owners',
                id: 'owners',
                enableSorting: true,
                cell: RiskOwnerCell,
            },
            {
                accessorKey: 'type',
                header: 'Type',
                id: 'isInternal',
                enableSorting: true,
                cell: IsInternalCell,
            },
            {
                accessorKey: 'categories',
                header: 'Categories',
                id: 'categories',
                enableSorting: true,
                cell: RiskCategoryCell,
            },
            {
                accessorKey: 'anticipatedCompletionDate',
                header: 'Targeted completion date',
                id: 'targetedCompletionDate',
                enableSorting: true,
                cell: TargetedCompletionDateCell,
            },
            {
                accessorKey: 'completionDate',
                header: 'Completed date',
                id: 'completedDate',
                enableSorting: true,
                cell: CompletedDateCell,
            },
        ];
    }

    onRowClick: DatatableProps<RiskWithCustomFieldsResponseDto>['onRowClick'] =
        ({ row }) => {
            sharedProgrammaticNavigationController.navigateTo(
                `${routeController.userPartOfUrl}/risk/management/registers/${routeController.currentParams.registerId}/register-risks/${row.riskId}/overview`,
            );
        };

    emptyStateProps: DatatableProps<RiskWithCustomFieldsResponseDto>['emptyStateProps'] =
        {
            title: t`No results found`,
            description: t`Try adjusting your search terms or filters.`,
        };

    tableSearchProps: DatatableProps<RiskWithCustomFieldsResponseDto>['tableSearchProps'] =
        {
            placeholder: t`Search by name, description, control code, or requirement`,
            debounceDelay: 1000,
            defaultValue: '',
        };

    get tableActions(): DatatableProps<RiskWithCustomFieldsResponseDto>['tableActions'] {
        const items: SchemaDropdownItemData[] = [];
        const { hasRiskManagePermission, isReleaseBulkImportRiskEnabled } =
            sharedFeatureAccessModel;

        if (hasRiskManagePermission) {
            items.push({
                id: 'create-risk',
                'data-testid': 'CreateRiskAction',
                label: t`Create a single risk`,
                type: 'item',
                onClick: () => {
                    sharedProgrammaticNavigationController.navigateTo(
                        `${routeController.userPartOfUrl}/risk/register/create-risk`,
                    );
                },
            });
        }

        if (isReleaseBulkImportRiskEnabled) {
            items.push({
                id: 'import-risks',
                'data-testid': 'ImportRiskAction',
                label: t`Create/update risks in bulk`,
                type: 'item',
                onClick: () => {
                    sharedFlatfileController.createSpace({
                        entityType: FlatfileEntityEnum.RISK,
                    });
                },
            });
        }

        return [
            {
                actionType: 'dropdown',
                id: 'add-risk-dropdown',
                typeProps: {
                    label: t`Add risk`,
                    endIconName: 'ChevronDown',
                    level: 'primary',
                    items,
                },
            },
        ];
    }
}

export const managementDatatableController =
    new ManagementDatatableController();
