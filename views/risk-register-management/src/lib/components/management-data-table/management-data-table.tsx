import { AppDatatable } from '@components/app-datatable';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { FirstUseEmptyState } from './components/first-use-empty-state';
import {
    managementDatatableController,
    type RiskManagementDatatableQuery,
} from './management-data-table.controller';

export const ManagementDataTable = observer((): JSX.Element => {
    if (managementDatatableController.isFirstUse) {
        return <FirstUseEmptyState />;
    }

    return (
        <AppDatatable<
            RiskWithCustomFieldsResponseDto,
            RiskManagementDatatableQuery
        >
            controller={managementDatatableController}
            data-testid="ManagementDataTable"
            data-id="QiFohnTC"
        />
    );
});
