import {
    sharedRiskManagementController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import type { Action } from '@cosmos/components/action-stack';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { DataPostureBox } from '@cosmos-lab/components/data-posture';
import type { RiskThresholdResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { generateRiskPostureBoxes } from '@views/risk-insights';

export class RegisterPostureCardModel {
    constructor() {
        makeAutoObservable(this);
    }

    handleRiskSourceChange = (selectedItem: ListBoxItemData): void => {
        sharedRiskManagementController.riskFilter =
            selectedItem.id === 'ALL'
                ? undefined
                : (selectedItem.id as 'INTERNAL_ONLY' | 'EXTERNAL_ONLY');

        sharedRiskManagementController.loadDashboard();
    };

    handleRiskScoreChange = (selectedItem: ListBoxItemData): void => {
        sharedRiskManagementController.scoreType = selectedItem.id as
            | 'RESIDUAL'
            | 'INHERENT';

        sharedRiskManagementController.loadDashboard();
    };

    get actions(): Action[] {
        return [
            {
                /**
                 * TODO: update this to use select-field when available.
                 * Ticket: https://drata.atlassian.net/browse/ENG-72892.
                 */
                actionType: 'select',
                id: 'risk-source-select-action',
                typeProps: {
                    id: 'risk-source-select',
                    name: 'risk-source',
                    'aria-labelledby': 'non-working-id-see-ENG-72892',
                    onChange: this.handleRiskSourceChange,
                    defaultValue: {
                        id: 'ALL',
                        label: t`All`,
                    },
                    options: [
                        {
                            id: 'ALL',
                            label: t`All`,
                        },
                        {
                            id: 'INTERNAL_ONLY',
                            label: t`Internal`,
                        },
                        {
                            id: 'EXTERNAL_ONLY',
                            label: t`External`,
                        },
                    ],
                },
            },
            {
                /**
                 * TODO: update this to use select-field when available.
                 * Ticket: https://drata.atlassian.net/browse/ENG-72892.
                 */
                actionType: 'select',
                id: 'risk-score-select-action',
                typeProps: {
                    id: 'risk-score-select',
                    name: 'risk-score',
                    'aria-labelledby': 'non-working-id-see-ENG-72892',
                    onChange: this.handleRiskScoreChange,
                    defaultValue: {
                        id: 'RESIDUAL',
                        label: t`Residual`,
                    },
                    options: [
                        {
                            id: 'RESIDUAL',
                            label: t`Residual`,
                        },
                        {
                            id: 'INHERENT',
                            label: t`Inherent`,
                        },
                    ],
                },
            },
        ];
    }

    get boxes(): DataPostureBox[] {
        const riskPosture =
            sharedRiskManagementController.dashboard?.riskPosture ?? {};

        const { thresholds } = sharedRiskSettingsController.riskSettings ?? {
            thresholds: [] as RiskThresholdResponseDto[],
        };

        const generatedBoxes = generateRiskPostureBoxes(
            riskPosture,
            thresholds,
        );

        const sortedThresholds = [...thresholds].sort(
            (a, b) => a.minThreshold - b.minThreshold,
        );

        return sortedThresholds
            .map((threshold) => {
                const foundBox = generatedBoxes.find((box) =>
                    box.id.includes(`-${threshold.id}`),
                );

                return foundBox ?? null;
            })
            .filter((box): box is NonNullable<typeof box> => box !== null);
    }
}

export const sharedRegisterPostureCardModel = new RegisterPostureCardModel();
