import { CreateRiskWizard } from '@components/create-risk-wizard';
import { sharedVendorsProfileRisksController } from '@controllers/vendors';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { observer } from '@globals/mobx';

export const VendorsProfileRisksAddView = observer((): React.JSX.Element => {
    const { currentVendorId } = sharedVendorsProfileRisksController;

    const isRiskManagerRV =
        sharedFeatureAccessModel.isRiskManagerWithRestrictedView;

    if (isRiskManagerRV) {
        throw new Error('Risk managers with restricted view cannot add risks');
        // TODO: implement proper error handling and redirection TALK TO @CHRIS
    }

    return (
        <CreateRiskWizard
            riskType="VENDOR_RISK"
            vendorId={currentVendorId}
            data-testid="VendorsProfileRisksAddView"
            data-id="0z3FonYM"
        />
    );
});
