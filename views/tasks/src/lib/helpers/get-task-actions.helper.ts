import { routeController } from '@controllers/route';
import { CustomTaskCompleteController } from '@controllers/tasks';
import type { Action } from '@cosmos/components/action-stack';
import type { UpcomingTaskDetailResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedTaskModalState } from '@models/tasks';
import { useNavigate } from '@remix-run/react';

const EDIT_TASK_TYPES = ['CONTROL', 'RISK', 'GENERAL'];

const getEditAction = (
    task: UpcomingTaskDetailResponseDto,
): Action | undefined => {
    if (!EDIT_TASK_TYPES.includes(task.type)) {
        return undefined;
    }

    const onEdit = () => {
        sharedTaskModalState
            .openModal({
                taskId: task.id,
                configurationId: task.configurationId,
                scheduleDate: task.scheduleDate,
            })
            .catch((error) => {
                // Errors are handled in the modal
                console.error('Error opening task modal:', error);
            });
    };

    return {
        id: 'edit-task-button',
        actionType: 'button',
        typeProps: {
            'data-id': 'edit-task-button',
            startIconName: 'Edit',
            label: t`Edit`,
            isIconOnly: true,
            level: 'tertiary',
            colorScheme: 'neutral',
            onClick: onEdit,
        },
    };
};

const useReviewAction = (
    task: UpcomingTaskDetailResponseDto,
    entityId?: string,
): Action | undefined => {
    const navigate = useNavigate();
    const userPart = routeController.userPartOfUrl;

    let route = '';

    if (task.type === 'POLICY_APPROVALS' || task.type === 'POLICY_RENEWALS') {
        route = `${userPart}/governance/policies/builder/${task.id}/overview`;
    }

    if (task.type === 'EVIDENCE' || task.type === 'EXTERNAL_EVIDENCE') {
        route = `${userPart}/compliance/evidence/${task.id}`;
    }

    if (task.type === 'VENDOR') {
        route = `${userPart}/vendors/current/${task.id}/overview`;
    }

    if (task.type === 'CONTROL' || task.type === 'CONTROL_APPROVALS') {
        route = `${userPart}/compliance/controls/${entityId}/overview`;
    }

    if (task.type === 'RISK') {
        /*
         * TODO: this navigation has to go to /risk/management/registers/[register_id]/register-risks/[task-id]
         * That created a problem because we don't have the register_id outside of /risk/management/registers/[register_id].
         *
         * At the moment it was decided to hardcode the register_id to 1 since that's all tenants will have.
         *
         * See: https://drata.atlassian.net/browse/ENG-72803
         */
        route = `${userPart}/risk/management/registers/1/register-risks/${entityId}/overview`;
    }

    if (!route) {
        return undefined;
    }

    // TODO: converted to a link and remove the navigate use
    return {
        actionType: 'button',
        id: 'review-button',
        typeProps: {
            href: route,
            label: t`Review`,
            level: 'secondary',
            colorScheme: 'neutral',
            onClick: () => {
                navigate(route);
            },
        },
    };
};

const getCompleteAction = (
    task: UpcomingTaskDetailResponseDto,
): Action | undefined => {
    if (!EDIT_TASK_TYPES.includes(task.type)) {
        return undefined;
    }

    const completeController = new CustomTaskCompleteController();

    const onToggleComplete = () => {
        completeController
            .completeTask(
                {
                    taskId: task.id,
                    configurationId: task.configurationId,
                    scheduleDate: task.scheduleDate,
                },
                task.completedAt,
            )
            .catch((error) => {
                // Errors are handled in the controller
                console.error('Error toggling task completion:', error);
            });
    };

    if (task.completedAt) {
        return {
            id: 'mark-as-incomplete-button',
            actionType: 'button',
            typeProps: {
                'data-id': 'mark-as-incomplete-button',
                startIconName: 'CheckCircle',
                label: t`Mark as incomplete`,
                isIconOnly: true,
                level: 'tertiary',
                colorScheme: 'neutral',
                isLoading: completeController.isLoading,
                onClick: onToggleComplete,
            },
        };
    }

    return {
        id: 'mark-as-completed-button',
        actionType: 'button',
        typeProps: {
            'data-id': 'mark-as-completed-button',
            isIconOnly: true,
            startIconName: 'NotStarted',
            size: 'md',
            level: 'primary',
            colorScheme: 'primary',
            label: t`Mark as completed`,
            isLoading: completeController.isLoading,
            onClick: onToggleComplete,
        },
    };
};

export const useTaskActions = action(
    (task: UpcomingTaskDetailResponseDto, entityId?: string): Action[] => {
        const actions = [
            getEditAction(task),
            useReviewAction(task, entityId),
            getCompleteAction(task),
        ];

        return actions.filter((a) => a !== undefined);

        // return [
        //     {
        //         id: 'review-task',
        //         actionType: 'button',
        //         typeProps: {
        //             label: t`Review`,
        //             size: 'md',
        //             level: 'secondary',
        //             colorScheme: 'primary',
        //             onClick: () => {
        //                 console.info('Review task', task.id);
        //             },
        //         },
        //     },
    },
);
