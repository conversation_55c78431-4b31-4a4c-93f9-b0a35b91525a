import {
    sharedControlsController,
    sharedControlsCustomReportController,
    sharedControlsDownloadController,
} from '@controllers/controls';
import type { TableAction } from '@cosmos/components/datatable';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

export class ControlsListTableActionsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get filterViewAction(): SchemaDropdownItemData[] {
        const { hasActiveFilters, currentAppliedFilters, total } =
            sharedControlsController;
        const { downloadControls } = sharedControlsDownloadController;

        if (!hasActiveFilters || total === 0) {
            return [];
        }

        return [
            {
                id: 'filtered-view-item',
                label: t`Filtered view`,
                startIconName: 'File',
                onSelect: () => {
                    if (currentAppliedFilters) {
                        downloadControls(currentAppliedFilters);
                    }
                },
            },
        ];
    }

    get downloadAllAction(): SchemaDropdownItemData[] {
        const { downloadControls } = sharedControlsDownloadController;

        return [
            {
                id: 'all-controls-item',
                label: t`All controls`,
                startIconName: 'File',
                onSelect: () => {
                    downloadControls();
                },
            },
        ];
    }

    get downloadCustomReportAction(): SchemaDropdownItemData[] {
        const { isShowSyncronCustomReports } = sharedFeatureAccessModel;
        const { loadControlsCustomReports } =
            sharedControlsCustomReportController;

        if (!isShowSyncronCustomReports) {
            return [];
        }

        return [
            {
                id: 'report-item',
                label: t`Report`,
                startIconName: 'File',
                onSelect: loadControlsCustomReports,
            },
        ];
    }

    get tableActions(): TableAction[] {
        const { isDownloadControlEnabled } = sharedFeatureAccessModel;

        if (!isDownloadControlEnabled) {
            return [];
        }

        return [
            {
                actionType: 'dropdown',
                id: 'controls-list-download-dropdown',
                typeProps: {
                    startIconName: 'Download',
                    colorScheme: 'neutral',
                    level: 'tertiary',
                    label: t`Download`,
                    items: [
                        {
                            id: 'csv-download',
                            label: t`Download CSV`,
                            type: 'group',
                            items: [
                                ...this.downloadAllAction,
                                ...this.filterViewAction,
                                ...this.downloadCustomReportAction,
                            ],
                        },
                    ],
                },
            },
        ];
    }
}
