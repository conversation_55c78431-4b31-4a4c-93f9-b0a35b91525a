import type { FilterProps } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import {
    RequirementIndexCategory,
    RequirementIndexSubcategory,
} from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

/**
 * Gets ISO270182019 category options with labels and values.
 */
function getISO270182019CategoryOptions() {
    return [
        {
            label: t`Information Security Policies`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.ISO_INFORMATION_SECURITY_POLICIES
            ],
        },
        {
            label: t`Organization of Information Security`,
            value: RequirementIndexCategory[
                RequirementIndexCategory
                    .ISO_ORGANIZATION_OF_INFORMATION_SECURITY
            ],
        },
        {
            label: t`Human Resources Security`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.ISO_HUMAN_RESOURCES_SECURITY
            ],
        },
        {
            label: t`Access Control`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.ISO_ACCESS_CONTROL
            ],
        },
        {
            label: t`Cryptography`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.ISO_CRYPTOGRAPHY
            ],
        },
        {
            label: t`Physical and Environmental Security`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.ISO_PHYSICAL_AND_ENVIRONMENTAL_SECURITY
            ],
        },
        {
            label: t`Operations Security`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.ISO_OPERATIONS_SECURITY
            ],
        },
        {
            label: t`Communications Security`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.ISO_COMMUNICATIONS_SECURITY
            ],
        },
        {
            label: t`Information Security Incident Management`,
            value: RequirementIndexCategory[
                RequirementIndexCategory
                    .ISO_INFORMATION_SECURITY_INCIDENT_MANAGEMENT
            ],
        },
        {
            label: t`Compliance`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.ISO_COMPLIANCE
            ],
        },
        {
            label: t`PII (27018 Annex A)`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.ISO270182019_PII
            ],
        },
    ];
}

/**
 * Gets ISO270182019 subcategory options for PII (27018 Annex A).
 */
function getISO270182019SubcategoryOptions() {
    return [
        {
            label: t`Consent and Choice`,
            value: RequirementIndexSubcategory[
                RequirementIndexSubcategory.ISO270182019_CONSENT_AND_CHOICE
            ],
        },
        {
            label: t`Purpose Legitimacy and Specification`,
            value: RequirementIndexSubcategory[
                RequirementIndexSubcategory
                    .ISO270182019_PURPOSE_LEGITIMACY_AND_SPECIFICATION
            ],
        },
        {
            label: t`Data Minimization`,
            value: RequirementIndexSubcategory[
                RequirementIndexSubcategory.ISO270182019_DATA_MINIMIZATION
            ],
        },
        {
            label: t`Use, Retention and Disclosure Limitation`,
            value: RequirementIndexSubcategory[
                RequirementIndexSubcategory
                    .ISO270182019_USE_RETENTION_AND_DISCLOSURE_LIMITATION
            ],
        },
        {
            label: t`Accuracy and Quality`,
            value: RequirementIndexSubcategory[
                RequirementIndexSubcategory.ISO270182019_ACCURACY_AND_QUALITY
            ],
        },
        {
            label: t`Accountability`,
            value: RequirementIndexSubcategory[
                RequirementIndexSubcategory.ISO270182019_ACCOUNTABILITY
            ],
        },
        {
            label: t`Information Security`,
            value: RequirementIndexSubcategory[
                RequirementIndexSubcategory.ISO270182019_INFORMATION_SECURITY
            ],
        },
        {
            label: t`Privacy Compliance`,
            value: RequirementIndexSubcategory[
                RequirementIndexSubcategory.ISO270182019_PRIVACY_COMPLIANCE
            ],
        },
    ];
}

/**
 * Creates a subcategory filter for ISO270182019 PII (27018 Annex A) category.
 *
 * @param selectedCategory - The selected category value to determine if subcategories should be shown.
 * @returns Subcategory filter object or null if PII (27018 Annex A) category is not selected.
 */
function createSubcategoryFilter(selectedCategory?: string): Filter | null {
    // Only show subcategories for PII (27018 Annex A) category
    if (
        selectedCategory !==
        RequirementIndexCategory[RequirementIndexCategory.ISO270182019_PII]
    ) {
        return null;
    }

    // Find the category label from the options
    const categoryOption = getISO270182019CategoryOptions().find(
        (option) => option.value === selectedCategory,
    );

    return {
        filterType: 'radio',
        id: `subcategory-${selectedCategory}`,
        label: categoryOption?.label || t`Subcategory`,
        parentId: 'category',
        options: getISO270182019SubcategoryOptions(),
    };
}

/**
 * Gets ISO270182019 requirements filters with dynamic subcategory filter.
 *
 * @param selectedCategory - Optional selected category to show relevant subcategories.
 * @returns FilterProps configuration for ISO270182019 requirements.
 */
export function getISO270182019RequirementsFilters(
    selectedCategory?: string,
): FilterProps {
    const filters = [createCategoryFilter(getISO270182019CategoryOptions())];

    // Add subcategory filter only if PII (27018 Annex A) category is selected
    const subcategoryFilter = createSubcategoryFilter(selectedCategory);

    if (subcategoryFilter) {
        filters.push(subcategoryFilter);
    }

    return createRequirementFilters(filters);
}
