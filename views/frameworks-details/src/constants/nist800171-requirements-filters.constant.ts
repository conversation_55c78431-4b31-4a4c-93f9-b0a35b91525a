import { isEmpty } from 'lodash-es';
import type { FilterProps } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import {
    RequirementIndexCategory,
    RequirementIndexSubcategory,
} from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

/**
 * Gets subcategory options for a specific NIST 800-171 category.
 */
function getNIST800171SubcategoryOptions(category: RequirementIndexCategory) {
    switch (category) {
        case RequirementIndexCategory.NIST800171R2_TECHNICAL: {
            return [
                {
                    label: t`Access Control`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.NIST800171R2_ACCESS_CONTROL
                    ],
                },
                {
                    label: t`Audit and Accountability`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R2_AUDIT_AND_ACCOUNTABILITY
                    ],
                },
                {
                    label: t`Identification and Authentication`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R2_IDENTIFICATION_AND_AUTHENTICATION
                    ],
                },
                {
                    label: t`System and Communications Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R2_SYSTEM_AND_COMMUNICATIONS_PROTECTION
                    ],
                },
            ];
        }
        case RequirementIndexCategory.NIST800171R2_OPERATIONAL: {
            return [
                {
                    label: t`Awareness and Training`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R2_AWARENESS_AND_TRAINING
                    ],
                },
                {
                    label: t`Configuration Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R2_CONFIGURATION_MANAGEMENT
                    ],
                },
                {
                    label: t`Incident Response`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R2_INCIDENT_RESPONSE
                    ],
                },
                {
                    label: t`Maintenance`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.NIST800171R2_MAINTENANCE
                    ],
                },
                {
                    label: t`Media Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R2_MEDIA_PROTECTION
                    ],
                },
                {
                    label: t`Personnel Security`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R2_PERSONNEL_SECURITY
                    ],
                },
                {
                    label: t`System and Information Integrity`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R2_SYSTEM_AND_INFORMATION_INTEGRITY
                    ],
                },
            ];
        }
        case RequirementIndexCategory.NIST800171R2_MANAGEMENT: {
            return [
                {
                    label: t`Risk Assessment`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.NIST800171R2_RISK_ASSESSMENT
                    ],
                },
                {
                    label: t`Security Assessment`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R2_SECURITY_ASSESSMENT
                    ],
                },
            ];
        }
        default: {
            return [];
        }
    }
}

/**
 * Gets NIST 800-171 category options with labels and values.
 */
function getNIST800171CategoryOptions() {
    return [
        {
            label: t`Technical`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.NIST800171R2_TECHNICAL
            ],
        },
        {
            label: t`Operational`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.NIST800171R2_OPERATIONAL
            ],
        },
        {
            label: t`Management`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.NIST800171R2_MANAGEMENT
            ],
        },
    ];
}

/**
 * Creates a subcategory filter based on the selected category.
 *
 * @param selectedCategory - The selected category value to determine subcategory options.
 * @returns Subcategory filter object or null if no category is selected.
 */
function createSubcategoryFilter(selectedCategory?: string): Filter | null {
    if (!selectedCategory) {
        return null;
    }

    const categoryEnum = RequirementIndexCategory[
        selectedCategory as keyof typeof RequirementIndexCategory
    ] as RequirementIndexCategory;

    const options = getNIST800171SubcategoryOptions(categoryEnum);

    if (isEmpty(options)) {
        return null;
    }

    // Find the category label from the options
    const categoryOption = getNIST800171CategoryOptions().find(
        (option) => option.value === selectedCategory,
    );

    return {
        filterType: 'radio',
        id: `subcategory-${selectedCategory}`,
        label: categoryOption?.label || t`Subcategory`,
        parentId: 'category',
        options,
    };
}

/**
 * Gets NIST 800-171 requirements filters with optional dynamic subcategory filter.
 *
 * @param selectedCategory - Optional selected category to show relevant subcategories.
 * @returns FilterProps configuration for NIST 800-171 requirements.
 */
export function getNIST800171RequirementsFilters(
    selectedCategory?: string,
): FilterProps {
    const filterLabel = t`Control Class`;

    const filters: Filter[] = [
        createCategoryFilter(getNIST800171CategoryOptions(), filterLabel),
    ];

    // Add subcategory filter if a category is selected
    const subcategoryFilter = createSubcategoryFilter(selectedCategory);

    if (subcategoryFilter) {
        filters.push(subcategoryFilter);
    }

    return createRequirementFilters(filters);
}
