import { isEmpty } from 'lodash-es';
import type { FilterProps } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import {
    RequirementIndexCategory,
    RequirementIndexSubcategory,
} from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

/**
 * Gets subcategory options for a specific NIST 800-171 R3 category.
 */
function getNIST800171R3SubcategoryOptions(category: RequirementIndexCategory) {
    switch (category) {
        case RequirementIndexCategory.NIST800171R3_TECHNICAL: {
            return [
                {
                    label: t`Access Control`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.NIST800171R3_ACCESS_CONTROL
                    ],
                },
                {
                    label: t`Audit & Accountability`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R3_AUDIT_AND_ACCOUNTABILITY
                    ],
                },
                {
                    label: t`Identification & Authentication`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R3_IDENTIFICATION_AND_AUTHENTICATION
                    ],
                },
                {
                    label: t`System & Communications Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R3_SYSTEM_AND_COMMUNICATIONS_PROTECTION
                    ],
                },
            ];
        }
        case RequirementIndexCategory.NIST800171R3_OPERATIONAL: {
            return [
                {
                    label: t`Awareness & Training`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R3_AWARENESS_AND_TRAINING
                    ],
                },
                {
                    label: t`Configuration Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R3_CONFIGURATION_MANAGEMENT
                    ],
                },
                {
                    label: t`Incident Response`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R3_INCIDENT_RESPONSE
                    ],
                },
                {
                    label: t`Maintenance`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.NIST800171R3_MAINTENANCE
                    ],
                },
                {
                    label: t`Media Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R3_MEDIA_PROTECTION
                    ],
                },
                {
                    label: t`Personnel Security`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R3_PERSONNEL_SECURITY
                    ],
                },
                {
                    label: t`Physical Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R3_PHYSICAL_PROTECTION
                    ],
                },
                {
                    label: t`System & Information Integrity`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R3_SYSTEM_AND_INFORMATION_INTEGRITY
                    ],
                },
            ];
        }
        case RequirementIndexCategory.NIST800171R3_MANAGEMENT: {
            return [
                {
                    label: t`Planning`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.NIST800171R3_PLANNING
                    ],
                },
                {
                    label: t`Risk Assessment`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.NIST800171R3_RISK_ASSESSMENT
                    ],
                },
                {
                    label: t`Security Assessment & Monitoring`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R3_SECURITY_ASSESSMENT_AND_MONITORING
                    ],
                },
                {
                    label: t`Supply Chain Risk Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R3_SUPPLY_CHAIN_RISK_MANAGEMENT
                    ],
                },
                {
                    label: t`System & Services Acquisition`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NIST800171R3_SYSTEM_AND_SERVICES_ACQUISITION
                    ],
                },
            ];
        }
        default: {
            return [];
        }
    }
}

/**
 * Gets NIST 800-171 R3 category options with labels and values.
 */
function getNIST800171R3CategoryOptions() {
    return [
        {
            label: t`Technical`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.NIST800171R3_TECHNICAL
            ],
        },
        {
            label: t`Operational`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.NIST800171R3_OPERATIONAL
            ],
        },
        {
            label: t`Management`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.NIST800171R3_MANAGEMENT
            ],
        },
    ];
}

/**
 * Creates a subcategory filter based on the selected category.
 *
 * @param selectedCategory - The selected category value to determine subcategory options.
 * @returns Subcategory filter object or null if no category is selected.
 */
function createSubcategoryFilter(selectedCategory?: string): Filter | null {
    if (!selectedCategory) {
        return null;
    }

    const categoryEnum = RequirementIndexCategory[
        selectedCategory as keyof typeof RequirementIndexCategory
    ] as RequirementIndexCategory;

    const options = getNIST800171R3SubcategoryOptions(categoryEnum);

    if (isEmpty(options)) {
        return null;
    }

    // Find the category label from the options
    const categoryOption = getNIST800171R3CategoryOptions().find(
        (option) => option.value === selectedCategory,
    );

    return {
        filterType: 'radio',
        id: `subcategory-${selectedCategory}`,
        label: categoryOption?.label || t`Subcategory`,
        parentId: 'category',
        options,
    };
}

/**
 * Gets NIST 800-171 R3 requirements filters with optional dynamic subcategory filter.
 *
 * @param selectedCategory - Optional selected category to show relevant subcategories.
 * @returns FilterProps configuration for NIST 800-171 R3 requirements.
 */
export function getNIST800171R3RequirementsFilters(
    selectedCategory?: string,
): FilterProps {
    const filterLabel = t`Control Class`;

    const filters: Filter[] = [
        createCategoryFilter(getNIST800171R3CategoryOptions(), filterLabel),
    ];

    // Add subcategory filter if a category is selected
    const subcategoryFilter = createSubcategoryFilter(selectedCategory);

    if (subcategoryFilter) {
        filters.push(subcategoryFilter);
    }

    return createRequirementFilters(filters);
}
