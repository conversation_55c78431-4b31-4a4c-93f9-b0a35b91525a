import { isEmpty } from 'lodash-es';
import type { FilterProps } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import {
    RequirementIndexCategory,
    RequirementIndexSubcategory,
    RequirementIndexTag,
} from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

/**
 * Gets subcategory options for a specific CMMC category.
 */
function getCMMCSubcategoryOptions(category: RequirementIndexCategory) {
    switch (category) {
        case RequirementIndexCategory.CMMC_TECHNICAL: {
            return [
                {
                    label: t`Access Control`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.CMMC_ACCESS_CONTROL
                    ],
                },
                {
                    label: t`Audit and Accountability`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .CMMC_AUDIT_AND_ACCOUNTABILITY
                    ],
                },
                {
                    label: t`Identification and Authentication`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .CMMC_IDENTIFICATION_AND_AUTHENTICATION
                    ],
                },
                {
                    label: t`System and Communications Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .CMMC_SYSTEM_AND_COMMUNICATIONS_PROTECTION
                    ],
                },
            ];
        }
        case RequirementIndexCategory.CMMC_OPERATIONAL: {
            return [
                {
                    label: t`Awareness and Training`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.CMMC_AWARENESS_AND_TRAINING
                    ],
                },
                {
                    label: t`Configuration Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .CMMC_CONFIGURATION_MANAGEMENT
                    ],
                },
                {
                    label: t`Incident Response`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.CMMC_INCIDENT_RESPONSE
                    ],
                },
                {
                    label: t`Maintenance`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.CMMC_MAINTENANCE
                    ],
                },
                {
                    label: t`Media Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.CMMC_MEDIA_PROTECTION
                    ],
                },
                {
                    label: t`Physical Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.CMMC_PHYSICAL_PROTECTION
                    ],
                },
                {
                    label: t`Personnel Security`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.CMMC_PERSONNEL_SECURITY
                    ],
                },
                {
                    label: t`Recovery`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.CMMC_RECOVERY
                    ],
                },
                {
                    label: t`System and Information Integrity`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .CMMC_SYSTEM_AND_INFORMATION_INTEGRITY
                    ],
                },
            ];
        }
        case RequirementIndexCategory.CMMC_MANAGEMENT: {
            return [
                {
                    label: t`Risk Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.CMMC_RISK_MANAGEMENT
                    ],
                },
                {
                    label: t`Security Assessment`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.CMMC_SECURITY_ASSESSMENT
                    ],
                },
            ];
        }
        default: {
            return [];
        }
    }
}

/**
 * Gets CMMC category options with labels and values.
 */
function getCMMCCategoryOptions() {
    return [
        {
            label: t`Technical`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.CMMC_TECHNICAL
            ],
        },
        {
            label: t`Operational`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.CMMC_OPERATIONAL
            ],
        },
        {
            label: t`Management`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.CMMC_MANAGEMENT
            ],
        },
    ];
}

/**
 * Creates a level filter for CMMC.
 */
function createLevelFilter(): Filter {
    return {
        filterType: 'radio',
        id: 'level',
        label: t`Level`,
        options: [
            {
                label: t`Level 1`,
                value: RequirementIndexTag[RequirementIndexTag.LEVEL_1],
            },
            {
                label: t`Level 2`,
                value: RequirementIndexTag[RequirementIndexTag.LEVEL_2],
            },
        ],
    };
}

/**
 * Creates a subcategory filter based on the selected category.
 *
 * @param selectedCategory - The selected category value to determine subcategory options.
 * @returns Subcategory filter object or null if no category is selected.
 */
function createSubcategoryFilter(selectedCategory?: string): Filter | null {
    if (!selectedCategory) {
        return null;
    }

    const categoryEnum = RequirementIndexCategory[
        selectedCategory as keyof typeof RequirementIndexCategory
    ] as RequirementIndexCategory;

    const options = getCMMCSubcategoryOptions(categoryEnum);

    if (isEmpty(options)) {
        return null;
    }

    // Find the category label from the options
    const categoryOption = getCMMCCategoryOptions().find(
        (option) => option.value === selectedCategory,
    );
    const categoryLabel = categoryOption?.label ?? t`Subcategory`;

    return {
        filterType: 'radio',
        id: `subcategory-${selectedCategory}`,
        label: categoryLabel,
        parentId: 'category',
        options,
    };
}

/**
 * Gets CMMC requirements filters with optional dynamic subcategory filter.
 *
 * @param selectedCategory - Optional selected category to show relevant subcategories.
 * @returns FilterProps configuration for CMMC requirements.
 */
export function getCMMCRequirementsFilters(
    selectedCategory?: string,
): FilterProps {
    const filterLabel = t`Control class`;

    const filters: Filter[] = [
        createLevelFilter(),
        createCategoryFilter(getCMMCCategoryOptions(), filterLabel),
    ];

    // Add subcategory filter if a category is selected
    const subcategoryFilter = createSubcategoryFilter(selectedCategory);

    if (subcategoryFilter) {
        filters.push(subcategoryFilter);
    }

    return createRequirementFilters(filters);
}
