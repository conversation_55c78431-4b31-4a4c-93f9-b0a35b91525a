import { isEmpty } from 'lodash-es';
import type { FilterProps } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import {
    RequirementIndexCategory,
    RequirementIndexSubcategory,
    RequirementIndexTag,
    TrustServiceCriteria,
} from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

/**
 * Gets subcategory options for a specific NIST 80053 category.
 */
function getNIST80053SubcategoryOptions(category: RequirementIndexCategory) {
    switch (category) {
        case RequirementIndexCategory.NISTSP80053_TECHNICAL: {
            return [
                {
                    label: t`Access Control`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.NISTSP80053_ACCESS_CONTROL
                    ],
                },
                {
                    label: t`Audit and Accountability`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_AUDIT_AND_ACCOUNTABILITY
                    ],
                },
                {
                    label: t`Identification and Authentication`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_IDENTIFICATION_AND_AUTHENTICATION
                    ],
                },
                {
                    label: t`System and Communications Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_SYSTEM_AND_COMMUNICATIONS_PROTECTION
                    ],
                },
            ];
        }
        case RequirementIndexCategory.NISTSP80053_OPERATIONAL: {
            return [
                {
                    label: t`Awareness and Training`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_AWARENESS_AND_TRAINING
                    ],
                },
                {
                    label: t`Configuration Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_CONFIGURATION_MANAGEMENT
                    ],
                },
                {
                    label: t`Contingency Planning`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_CONTINGENCY_PLANNING
                    ],
                },
                {
                    label: t`Incident Response`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_INCIDENT_RESPONSE
                    ],
                },
                {
                    label: t`Maintenance`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.NISTSP80053_MAINTENANCE
                    ],
                },
                {
                    label: t`Media Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.NISTSP80053_MEDIA_PROTECTION
                    ],
                },
                {
                    label: t`Physical and Environmental Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_PHYSICAL_AND_ENVIRONMENTAL_PROTECTION
                    ],
                },
                {
                    label: t`Personnel Security`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_PERSONNEL_SECURITY
                    ],
                },
                {
                    label: t`System and Information Integrity`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_SYSTEM_AND_INFORMATION_INTEGRITY
                    ],
                },
            ];
        }
        case RequirementIndexCategory.NISTSP80053_MANAGEMENT: {
            return [
                {
                    label: t`Assessment, Authorization, and Monitoring`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_ASSESSMENT_AUTHORIZATION_AND_MONITORING
                    ],
                },
                {
                    label: t`PII Processing and Transparency`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_PII_PROCESSING_AND_TRANSPARENCY
                    ],
                },
                {
                    label: t`Program Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_PROGRAM_MANAGEMENT
                    ],
                },
                {
                    label: t`Planning`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.NISTSP80053_PLANNING
                    ],
                },
                {
                    label: t`Risk Assessment`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.NISTSP80053_RISK_ASSESSMENT
                    ],
                },
                {
                    label: t`System and Services Acquisition`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_SYSTEM_AND_SERVICES_ACQUISITION
                    ],
                },
                {
                    label: t`Supply Chain Risk Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .NISTSP80053_SUPPLY_CHAIN_RISK_MANAGEMENT
                    ],
                },
            ];
        }
        default: {
            return [];
        }
    }
}

/**
 * Gets NIST 80053 category options with labels and values.
 */
function getNIST80053CategoryOptions() {
    return [
        {
            label: t`Technical`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.NISTSP80053_TECHNICAL
            ],
        },
        {
            label: t`Operational`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.NISTSP80053_OPERATIONAL
            ],
        },
        {
            label: t`Management`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.NISTSP80053_MANAGEMENT
            ],
        },
    ];
}

/**
 * Creates a privacy filter for NIST 80053.
 */
function createTopicFilter(): Filter {
    return {
        filterType: 'radio',
        id: 'topic',
        label: t`Topic`,
        options: [
            {
                label: t`Privacy`,
                value: TrustServiceCriteria[
                    TrustServiceCriteria.NIST80053_PRIVACY
                ],
            },
        ],
    };
}

/**
 * Creates a control baseline filter for NIST 80053.
 */
function createControlBaselineFilter(): Filter {
    return {
        filterType: 'radio',
        id: 'level',
        label: t`Control Baseline`,
        options: [
            {
                label: t`Security - Low`,
                value: RequirementIndexTag[RequirementIndexTag.SECURITY_LOW],
            },
            {
                label: t`Security - Moderate`,
                value: RequirementIndexTag[
                    RequirementIndexTag.SECURITY_MODERATE
                ],
            },
            {
                label: t`Security - High`,
                value: RequirementIndexTag[RequirementIndexTag.SECURITY_HIGH],
            },
        ],
    };
}

/**
 * Creates a subcategory filter based on the selected category.
 *
 * @param selectedCategory - The selected category value to determine subcategory options.
 * @returns Subcategory filter object or null if no category is selected.
 */
function createSubcategoryFilter(selectedCategory?: string): Filter | null {
    if (!selectedCategory) {
        return null;
    }

    const categoryEnum = RequirementIndexCategory[
        selectedCategory as keyof typeof RequirementIndexCategory
    ] as RequirementIndexCategory;

    const options = getNIST80053SubcategoryOptions(categoryEnum);

    if (isEmpty(options)) {
        return null;
    }

    // Find the category label from the options
    const categoryOption = getNIST80053CategoryOptions().find(
        (option) => option.value === selectedCategory,
    );

    return {
        filterType: 'radio',
        id: `subcategory-${selectedCategory}`,
        label: categoryOption?.label || t`Subcategory`,
        parentId: 'category',
        options,
    };
}

/**
 * Gets NIST 80053 requirements filters with control baseline and optional dynamic subcategory filter.
 *
 * @param selectedCategory - Optional selected category to show relevant subcategories.
 * @returns FilterProps configuration for NIST 80053 requirements.
 */
export function getNIST80053RequirementsFilters(
    selectedCategory?: string,
): FilterProps {
    const filterLabel = t`Control class`;

    const filters: Filter[] = [
        createTopicFilter(),
        createControlBaselineFilter(),
        createCategoryFilter(getNIST80053CategoryOptions(), filterLabel),
    ];

    // Add subcategory filter if a category is selected
    const subcategoryFilter = createSubcategoryFilter(selectedCategory);

    if (subcategoryFilter) {
        filters.push(subcategoryFilter);
    }

    return createRequirementFilters(filters);
}
