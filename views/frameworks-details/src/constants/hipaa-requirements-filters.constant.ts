import type { FilterProps } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import { RequirementIndexCategory, TrustServiceCriteria } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

/**
 * Gets HIPAA category options with labels and values.
 */
function getHIPAACategoryOptions() {
    return [
        {
            label: t`Breach Notification`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.HIPAA_BREACH_NOTIFICATION
            ],
        },
        {
            label: t`Privacy`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.HIPAA_PRIVACY
            ],
        },
        {
            label: t`Security`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.HIPAA_SECURITY
            ],
        },
    ];
}

/**
 * Gets HIPAA subcategory options.
 */
function getHIPAASubcategoryOptions() {
    return [
        {
            label: t`General Rules`,
            value: TrustServiceCriteria[TrustServiceCriteria.GENERAL_RULES],
        },
        {
            label: t`Administrative Safeguards`,
            value: TrustServiceCriteria[
                TrustServiceCriteria.ADMINISTRATIVE_SAFEGUARDS
            ],
        },
        {
            label: t`Physical Safeguards`,
            value: TrustServiceCriteria[
                TrustServiceCriteria.PHYSICAL_SAFEGUARDS
            ],
        },
        {
            label: t`Technical Safeguards`,
            value: TrustServiceCriteria[
                TrustServiceCriteria.TECHNICAL_SAFEGUARDS
            ],
        },
        {
            label: t`Requirements - Organization`,
            value: TrustServiceCriteria[
                TrustServiceCriteria.REQUIREMENTS_ORGANIZATION
            ],
        },
        {
            label: t`Requirements - Policies, Procedures, Documentation`,
            value: TrustServiceCriteria[
                TrustServiceCriteria.REQUIREMENTS_POLICIES_PROCEDURES
            ],
        },
    ];
}

/**
 * Creates a subcategory filter for HIPAA Security category.
 *
 * @param selectedCategory - The selected category value to determine if subcategories should be shown.
 * @returns Subcategory filter object or null if Security category is not selected.
 */
function createSubcategoryFilter(selectedCategory?: string): Filter | null {
    // Only show subcategories for Security category
    if (
        selectedCategory !==
        RequirementIndexCategory[RequirementIndexCategory.HIPAA_SECURITY]
    ) {
        return null;
    }

    // Find the category label from the options
    const categoryOption = getHIPAACategoryOptions().find(
        (option) => option.value === selectedCategory,
    );

    return {
        filterType: 'radio',
        id: `topic-${selectedCategory}`,
        label: categoryOption?.label || t`Subcategory`,
        parentId: 'category',
        options: getHIPAASubcategoryOptions(),
    };
}

/**
 * Gets HIPAA requirements filters with dynamic subcategory filter.
 *
 * @param selectedCategory - Optional selected category to show relevant subcategories.
 * @returns FilterProps configuration for HIPAA requirements.
 */
export function getHIPAARequirementsFilters(
    selectedCategory?: string,
): FilterProps {
    const filterLabel = t`Rules`;

    const filters = [
        createCategoryFilter(getHIPAACategoryOptions(), filterLabel),
    ];

    // Add subcategory filter only if Security category is selected
    const subcategoryFilter = createSubcategoryFilter(selectedCategory);

    if (subcategoryFilter) {
        filters.push(subcategoryFilter);
    }

    return createRequirementFilters(filters);
}
