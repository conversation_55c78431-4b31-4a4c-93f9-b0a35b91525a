import { isEmpty } from 'lodash-es';
import type { FilterProps } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import {
    RequirementIndexCategory,
    RequirementIndexSubcategory,
} from '@drata/enums';
import { t } from '@globals/i18n/macro';
import {
    createCategoryFilter,
    createRequirementFilters,
} from './base-requirements-filters.constant';

/**
 * Gets category options for FedRAMP.
 */
function getFEDRAMPCategoryOptions() {
    return [
        {
            label: t`Technical`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.FEDRAMP_TECHNICAL
            ],
        },
        {
            label: t`Operational`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.FEDRAMP_OPERATIONAL
            ],
        },
        {
            label: t`Management`,
            value: RequirementIndexCategory[
                RequirementIndexCategory.FEDRAMP_MANAGEMENT
            ],
        },
    ];
}

/**
 * Gets subcategory options for a specific FedRAMP category.
 */
function getFEDRAMPSubcategoryOptions(category: RequirementIndexCategory) {
    switch (category) {
        case RequirementIndexCategory.FEDRAMP_TECHNICAL: {
            return [
                {
                    label: t`Access Control`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.FEDRAMP_ACCESS_CONTROL
                    ],
                },
                {
                    label: t`Audit and Accountability`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .FEDRAMP_AUDIT_AND_ACCOUNTABILITY
                    ],
                },
                {
                    label: t`Identification and Authentication`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .FEDRAMP_IDENTIFICATION_AND_AUTHENTICATION
                    ],
                },
                {
                    label: t`System and Communications Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .FEDRAMP_SYSTEM_AND_COMMUNICATIONS_PROTECTION
                    ],
                },
            ];
        }
        case RequirementIndexCategory.FEDRAMP_OPERATIONAL: {
            return [
                {
                    label: t`Awareness and Training`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .FEDRAMP_AWARENESS_AND_TRAINING
                    ],
                },
                {
                    label: t`Configuration Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .FEDRAMP_CONFIGURATION_MANAGEMENT
                    ],
                },
                {
                    label: t`Contingency Planning`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.FEDRAMP_CONTINGENCY_PLANNING
                    ],
                },
                {
                    label: t`Incident Response`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.FEDRAMP_INCIDENT_RESPONSE
                    ],
                },
                {
                    label: t`Maintenance`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.FEDRAMP_MAINTENANCE
                    ],
                },
                {
                    label: t`Media Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.FEDRAMP_MEDIA_PROTECTION
                    ],
                },
                {
                    label: t`Physical and Environmental Protection`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .FEDRAMP_PHYSICAL_AND_ENVIRONMENTAL_PROTECTION
                    ],
                },
                {
                    label: t`Personnel Security`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.FEDRAMP_PERSONNEL_SECURITY
                    ],
                },
                {
                    label: t`System and Information Integrity`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .FEDRAMP_SYSTEM_AND_INFORMATION_INTEGRITY
                    ],
                },
            ];
        }
        case RequirementIndexCategory.FEDRAMP_MANAGEMENT: {
            return [
                {
                    label: t`Assessment, Authorization, and Monitoring`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .FEDRAMP_ASSESSMENT_AUTHORIZATION_AND_MONITORING
                    ],
                },
                {
                    label: t`Planning`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.FEDRAMP_PLANNING
                    ],
                },
                {
                    label: t`Risk Assessment`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory.FEDRAMP_RISK_ASSESSMENT
                    ],
                },
                {
                    label: t`System and Services Acquisition`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .FEDRAMP_SYSTEM_AND_SERVICES_ACQUISITION
                    ],
                },
                {
                    label: t`Supply Chain Risk Management`,
                    value: RequirementIndexSubcategory[
                        RequirementIndexSubcategory
                            .FEDRAMP_SUPPLY_CHAIN_RISK_MANAGEMENT
                    ],
                },
            ];
        }
        default: {
            return [];
        }
    }
}

/**
 * Creates a subcategory filter based on the selected category.
 *
 * @param selectedCategory - The selected category value to determine subcategory options.
 * @returns Subcategory filter object or null if no category is selected.
 */
function createSubcategoryFilter(selectedCategory?: string): Filter | null {
    if (!selectedCategory) {
        return null;
    }

    const categoryEnum = RequirementIndexCategory[
        selectedCategory as keyof typeof RequirementIndexCategory
    ] as RequirementIndexCategory;

    const options = getFEDRAMPSubcategoryOptions(categoryEnum);

    if (isEmpty(options)) {
        return null;
    }

    // Find the category label from the options
    const categoryOption = getFEDRAMPCategoryOptions().find(
        (option) => option.value === selectedCategory,
    );

    return {
        filterType: 'radio',
        id: `subcategory-${selectedCategory}`,
        label: categoryOption?.label || t`Subcategory`,
        parentId: 'category',
        options,
    };
}

/**
 * Creates a parameters filter for FedRAMP.
 */
function createParametersFilter(): Filter {
    return {
        filterType: 'radio',
        id: 'isParameterCompleted',
        label: t`Parameters`,
        options: [
            {
                label: t`Complete`,
                value: 'true',
            },
            {
                label: t`Incomplete`,
                value: 'false',
            },
        ],
    };
}

/**
 * Gets FedRAMP requirements filters with optional dynamic subcategory filter.
 *
 * @param selectedCategory - Optional selected category to show relevant subcategories.
 * @returns FilterProps configuration for FedRAMP requirements.
 */
export function getFEDRAMPRequirementsFilters(
    selectedCategory?: string,
): FilterProps {
    const filterLabel = t`Control class`;

    const filters: Filter[] = [
        createCategoryFilter(getFEDRAMPCategoryOptions(), filterLabel),
    ];

    // Add subcategory filter if a category is selected
    const subcategoryFilter = createSubcategoryFilter(selectedCategory);

    if (subcategoryFilter) {
        filters.push(subcategoryFilter);
    }

    filters.push(createParametersFilter());

    return createRequirementFilters(filters);
}
