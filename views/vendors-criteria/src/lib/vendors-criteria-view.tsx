import { sharedVendorsCriteriaController } from '@controllers/vendors';
import {
    Datatable,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';

export const VendorsCriteriaView = observer((): React.JSX.Element => {
    const { currentWorkspace } = sharedWorkspacesController;
    const navigate = useNavigate();

    // Check if VRM Agent MVP feature is enabled
    if (!sharedFeatureAccessModel.isVrmAgentMvpEnabled) {
        navigate(`/workspaces/${currentWorkspace?.id}/vendors/current`);
    }

    const handleFetchData = (params: FetchDataResponseParams) => {
        // Store table params in controller for future use
        sharedVendorsCriteriaController.tableParams = params;
        // Implement actual data loading
    };

    const handleRowClick = ({ row }: { row: { id?: string | number } }) => {
        const { id } = row;

        runInAction(() => {
            if (currentWorkspace?.id && id) {
                navigate(
                    `/workspaces/${currentWorkspace.id}/vendors/criteria/${id}`,
                );
            }
        });
    };

    return (
        <Datatable
            isLoading={sharedVendorsCriteriaController.isLoading}
            tableId="datatable-vendors-criteria"
            data={sharedVendorsCriteriaController.criteriaData}
            data-id="datatable-vendors-criteria-data-id"
            columns={[]}
            total={sharedVendorsCriteriaController.total}
            tableSearchProps={{
                placeholder: t`Search criteria`,
                hideSearch: false,
                debounceDelay: 500,
                defaultValue: '',
            }}
            onRowClick={handleRowClick}
            onFetchData={handleFetchData}
        />
    );
});
