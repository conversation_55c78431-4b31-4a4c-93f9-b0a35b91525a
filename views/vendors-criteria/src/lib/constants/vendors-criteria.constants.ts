import type { ComponentProps } from 'react';
import type { ActionStack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';
import { runInAction } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export const VENDORS_CRITERIA_PAGE_HEADER_KEY = 'vendors-criteria-header';

export const getVendorsCriteriaPageHeaderActions = (
    navigate: (path: string) => void,
): ComponentProps<typeof ActionStack>['actions'] => {
    // Check if user has permission to manage security criteria
    // const hasManagePermission =
    //     sharedFeatureAccessModel....;

    // If user doesn't have ... permission, don't show any actions
    // if (!hasManagePermission) {
    //     return [];
    // }

    return [
        {
            actionType: 'dropdown',
            id: `${VENDORS_CRITERIA_PAGE_HEADER_KEY}-add-action`,
            typeProps: {
                label: t`Add criteria`,
                endIconName: 'ChevronDown',
                align: 'end',

                items: [
                    {
                        id: `${VENDORS_CRITERIA_PAGE_HEADER_KEY}-dropdown-from-scratch`,
                        label: t`From scratch`,
                        type: 'item',
                        value: 'ADD_SINGLE',
                        description: t`Create from a list of criteria types`,
                        onSelect: () => {
                            runInAction(() => {
                                const { currentWorkspace } =
                                    sharedWorkspacesController;

                                if (!currentWorkspace?.id) {
                                    return;
                                }

                                const workspaceId = currentWorkspace.id;

                                navigate(
                                    `/workspaces/${workspaceId}/vendors/criteria`,
                                );
                            });
                        },
                    },
                ],
            },
        },
    ];
};
