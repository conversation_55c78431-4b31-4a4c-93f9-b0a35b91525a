import { isNil } from 'lodash-es';
import { useCallback, useEffect, useMemo } from 'react';
import { sharedFrameworkCreateController } from '@controllers/frameworks';
import { routeController } from '@controllers/route';
import {
    Wizard,
    type WizardStepDataProps,
} from '@cosmos-lab/components/wizard';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { useLingui } from '@globals/i18n/macro';
import { action, flowResult, observer, runInAction, when } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import { useLocation, useNavigate } from '@remix-run/react';
import { useFormSubmit } from '@ui/forms';
import { CreateFrameworkWizardConfirmUploadStep } from './create-framework-wizard-confirm-upload-step.component';
import { CreateFrameworkWizardDetailsStep } from './create-framework-wizard-create-step.component';
import { CreateFrameworkWizardConfirmStep } from './create-framework-wizard-finish-step';
import { CreateFrameworkWizardUploadRequirementsStep } from './create-framework-wizard-upload-requirements-step.component';

export const CreateFrameworkView = observer((): React.JSX.Element => {
    const location = useLocation();
    const { pathname } = location;
    const parentRoute = getParentRoute(pathname);
    const navigate = useNavigate();
    const { t } = useLingui();

    const { hasReachedLimit, frameworkCreateModel } =
        sharedFrameworkCreateController;
    const { isCustomFrameworksEnabled } = sharedEntitlementFlagController;

    useEffect(() => {
        if (hasReachedLimit || !isCustomFrameworksEnabled) {
            navigate(parentRoute);
        }
    }, [navigate, parentRoute, hasReachedLimit, isCustomFrameworksEnabled]);

    const goBackToFrameworks = useCallback((): void => {
        action(() => {
            frameworkCreateModel.resetAll();
            navigate(parentRoute);
        })();
    }, [navigate, parentRoute, frameworkCreateModel]);

    const goToFrameworkDetails = useCallback((): void => {
        runInAction(() => {
            const { createdFrameworkId } = sharedFrameworkCreateController;
            const { userPartOfUrl } = routeController;

            when(
                () => !isNil(createdFrameworkId),
                () => {
                    navigate(
                        `${userPartOfUrl}/compliance/frameworks/all/current/${createdFrameworkId}/requirements`,
                    );
                },
            );
        });
    }, [navigate]);

    const createFrameworkFormSubmit = useFormSubmit();

    const handleValidateDetailsStep = useCallback(async () => {
        return action(async () => {
            const isFormValid = await createFrameworkFormSubmit.triggerSubmit();

            if (!isFormValid) {
                return false;
            }

            return sharedFrameworkCreateController.validateDetailsStep();
        })();
    }, [createFrameworkFormSubmit]);

    const steps: WizardStepDataProps[] = useMemo(
        () => [
            {
                component: () => (
                    <CreateFrameworkWizardDetailsStep
                        formRef={createFrameworkFormSubmit.formRef}
                        data-id="Qs6RvXHw"
                        data-testid="WrappedDetailsStep"
                    />
                ),
                stepTitle: t`Enter framework details`,
                isStepSkippable: false,
                onStepChange: handleValidateDetailsStep,
            },
            {
                component: CreateFrameworkWizardUploadRequirementsStep,
                stepTitle: t`Upload requirements`,
                isStepSkippable: true,
                skipButtonLabelOverride: t`Add requirements later`,
                onStepChange: action(() =>
                    flowResult(
                        sharedFrameworkCreateController.handleUploadStep(),
                    ),
                ),
            },
            {
                component: CreateFrameworkWizardConfirmUploadStep,
                stepTitle: t`Confirm requirements`,
                isStepSkippable: false,
                onStepChange: action(() =>
                    flowResult(
                        sharedFrameworkCreateController.handleCreateFramework(),
                    ),
                ),
            },
            {
                component: CreateFrameworkWizardConfirmStep,
                stepTitle: t`Finish`,
                isStepSkippable: false,
                canGoBack: false,
            },
        ],
        [t, handleValidateDetailsStep, createFrameworkFormSubmit.formRef],
    );

    return (
        <Wizard
            steps={steps}
            data-testid="CreateFrameworkView"
            data-id="Rloh_HB8"
            completeButtonLabel={t`Done`}
            onCancel={goBackToFrameworks}
            onComplete={goToFrameworkDetails}
        />
    );
});
