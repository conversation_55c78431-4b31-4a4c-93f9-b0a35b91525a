import { get, isNil, noop } from 'lodash-es';
import { useMemo } from 'react';
import { sharedUserDetailsController } from '@controllers/user-details';
import { sharedUserRisksController } from '@controllers/user-risks';
import type { Stack as ActionStackProps } from '@cosmos/components/action-stack';
import {
    PanelBody,
    PanelControls,
    PanelHeader,
} from '@cosmos/components/panel';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AssignedControlsAccordionComponent } from './components/assigned-controls-accordion.component';
import { AssignedPoliciesAccordionComponent } from './components/assigned-policies-accordion.component';
import { AssignedRisksAccordionComponent } from './components/assigned-risks-accordion.component';
import { AssignedRolesAccordionComponent } from './components/assigned-roles-accordion.component';
import { AssignedVendorsComponent } from './components/assigned-vendors.component';
import { RoleAdministrationAccordionComponent } from './components/role-administration-accordion.component';

const PANEL_CONTROL_PAGINATION = {
    currentItem: 2,
    onNextPageClick: noop,
    onPrevPageClick: noop,
    totalItems: 10,
} as const;

const ASSIGNED_ROLES_STACKS: ActionStackProps[] = [
    {
        actions: [
            {
                actionType: 'button',
                id: 'role-administration-user-details-assigned-roles-remove-button',
                typeProps: {
                    label: 'Remove all',
                    colorScheme: 'danger',
                    level: 'tertiary',
                    onClick: noop,
                },
            },
            {
                actionType: 'button',
                id: 'role-administration-user-details-assigned-roles-add-button',
                typeProps: {
                    label: 'Add role',
                    level: 'secondary',
                    onClick: noop,
                },
            },
        ],
        id: 'role-administration-user-details-assigned-roles-stack',
    },
];

const ASSIGNED_CONTROLS_STACKS: ActionStackProps[] = [
    {
        actions: [
            {
                actionType: 'button',
                id: 'role-administration-user-details-assigned-controls-go-to-button',
                typeProps: {
                    label: 'Go to Controls',
                    level: 'tertiary',
                    href: '/compliance/controls',
                },
            },
        ],
        id: 'role-administration-user-details-assigned-controls-stack',
    },
];

const ASSIGNED_POLICIES_STACKS: ActionStackProps[] = [
    {
        actions: [
            {
                actionType: 'button',
                id: 'role-administration-user-details-assigned-policies-go-to-button',
                typeProps: {
                    label: 'Go to Policies',
                    level: 'tertiary',
                    href: '/governance/policies/active',
                },
            },
        ],
        id: 'role-administration-user-details-assigned-policies-stack',
    },
];

const ASSIGNED_VENDORS_STACKS: ActionStackProps[] = [
    {
        actions: [
            {
                actionType: 'button',
                id: 'role-administration-user-details-assigned-vendors-go-to-button',
                typeProps: {
                    label: 'Go to Vendors',
                    level: 'tertiary',
                    href: '/vendors/current',
                },
            },
        ],
        id: 'role-administration-user-details-assigned-vendors-stack',
    },
];

export const SettingsRoleAdministrationUserDetailsView = observer(
    (): React.JSX.Element | null => {
        const {
            userOwnedControlsIsLoading,
            userOwnedPoliciesIsLoading,
            userOwnedVendorsIsLoading,
            userAssignedRolesIsLoading,
            userDetailsIsLoading,
            userOwnedControls,
            userOwnedPolicies,
            userOwnedVendors,
            userAssignedRoles,
            userDetails,
        } = sharedUserDetailsController;

        const { userRisks } = sharedUserRisksController;

        const isLoading: boolean = useMemo(
            () =>
                userOwnedControlsIsLoading ||
                userOwnedPoliciesIsLoading ||
                userOwnedVendorsIsLoading ||
                userAssignedRolesIsLoading ||
                userDetailsIsLoading,
            [
                userOwnedControlsIsLoading,
                userOwnedPoliciesIsLoading,
                userOwnedVendorsIsLoading,
                userAssignedRolesIsLoading,
                userDetailsIsLoading,
            ],
        );

        const isRoleModifiable = useMemo(() => {
            return !userAssignedRoles
                .flatMap((roles) => roles.roles)
                .some(
                    (role) =>
                        role === 'WORKSPACE_ADMINISTRATOR' ||
                        role === 'SERVICE_USER',
                );
        }, [userAssignedRoles]);

        const userFullName = useMemo(() => {
            const userName = get(userDetails, 'firstName', '');
            const userLastName = get(userDetails, 'lastName', '');

            return isNil(userName) || isNil(userLastName)
                ? 'Pending name setup'
                : `${userName} ${userLastName}`;
        }, [userDetails]);

        if (isLoading) {
            return null;
        }

        return (
            <Stack
                direction="column"
                data-testid="SettingsRoleAdministrationUserDetailsView"
                data-id="L4Cgb_AF"
            >
                <PanelControls
                    closeButtonLabel="Close"
                    data-id="role-administration-user-details-panel"
                    pagination={PANEL_CONTROL_PAGINATION}
                    onClose={noop}
                />
                <PanelHeader
                    data-id="role-administration-user-details-panel-header"
                    title={userFullName}
                />
                <PanelBody data-id="role-administration-user-details-panel-body">
                    <Stack direction="column" gap="2xl">
                        <RoleAdministrationAccordionComponent
                            title="Assigned roles"
                            headerStacks={
                                isRoleModifiable ? ASSIGNED_ROLES_STACKS : []
                            }
                            quantity={
                                userAssignedRoles.flatMap(
                                    (roles) => roles.roles,
                                ).length
                            }
                        >
                            <AssignedRolesAccordionComponent
                                roles={userAssignedRoles.flatMap(
                                    (roles) => roles.roles,
                                )}
                            />
                        </RoleAdministrationAccordionComponent>

                        <Divider />

                        <RoleAdministrationAccordionComponent
                            title="Assigned controls"
                            quantity={userOwnedControls.length}
                            headerStacks={ASSIGNED_CONTROLS_STACKS}
                        >
                            <AssignedControlsAccordionComponent
                                controls={userOwnedControls}
                            />
                        </RoleAdministrationAccordionComponent>

                        <Divider />

                        <RoleAdministrationAccordionComponent
                            title="Assigned policies"
                            quantity={userOwnedPolicies.length}
                            headerStacks={ASSIGNED_POLICIES_STACKS}
                        >
                            <AssignedPoliciesAccordionComponent
                                policies={userOwnedPolicies}
                            />
                        </RoleAdministrationAccordionComponent>

                        <Divider />

                        <RoleAdministrationAccordionComponent
                            title="Assigned risks"
                            quantity={userRisks.length}
                            headerStacks={[
                                {
                                    actions: [
                                        {
                                            actionType: 'button',
                                            id: 'role-administration-user-details-assigned-risks-go-to-button',
                                            typeProps: {
                                                label: t`Go to Risks`,
                                                level: 'tertiary',
                                                /*
                                                 * TODO: this navigation has to go to /risk/management/registers/[register_id]/register-risks/[task-id]
                                                 * That created a problem because we don't have the register_id outside of /risk/management/registers/[register_id].
                                                 *
                                                 * At the moment it was decided to hardcode the register_id to 1 since that's all tenants will have.
                                                 *
                                                 * See: https://drata.atlassian.net/browse/ENG-72803
                                                 */
                                                href: `/risk/management/registers/1/register-risks`,
                                            },
                                        },
                                    ],
                                    id: 'role-administration-user-details-assigned-risks-stack',
                                },
                            ]}
                        >
                            <AssignedRisksAccordionComponent
                                risks={userRisks}
                            />
                        </RoleAdministrationAccordionComponent>

                        <Divider />

                        <RoleAdministrationAccordionComponent
                            title="Assigned vendors"
                            quantity={userOwnedVendors.length}
                            headerStacks={ASSIGNED_VENDORS_STACKS}
                        >
                            <AssignedVendorsComponent
                                vendors={userOwnedVendors}
                            />
                        </RoleAdministrationAccordionComponent>
                    </Stack>
                </PanelBody>
            </Stack>
        );
    },
);
