import { isNumber, isString } from 'lodash-es';
import {
    afterEach,
    beforeAll,
    beforeEach,
    describe,
    expect,
    test,
    vi,
} from 'vitest';

// Mock dependencies
const mockModalController = {
    closeModal: vi.fn(),
};

const mockSnackbarController = {
    addSnackbar: vi.fn(),
};

const mockVendorsController = {
    formModel: {
        updateFormField: vi.fn(),
        formData: {
            questions: [],
        },
    },
    showImportBanner: vi.fn(),
};

const mockParseQuestionnaireCSV = vi.fn();

vi.mock('@controllers/modal', () => ({
    modalController: mockModalController,
}));

vi.mock('@controllers/snackbar', () => ({
    snackbarController: mockSnackbarController,
}));

vi.mock('@controllers/vendors', () => ({
    sharedVendorsQuestionnaireAddController: mockVendorsController,
}));

vi.mock('@globals/i18n/macro', () => ({
    t: (strings: TemplateStringsArray, ...values: unknown[]) => {
        let result = '';

        for (const [index, string] of strings.entries()) {
            result = result + string;

            const value = values[index];

            if (isString(value) || isNumber(value)) {
                result = result + String(value);
            }
        }

        return result;
    },
}));

vi.mock('@globals/mobx', () => ({
    runInAction: vi.fn((fn: () => unknown) => fn()),
}));

vi.mock('./csv-parser.helper.js', () => ({
    parseQuestionnaireCSV: mockParseQuestionnaireCSV,
}));

describe('questionnaire import helper', () => {
    let downloadQuestionnaireTemplate: (() => void) | undefined;
    let uploadQuestionnaireFile: ((file: File) => Promise<unknown>) | undefined;

    // Mock window.open
    const mockWindowOpen = vi.fn();

    beforeAll(() => {
        Object.defineProperty(window, 'open', {
            writable: true,
            value: mockWindowOpen,
        });
    });

    beforeEach(async () => {
        // Reset all mocks
        vi.clearAllMocks();

        // Import the functions to test
        try {
            const module = await import('./questionnaire-import.helper');

            downloadQuestionnaireTemplate =
                module.downloadQuestionnaireTemplate;
            uploadQuestionnaireFile = module.uploadQuestionnaireFile;
        } catch {
            // Expected in TDD red phase
            downloadQuestionnaireTemplate = undefined;
            uploadQuestionnaireFile = undefined;
        }
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('downloadQuestionnaireTemplate', () => {
        test('should be defined', () => {
            expect(downloadQuestionnaireTemplate).toBeDefined();
        });

        test('should open template URL in new tab', () => {
            downloadQuestionnaireTemplate?.();

            expect(window.open).toHaveBeenCalledWith(
                'https://cdn.drata.com/templates/vendors/questionnaire/Import Questionnaire Template.csv',
                '_blank',
                // cspell:disable-next-line
                'noopener,noreferrer',
            );
        });

        test('should show success snackbar when download starts', () => {
            downloadQuestionnaireTemplate?.();

            expect(mockSnackbarController.addSnackbar).toHaveBeenCalledWith({
                id: 'questionnaire-template-download',
                props: {
                    title: 'Template download started',
                    description:
                        'The questionnaire template is being downloaded',
                    severity: 'success',
                    closeButtonAriaLabel: 'Close',
                },
            });
        });

        test('should show error snackbar when window.open throws', () => {
            mockWindowOpen.mockImplementation(() => {
                throw new Error('Failed to open');
            });

            downloadQuestionnaireTemplate?.();

            expect(mockSnackbarController.addSnackbar).toHaveBeenCalledWith({
                id: 'questionnaire-template-download-error',
                props: {
                    title: 'Download failed',
                    description:
                        'Unable to download the questionnaire template. Please try again.',
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });
        });
    });

    describe('uploadQuestionnaireFile', () => {
        test('should be defined', () => {
            expect(uploadQuestionnaireFile).toBeDefined();
        });

        test('should parse CSV file and update form with questions', async () => {
            const mockFile = new File(['test'], 'test.csv', {
                type: 'text/csv',
            });
            const mockResult = {
                importedQuestions: [
                    {
                        ref: 'q1',
                        title: 'Test Question',
                        type: 'Yes/No',
                        shortAnswerType: '',
                        required: true,
                        choices: [],
                        followUpQn: '',
                        allowOtherChoice: false,
                        includeFollowUpQn: false,
                        followUpQnTrigger: false,
                    },
                ],
                failedQuestions: [],
            };

            mockParseQuestionnaireCSV.mockResolvedValue(mockResult);

            const result = await uploadQuestionnaireFile?.(mockFile);

            expect(mockParseQuestionnaireCSV).toHaveBeenCalledWith(mockFile);
            expect(
                mockVendorsController.formModel.updateFormField,
            ).toHaveBeenCalledWith(
                'questions',
                expect.arrayContaining([
                    expect.objectContaining({
                        title: 'Test Question',
                        type: 'YES_NO',
                        required: true,
                    }),
                ]),
            );
            expect(result).toStrictEqual(mockResult);
        });

        test('should show warning when no questions found', async () => {
            const mockFile = new File(['test'], 'test.csv', {
                type: 'text/csv',
            });
            const mockResult = {
                importedQuestions: [],
                failedQuestions: [],
            };

            mockParseQuestionnaireCSV.mockResolvedValue(mockResult);

            await uploadQuestionnaireFile?.(mockFile);

            expect(mockSnackbarController.addSnackbar).toHaveBeenCalledWith({
                id: 'questionnaire-upload-no-questions',
                props: {
                    title: 'No questions found',
                    description:
                        'The uploaded file contains no valid questions.',
                    severity: 'warning',
                    closeButtonAriaLabel: 'Close',
                },
            });
        });

        test('should store success data in localStorage when all questions imported successfully', async () => {
            const mockFile = new File(['test'], 'test.csv', {
                type: 'text/csv',
            });
            const mockResult = {
                importedQuestions: [
                    {
                        ref: 'q1',
                        title: 'Test Question',
                        type: 'Long Answer',
                        shortAnswerType: '',
                        required: true,
                        choices: [],
                        followUpQn: '',
                        allowOtherChoice: false,
                        includeFollowUpQn: false,
                        followUpQnTrigger: false,
                    },
                ],
                failedQuestions: [],
            };

            mockParseQuestionnaireCSV.mockResolvedValue(mockResult);

            await uploadQuestionnaireFile?.(mockFile);

            // Should store data in localStorage instead of showing immediate snackbar
            const storedData = localStorage.getItem(
                'questionnaire-import-data',
            );

            expect(storedData).toBeTruthy();

            const parsedData = JSON.parse(storedData as string) as {
                message: { title: string; severity: string };
                questions: unknown[];
            };

            expect(parsedData.message.title).toBe(
                'Questions imported successfully',
            );
            expect(parsedData.message.severity).toBe('success');
            expect(parsedData.questions).toHaveLength(1);
        });

        test('should store warning data in localStorage when questions need attention', async () => {
            const mockFile = new File(['test'], 'test.csv', {
                type: 'text/csv',
            });
            const mockResult = {
                importedQuestions: [
                    {
                        ref: 'q1',
                        title: 'Test Question',
                        type: 'Short Answer',
                        shortAnswerType: '', // Missing short answer type
                        required: true,
                        choices: [],
                        followUpQn: '',
                        allowOtherChoice: false,
                        includeFollowUpQn: false,
                        followUpQnTrigger: false,
                    },
                ],
                failedQuestions: [],
            };

            mockParseQuestionnaireCSV.mockResolvedValue(mockResult);

            await uploadQuestionnaireFile?.(mockFile);

            // Should store warning data in localStorage instead of showing immediate banner
            const storedData = localStorage.getItem(
                'questionnaire-import-data',
            );

            expect(storedData).toBeTruthy();

            const parsedData = JSON.parse(storedData as string) as {
                message: { title: string; severity: string };
                questionsWithIssues: number[];
            };

            expect(parsedData.message.title).toBe(
                '1 questions imported. Some need your attention.',
            );
            expect(parsedData.message.severity).toBe('warning');
            expect(parsedData.questionsWithIssues).toStrictEqual([1]);
        });

        test('should handle localized question types correctly', async () => {
            const mockFile = new File(['test'], 'test.csv', {
                type: 'text/csv',
            });
            const mockResult = {
                importedQuestions: [
                    {
                        ref: 'q1',
                        title: 'Yes/No Question',
                        type: 'Yes/No', // English Yes/No (will be translated in real usage)
                        shortAnswerType: '',
                        required: true,
                        choices: [],
                        followUpQn: '',
                        allowOtherChoice: false,
                        includeFollowUpQn: false,
                        followUpQnTrigger: false,
                    },
                    {
                        ref: 'q2',
                        title: 'Short Answer Question',
                        type: 'Short Answer', // English Short Answer (will be translated in real usage)
                        shortAnswerType: 'text',
                        required: false,
                        choices: [],
                        followUpQn: '',
                        allowOtherChoice: false,
                        includeFollowUpQn: false,
                        followUpQnTrigger: false,
                    },
                ],
                failedQuestions: [],
            };

            mockParseQuestionnaireCSV.mockResolvedValue(mockResult);

            await uploadQuestionnaireFile?.(mockFile);

            expect(
                mockVendorsController.formModel.updateFormField,
            ).toHaveBeenCalledWith(
                'questions',
                expect.arrayContaining([
                    expect.objectContaining({
                        title: 'Yes/No Question',
                        type: 'YES_NO', // Should be mapped correctly
                        required: true,
                    }),
                    expect.objectContaining({
                        title: 'Short Answer Question',
                        type: 'SHORT_ANSWER', // Should be mapped correctly
                        required: false,
                    }),
                ]),
            );
        });

        test('should close modal after successful import', async () => {
            const mockFile = new File(['test'], 'test.csv', {
                type: 'text/csv',
            });
            const mockResult = {
                importedQuestions: [
                    {
                        ref: 'q1',
                        title: 'Test Question',
                        type: 'Yes/No',
                        shortAnswerType: '',
                        required: true,
                        choices: [],
                        followUpQn: '',
                        allowOtherChoice: false,
                        includeFollowUpQn: false,
                        followUpQnTrigger: false,
                    },
                ],
                failedQuestions: [],
            };

            mockParseQuestionnaireCSV.mockResolvedValue(mockResult);

            await uploadQuestionnaireFile?.(mockFile);

            expect(mockModalController.closeModal).toHaveBeenCalledWith(
                'import-questions-modal',
            );
        });

        test('should filter out questions with invalid types and empty titles', async () => {
            const mockFile = new File(['test'], 'test.csv', {
                type: 'text/csv',
            });
            const mockResult = {
                importedQuestions: [
                    {
                        ref: 'q1',
                        title: 'Valid Question',
                        type: 'Yes/No',
                        shortAnswerType: '',
                        required: true,
                        choices: [],
                        followUpQn: '',
                        allowOtherChoice: false,
                        includeFollowUpQn: false,
                        followUpQnTrigger: false,
                    },
                    {
                        ref: 'q2',
                        title: '', // Empty title - should be filtered
                        type: 'Yes/No',
                        shortAnswerType: '',
                        required: true,
                        choices: [],
                        followUpQn: '',
                        allowOtherChoice: false,
                        includeFollowUpQn: false,
                        followUpQnTrigger: false,
                    },
                    {
                        ref: 'q3',
                        title: 'Invalid Type Question',
                        type: 'Invalid Type', // Invalid type - should be filtered
                        shortAnswerType: '',
                        required: true,
                        choices: [],
                        followUpQn: '',
                        allowOtherChoice: false,
                        includeFollowUpQn: false,
                        followUpQnTrigger: false,
                    },
                    {
                        ref: 'q4',
                        title: 'Another Valid Question',
                        type: 'Long Answer',
                        shortAnswerType: '',
                        required: false,
                        choices: [],
                        followUpQn: '',
                        allowOtherChoice: false,
                        includeFollowUpQn: false,
                        followUpQnTrigger: false,
                    },
                ],
                failedQuestions: [],
            };

            mockParseQuestionnaireCSV.mockResolvedValue(mockResult);

            await uploadQuestionnaireFile?.(mockFile);

            // Should only have 2 valid questions (q1 and q4)
            expect(
                mockVendorsController.formModel.updateFormField,
            ).toHaveBeenCalledWith(
                'questions',
                expect.arrayContaining([
                    expect.objectContaining({
                        title: 'Valid Question',
                        type: 'YES_NO',
                    }),
                    expect.objectContaining({
                        title: 'Another Valid Question',
                        type: 'LONG_ANSWER',
                    }),
                ]),
            );

            // Should store warning data in localStorage for failed questions (q2 and q3)
            const storedData = localStorage.getItem(
                'questionnaire-import-data',
            );

            expect(storedData).toBeTruthy();

            const parsedData = JSON.parse(storedData as string) as {
                message: {
                    severity: string;
                    title: string;
                    description: string;
                };
                questionsWithIssues: number[];
            };

            expect(parsedData.message.severity).toBe('warning');
            expect(parsedData.message.title).toContain(
                'Some need your attention',
            );
            expect(parsedData.message.description).toContain('2, 3');
            expect(parsedData.questionsWithIssues).toStrictEqual([2, 3]);
        });
    });
});
