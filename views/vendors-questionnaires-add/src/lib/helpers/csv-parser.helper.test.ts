import { beforeEach, describe, expect, test, vi } from 'vitest';
import { QUESTIONNAIRE_TEMPLATE_HEADER_ENUM } from '../types/questionnaire-import.types';
import { parseQuestionnaireCSV } from './csv-parser.helper';

/**
 * Helper functions moved to outer scope.
 */
const createMockFile = (content: string): File => {
    return new File([content], 'test.csv', { type: 'text/csv' });
};

const createValidCSVContent = (): string => {
    const headers = Object.values(QUESTIONNAIRE_TEMPLATE_HEADER_ENUM).join(',');
    const rows = [
        headers,
        '"What is your name?","Short Answer","text","Yes"',
        '"What is your email?","Short Answer","email","Yes"',
        '"Select your department","Multiple Choice","HR;IT;Finance","No"',
        '"Do you agree?","Yes/No","","Yes"',
    ];

    return rows.join('\n');
};

// Mock FileReader
class MockFileReader implements Partial<FileReader> {
    onload: ((event: ProgressEvent<FileReader>) => void) | null = null;
    onerror: ((event: ProgressEvent<FileReader>) => void) | null = null;
    result: string | null = null;
    error: DOMException | null = null;

    readAsText(): void {
        // Simulate async file reading
        setTimeout(() => {
            if (this.onload) {
                this.onload({
                    target: this,
                } as unknown as ProgressEvent<FileReader>);
            }
        }, 0);
    }

    simulateError(errorMessage: string): void {
        this.error = new DOMException(errorMessage);
        setTimeout(() => {
            if (this.onerror) {
                this.onerror({
                    target: this,
                } as unknown as ProgressEvent<FileReader>);
            }
        }, 0);
    }
}

// Setup global FileReader mock
vi.mock('global', () => ({
    FileReader: MockFileReader,
}));

describe('csv-parser.helper', () => {
    let mockFileReader: MockFileReader;

    beforeEach(() => {
        vi.clearAllMocks();
        // Create a new mock instance for each test
        mockFileReader = new MockFileReader();
        vi.spyOn(global, 'FileReader').mockImplementation(
            () => mockFileReader as unknown as FileReader,
        );
    });

    describe('parseQuestionnaireCSV', () => {
        test('should successfully parse valid CSV file', async () => {
            const csvContent = createValidCSVContent();
            const file = createMockFile(csvContent);

            mockFileReader.result = csvContent;

            const result = await parseQuestionnaireCSV(file);

            expect(result.importedQuestions).toHaveLength(4);
            // Note: Some questions might be flagged as needing attention
            expect(result.failedQuestions.length).toBeGreaterThanOrEqual(0);

            // Check first question
            const firstQuestion = result.importedQuestions[0];

            expect(firstQuestion.title).toBe('What is your name?');
            expect(firstQuestion.type).toBe('Short Answer');
            expect(firstQuestion.required).toBeTruthy();
            expect(firstQuestion.shortAnswerType).toBe('text');
            expect(firstQuestion.choices).toHaveLength(0);
        });

        test('should parse multiple choice questions with choices', async () => {
            const csvContent = createValidCSVContent();
            const file = createMockFile(csvContent);

            mockFileReader.result = csvContent;

            const result = await parseQuestionnaireCSV(file);

            // Check multiple choice question
            const multipleChoiceQuestion = result.importedQuestions[2];

            expect(multipleChoiceQuestion.title).toBe('Select your department');
            expect(multipleChoiceQuestion.type).toBe('Multiple Choice');
            expect(multipleChoiceQuestion.required).toBeFalsy();
            expect(multipleChoiceQuestion.choices).toHaveLength(3);
            expect(multipleChoiceQuestion.choices[0].label).toBe('HR');
            expect(multipleChoiceQuestion.choices[1].label).toBe('IT');
            expect(multipleChoiceQuestion.choices[2].label).toBe('Finance');
        });

        test('should handle short answer subtypes correctly', async () => {
            const headers = Object.values(
                QUESTIONNAIRE_TEMPLATE_HEADER_ENUM,
            ).join(',');
            const csvContent = [
                headers,
                '"Email question","Short Answer","email","Yes"',
                '"URL question","Short Answer","website url","Yes"',
                '"Phone question","Short Answer","phone number","Yes"',
                '"Text question","Short Answer","text","Yes"',
                '"Unclear question","Short Answer","choose one from list","Yes"',
            ].join('\n');

            const file = createMockFile(csvContent);

            mockFileReader.result = csvContent;

            const result = await parseQuestionnaireCSV(file);

            expect(result.importedQuestions).toHaveLength(5);
            expect(result.importedQuestions[0].shortAnswerType).toBe('email');
            expect(result.importedQuestions[1].shortAnswerType).toBe(
                'website url',
            );
            expect(result.importedQuestions[2].shortAnswerType).toBe(
                'phone number',
            );
            expect(result.importedQuestions[3].shortAnswerType).toBe('text');
            expect(result.importedQuestions[4].shortAnswerType).toBe(''); // Unclear, should be empty
        });

        test('should track failed questions with missing titles', async () => {
            const headers = Object.values(
                QUESTIONNAIRE_TEMPLATE_HEADER_ENUM,
            ).join(',');
            const csvContent = [
                headers,
                '"Valid question","Short Answer","text","Yes"',
                '"","Short Answer","text","Yes"', // Missing title
                '"Another valid question","Yes/No","","No"',
            ].join('\n');

            const file = createMockFile(csvContent);

            mockFileReader.result = csvContent;

            const result = await parseQuestionnaireCSV(file);

            expect(result.importedQuestions).toHaveLength(2); // Only valid questions
            expect(result.failedQuestions).toContain(2); // Second question failed
        });

        test('should track questions needing attention (empty short answer type)', async () => {
            const headers = Object.values(
                QUESTIONNAIRE_TEMPLATE_HEADER_ENUM,
            ).join(',');
            const csvContent = [
                headers,
                '"Question 1","Short Answer","text","Yes"', // Valid
                '"Question 2","Short Answer","","Yes"', // Needs attention - empty subtype
                '"Question 3","Yes/No","","No"', // Valid
            ].join('\n');

            const file = createMockFile(csvContent);

            mockFileReader.result = csvContent;

            const result = await parseQuestionnaireCSV(file);

            expect(result.importedQuestions).toHaveLength(3);
            expect(result.failedQuestions).toContain(2); // Question 2 needs attention
        });

        test('should handle CSV with quoted fields containing commas', async () => {
            const headers = Object.values(
                QUESTIONNAIRE_TEMPLATE_HEADER_ENUM,
            ).join(',');
            const csvContent = [
                headers,
                '"What is your name, including middle name?","Short Answer","text","Yes"',
                '"Select options","Multiple Choice","Option 1, with comma;Option 2;Option 3","No"',
            ].join('\n');

            const file = createMockFile(csvContent);

            mockFileReader.result = csvContent;

            const result = await parseQuestionnaireCSV(file);

            expect(result.importedQuestions).toHaveLength(2);
            expect(result.importedQuestions[0].title).toBe(
                'What is your name, including middle name?',
            );
            expect(result.importedQuestions[1].choices).toHaveLength(3);
            expect(result.importedQuestions[1].choices[0].label).toBe(
                'Option 1, with comma',
            );
        });

        test('should reject file with missing headers', async () => {
            const csvContent =
                'Invalid,Headers,Only\n"Question","Answer","Required"';
            const file = createMockFile(csvContent);

            mockFileReader.result = csvContent;

            await expect(parseQuestionnaireCSV(file)).rejects.toThrow(
                'The uploaded file must use the template',
            );
        });

        test('should reject empty CSV file', async () => {
            const file = createMockFile('');

            mockFileReader.result = '';

            await expect(parseQuestionnaireCSV(file)).rejects.toThrow(
                'CSV file has no headers',
            );
        });

        test('should reject CSV file with no headers', async () => {
            const file = createMockFile('\n\n');

            mockFileReader.result = '\n\n';

            await expect(parseQuestionnaireCSV(file)).rejects.toThrow(
                'CSV file has no headers',
            );
        });

        test('should handle file reading errors', async () => {
            const file = createMockFile('test content');

            // Override the mock to simulate error immediately
            vi.spyOn(global, 'FileReader').mockImplementation(() => {
                const errorReader = new MockFileReader();

                errorReader.readAsText = () => {
                    errorReader.error = new DOMException('File read error');
                    setTimeout(() => {
                        if (errorReader.onerror) {
                            errorReader.onerror({
                                target: errorReader,
                            } as unknown as ProgressEvent<FileReader>);
                        }
                    }, 0);
                };

                return errorReader as unknown as FileReader;
            });

            await expect(parseQuestionnaireCSV(file)).rejects.toThrow(
                'File read error',
            );
        });

        test('should handle file reading failure without specific error', async () => {
            const file = createMockFile('test content');

            // Override the mock to simulate error without specific message
            vi.spyOn(global, 'FileReader').mockImplementation(() => {
                const errorReader = new MockFileReader();

                errorReader.readAsText = () => {
                    errorReader.error = null;
                    setTimeout(() => {
                        if (errorReader.onerror) {
                            errorReader.onerror({
                                target: errorReader,
                            } as unknown as ProgressEvent<FileReader>);
                        }
                    }, 0);
                };

                return errorReader as unknown as FileReader;
            });

            await expect(parseQuestionnaireCSV(file)).rejects.toThrow(
                'Failed to read file',
            );
        });

        test('should filter out empty rows', async () => {
            const headers = Object.values(
                QUESTIONNAIRE_TEMPLATE_HEADER_ENUM,
            ).join(',');
            const csvContent = [
                headers,
                '"Question 1","Short Answer","text","Yes"',
                '', // Empty row
                '   ', // Row with only spaces
                '"Question 2","Yes/No","","No"',
            ].join('\n');

            const file = createMockFile(csvContent);

            mockFileReader.result = csvContent;

            const result = await parseQuestionnaireCSV(file);

            expect(result.importedQuestions).toHaveLength(2);
            expect(result.importedQuestions[0].title).toBe('Question 1');
            expect(result.importedQuestions[1].title).toBe('Question 2');
        });
    });
});
