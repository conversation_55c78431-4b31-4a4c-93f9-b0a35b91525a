import { head, isEmpty } from 'lodash-es';
import {
    type ImportCsvFileResult,
    type ImportedQuestion,
    QUESTIONNAIRE_TEMPLATE_HEADER_ENUM,
} from '../types/questionnaire-import.types';

/**
 * Parses a CSV row handling quoted fields properly.
 */
const parseCSVRow = (row: string): string[] => {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;

    for (const char of row) {
        if (char === '"') {
            inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
            result.push(current.trim());
            current = '';
        } else {
            current = current + char;
        }
    }

    result.push(current.trim());

    return result;
};

/**
 * Extracts short answer subtype from options text.
 * If the options text contains multiple choices, it should be left empty for user to choose.
 */
const extractShortAnswerSubtype = (
    responseType: string,
    optionsText: string,
): string => {
    // Only process for Short Answer types
    if (responseType.trim() !== 'Short Answer') {
        return 'short_text';
    }

    if (!optionsText.trim() || optionsText.trim() === '-') {
        return ''; // Empty so user needs to configure it
    }

    const text = optionsText.toLowerCase().trim();

    // If it contains "choose one" or multiple options, leave empty for user to configure
    if (
        text.includes('choose one') ||
        text.includes('choose from') ||
        text.includes(',')
    ) {
        return ''; // Empty so user needs to configure it
    }

    // Only set specific subtype if it's explicitly one type
    if (text === 'email') {
        return 'email';
    }
    if (text === 'website url' || text === 'url') {
        return 'website url';
    }
    if (text === 'phone number' || text === 'phone') {
        return 'phone number';
    }
    if (text === 'text') {
        return 'text';
    }

    // Default to empty if unclear
    return '';
};

/**
 * Parses choices from options text for multiple choice questions.
 */
const parseChoices = (
    responseType: string,
    optionsText: string,
    questionId: number,
): { ref: string; label: string }[] => {
    const type = responseType.toLowerCase().trim();

    if (type !== 'multiple choice' && type !== 'checkboxes') {
        return [];
    }

    if (!optionsText.trim()) {
        return [];
    }

    // Split by semicolon or pipe, common delimiters
    const options = optionsText
        .split(/[;|]/)
        .map((opt) => opt.trim())
        .filter(Boolean);

    return options.map((option, index) => ({
        ref: `C${Date.now()}_${questionId}_${index}`,
        label: option,
    }));
};

/**
 * Builds questions from CSV rows.
 * Simplified version based on web project.
 */
const buildQuestions = (
    csvRows: string[],
    headers: string[],
    failedQuestions: number[],
): ImportedQuestion[] => {
    return csvRows
        .slice(1) // Skip header row
        .filter((row) => row.trim()) // Filter empty rows
        .map((row, index) => {
            const values = parseCSVRow(row);
            const rowData = Object.fromEntries(
                headers.map((header, headerIndex) => [
                    header,
                    values[headerIndex] || '',
                ]),
            );

            const id = index + 1;

            // Extract question data
            const title =
                rowData[QUESTIONNAIRE_TEMPLATE_HEADER_ENUM.QUESTIONS] || '';
            const responseType =
                rowData[QUESTIONNAIRE_TEMPLATE_HEADER_ENUM.RESPONSE_TYPE] ||
                'short_answer';
            const isRequired =
                (
                    rowData[
                        QUESTIONNAIRE_TEMPLATE_HEADER_ENUM.MARK_AS_REQUIRED
                    ] || ''
                ).toLowerCase() === 'yes';
            const optionsText =
                rowData[
                    QUESTIONNAIRE_TEMPLATE_HEADER_ENUM.OPTIONS_OR_SUB_TYPES
                ] || '';

            // Parse choices for multiple choice questions
            const choices = parseChoices(responseType, optionsText, id);

            // Validation - track failed questions
            const hasValidTitle = !isEmpty(title.trim());
            const hasValidType = !isEmpty(responseType.trim());

            if (!hasValidTitle || !hasValidType) {
                failedQuestions.push(id);

                // Return null for failed questions, will be filtered out
                return null;
            }

            // Extract short answer subtype from options text
            const shortAnswerSubtype = extractShortAnswerSubtype(
                responseType,
                optionsText,
            );

            // Track questions that need attention (empty shortAnswerType for Short Answer)
            if (
                responseType.trim() === 'Short Answer' &&
                shortAnswerSubtype === ''
            ) {
                failedQuestions.push(id);
            }

            return {
                ref: `Q${Date.now()}_${id}`,
                title: title.trim(),
                type: responseType, // Pass the CSV type directly
                shortAnswerType: shortAnswerSubtype,
                includeFollowUpQn: false,
                followUpQnTrigger: true,
                followUpQn: '',
                allowOtherChoice: false,
                choices,
                required: isRequired,
            };
        })
        .filter(
            (question): question is ImportedQuestion =>
                question !== null && question.title.trim() !== '',
        ); // Filter out failed questions and questions without titles
};

/**
 * Parses a CSV file and extracts questionnaire questions.
 * Based on the web project implementation.
 */
export const parseQuestionnaireCSV = async (
    file: File,
): Promise<ImportCsvFileResult> => {
    const reader = new FileReader();
    const failedQuestions: number[] = [];

    const importedQuestions = await new Promise<ImportedQuestion[]>(
        (resolve, reject) => {
            reader.onload = (event) => {
                if (!event.target) {
                    reject(new Error('Failed to read file content'));

                    return;
                }

                const csvContent = event.target.result as string;
                const csvRows = csvContent.split('\n');

                if (isEmpty(csvRows)) {
                    reject(new Error('CSV file is empty'));

                    return;
                }

                const firstRow = head(csvRows);

                if (!firstRow) {
                    reject(new Error('CSV file has no headers'));

                    return;
                }

                const headers = parseCSVRow(firstRow);

                // Validate headers match template
                const expectedHeaders = Object.values(
                    QUESTIONNAIRE_TEMPLATE_HEADER_ENUM,
                );
                const missingHeaders = expectedHeaders.filter(
                    (header) => !headers.includes(header),
                );

                if (!isEmpty(missingHeaders)) {
                    reject(
                        new Error('The uploaded file must use the template'),
                    );

                    return;
                }

                const questions: ImportedQuestion[] = buildQuestions(
                    csvRows,
                    headers,
                    failedQuestions,
                );

                resolve(questions);
            };

            reader.onerror = () => {
                reject(
                    new Error(reader.error?.message || 'Failed to read file'),
                );
            };
            reader.readAsText(file);
        },
    );

    return {
        importedQuestions,
        failedQuestions,
    };
};
