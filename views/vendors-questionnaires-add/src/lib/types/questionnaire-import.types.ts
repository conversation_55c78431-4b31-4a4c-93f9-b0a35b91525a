export interface ImportedQuestion {
    ref: string;
    title: string;
    type: string;
    shortAnswerType: string;
    includeFollowUpQn: boolean;
    followUpQnTrigger: boolean;
    followUpQn: string;
    allowOtherChoice: boolean;
    choices: { ref: string; label: string }[];
    required: boolean;
}

export interface ImportCsvFileResult {
    importedQuestions: ImportedQuestion[];
    failedQuestions: number[];
}

export const QUESTIONNAIRE_TEMPLATE_HEADER_ENUM = {
    QUESTIONS: 'Questions (Max 500)',
    RESPONSE_TYPE: 'Response Type',
    OPTIONS_OR_SUB_TYPES:
        'Options or Sub-Types (Please use exact formatting if applicable)',
    MARK_AS_REQUIRED: 'Mark as Required (Yes or No)',
} as const;
