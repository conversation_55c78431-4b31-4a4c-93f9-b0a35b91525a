import { isEmpty } from 'lodash-es';
import { z } from 'zod';
import { t } from '@globals/i18n/macro';
import type { FormSchema } from '@ui/forms';

/**
 * Form schema for the import questions modal.
 * Validates CSV file upload for questionnaire import.
 */
export const importQuestionsModalFormSchema = (): FormSchema => ({
    completedFile: {
        type: 'file',
        initialValue: undefined,
        isOptional: false,
        label: t`Completed file`,
        acceptedFormats: ['csv'],
        errorCodeMessages: {
            'file-invalid-type': t`Please upload a CSV file`,
            'file-too-large': t`File size is too large`,
            'file-too-small': t`File size is too small`,
            'too-many-files': t`Please upload only one file`,
        },
        innerLabel: t`Or drop files here`,
        isMulti: false,
        oneFileOnly: true,
        removeButtonText: t`Remove file`,
        selectButtonText: t`Upload files`,
        validator: z
            .array(z.instanceof(File))
            .min(1, { message: t`Please upload a CSV file` })
            .max(1, { message: t`Please upload only one file` })
            .refine(
                (files) => {
                    if (isEmpty(files)) {
                        return false;
                    }
                    const file = files[0];

                    return (
                        file.type === 'text/csv' || file.name.endsWith('.csv')
                    );
                },
                {
                    message: t`Please upload a valid CSV file`,
                },
            ),
    },
});

/**
 * Initial values for the import questions modal form.
 */
export const importQuestionsModalInitialValues = {
    completedFile: undefined,
};

/**
 * Type for the import questions modal form data.
 */
export interface ImportQuestionsModalFormData {
    completedFile: File[] | undefined;
}
