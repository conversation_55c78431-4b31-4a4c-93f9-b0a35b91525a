import { modalController } from '@controllers/modal';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { action, observer } from '@globals/mobx';
import { useNavigate, useParams } from '@remix-run/react';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';
import {
    downloadQuestionnaireTemplate,
    uploadQuestionnaireFile,
} from '../helpers/questionnaire-import.helper';
import { importQuestionsModalFormSchema } from '../schemas/import-questions-modal-form.schema';

export const ImportQuestionsModal = observer((): React.JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();
    const navigate = useNavigate();
    const { workspaceId } = useParams();

    const handleFormSubmit = async (formData: FormValues) => {
        const files = formData.completedFile as File[] | undefined;

        if (files?.[0]) {
            await uploadQuestionnaireFile(files[0]);

            // Navigate to add questionnaire page after successful import
            if (workspaceId) {
                // Use navigate hook following project patterns
                navigate(
                    `/workspaces/${workspaceId}/vendors/questionnaires/add`,
                );
            }
        } else {
            logger.error({
                message: 'Import questions modal submitted without file',
                additionalInfo: {
                    formData,
                    hasFiles: Boolean(files),
                    filesLength: files?.length ?? 0,
                },
            });
        }
    };

    const handleClose = action(() => {
        modalController.closeModal('import-questions-modal');
    });

    const handleUpload = () => {
        triggerSubmit();
    };

    return (
        <Modal
            data-testid="ImportQuestionsModal"
            data-id="import-questions-modal"
            size="md"
            onClose={handleClose}
        >
            <Modal.Header
                title={t`Import questions`}
                closeButtonAriaLabel={t`Close`}
                onClose={handleClose}
            />
            <Modal.Body>
                <Stack
                    gap="xl"
                    direction="column"
                    align="start"
                    data-testid="VendorsQuestionnairesImportQuestionsModalView"
                    data-id="BVI0P-7w"
                >
                    <Text size="100">
                        <Trans>
                            1. Use the template to add your questions and
                            response type
                        </Trans>
                    </Text>
                    <Button
                        size="sm"
                        colorScheme="neutral"
                        level="tertiary"
                        label={t`Download template`}
                        startIconName="Download"
                        onClick={downloadQuestionnaireTemplate}
                    />
                    <Text size="100">
                        <Trans>2. Upload the completed file</Trans>
                    </Text>

                    <Box width="100%">
                        <Form
                            hasExternalSubmitButton
                            ref={formRef}
                            formId="import-questions-modal-form"
                            schema={importQuestionsModalFormSchema()}
                            data-id="import-questions-modal-form"
                            onSubmit={handleFormSubmit}
                        />
                    </Box>
                </Stack>
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Upload`,
                        level: 'primary',
                        onClick: handleUpload,
                    },
                ]}
            />
        </Modal>
    );
});
