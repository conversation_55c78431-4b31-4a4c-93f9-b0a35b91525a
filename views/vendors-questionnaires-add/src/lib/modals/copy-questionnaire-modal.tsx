import { useState } from 'react';
import { sharedCopyQuestionnaireValidationController } from '@controllers/copy-questionnaire-validation';
import { modalController } from '@controllers/modal';
import { sharedVendorsTypeformQuestionnairesController } from '@controllers/vendors';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { TextField } from '@cosmos/components/text-field';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';

interface ValidationController {
    validateTitleOnSubmit: (
        title: string,
    ) => Promise<{ isValid: boolean; error?: string }>;
    getTitleValidationError: () => string | null;
    clearTitleValidationError: () => void;
    titleValidationQuery: {
        isLoading: boolean;
    };
}

export interface CopyQuestionnaireModalProps {
    defaultName: string;
    questionnaireId: number;
}

const CopyQuestionnaireModalContent = observer(
    ({
        defaultName,
        questionnaireId,
    }: CopyQuestionnaireModalProps): React.JSX.Element => {
        const [name, setName] = useState(defaultName);
        const [isLoading, setIsLoading] = useState(false);

        // Type-safe controller access
        const controller =
            sharedCopyQuestionnaireValidationController as ValidationController;
        const {
            validateTitleOnSubmit,
            getTitleValidationError,
            clearTitleValidationError,
            titleValidationQuery,
        } = controller;
        const { copyQuestionnaireWithName } =
            sharedVendorsTypeformQuestionnairesController;

        // Note: Removed real-time validation - now only validates on submit

        const handleSubmit = action(async () => {
            if (!name.trim() || titleValidationQuery.isLoading) {
                return;
            }

            // Validate title before submitting
            const titleValidation = await validateTitleOnSubmit(name);

            if (!titleValidation.isValid) {
                // Error will be shown by the validation method
                return;
            }

            setIsLoading(true);

            // Call the copy method with completion callback
            copyQuestionnaireWithName(questionnaireId, name.trim(), () => {
                setIsLoading(false);
            });
        });

        const handleCancel = action(() => {
            modalController.closeModal('copy-questionnaire-modal');
        });

        const handleKeyPress = (e: React.KeyboardEvent) => {
            if (
                e.key === 'Enter' &&
                name.trim() &&
                !titleValidationQuery.isLoading &&
                !isLoading
            ) {
                handleSubmit();
            }
        };

        const getFeedback = () => {
            const titleError = getTitleValidationError();

            if (titleError) {
                return {
                    type: 'error' as const,
                    message: titleError,
                };
            }
            if (!name.trim()) {
                return {
                    type: 'error' as const,
                    message: t`Name is required`,
                };
            }

            return undefined;
        };
        const isSubmitDisabled =
            !name.trim() || titleValidationQuery.isLoading || isLoading;

        return (
            <>
                <Modal.Header
                    title={t`Copy Questionnaire`}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={handleCancel}
                />
                <Modal.Body>
                    <Stack direction="column" gap="md">
                        {/* TODO: This is anti-pattern. We should use a form */}
                        <TextField
                            label={t`Questionnaire name`}
                            formId="copy-questionnaire-form"
                            name="questionnaire-name"
                            value={name}
                            disabled={isLoading}
                            feedback={getFeedback()}
                            onKeyDown={handleKeyPress}
                            onChange={(e) => {
                                setName(e.target.value);
                                // Clear any previous validation error when user starts typing
                                clearTitleValidationError();
                            }}
                        />
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Discard`,
                            level: 'tertiary',
                            onClick: handleCancel,
                            cosmosUseWithCaution_isDisabled: isLoading,
                        },
                        {
                            label: t`Save`,
                            level: 'primary',
                            onClick: () => {
                                handleSubmit();
                            },
                            cosmosUseWithCaution_isDisabled: isSubmitDisabled,
                            isLoading,
                        },
                    ]}
                />
            </>
        );
    },
);

export const CopyQuestionnaireModal = ({
    defaultName,
    questionnaireId,
}: CopyQuestionnaireModalProps): React.JSX.Element => {
    return (
        <CopyQuestionnaireModalContent
            key={defaultName}
            defaultName={defaultName}
            questionnaireId={questionnaireId}
            data-testid="CopyQuestionnaireModal"
            data-id="udY6Bvrn"
        />
    );
};
