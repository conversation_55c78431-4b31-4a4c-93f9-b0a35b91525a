import { getMaturityIcon, getMaturityLevelTooltip } from '@controllers/vendors';
import { Avatar } from '@cosmos/components/avatar';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { dimension40x } from '@cosmos/constants/tokens';
import { getInitials } from '@helpers/formatters';
import type { ItemMaturityLevel } from '@views/vendors-profile-trust-center';

interface VendorsComplianceFrameworkProps {
    name: string;
    imgSrc: string;
    maturityLevel: ItemMaturityLevel;
}

export const VendorsComplianceFramework = ({
    name,
    imgSrc,
    maturityLevel,
}: VendorsComplianceFrameworkProps): React.JSX.Element => {
    const maturityIconProps = getMaturityIcon(maturityLevel);

    return (
        <Stack
            direction="column"
            align="center"
            gap="sm"
            data-id="CdnCZzWt"
            data-testid="VendorsComplianceFramework"
            width={dimension40x}
        >
            <Avatar
                fallbackText={getInitials(name, { maxInitials: 4 })}
                imgSrc={imgSrc}
                imgAlt={name}
                size="lg"
            />
            <Stack direction="row" justify="center" gap="xs" width="100%">
                <Stack direction="row" align="start">
                    <Tooltip
                        text={getMaturityLevelTooltip(maturityLevel)}
                        preferredSide="left"
                    >
                        <Icon {...maturityIconProps} />
                    </Tooltip>
                </Stack>
                <Stack direction="row" align="center" justify="center">
                    <Text align="center" size="100" as="span">
                        {name}
                    </Text>
                </Stack>
            </Stack>
        </Stack>
    );
};
