import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';

interface CustomTestLimitModalProps {
    testLimit: number;
    onClose: () => void;
}

export const CustomTestLimitModal = observer(
    ({ testLimit, onClose }: CustomTestLimitModalProps): React.JSX.Element => {
        return (
            <>
                <Modal.Header
                    title={t`Maximum number of custom tests created.`}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={onClose}
                />
                <Modal.Body data-id="custom-test-limit-modal-body">
                    <Stack direction="column" justify="between" height="100%">
                        <Text colorScheme="neutral">
                            {t`You cannot create a new test because you have reached your maximum of ${testLimit} custom tests for this Workspace.`}
                        </Text>
                        <AppLink
                            isExternal
                            href="https://help.drata.com/en/articles/8840053-create-custom-test-for-adaptive-automation-beta"
                        >
                            {t`Here's how Drata counts custom tests.`}
                        </AppLink>
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Done`,
                            level: 'primary',
                            onClick: onClose,
                        },
                    ]}
                />
            </>
        );
    },
);
