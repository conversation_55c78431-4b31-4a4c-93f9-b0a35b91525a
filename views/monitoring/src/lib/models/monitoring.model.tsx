import { modalController } from '@controllers/modal';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import type { TableAction } from '@cosmos/components/datatable';
import { monitorsControllerGetMonitorTestMetadataOptions } from '@globals/api-sdk/queries';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { action, makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { CustomTestLimitModal } from '../components/custom-test-limit-modal.tsx';
import { CUSTOM_TEST_LIMIT_MODAL_ID } from '../constants/custom-test-limit-modal.constant';

export class MonitoringModel {
    constructor() {
        makeAutoObservable(this);
    }

    monitorTestMetadataQuery = new ObservedQuery(
        monitorsControllerGetMonitorTestMetadataOptions,
    );

    get isLimitReached(): boolean {
        return this.monitorTestMetadataQuery.data?.limitReached ?? false;
    }

    get testLimit(): number {
        return this.monitorTestMetadataQuery.data?.limit ?? 0;
    }

    checkTestLimitAndNavigate = (): void => {
        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        if (!workspaceId) {
            return;
        }

        // Load the latest metadata to check current limit status
        this.monitorTestMetadataQuery.load({
            path: {
                xProductId: workspaceId,
            },
        });

        // Wait for the API call to complete and then navigate
        when(
            () =>
                !this.monitorTestMetadataQuery.isLoading &&
                (Boolean(this.monitorTestMetadataQuery.data) ||
                    Boolean(this.monitorTestMetadataQuery.error)),
            () => {
                // Handle API error case
                if (this.monitorTestMetadataQuery.error) {
                    // Log the error for debugging
                    logger.error({
                        message: 'Failed to fetch monitor test metadata',
                        additionalInfo: {
                            action: 'checkTestLimitAndNavigate',
                            workspaceId:
                                sharedWorkspacesController.currentWorkspace?.id,
                        },
                        errorObject: {
                            message: String(
                                this.monitorTestMetadataQuery.error,
                            ),
                            statusCode: 'unknown',
                        },
                    });

                    // Show error message to user
                    snackbarController.addSnackbar({
                        id: 'monitor-test-metadata-error',
                        props: {
                            title: t`Unable to check test limits`,
                            description: t`Please try again in a moment.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                if (this.isLimitReached) {
                    // Open modal to inform user they've reached the limit
                    this.openLimitReachedModal();

                    return;
                }

                // Navigate to the test builder using the proper navigation controller
                sharedProgrammaticNavigationController.navigateTo(
                    '/compliance/monitoring/builder',
                );
            },
        );
    };

    openLimitReachedModal = (): void => {
        modalController.openModal({
            id: CUSTOM_TEST_LIMIT_MODAL_ID,
            content: () => (
                <CustomTestLimitModal
                    testLimit={this.testLimit}
                    data-id="JKej-he6"
                    onClose={() => {
                        modalController.closeModal(CUSTOM_TEST_LIMIT_MODAL_ID);
                    }}
                />
            ),
            size: 'sm',
            centered: true,
        });
    };

    get tableActions(): TableAction[] {
        const actions: TableAction[] = [];

        if (sharedFeatureAccessModel.hasCustomConnectionsAndTests) {
            actions.push({
                actionType: 'button',
                id: 'create-button',
                typeProps: {
                    label: t`Create test`,
                    level: 'secondary',
                    onClick: action(() => {
                        this.checkTestLimitAndNavigate();
                    }),
                },
            });
        }

        return actions;
    }
}
