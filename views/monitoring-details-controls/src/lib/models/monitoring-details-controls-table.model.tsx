import { isEmpty } from 'lodash-es';
import {
    LINK_CONTROLS_MODAL_ID,
    LinkControlsModal,
} from '@components/evidence-library';
import { sharedLinkControlsController } from '@controllers/evidence-library';
import { modalController } from '@controllers/modal';
import {
    sharedMonitoringMapTestsMutationController,
    sharedMonitoringUnmapControlMutationController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import type { DatatableProps, TableAction } from '@cosmos/components/datatable';
import type { ControlMonitorResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { ActionCellComponent } from '../components/action-cell-component';
import { ControlCodeCellComponent } from '../components/control-code-cell-component';
import { FrameworksCellComponent } from '../components/frameworks-cell-component';
import { NameCellComponent } from '../components/name-cell-component';
import { OwnersCellComponent } from '../components/owners-cell-component';

class MonitoringDetailsControlsTableModel {
    constructor() {
        makeAutoObservable(this);
    }

    unmapControl = (controlId: number) => {
        const { testDetails } = sharedMonitoringTestDetailsController;

        if (!testDetails) {
            return;
        }

        sharedMonitoringUnmapControlMutationController.unmapControlFromMonitor(
            testDetails.testId,
            controlId,
        );
    };

    get controlsColumns(): DatatableProps<ControlMonitorResponseDto>['columns'] {
        const { hasWriteControlPermission, isMapControlsTestsEnabled } =
            sharedFeatureAccessModel;
        const actionColumn = [];

        if (hasWriteControlPermission && isMapControlsTestsEnabled) {
            actionColumn.push({
                id: 'ACTIONS',
                cell: ActionCellComponent,
                enableSorting: false,
                isActionColumn: true,
                meta: {
                    shouldIgnoreRowClick: true,
                },
                size: 50,
            });
        }

        return [
            ...actionColumn,
            {
                id: 'CONTROL_CODE',
                accessorKey: 'code',
                header: t`Control code`,
                enableSorting: false,
                cell: ControlCodeCellComponent,
                size: 150,
            },
            {
                id: 'NAME',
                accessorKey: 'name',
                header: t`Name`,
                enableSorting: false,
                cell: NameCellComponent,
            },
            {
                id: 'OWNER',
                accessorKey: 'owners',
                header: t`Owners`,
                enableSorting: false,
                cell: OwnersCellComponent,
                meta: {
                    shouldIgnoreRowClick: true,
                },
            },
            {
                id: 'FRAMEWORKS',
                accessorKey: 'frameworks',
                header: t`Frameworks`,
                enableSorting: false,
                cell: FrameworksCellComponent,
            },
        ];
    }

    get tableActions(): TableAction[] {
        const { isMapControlsTestsEnabled } = sharedFeatureAccessModel;

        if (!isMapControlsTestsEnabled) {
            return [];
        }

        return [
            {
                actionType: 'button',
                id: 'map-controls-button',
                typeProps: {
                    label: t`Map control`,
                    level: 'secondary',
                    onClick: this.handleOpenMapControlsModal,
                },
            },
        ];
    }

    handleOpenMapControlsModal = (): void => {
        const { testDetails } = sharedMonitoringTestDetailsController;

        if (!testDetails) {
            return;
        }

        sharedLinkControlsController.setExcludeTestId(testDetails.testId);
        sharedLinkControlsController.loadControls();

        modalController.openModal({
            id: LINK_CONTROLS_MODAL_ID,
            content: () => (
                <LinkControlsModal
                    objectType="monitor"
                    data-id="R-AggfmB"
                    onConfirm={(selectedControls) => {
                        if (isEmpty(selectedControls)) {
                            return;
                        }
                        const controlIds = selectedControls
                            .map((item) => item.controlData.id)
                            .filter(Boolean) as number[];

                        sharedMonitoringMapTestsMutationController.mapTestsToControl(
                            controlIds,
                            [testDetails.testId],
                        );
                    }}
                />
            ),
            centered: true,
            disableClickOutsideToClose: true,
            size: 'lg',
        });
    };

    get tableSearchProps(): DatatableProps<ControlMonitorResponseDto>['tableSearchProps'] {
        return {
            hideSearch: true,
        };
    }

    get emptyStateProps(): DatatableProps<ControlMonitorResponseDto>['emptyStateProps'] {
        return {
            title: t`Monitoring Details Controls View`,
            description: t`description`,
        };
    }

    get filterViewModeProps(): DatatableProps<ControlMonitorResponseDto>['filterViewModeProps'] {
        return {
            props: {
                selectedOption: 'pinned',
                initialSelectedOption: 'pinned',
                togglePinnedLabel: t`Pin filters to page`,
                toggleUnpinnedLabel: t`Move filters to dropdown`,
            },
            viewMode: 'toggleable',
        };
    }
}

export const sharedMonitoringDetailsControlsTableModel =
    new MonitoringDetailsControlsTableModel();
