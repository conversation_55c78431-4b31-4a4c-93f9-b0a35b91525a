import { noop } from 'lodash-es';
import { useCallback } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { ControlPanel } from '@components/controls';
import { sharedControlDetailsOrchestratorController } from '@controllers/controls';
import {
    activeMonitoringController,
    sharedMonitoringDetailsControlsController,
} from '@controllers/monitoring-details';
import { panelController } from '@controllers/panel';
import { Banner } from '@cosmos/components/banner';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { Stack } from '@cosmos/components/stack';
import type { ControlMonitorResponseDto } from '@globals/api-sdk/types';
import { action, observer } from '@globals/mobx';
import { sharedMonitoringDetailsControlsTableModel } from './models/monitoring-details-controls-table.model';

const handleOpenControlPanel = action(
    ({ row: control }: { row: ControlMonitorResponseDto }) => {
        sharedControlDetailsOrchestratorController.load(control.id);
        panelController.openPanel({
            id: 'monitoring-details-control-panel',
            content: () => (
                <ControlPanel
                    controlSource="MONITORING_DETAILS"
                    data-id="monitoring-details-control-panel"
                />
            ),
        });
    },
);

interface MonitoringDetailsControlsViewProps {
    code?: boolean;
    'data-testid'?: string;
    'data-id'?: string;
}

export const MonitoringDetailsControlsView = observer(
    ({ code = false }: MonitoringDetailsControlsViewProps) => {
        const {
            monitoringDetailsControlsData,
            monitoringDetailsControlsTotal,
            isLoading,
            loadMonitoringDetailsControls,
        } = sharedMonitoringDetailsControlsController;

        const {
            controlsColumns,
            tableActions,
            tableSearchProps,
            emptyStateProps,
            filterViewModeProps,
        } = sharedMonitoringDetailsControlsTableModel;

        const { monitor } = activeMonitoringController;
        const currentTestId = monitor?.testId;

        const loadControls = useCallback(
            (params: FetchDataResponseParams) => {
                if (currentTestId) {
                    loadMonitoringDetailsControls({
                        params,
                        monitorTestId: currentTestId,
                    });
                }
            },
            [currentTestId, loadMonitoringDetailsControls],
        );

        return (
            <Stack
                gap="4x"
                pt="8x"
                direction="column"
                data-testid="MonitoringDetailsControlsView"
                data-id="MonitoringDetailsControlsView"
            >
                {code && (
                    <Banner
                        title="Codebase tests do not impact control readiness."
                        severity="primary"
                        body="Codebase tests monitor your infrastructure-as-code and therefore do contribute to control readiness."
                    />
                )}
                <AppDatatable
                    isFullPageTable
                    getRowId={(row) => row.id.toString()}
                    isLoading={isLoading}
                    tableId="MonitoringDetailsControlsView-table"
                    data-id="MonitoringDetailsControlsView-table"
                    data={monitoringDetailsControlsData}
                    total={monitoringDetailsControlsTotal}
                    columns={controlsColumns}
                    data-testid="MonitoringDetailsControlsView"
                    tableActions={tableActions}
                    tableSearchProps={tableSearchProps}
                    emptyStateProps={emptyStateProps}
                    filterViewModeProps={filterViewModeProps}
                    onRowSelection={noop}
                    onRowClick={handleOpenControlPanel}
                    onFetchData={loadControls}
                />
            </Stack>
        );
    },
);
