import { isEmpty } from 'lodash-es';
import { useCallback, useMemo } from 'react';
import {
    getRiskFilterOptions,
    RISK_FILTER_ID,
} from '@components/risk-register';
import { VendorsRisksOverviewPanelComponent } from '@components/vendors-risks-overview-panel';
import {
    handleOpenDetailsPanel,
    sharedVendorsDetailsController,
    sharedVendorsProfileRisksController,
} from '@controllers/vendors';
import { Button } from '@cosmos/components/button';
import {
    Datatable,
    DEFAULT_TABLE_SEARCH_PROPS,
} from '@cosmos/components/datatable';
import { EmptyState } from '@cosmos/components/empty-state';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { sharedCurrentUserController } from '@globals/current-user';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { getTableActions } from './helpers/get-table-actions.helper';
import { getTableColumns } from './vendors-profile-risks-table.config';

export const VendorsProfileRisksView = observer((): React.JSX.Element => {
    const {
        allVendorsRisks,
        isLoading,
        hasSearchFilter,
        loadVendorsRisks,
        total,
    } = sharedVendorsProfileRisksController;
    const { vendorDetails } = sharedVendorsDetailsController;

    // if last char is a period, remove it
    const vendorName = vendorDetails?.name.replace(/\.$/, '') ?? '';

    const navigate = useNavigate();
    const EXTERNAL_RISK_FILTER_VALUE = useMemo(
        () =>
            getRiskFilterOptions().find(
                (option) => option.value === 'EXTERNAL_RISKS',
            )?.value,
        [],
    );

    const onAddRiskClick = useCallback(() => {
        navigate('add');
    }, [navigate]);

    const emptyRisksListDescription = useMemo(() => {
        return vendorName
            ? t`Add and track risks related to ${vendorName}.`
            : t`Add and track risks related to this vendor.`;
    }, [vendorName]);

    const shouldShowHeaderTable = useMemo(() => {
        if (isLoading || !vendorDetails?.name) {
            return false;
        }

        // Auditor Restriction: Do not show header if user is in "auditor read-only mode"
        if (sharedCurrentUserController.isAuditorInReadOnlyMode) {
            return false;
        }

        // Company has risk management capabilities enabled and user has required permissions
        // This covers both company capabilities and user roles (ADMIN, RISK_MANAGER, ACT_AS_READ_ONLY, WORKSPACE_ADMINISTRATOR)
        // through the permission-based access control system
        if (!sharedFeatureAccessModel.isRiskDomainReadEnabled) {
            return false;
        }

        return sharedFeatureAccessModel.hasRiskReadPermission;
    }, [isLoading, vendorDetails?.name]);

    if (!isLoading && !hasSearchFilter && isEmpty(allVendorsRisks)) {
        return (
            <EmptyState
                title={t`Stay on top of risks that vendors pose to your organization.`}
                description={emptyRisksListDescription}
                illustrationName="RiskManagement"
                leftAction={
                    sharedFeatureAccessModel.isRiskManagerWithRestrictedView ? null : (
                        <Button
                            label={t`Add risk`}
                            level="primary"
                            data-id="add-risk-empty-state-button"
                            onClick={onAddRiskClick}
                        />
                    )
                }
            />
        );
    }

    return (
        <Stack direction="column" gap="2xl" data-id="jUNdRA3f">
            {shouldShowHeaderTable && (
                <Text>
                    {vendorName ? (
                        <Trans>
                            These are all the risks from{' '}
                            <AppLink
                                /*
                                 * TODO: this navigation has to go to /risk/management/registers/[register_id]/register-risks/[task-id]
                                 * That created a problem because we don't have the register_id outside of /risk/management/registers/[register_id].
                                 *
                                 * At the moment it was decided to hardcode the register_id to 1 since that's all tenants will have.
                                 *
                                 * See: https://drata.atlassian.net/browse/ENG-72803
                                 */
                                href={`/workspaces/${sharedWorkspacesController.currentWorkspace?.id}/risk/management/registers/1/register-risks?${RISK_FILTER_ID}=${EXTERNAL_RISK_FILTER_VALUE}`}
                                data-id={`vendor-risk-management-link`}
                            >
                                Risk management
                            </AppLink>{' '}
                            related to {vendorName}.
                        </Trans>
                    ) : (
                        <Trans>
                            These are all the risks from{' '}
                            <AppLink
                                /*
                                 * TODO: this navigation has to go to /risk/management/registers/[register_id]/register-risks/[task-id]
                                 * That created a problem because we don't have the register_id outside of /risk/management/registers/[register_id].
                                 *
                                 * At the moment it was decided to hardcode the register_id to 1 since that's all tenants will have.
                                 *
                                 * See: https://drata.atlassian.net/browse/ENG-72803
                                 */
                                href={`/workspaces/${sharedWorkspacesController.currentWorkspace?.id}/risk/management/registers/1/register-risks?${RISK_FILTER_ID}=${EXTERNAL_RISK_FILTER_VALUE}`}
                                data-id={`vendor-risk-management-link`}
                            >
                                Risk management
                            </AppLink>{' '}
                            related to this vendor.
                        </Trans>
                    )}
                </Text>
            )}

            <Datatable
                isLoading={isLoading}
                tableId="vendor-risks-overview-datatable"
                total={total}
                data={allVendorsRisks}
                data-id="vendor-risks-overview-datatable"
                columns={getTableColumns()}
                tableActions={getTableActions(onAddRiskClick)}
                tableSearchProps={DEFAULT_TABLE_SEARCH_PROPS}
                emptyStateProps={{
                    title: t`No risks found for this vendor`,
                    description: t`This vendor doesn't have any risks matching your search criteria.`,
                    illustrationName: 'RiskManagement',
                }}
                filterViewModeProps={{
                    viewMode: 'pinned',
                }}
                onFetchData={loadVendorsRisks}
                onRowClick={({ row }) => {
                    handleOpenDetailsPanel(row.riskId, () => (
                        <VendorsRisksOverviewPanelComponent data-id="AuHVo_C4" />
                    ));
                }}
            />
        </Stack>
    );
});
