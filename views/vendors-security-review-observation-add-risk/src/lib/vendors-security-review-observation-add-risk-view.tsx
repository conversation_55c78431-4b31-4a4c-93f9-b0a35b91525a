import { CreateRiskWizard } from '@components/create-risk-wizard';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { observer } from '@globals/mobx';
import { useParams } from '@remix-run/react';

export interface VendorsSecurityReviewObservationAddRiskViewProps {
    'data-testid'?: string;
    'data-id'?: string;
}

export const VendorsSecurityReviewObservationAddRiskView = observer(
    ({
        'data-testid': dataTestId,
        'data-id': dataId,
    }: VendorsSecurityReviewObservationAddRiskViewProps): React.JSX.Element => {
        const { workspaceId, vendorId, securityReviewId } = useParams();

        const isRiskManagerRV =
            sharedFeatureAccessModel.isRiskManagerWithRestrictedView;

        if (isRiskManagerRV) {
            throw new Error(
                'Risk managers with restricted view cannot add risks',
            );
            // TODO: implement proper error handling and redirection TALK TO @CHRIS
        }

        const backRoute = `/workspaces/${workspaceId}/vendors/current/${vendorId}/security-reviews/${securityReviewId}/completed`;

        return (
            <CreateRiskWizard
                riskType="VENDOR_RISK"
                vendorId={vendorId ? Number(vendorId) : undefined}
                backRoute={backRoute}
                data-testid={dataTestId}
                data-id={dataId}
            />
        );
    },
);
