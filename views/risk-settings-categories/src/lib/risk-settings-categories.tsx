import { RiskSettingsCategories } from '@components/risk-settings-categories';
import { sharedRiskCategoriesController } from '@controllers/risk';
import { observer } from '@globals/mobx';

export const RiskSettingsCategoriesView = observer((): React.JSX.Element => {
    const {
        categories,
        addCategoryWithValidation,
        updateNewCategoryName,
        deleteCategoryWithConfirmation,
        newCategoryName,
        validationError,
        isCreatingCategory,
        isDeletingCategory,
        isLoading,
        hasError,
    } = sharedRiskCategoriesController;

    const options = categories.map((category) => ({
        id: String(category.id),
        label: category.name,
        value: String(category.id),
    }));

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        updateNewCategoryName(event.target.value);
    };

    const handleAddCategory = () => {
        addCategoryWithValidation();
    };

    const handleDeleteCategory = (categoryId: string, categoryName: string) => {
        deleteCategoryWithConfirmation(categoryId, categoryName);
    };

    return (
        <RiskSettingsCategories
            categories={options}
            newCategoryName={newCategoryName}
            validationError={validationError}
            isCreatingCategory={isCreatingCategory}
            isDeletingCategory={isDeletingCategory}
            isLoading={isLoading}
            hasError={hasError}
            data-id="risk-settings-categories-view"
            onInputChange={handleInputChange}
            onAddCategory={handleAddCategory}
            onDeleteCategory={handleDeleteCategory}
        />
    );
});
