import { AppDatatable } from '@components/app-datatable';
import {
    sharedMonitoringCodeController,
    sharedMonitoringStatsController,
} from '@controllers/monitoring';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { MonitoringCodeBulkActionsModel } from './models/monitoring-code-bulk-actions.model';
import {
    getMonitoringCodeColumns,
    getMonitoringCodeFilters,
} from './monitoring-code.constants';

export const MonitoringCodeView = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    const { bulkActions, handleRowSelection } =
        new MonitoringCodeBulkActionsModel();

    const {
        monitoringCodeListData,
        monitoringCodeTotal,
        monitoringCodeListLoad,
        isLoading,
    } = sharedMonitoringCodeController;

    const {
        monitoringTotalRepositories,
        monitoringNumRepositoriesMonitored,
        monitoringStatsFailedTests,
        monitoringStatsPassedTests,
        isLoadingStats,
    } = sharedMonitoringStatsController;

    return (
        <>
            {isLoadingStats ? (
                <Card
                    title={t`Tests`}
                    body={<Loader isSpinnerOnly label={t`Loading...`} />}
                />
            ) : (
                <Grid columns="3" gap="4x" pb="4x">
                    <Box
                        borderColor="neutralBorderInitial"
                        borderWidth="borderWidth1"
                        borderRadius="borderRadius2x"
                        p="4x"
                    >
                        <StatBlock
                            title={t`Repositories monitored`}
                            totalText={t`Out of ${monitoringTotalRepositories} total`}
                            statValue={
                                monitoringNumRepositoriesMonitored > 0
                                    ? monitoringNumRepositoriesMonitored
                                    : 0
                            }
                        />
                    </Box>
                    <Box
                        borderColor="neutralBorderInitial"
                        borderWidth="borderWidth1"
                        borderRadius="borderRadius2x"
                        p="4x"
                    >
                        <StatBlock
                            title={t`Failed tests`}
                            statValue={monitoringStatsFailedTests}
                            statIcon="Cancel"
                            statIconColor="critical"
                        />
                    </Box>
                    <Box
                        borderColor="neutralBorderInitial"
                        borderWidth="borderWidth1"
                        borderRadius="borderRadius2x"
                        p="4x"
                    >
                        <StatBlock
                            title={t`Passed tests`}
                            statValue={monitoringStatsPassedTests}
                            statIcon="CheckCircle"
                            statIconColor="success"
                        />
                    </Box>
                </Grid>
            )}

            <AppDatatable
                isFullPageTable
                isRowSelectionEnabled
                isLoading={isLoading}
                data={monitoringCodeListData}
                columns={getMonitoringCodeColumns()}
                total={monitoringCodeTotal}
                filterProps={getMonitoringCodeFilters.get()}
                tableId="datatable-monitoring-code"
                data-id="datatable-monitoring-code"
                data-testid="MonitoringCodeView"
                bulkActionDropdownItems={bulkActions}
                getRowId={(row) => String(row.testId)}
                emptyStateProps={{
                    title: t`No monitoring code found`,
                    description: t`No monitoring code found`,
                }}
                tableSearchProps={{
                    hideSearch: false,
                    placeholder: t`Search`,
                }}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: t`Pin filters to page`,
                        toggleUnpinnedLabel: t`Move filters to dropdown`,
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={monitoringCodeListLoad}
                onRowSelection={handleRowSelection}
                onRowClick={({ row }) => {
                    navigate(`${row.testId}/overview`);
                }}
            />
        </>
    );
});
