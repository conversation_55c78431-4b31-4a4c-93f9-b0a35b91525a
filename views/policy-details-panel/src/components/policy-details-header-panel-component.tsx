import { getPolicyStatusLabel } from '@components/policies';
import { panelController } from '@controllers/panel';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { Button } from '@cosmos/components/button';
import { Grid } from '@cosmos/components/grid';
import { Metadata } from '@cosmos/components/metadata';
import { PanelControls, PanelHeader } from '@cosmos/components/panel';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedPolicyDetailsModel } from '@models/policies';
import { useNavigate } from '@remix-run/react';

export const PolicyDetailsHeaderPanelComponent = observer(
    (): React.JSX.Element => {
        const navigate = useNavigate();
        const {
            name,
            onNextPageClick,
            onPrevPageClick,
            currentIndex,
            totalItems,
            versionStatus,
        } = sharedPolicyDetailsModel;
        const { isPolicyLoading, policy: policyDetails } =
            sharedPolicyBuilderController;

        const navigateToDetails = (): void => {
            panelController.closePanel();
            navigate(
                `governance/policies/builder/${policyDetails?.id}/overview`,
            );
        };

        return (
            <>
                <PanelControls
                    closeButtonLabel={t`Close`}
                    pagination={{
                        currentItem: currentIndex + 1,
                        onNextPageClick,
                        onPrevPageClick,
                        totalItems,
                    }}
                    onClose={() => {
                        panelController.closePanel();
                    }}
                />
                <PanelHeader
                    data-id="97LOuuOO"
                    data-testid="PolicyDetailsHeaderPanelComponent"
                    title={isPolicyLoading ? t`Loading...` : name}
                    slot={
                        <Grid columns="1fr auto" align="center" gap="md">
                            <Metadata
                                type="tag"
                                label={
                                    getPolicyStatusLabel(versionStatus).label
                                }
                                colorScheme={
                                    getPolicyStatusLabel(versionStatus)
                                        .colorScheme
                                }
                            />
                            <Button
                                colorScheme="primary"
                                endIconName="Expand"
                                label={t`Open`}
                                level="secondary"
                                size="sm"
                                onClick={() => {
                                    navigateToDetails();
                                }}
                            />
                        </Grid>
                    }
                />
            </>
        );
    },
);
