import type { DatatableProps } from '@cosmos/components/datatable';
import type { CustomerRequestListItemResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { ActionsCell } from '../lib/requests-table-cells/actions-cell';
import { ControlsCell } from '../lib/requests-table-cells/controls-cell';
import { MessagesCell } from '../lib/requests-table-cells/messages-cell';
import { OwnersCell } from '../lib/requests-table-cells/owners-cell';
import { StatusCell } from '../lib/requests-table-cells/status-cell';

export const getAuditorsListTableColumns =
    (): DatatableProps<CustomerRequestListItemResponseDto>['columns'] =>
        [
            {
                accessorKey: 'id',
                header: '',
                id: 'actions',
                enableSorting: false,
                enableHiding: true,
                cell: ActionsCell,
                isActionColumn: true,
                meta: { shouldIgnoreRowClick: true },
                minSize: 80,
            },
            {
                accessorKey: 'unreadMessages',
                header: t`Message`,
                id: 'message',
                enableSorting: false,
                cell: MessagesCell,
                minSize: 90,
            },
            {
                accessorKey: 'status',
                header: t`Status`,
                id: 'status',
                enableSorting: false,
                cell: StatusCell,
                size: 128,
                maxSize: 128,
                minSize: 128,
            },
            {
                accessorKey: 'code',
                header: t`ID`,
                id: 'code',
                enableSorting: false,
                size: 128,
                maxSize: 128,
                minSize: 128,
            },
            {
                accessorKey: 'description',
                header: t`Title`,
                id: 'title',
                enableSorting: false,
                size: 336,
                maxSize: 336,
                minSize: 336,
            },
            {
                accessorKey: 'controls',
                header: t`Controls`,
                id: 'controls',
                enableSorting: false,
                cell: ControlsCell,
                meta: { shouldIgnoreRowClick: true },
                size: 336,
                maxSize: 336,
                minSize: 336,
            },
            {
                accessorKey: 'owners',
                header: t`Owners`,
                id: 'owners',
                enableSorting: false,
                cell: OwnersCell,
                meta: { shouldIgnoreRowClick: true },
                size: 336,
                maxSize: 336,
                minSize: 336,
            },
        ] as const satisfies DatatableProps<CustomerRequestListItemResponseDto>['columns'];
