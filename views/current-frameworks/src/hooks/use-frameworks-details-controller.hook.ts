import { useEffect, useMemo } from 'react';
import { FrameworkDetailsController } from '@controllers/frameworks';
import { action, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

/**
 * # useFrameworkDetailsController
 *
 * - Instantiate FrameworkDetailsController
 * - Load framework details
 * - Load profile and level if needed.
 */
export function useFrameworkDetailsController(
    frameworkId: number,
): FrameworkDetailsController {
    const { currentWorkspace } = sharedWorkspacesController;

    const currentWorkspaceId = currentWorkspace?.id;

    const frameworksDetailsController = useMemo(
        () => new FrameworkDetailsController(),
        [],
    );

    useEffect(() => {
        if (!currentWorkspaceId || isNaN(frameworkId) || frameworkId === 0) {
            return;
        }

        action(() => {
            frameworksDetailsController.frameworkDetailsQuery.load({
                path: {
                    frameworkId,
                    xProductId: currentWorkspaceId,
                },
            });
        })();

        when(
            () => !frameworksDetailsController.isLoading,
            () => {
                if (frameworksDetailsController.usesProfile) {
                    frameworksDetailsController.getProfileQuery.load({
                        path: {
                            frameworkId,
                        },
                    });
                }

                if (frameworksDetailsController.usesLevel) {
                    frameworksDetailsController.getLevelQuery.load({
                        path: {
                            frameworkId,
                        },
                    });
                }
            },
        );
    }, [
        currentWorkspaceId,
        frameworkId,
        frameworksDetailsController.frameworkDetailsQuery,
        frameworksDetailsController.getLevelQuery,
        frameworksDetailsController.getProfileQuery,
        frameworksDetailsController.isLoading,
        frameworksDetailsController.usesLevel,
        frameworksDetailsController.usesProfile,
    ]);

    return useMemo(
        () => frameworksDetailsController,
        [frameworksDetailsController],
    );
}
