import { modalController } from '@controllers/modal';
import { runInAction } from '@globals/mobx';
import { ImportQuestionsModal } from '@views/vendors-questionnaires-add';

export const openImportQuestionsModal = (): void => {
    runInAction(() => {
        modalController.openModal({
            id: 'import-questions-modal',
            content: () => <ImportQuestionsModal data-id="JrezEQ99" />,
            size: 'md',
        });
    });
};
