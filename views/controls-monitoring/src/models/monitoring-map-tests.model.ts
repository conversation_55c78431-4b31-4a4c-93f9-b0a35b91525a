import { isEmpty } from 'lodash-es';
import { closeMapAutomatedTestModal } from '@components/map-tests-modal';
import { sharedMonitoringMapTestsMutationController } from '@controllers/monitoring-details';
import {
    sharedMonitorsController,
    sharedMonitorsInfiniteController,
} from '@controllers/monitors';
import { makeAutoObservable } from '@globals/mobx';
import { MAP_TESTS_MODAL_ID } from '../constants/modal.constant';

class MonitoringMapTestsModel {
    constructor() {
        makeAutoObservable(this);
    }

    saveMappedTests = async (): Promise<void> => {
        const { selectedMonitors } = sharedMonitorsInfiniteController;

        if (isEmpty(selectedMonitors)) {
            closeMapAutomatedTestModal(MAP_TESTS_MODAL_ID);

            return;
        }

        const { controlId } = sharedMonitorsController;
        const testIds = selectedMonitors.map((monitor) => monitor.testId);

        await sharedMonitoringMapTestsMutationController.mapTestsToControl(
            [Number(controlId)],
            testIds,
        );

        sharedMonitorsInfiniteController.clearSelectedMonitors();
    };
}

export const sharedMonitoringMapTestsModel = new MonitoringMapTestsModel();
