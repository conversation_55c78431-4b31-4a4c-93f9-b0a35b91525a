import { useMemo } from 'react';
import { sharedAuditCreationWizardController } from '@controllers/audit-creation-wizard';
import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { breakpointMd } from '@cosmos/constants/tokens';
import { Wizard, type WizardProps } from '@cosmos-lab/components/wizard';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { AssignAuditorsStep } from './steps/assign-auditors-step';
import { AuditDetailsStep } from './steps/audit-details-step';
import { ConductAuditStep } from './steps/conduct-audit-step';
import { ReviewStep } from './steps/review-step';

export const AuditCreationWizardView = observer((): React.JSX.Element => {
    const { cancelWizard, completeWizard } =
        sharedAuditCreationWizardController;

    const {
        formRef: conductAuditFormRef,
        triggerSubmit: triggerSubmitConductAudit,
    } = useFormSubmit();

    const wizardProps: WizardProps = useMemo(
        () => ({
            steps: [
                {
                    component: () => (
                        <ConductAuditStep
                            formRef={conductAuditFormRef}
                            data-id="ahTBO87M"
                        />
                    ),
                    stepTitle: t`Conduct audit`,
                    isStepSkippable: false,
                    onStepChange: async () => {
                        return triggerSubmitConductAudit();
                    },
                },
                {
                    component: () => <AuditDetailsStep data-id="m-mS2nYi" />,
                    stepTitle: t`Audit details`,
                    isStepSkippable: false,
                    onStepChange: async () => {
                        return Promise.resolve(
                            sharedAuditCreationWizardController.validateAuditDetailsStep(),
                        );
                    },
                },
                {
                    component: () => <AssignAuditorsStep data-id="YInRbMv6" />,
                    stepTitle: t`Assign auditors`,
                    isStepSkippable: false,
                    onStepChange: async () => {
                        return Promise.resolve(
                            sharedAuditCreationWizardController.validateAssignAuditorsStep(),
                        );
                    },
                },
                {
                    component: () => <ReviewStep data-id="TGZFONMc" />,
                    stepTitle: t`Review`,
                    isStepSkippable: false,
                },
            ],
            onCancel: cancelWizard,
            onComplete: completeWizard,
        }),
        [
            cancelWizard,
            completeWizard,
            conductAuditFormRef,
            triggerSubmitConductAudit,
        ],
    );

    return (
        <Stack
            justify="center"
            height="100%"
            data-id="audit-creation-wizard-view"
        >
            <Box width={breakpointMd}>
                <Wizard
                    steps={wizardProps.steps}
                    completeButtonLabel={t`Create Audit`}
                    nextButtonLabel={t`Continue`}
                    onCancel={cancelWizard}
                    onComplete={completeWizard}
                />
            </Box>
        </Stack>
    );
});
