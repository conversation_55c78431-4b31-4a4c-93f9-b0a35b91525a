import { forwardRef } from 'react';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';

const BaseAuditDetailsStep = (
    _props: Record<string, never>,
    ref: React.ForwardedRef<HTMLDivElement>,
) => {
    return (
        <Stack
            ref={ref}
            direction="column"
            gap="5xl"
            data-id="audit-details-step"
        >
            <Stack direction="column" align="center" gap="md">
                <Text as="h1" type="headline" size="600">
                    {t`Audit Details`}
                </Text>
                <Text as="h2" type="subheadline" size="100" align="center">
                    {t`Configure the details for your audit`}
                </Text>
            </Stack>

            {/* TODO: Add audit details form in https://drata.atlassian.net/browse/ENG-72178*/}
            <Stack direction="column" gap="lg">
                <Text type="body" size="200" colorScheme="neutral">
                    {t`Audit details form will be implemented here`}
                </Text>
            </Stack>
        </Stack>
    );
};

export const AuditDetailsStep = forwardRef(BaseAuditDetailsStep);
