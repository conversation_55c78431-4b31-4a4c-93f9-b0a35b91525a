import { forwardRef } from 'react';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';

const BaseReviewStep = (
    _props: Record<string, never>,
    ref: React.ForwardedRef<HTMLDivElement>,
) => {
    return (
        <Stack ref={ref} direction="column" gap="5xl" data-id="review-step">
            <Stack direction="column" align="center" gap="md">
                <Text as="h1" type="headline" size="600">
                    {t`Review`}
                </Text>
                <Text as="h2" type="subheadline" size="100" align="center">
                    {t`Review your audit configuration`}
                </Text>
            </Stack>

            {/* TODO: Add review content */}
            <Stack direction="column" gap="lg">
                <Text type="body" size="200" colorScheme="neutral">
                    {t`Review content will be implemented here`}
                </Text>
            </Stack>
        </Stack>
    );
};

export const ReviewStep = forwardRef(BaseReviewStep);
