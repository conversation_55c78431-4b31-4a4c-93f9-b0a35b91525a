import { forwardRef } from 'react';
import { sharedAuditCreationWizardController } from '@controllers/audit-creation-wizard';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedConductAuditStepModel } from '@models/audit-creation-wizard';
import { Form, type FormValues } from '@ui/forms';

interface ConductAuditStepProps {
    formRef: React.ForwardedRef<HTMLFormElement>;
}

const BaseConductAuditStep = (
    { formRef }: ConductAuditStepProps,
    ref: React.ForwardedRef<HTMLDivElement>,
) => {
    const { setConductAuditOption } = sharedAuditCreationWizardController;
    const { formSchema } = sharedConductAuditStepModel;

    const handleSubmit = (values: FormValues) => {
        setConductAuditOption(values.conductAuditOption as string);
    };

    return (
        <Stack
            ref={ref}
            direction="column"
            gap="5xl"
            data-id="conduct-audit-step"
            data-testid="BaseConductAuditStep"
        >
            <Stack direction="column" align="center" gap="md">
                <Text as="h1" type="headline" size="600">
                    {t`Choose how to conduct an audit`}
                </Text>
            </Stack>

            <Form
                hasExternalSubmitButton
                ref={formRef}
                formId="conduct-audit-form"
                data-id="conduct-audit-form"
                schema={formSchema}
                onSubmit={handleSubmit}
            />
        </Stack>
    );
};

export const ConductAuditStep = observer(
    forwardRef<HTMLDivElement, ConductAuditStepProps>(BaseConductAuditStep),
);
