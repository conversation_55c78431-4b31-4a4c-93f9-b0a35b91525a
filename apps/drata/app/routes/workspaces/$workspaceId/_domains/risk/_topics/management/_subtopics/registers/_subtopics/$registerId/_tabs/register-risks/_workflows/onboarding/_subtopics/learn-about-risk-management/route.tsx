import type { ClientLoader } from '@app/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { RouteLandmark } from '@ui/layout-landmarks';
import { RiskRegisterOnboardingLearnView } from '@views/risk-register-onboarding-learn';

export const meta: MetaFunction = () => {
    return [{ title: t`Risk register onboarding` }];
};

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { registerId } = params;

        return {
            pageHeader: {
                title: t`Get started with risk management`,
                pageId: 'risk-register-onboarding-learn',
                backLink: (
                    <AppLink
                        href={`risk/management/registers/${registerId}/register-risks`}
                        size="sm"
                    >{t`Back to risk register`}</AppLink>
                ),
                isCentered: true,
            },
            contentNav: {
                tabs: [],
            },
        };
    },
);

const RiskRegisterOnboardingLearn = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="RiskRegisterOnboardingLearn"
            data-id="risk-register-onboarding-learn"
        >
            <RiskRegisterOnboardingLearnView />
        </RouteLandmark>
    );
};

export default RiskRegisterOnboardingLearn;
