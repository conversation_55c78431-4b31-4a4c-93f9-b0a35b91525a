import { t } from '@globals/i18n/macro';
import type { MetaFunction } from '@remix-run/node';
import { RouteLandmark } from '@ui/layout-landmarks';
import { RiskRegisterLibraryView } from '@views/risk-register-library';

export const meta: MetaFunction = () => {
    return [{ title: t`Library Risks` }];
};

const RiskLibraryRisks = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="RiskLibraryRisks"
            data-id="sPQHz2jj"
        >
            <RiskRegisterLibraryView />
        </RouteLandmark>
    );
};

export default RiskLibraryRisks;
