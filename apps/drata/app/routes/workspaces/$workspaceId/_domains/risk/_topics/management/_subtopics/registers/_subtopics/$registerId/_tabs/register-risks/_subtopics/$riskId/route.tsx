import type { ClientLoader } from '@app/types';
import {
    sharedRiskCustomFieldsSubmissionsController,
    sharedRiskTicketsController,
} from '@controllers/risk';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import type { UtilitiesName } from '@controllers/route';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { action, when } from '@globals/mobx';
import {
    RiskDetailsContentHeaderModel,
    RiskDetailsContentNavModel,
} from '@models/risk-details';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: 'Risk Detail Overview' }];
};

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { riskId } = params;

        if (!riskId) {
            throw new Error('Risk ID is required.');
        }

        sharedRiskDetailsController.loadRiskDetails(riskId);
        sharedRiskTicketsController.load(riskId);

        when(
            () =>
                !sharedRiskDetailsController.isLoading &&
                Boolean(sharedRiskDetailsController.riskDetails?.id),
            () => {
                const riskNumericId =
                    sharedRiskDetailsController.riskDetails?.id;

                if (riskNumericId) {
                    sharedRiskCustomFieldsSubmissionsController.load(
                        riskNumericId,
                    );
                }
            },
        );

        const utilitiesList: UtilitiesName[] = ['notes_for_risk_management'];

        const hasRiskProEntitlements =
            sharedEntitlementFlagController.isRiskManagementEnabled ||
            sharedEntitlementFlagController.isVendorRiskManagementProEnabled;

        if (hasRiskProEntitlements) {
            utilitiesList.push(
                'tickets_for_risk_management',
                'tasks_for_risks',
            );
        }

        return {
            pageHeader: new RiskDetailsContentHeaderModel(),
            contentNav: new RiskDetailsContentNavModel(),
            utilities: {
                utilitiesList,
            },
        };
    },
);

const RiskManagementDetails = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="RiskManagementDetails"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default RiskManagementDetails;
