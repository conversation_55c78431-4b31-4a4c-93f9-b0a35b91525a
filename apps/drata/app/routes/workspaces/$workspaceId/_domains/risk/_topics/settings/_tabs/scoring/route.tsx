import type { ClientLoader } from '@app/types';
import { sharedRiskSettingsController } from '@controllers/risk-settings';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { RouteLandmark } from '@ui/layout-landmarks';
import { RiskSettingsScoring } from '@views/risk-settings-scoring';

export const clientLoader = action((): ClientLoader => {
    sharedRiskSettingsController.load();

    return {
        pageHeader: {
            title: t`Risk settings`,
            isCentered: true,
        },
    };
});

const ScoringTab = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="ScoringTab"
            data-id="RiskSettingsScoring"
        >
            <RiskSettingsScoring />
        </RouteLandmark>
    );
};

export default ScoringTab;
