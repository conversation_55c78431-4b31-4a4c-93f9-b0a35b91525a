import type { ClientLoader } from '@app/types';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { AppLink } from '@ui/app-link';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [{ title: 'Risk Settings' }];

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            isCentered: true,
            title: 'Risk Settings',
            backLink: (
                <AppLink
                    href="risk/register"
                    label="Back to Risk Register"
                    size="sm"
                />
            ),
        },
        tabs: [],
    };
});

const RiskRegisterSettings = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="RiskRegisterSettings"
            data-id="Nv6ynsTe"
        >
            <h1>RiskRegisterSettings</h1>
            <h2>TODO: This needs some help. Talk to chris</h2>
        </RouteLandmark>
    );
};

export default RiskRegisterSettings;
