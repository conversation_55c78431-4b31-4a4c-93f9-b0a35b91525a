import type { ClientLoader } from '@app/types';
import { sharedRiskCategoriesController } from '@controllers/risk';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { RouteLandmark } from '@ui/layout-landmarks';
import { RiskSettingsCategoriesView } from '@views/risk-settings-categories';

export const clientLoader = action((): ClientLoader => {
    sharedRiskCategoriesController.loadCategories();

    return {
        pageHeader: {
            title: t`Risk settings`,
            isCentered: true,
        },
    };
});

const CategoriesTab = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="CategoriesTab"
            data-id="RiskSettingsCategories"
        >
            <RiskSettingsCategoriesView />
        </RouteLandmark>
    );
};

export default CategoriesTab;
