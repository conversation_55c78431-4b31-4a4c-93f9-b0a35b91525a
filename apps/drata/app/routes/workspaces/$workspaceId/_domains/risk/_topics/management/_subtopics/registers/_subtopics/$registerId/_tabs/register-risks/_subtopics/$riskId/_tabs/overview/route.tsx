import { t } from '@globals/i18n/macro';
import type { MetaFunction } from '@remix-run/node';
import { RiskRegisterOverviewView } from '@views/risk-register-overview';

export const meta: MetaFunction = () => [{ title: t`Risk Overview` }];

const RiskRegisterManagementOverview = (): React.JSX.Element => {
    return (
        <RiskRegisterOverviewView
            data-testid="RiskRegisterManagementOverview"
            data-id="2S5so7Un"
        />
    );
};

export default RiskRegisterManagementOverview;
