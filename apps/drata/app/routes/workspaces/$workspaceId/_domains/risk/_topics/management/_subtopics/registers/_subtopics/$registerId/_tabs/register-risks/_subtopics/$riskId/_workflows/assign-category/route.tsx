import type { MetaFunction } from '@remix-run/node';
import { RiskRegisterAssignCategoryView } from '@views/risk-register-assign-category';

export const meta: MetaFunction = () => [{ title: 'Assign risk category' }];

const RiskRegisterAssignCategory = (): React.JSX.Element => {
    return (
        <RiskRegisterAssignCategoryView
            data-testid="RiskRegisterAssignCategory"
            data-id="ViT_56hP"
        />
    );
};

export default RiskRegisterAssignCategory;
