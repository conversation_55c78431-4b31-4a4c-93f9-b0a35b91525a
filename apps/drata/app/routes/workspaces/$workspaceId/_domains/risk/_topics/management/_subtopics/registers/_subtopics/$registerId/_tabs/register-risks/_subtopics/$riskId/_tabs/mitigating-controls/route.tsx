import { t } from '@globals/i18n/macro';
import type { MetaFunction } from '@remix-run/node';
import { RiskRegisterMitigationControlsView } from '@views/risk-register-mitigation-controls';

export const meta: MetaFunction = () => {
    return [{ title: t`Risk Mitigating Controls` }];
};

const RiskRegisterManagementMitigatingControls = (): React.JSX.Element => {
    return (
        <RiskRegisterMitigationControlsView
            data-testid="RiskRegisterManagementMitigatingControls"
            data-id="DTQmr464"
        />
    );
};

export default RiskRegisterManagementMitigatingControls;
