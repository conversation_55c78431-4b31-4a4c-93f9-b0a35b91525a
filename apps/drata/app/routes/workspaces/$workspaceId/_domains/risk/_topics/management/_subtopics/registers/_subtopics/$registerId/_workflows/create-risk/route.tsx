import type { ClientLoader } from '@app/types';
import {
    RISK_OWNER_ROLES,
    sharedRiskCategoriesController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { sharedUsersInfiniteController } from '@controllers/users';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { RiskRegisterCreateRiskView } from '@views/risk-register-create-risk';

export const meta: MetaFunction = () => [{ title: 'New risk' }];

export const clientLoader = action(
    ({ params }: ClientLoaderFunctionArgs): ClientLoader => {
        sharedUsersInfiniteController.loadUsers({ roles: RISK_OWNER_ROLES });
        sharedRiskCategoriesController.loadCategories();
        sharedRiskSettingsController.load();

        return {
            pageHeader: {
                isCentered: true,
                title: 'Add Risk',
                backLink: (
                    <AppLink
                        href={`risk/management/registers/${params.registerId}/library-risks`}
                        size="sm"
                    >
                        {t`Back to Risk Management`}
                    </AppLink>
                ),
            },
            contentNav: {
                tabs: [],
            },
        };
    },
);

const RiskRegisterCreateRisk = (): React.JSX.Element => {
    return (
        <RiskRegisterCreateRiskView
            data-testid="RiskRegisterCreateRisk"
            data-id="Svebw83n"
        />
    );
};

export default RiskRegisterCreateRisk;
