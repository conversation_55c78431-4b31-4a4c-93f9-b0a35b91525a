import type { <PERSON>lientLoader } from '@app/types';
import { sharedConnectionsController } from '@controllers/connections';
import {
    sharedRiskManagementController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_INDEX,
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => {
    return [{ title: t`Register Risks` }];
};

export const clientLoader = action((): ClientLoader => {
    sharedRiskManagementController.loadRiskManagement({
        pagination: {
            page: DEFAULT_PAGE,
            pageSize: DEFAULT_PAGE_SIZE,
            pageIndex: DEFAULT_PAGE_INDEX,
            pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
        },
        globalFilter: {
            search: '',
            filters: {},
        },
        sorting: [],
    });

    sharedRiskManagementController.loadStatistics();

    sharedRiskManagementController.loadDashboard();

    sharedRiskSettingsController.load();

    /**
     * We need this to check if the user has a ticketing connection with write access.
     * ```
     * sharedConnectionsController.hasTicketingConnectionWithWriteAccess
     * ```
     */
    sharedConnectionsController.allConfiguredConnectionsQuery.load();

    return null;
});

const RiskRegisterRisks = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="RiskRegisterRisks"
            data-id="5b7ROC3p"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default RiskRegisterRisks;
