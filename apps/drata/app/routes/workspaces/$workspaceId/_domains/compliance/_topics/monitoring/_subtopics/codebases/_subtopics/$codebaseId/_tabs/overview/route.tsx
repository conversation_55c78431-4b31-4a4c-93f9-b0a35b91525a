import type { ClientLoader } from '@app/types';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedEventsController } from '@controllers/events';
import {
    activeMonitoringController,
    activeTicketsMetadataController,
    activeTrackCardController,
    sharedMonitorFindingsController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { action } from '@globals/mobx';
import { MonitoringCodePageHeaderModel } from '@models/monitoring-code-details';
import type { ClientLoaderFunction } from '@remix-run/react';
import { MonitoringDetailsOverviewView } from '@views/monitoring-details-overview';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { codebaseId: testId } = params;

        activeMonitoringController.loadMonitor(Number(testId));
        sharedConnectionsController.allConfiguredConnectionsQuery.load();
        sharedEventsController.loadEvents();
        activeTicketsMetadataController.loadTicketsMetadata(Number(testId));
        activeTrackCardController.loadTrackData(Number(testId));
        sharedMonitorFindingsController.loadFindingsResponse(Number(testId));
        sharedMonitoringTestDetailsController.loadTest(Number(testId));

        return {
            pageHeader: new MonitoringCodePageHeaderModel(),
        };
    },
);

const MonitoringCodeDetailsOverview = (): React.JSX.Element => {
    return (
        <MonitoringDetailsOverviewView
            data-testid="MonitoringCodeDetailsOverview"
            data-id="BIlnuYYz"
        />
    );
};

export default MonitoringCodeDetailsOverview;
