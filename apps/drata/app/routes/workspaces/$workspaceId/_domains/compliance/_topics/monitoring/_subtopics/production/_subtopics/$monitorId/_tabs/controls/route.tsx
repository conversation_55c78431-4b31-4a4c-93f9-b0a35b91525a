import type { ClientLoader } from '@app/types';
import { sharedMonitoringDetailsControlsController } from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { action } from '@globals/mobx';
import type { ClientLoaderFunction } from '@remix-run/react';
import { MonitoringDetailsControlsView } from '@views/monitoring-details-controls';

const DEFAULT_PARAMS: FetchDataResponseParams = {
    pagination: {
        page: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        pageIndex: 0,
        pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
    },
    globalFilter: {
        search: '',
        filters: {},
    },
    sorting: [],
};

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { monitorId } = params;
        const testId = Number(monitorId);

        sharedMonitoringDetailsControlsController.loadMonitoringDetailsControls(
            {
                params: DEFAULT_PARAMS,
            },
        );
        sharedMonitoringTestDetailsController.loadTest(testId);

        return null;
    },
);

const MonitoringDetailsControls = (): React.JSX.Element => {
    return (
        <MonitoringDetailsControlsView
            data-testid="MonitoringDetailsControls"
            data-id="AW9o6hGx"
        />
    );
};

export default MonitoringDetailsControls;
