import type { ClientLoader } from '@app/types';
import { sharedMonitoringDetailsControlsController } from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_INDEX,
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { action } from '@globals/mobx';
import type { ClientLoaderFunction } from '@remix-run/react';
import { MonitoringDetailsControlsView } from '@views/monitoring-details-controls';

const DEFAULT_PARAMS: FetchDataResponseParams = {
    pagination: {
        page: DEFAULT_PAGE,
        pageSize: DEFAULT_PAGE_SIZE,
        pageIndex: DEFAULT_PAGE_INDEX,
        pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
    },
    globalFilter: {
        search: '',
        filters: {},
    },
    sorting: [],
};

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { codebaseId } = params;
        const testId = Number(codebaseId);

        sharedMonitoringDetailsControlsController.loadMonitoringDetailsControls(
            {
                params: DEFAULT_PARAMS,
            },
        );
        sharedMonitoringTestDetailsController.loadTest(testId);

        return null;
    },
);

const MonitoringCodeDetailsControls = (): React.JSX.Element => {
    return (
        <MonitoringDetailsControlsView
            code
            data-testid="MonitoringCodeDetailsControls"
            data-id="AW9o6hGx"
        />
    );
};

export default MonitoringCodeDetailsControls;
