import type { ClientLoader } from '@app/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { AuditCreationWizardPageHeaderModel } from '@models/audit-creation-wizard';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AuditCreationWizardView } from '@views/audit-creation-wizard';

export const meta: MetaFunction = () => [{ title: t`Create Audit` }];

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    return {
        pageHeader: new AuditCreationWizardPageHeaderModel(),
        tabs: [],
    };
});

const AuditsCreateWizard = (): React.JSX.Element => {
    return (
        <AuditCreationWizardView
            data-testid="AuditsCreateWizard"
            data-id="0k1v8e2P"
        />
    );
};

export default AuditsCreateWizard;
