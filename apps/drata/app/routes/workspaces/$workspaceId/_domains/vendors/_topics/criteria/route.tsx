import type { ClientLoader } from '@app/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [{ title: 'Vendors Criteria' }];

export const clientLoader = action((): ClientLoader => {
    if (!sharedFeatureAccessModel.isVrmAgentMvpEnabled) {
        throw new Error('Missing permission to access Vendors Criteria', {
            cause: '403',
        });
    }

    return {
        pageHeader: {
            title: t`Criteria`,
            pageId: 'vendors-criteria-page',
        },
        utilities: {
            utilitiesList: ['vrm_agent_criteria'],
        },
    };
});

const VendorsCriteria = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsCriteria"
            data-id="YKRutia8"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsCriteria;
