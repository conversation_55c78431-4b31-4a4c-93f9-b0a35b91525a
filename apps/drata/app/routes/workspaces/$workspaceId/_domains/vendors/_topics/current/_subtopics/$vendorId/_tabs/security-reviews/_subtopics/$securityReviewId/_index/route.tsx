import { t } from '@globals/i18n/macro';
import type { MetaFunction } from '@remix-run/node';
import { VendorsProfileSecurityReviewView } from '@views/vendors-profile-security-review';

export const meta: MetaFunction = () => [
    { title: t`Current Vendors Security Reviews` },
];

const VendorsProfileSecurityReviews = (): React.JSX.Element => {
    return (
        <VendorsProfileSecurityReviewView
            data-testid="VendorsProfileSecurityReviews"
            data-id="tYXVaOAx"
        />
    );
};

export default VendorsProfileSecurityReviews;
