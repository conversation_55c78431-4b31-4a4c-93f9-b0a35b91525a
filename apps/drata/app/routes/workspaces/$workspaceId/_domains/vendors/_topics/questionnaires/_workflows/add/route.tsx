import type { <PERSON><PERSON><PERSON>oader } from '@app/types';
import type { PageHeaderOverrides } from '@controllers/page-header';
import { sharedVendorsQuestionnaireAddController } from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import { dimension3x } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable, observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import {
    getQuestionnairesAddHeaderActions,
    loadImportedQuestionsFromStorage,
    QUESTIONNAIRES_ADD_HEADER_PAGE_ID,
    VendorsQuestionnairesAddView,
} from '@views/vendors-questionnaires-add';

export const meta: MetaFunction = () => [
    { title: t`Create vendor questionnaire` },
];

export class VendorsQuestionnairesAddPageHeaderModel
    implements PageHeaderOverrides
{
    constructor() {
        makeAutoObservable(this);
    }

    pageId = QUESTIONNAIRES_ADD_HEADER_PAGE_ID;
    isCentered = true;

    get actionStack(): React.JSX.Element {
        return (
            <ActionStack
                data-id="vendors-questionnaires-add-page-action-stack"
                gap={dimension3x}
                stacks={[
                    {
                        actions: getQuestionnairesAddHeaderActions(true),
                        id: `${QUESTIONNAIRES_ADD_HEADER_PAGE_ID}-actions-stack-0`,
                    },
                ]}
            />
        );
    }

    get backLink(): React.JSX.Element {
        const { currentWorkspace } = sharedWorkspacesController;
        const questionnairesPath = `/workspaces/${currentWorkspace?.id}/vendors/questionnaires`;

        return (
            <AppLink href={questionnairesPath} size="sm">
                {t`Back to Questionnaires`}
            </AppLink>
        );
    }

    title = t`Create vendor questionnaire`;
}

const headerModel = new VendorsQuestionnairesAddPageHeaderModel();

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    // Reset the controller when navigating to add questionnaire to ensure clean state
    sharedVendorsQuestionnaireAddController.resetForNewQuestionnaire();

    // Clear any existing import banners when entering the page
    sharedVendorsQuestionnaireAddController.clearImportBanner();

    // Load imported questions from localStorage if available
    loadImportedQuestionsFromStorage();

    return {
        pageHeader: {
            title: headerModel.title,
            pageId: headerModel.pageId,
            actionStack: headerModel.actionStack,
            backLink: headerModel.backLink,
            isCentered: headerModel.isCentered,
        },
    };
});

const VendorsQuestionnairesAdd = observer((): React.JSX.Element => {
    return (
        <VendorsQuestionnairesAddView
            data-testid="VendorsQuestionnairesAdd"
            data-id="hBxXw1W2"
        />
    );
});

export default VendorsQuestionnairesAdd;
