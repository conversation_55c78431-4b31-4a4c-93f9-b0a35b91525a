import { t } from '@globals/i18n/macro';
import type { MetaFunction } from '@remix-run/node';
import { VendorsCriteriaView } from '@views/vendors-criteria';

export const meta: MetaFunction = () => [{ title: t`Vendors Criteria` }];

const VendorsCriteria = (): React.JSX.Element => {
    return (
        <VendorsCriteriaView data-testid="VendorsCriteria" data-id="YKRutia8" />
    );
};

export default VendorsCriteria;
