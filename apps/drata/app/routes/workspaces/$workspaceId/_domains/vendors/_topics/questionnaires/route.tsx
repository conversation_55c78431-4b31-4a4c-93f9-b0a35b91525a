import type { ClientLoader } from '@app/types';
import { sharedVendorsTypeformQuestionnairesController } from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import { dimension3x } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet, useNavigate } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import {
    getVendorsQuestionnairesPageHeaderActions,
    VENDORS_QUESTIONNAIRES_PAGE_HEADER_KEY,
} from '@views/vendors-questionnaires';

export const meta: MetaFunction = () => [{ title: 'Vendors Questionnaires' }];

const VendorsQuestionnairesActionStack = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    return (
        <ActionStack
            data-id="vendors-questionnaires-page-action-stack"
            gap={dimension3x}
            data-testid="VendorsQuestionnairesActionStack"
            stacks={[
                {
                    actions:
                        getVendorsQuestionnairesPageHeaderActions(navigate) ??
                        [],
                    id: `${VENDORS_QUESTIONNAIRES_PAGE_HEADER_KEY}-actions-stack`,
                },
            ]}
        />
    );
});

export const clientLoader = action((): ClientLoader => {
    sharedVendorsTypeformQuestionnairesController.loadQuestionnaires({
        globalFilter: { search: '', filters: {} },
        pagination: {
            page: 1,
            pageIndex: 0,
            pageSize: DEFAULT_PAGE_SIZE,
            pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
        },
        sorting: [],
    });

    return {
        pageHeader: {
            title: t`Questionnaires`,
            pageId: 'vendors-questionnaires-page',
            actionStack: <VendorsQuestionnairesActionStack />,
        },
        utilities: {
            utilitiesList: ['vrm_agent_criteria'],
        },
    };
});

const VendorsQuestionnaires = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsQuestionnaires"
            data-id="YKRutia8"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsQuestionnaires;
