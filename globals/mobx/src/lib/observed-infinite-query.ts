import { isFunction } from 'lodash-es';
// eslint-disable-next-line no-restricted-imports -- this is the only place mobx-react is allowed, DO NOT COPY
import { makeAutoObservable } from 'mobx';
import { queryClient } from '@globals/query-client';
import { MobxInfiniteQuery } from './MobxInfiniteQuery';
import type { AnyInfiniteOptions, InfiniteQueryReponse } from './types';

export type InfiniteQueryInput = (
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- todo
    ...args: any[]
) => AnyInfiniteOptions;

type TData<T extends InfiniteQueryInput> = Awaited<
    ReturnType<NotSymbol<NotUndefined<ReturnType<T>['queryFn']>>>
>;

type InfiniteQueryOptions<T extends InfiniteQueryInput> = Omit<
    ReturnType<T>,
    'queryKey' | 'queryFn' | `_${string}`
>;

interface PaginationResponseDto {
    /**
     * Generic Pagination data.
     */
    data: unknown[];
    /**
     * Which page of data are you requesting.
     */
    page: number;
    /**
     * How many items are you requesting.
     */
    limit: number;
    /**
     * How many items are in the overall set.
     */
    total: number;
}

function makeObserver<
    T extends InfiniteQueryInput,
    R extends InfiniteQueryOptions<T> = InfiniteQueryOptions<T>,
>(apiSdkFn: T, parameters: Parameters<T>, observerOptions: R) {
    return new MobxInfiniteQuery<
        TData<T>,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any -- todo
        any,
        TData<T>,
        ReturnType<T>['queryKey'],
        ReturnType<T>['initialPageParam']
    >(
        queryClient,

        () => {
            const options = {
                ...apiSdkFn(...parameters),
                ...observerOptions,
            } as AnyInfiniteOptions;

            if (!isFunction(options.getNextPageParam)) {
                options.getNextPageParam = (
                    lastPage: PaginationResponseDto,
                ) => {
                    if (lastPage.page * lastPage.limit >= lastPage.total) {
                        return null;
                    }

                    return lastPage.page + 1;
                };
            }

            if (!isFunction(options.getPreviousPageParam)) {
                options.getPreviousPageParam = (
                    firstPage: PaginationResponseDto,
                ) => {
                    if (firstPage.page <= 1) {
                        return false;
                    }

                    return firstPage.page - 1;
                };
            }

            options.initialPageParam ??= 1;

            return options;
        },
    );
}

export class ObservedInfiniteQuery<
    T extends InfiniteQueryInput,
    R extends InfiniteQueryOptions<T> = InfiniteQueryOptions<T>,
> {
    #apiSdkFn: T;
    #observerOptions: R;

    constructor(apiSdkFn: T, observerOptions?: R) {
        makeAutoObservable(this);
        this.#apiSdkFn = apiSdkFn;
        this.#observerOptions = observerOptions ?? ({} as R);
    }

    /**
     * The query being observed.
     */
    query: ReturnType<typeof makeObserver<T, R>> | null = null;

    /**
     * Has the api call been completed at least once (success doesn't matter).
     */
    get isReady(): boolean {
        return this.query === null;
    }

    /**
     * Are we fetching the FIRST page.
     *
     * Subsequent pages will not update this status.
     *
     * Useful for showing a whole-component loader/skeleton.
     */
    get isLoading(): boolean {
        return this.query?.state.isLoading ?? true;
    }

    /**
     * Status of ALL pages fetching.
     *
     * Useful for showing the small loading-next-page spinner/skeleton.
     */
    get isFetching(): boolean {
        return this.query?.state.isFetching ?? true;
    }

    /**
     * Convenience method for getting the error status of the query results.
     */
    get hasError(): boolean {
        return this.query?.state.isError ?? false;
    }

    /**
     * Convenience method for getting the Error of the query results.
     */
    get error(): Error | null {
        // this cast is only needed because the generic uses `any`
        return (this.query?.state.error ?? null) as Error | null;
    }

    /**
     * Convenience method for getting the hasNextPage of the query results.
     */
    get hasNextPage(): boolean {
        return this.query?.state.hasNextPage ?? false;
    }

    /**
     * Convenience method for getting the response from the query.
     */
    get data(): InfiniteQueryReponse<T> | null {
        // this cast is only needed because the generic uses `any`
        return (this.query?.state.data ?? null) as InfiniteQueryReponse<T>;
    }

    /**
     * Init and fetch the query.
     */
    load = (...args: Parameters<T>): void => {
        this.query?.dispose();
        this.query = makeObserver(this.#apiSdkFn, args, this.#observerOptions);
    };

    /**
     * Clear the query as if we never called `.load()`.
     */
    unload = (): void => {
        this.query = null;
    };

    /**
     * Get the next page of query results (if possible).
     */
    nextPage = (): void => {
        if (this.query === null) {
            throw new Error('Query not loaded');
        }

        this.query.nextPage();
    };

    /**
     * Get the previous page of query results (if possible).
     */
    prevPage = (): void => {
        if (this.query === null) {
            throw new Error('Query not loaded');
        }

        this.query.prevPage();
    };

    /**
     * Invalidate the query by query key.
     *
     * This will cause the query to be refetched everywhere it is used in a controller.
     *
     * Note from Kirk Rehal:
     * Does this actually work on infinite queries? I have not had success.
     * Instead, the removeInfiniteQuery() method seems to properly work.
     */
    invalidate = (): void => {
        if (this.query === null) {
            return;
        }

        const { queryKey } = this.query.queryOptions;

        queryClient.invalidateQueries({ queryKey }).catch((error) => {
            console.error('Error invalidating query', { error, queryKey });
        });
    };

    remove = (): void => {
        if (this.query === null) {
            return;
        }

        const { queryKey } = this.query.queryOptions;

        queryClient.removeQueries({ queryKey, exact: true });
    };
}
