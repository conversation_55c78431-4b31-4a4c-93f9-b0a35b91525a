import { isError, isNil } from 'lodash-es';
import type { RefObject } from 'react';
import { logger } from '@globals/logger';
import { makeAutoObservable } from '@globals/mobx';
import { snapdom } from '@zumer/snapdom';
import type { SnapdomOptions, SnapdomResult } from './types';

class SnapdomController {
    result: SnapdomResult | null = null;
    error: Error | null = null;
    isCapturing = false;
    base64Result: string | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Captures an HTML element and updates the result/error properties.
     *
     * @param ref - React ref to the element to capture.
     * @param options - Configuration options for capture.
     */
    async captureElement(
        ref: RefObject<HTMLElement>,
        options?: SnapdomOptions,
    ): Promise<void> {
        this.error = null;
        this.result = null;
        this.isCapturing = true;

        if (!ref.current) {
            this.error = new Error(
                'SnapdomController: Element ref is null or undefined',
            );
            this.isCapturing = false;
            logger.warn({
                message:
                    'SnapdomController: captureElement - Element ref is null or undefined',
            });

            return;
        }

        try {
            const result = await snapdom(ref.current, options);

            // Wrap the result to match our interface
            this.result = {
                ...result,
                download: async (
                    filename?: string | SnapdomOptions,
                    downloadOptions?: SnapdomOptions,
                ) => {
                    if (typeof filename === 'string') {
                        // If filename is provided as string, use it with options
                        const opts = downloadOptions ?? {};

                        return result.download(opts);
                    }

                    // If filename is actually options or undefined, use it directly
                    return result.download(filename);
                },
            };
            this.isCapturing = false;
        } catch (error) {
            logger.error({
                message:
                    'SnapdomController: captureElement - Failed to capture element',
                additionalInfo: {
                    error: isError(error) ? error.message : String(error),
                },
            });
            this.error = isError(error) ? error : new Error(String(error));
            this.isCapturing = false;
        }
    }

    /**
     * Captures an HTML element and updates the base64Result property.
     *
     * @param ref - Direct HTML element reference of the element to capture.
     * @param options - Configuration options for capture (type determines format).
     */
    async captureElementFromHtmlAsBase64(
        ref: HTMLElement,
        options?: SnapdomOptions,
    ): Promise<void> {
        this.error = null;
        this.base64Result = null;
        this.isCapturing = true;

        if (isNil(ref)) {
            this.error = new Error(
                'SnapdomController: Element html is null or undefined',
            );
            this.isCapturing = false;
            logger.warn({
                message:
                    'SnapdomController: captureElementAsBase64 - Element html is null or undefined',
            });

            return;
        }

        try {
            const result = await snapdom(ref, options);
            const format = options?.type || 'png';

            let img: HTMLImageElement;

            switch (format) {
                case 'jpg': {
                    img = await result.toJpg();
                    break;
                }
                case 'webp': {
                    img = await result.toWebp();
                    break;
                }
                case 'png':
                default: {
                    img = await result.toPng();
                    break;
                }
            }

            // Convert HTMLImageElement to base64 data URL
            this.base64Result = img.src;
            this.isCapturing = false;
        } catch (error) {
            logger.error({
                message:
                    'SnapdomController: captureElementAsBase64 - Failed to capture element as base64',
                additionalInfo: {
                    error: isError(error) ? error.message : String(error),
                },
            });
            this.error = isError(error) ? error : new Error(String(error));
            this.isCapturing = false;
        }
    }

    /**
     * Captures an HTML element and updates the base64Result property.
     *
     * @param ref - React ref to the element to capture.
     * @param options - Configuration options for capture (type determines format).
     */
    async captureElementAsBase64(
        ref: RefObject<HTMLElement>,
        options?: SnapdomOptions,
    ): Promise<void> {
        this.error = null;
        this.base64Result = null;
        this.isCapturing = true;

        if (!ref.current) {
            this.error = new Error(
                'SnapdomController: Element ref is null or undefined',
            );
            this.isCapturing = false;
            logger.warn({
                message:
                    'SnapdomController: captureElementAsBase64 - Element ref is null or undefined',
            });

            return;
        }

        try {
            const result = await snapdom(ref.current, options);
            const format = options?.type || 'png';

            let img: HTMLImageElement;

            switch (format) {
                case 'jpg': {
                    img = await result.toJpg();
                    break;
                }
                case 'webp': {
                    img = await result.toWebp();
                    break;
                }
                case 'png':
                default: {
                    img = await result.toPng();
                    break;
                }
            }

            // Convert HTMLImageElement to base64 data URL
            this.base64Result = img.src;
            this.isCapturing = false;
        } catch (error) {
            logger.error({
                message:
                    'SnapdomController: captureElementAsBase64 - Failed to capture element as base64',
                additionalInfo: {
                    error: isError(error) ? error.message : String(error),
                },
            });
            this.error = isError(error) ? error : new Error(String(error));
            this.isCapturing = false;
        }
    }

    /**
     * Reset all capture state.
     */
    reset(): void {
        this.result = null;
        this.error = null;
        this.base64Result = null;
        this.isCapturing = false;
    }
}

export const sharedSnapdomController = new SnapdomController();
