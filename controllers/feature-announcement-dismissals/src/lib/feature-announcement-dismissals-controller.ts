import { isError } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    featureAnnouncementDismissalsControllerDismissAnnouncementMutation,
    featureAnnouncementDismissalsControllerGetFeatureAnnouncementDismissalsOptions,
} from '@globals/api-sdk/queries';
import type { FeatureAnnouncementDismissalResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';

class FeatureAnnouncementDismissalsController {
    constructor() {
        makeAutoObservable(this);
    }

    featureDismissalQuery = new ObservedQuery(
        featureAnnouncementDismissalsControllerGetFeatureAnnouncementDismissalsOptions,
    );

    dismissAnnouncementMutation = new ObservedMutation(
        featureAnnouncementDismissalsControllerDismissAnnouncementMutation,
    );

    loadFeatureDismissal(
        options: Parameters<typeof this.featureDismissalQuery.load>[0],
    ): void {
        this.featureDismissalQuery.load(options);
    }

    async dismissFeatureAnnouncement(
        dismissalType: FeatureAnnouncementDismissalResponseDto['type'],
    ): Promise<void> {
        try {
            await this.dismissAnnouncementMutation.mutateAsync({
                body: { type: dismissalType },
            });

            this.loadFeatureDismissal({ query: { type: dismissalType } });
        } catch (error: unknown) {
            snackbarController.addSnackbar({
                id: 'shared-feature-announcement-dismissal-error',
                props: {
                    title: 'Failed to closed feature announcement',
                    description: isError(error)
                        ? error.message
                        : 'An error occurred while closing feature announcement. Try again later.',
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });
        }
    }

    dismissAiGetStartedAnnouncement = (): void => {
        this.dismissAnnouncementMutation.mutate({
            body: { type: 'ENABLE_AI' },
        });

        when(
            () => !this.dismissAnnouncementMutation.isPending,
            () => {
                if (this.dismissAnnouncementMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'ai-get-started-dismissal-error',
                        props: {
                            title: t`Failed to dismiss AI get started callout`,
                            description: t`An error occurred while dismissing the callout. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'ai-get-started-dismissed',
                    props: {
                        title: t`You can enable AI summaries anytime from Settings`,
                        severity: 'primary',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                this.loadFeatureDismissal({ query: { type: 'ENABLE_AI' } });
            },
        );
    };

    get isLoading(): boolean {
        return this.featureDismissalQuery.isLoading;
    }

    get featureDismissalData() {
        return this.featureDismissalQuery.data;
    }
}

export const sharedFeatureAnnouncementDismissalsController =
    new FeatureAnnouncementDismissalsController();
