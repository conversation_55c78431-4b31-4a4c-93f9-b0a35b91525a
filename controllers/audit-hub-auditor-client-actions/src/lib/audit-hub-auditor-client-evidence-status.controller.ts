import { isNil } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, when } from '@globals/mobx';
import { getIsArchiveExpired } from '@models/auditor-client-audit';
import { sharedAuditEvidenceQueryController } from './queries/audit-evidence-query.controller';
import { sharedCompanyArchiveStatusQueryController } from './queries/company-archive-status-query.controller';

class AuditHubAuditorClientEvidenceStatusController {
    isEvidenceExpired = false;
    constructor() {
        makeAutoObservable(this);
    }

    updateEvidenceExpiredStatus() {
        when(
            () =>
                !sharedAuditEvidenceQueryController.isLatestAuditorFrameworkEvidencePingIsLoading &&
                !sharedAuditEvidenceQueryController.latestAuditorFrameworkEvidencePingHasError,
            () => {
                if (
                    isNil(
                        sharedAuditEvidenceQueryController
                            .latestAuditorFrameworkEvidencePingData
                            ?.companyArchiveLastUpdatedAt,
                    )
                ) {
                    return;
                }
                this.isEvidenceExpired = false;
                this.isEvidenceExpired = getIsArchiveExpired(
                    new Date(
                        sharedAuditEvidenceQueryController
                            .latestAuditorFrameworkEvidencePingData
                            .companyArchiveLastUpdatedAt,
                    ),
                );
            },
        );

        when(
            () =>
                sharedAuditEvidenceQueryController.latestAuditorFrameworkEvidencePingHasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'update-evidence-expired-status-error',
                    props: {
                        title: t`Update Evidence Expired Status Error`,
                        description: t`Couldn't Update Evidence Expired`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    }

    updateEvidenceExpiredStatusByArchiveStatus() {
        when(
            () =>
                !sharedCompanyArchiveStatusQueryController.preAuditArchiveStatusIsLoading &&
                !isNil(
                    sharedCompanyArchiveStatusQueryController.preAuditArchiveStatusData,
                ),
            () => {
                const archiveData =
                    sharedCompanyArchiveStatusQueryController.preAuditArchiveStatusData;

                if (!isNil(archiveData?.companyArchiveUpdatedAt)) {
                    this.isEvidenceExpired = getIsArchiveExpired(
                        new Date(archiveData.companyArchiveUpdatedAt),
                    );
                    this.updateEvidenceExpiredStatus();
                }
            },
        );
    }

    get latestCompanyArchiveStatusData() {
        return sharedCompanyArchiveStatusQueryController.preAuditArchiveStatusData;
    }

    get isFileGenerationPending() {
        return (
            this.latestCompanyArchiveStatusData?.companyArchiveStatus ===
            'PENDING'
        );
    }

    get isFileGenerationSuccess() {
        return (
            this.latestCompanyArchiveStatusData?.companyArchiveStatus ===
            'SUCCESS'
        );
    }

    get isFileGenerationFailed() {
        return (
            this.latestCompanyArchiveStatusData?.companyArchiveStatus ===
            'FAILED'
        );
    }
}

export const sharedAuditHubAuditorClientEvidenceStatusController =
    new AuditHubAuditorClientEvidenceStatusController();
