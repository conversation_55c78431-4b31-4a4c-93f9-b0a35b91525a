import { isNil, isNumber } from 'lodash-es';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { snackbarController } from '@controllers/snackbar';
import { companiesControllerGetLatestCompanyArchiveStatusOptions } from '@globals/api-sdk/queries';
import type { CompanyArchiveDownloadLinkResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { getIsArchiveExpired } from '@models/auditor-client-audit';

class CompanyArchiveStatusQueryController {
    isPreAuditExpired = false;
    /**
     * Separate queries for different categories.
     */
    preAuditArchiveStatusQuery = new ObservedQuery(
        companiesControllerGetLatestCompanyArchiveStatusOptions,
    );

    controlEvidenceArchiveStatusQuery = new ObservedQuery(
        companiesControllerGetLatestCompanyArchiveStatusOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    loadLatestCompanyArchiveStatus(
        category: 'PRE_AUDIT' | 'CONTROL_EVIDENCE',
        status?: 'PENDING' | 'SUCCESS' | 'FAILED',
    ): void {
        // Select the appropriate query based on category
        const query =
            category === 'PRE_AUDIT'
                ? this.preAuditArchiveStatusQuery
                : this.controlEvidenceArchiveStatusQuery;

        when(
            () =>
                !sharedAuditHubController.auditByIdIsLoading &&
                !isNil(sharedAuditHubController.auditByIdData),
            () => {
                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                query.load({
                    path: {
                        xProductId:
                            sharedAuditHubController.auditByIdData.framework
                                .productId,
                    },
                    query: {
                        category,
                        status,
                        auditFrameworkId:
                            sharedCustomerRequestsController.auditId,
                    },
                });
            },
        );

        when(
            () =>
                !query.isLoading && !isNil(query.data?.companyArchiveUpdatedAt),
            () => {
                if (isNil(query.data?.companyArchiveUpdatedAt)) {
                    return;
                }
                this.isPreAuditExpired = getIsArchiveExpired(
                    new Date(query.data.companyArchiveUpdatedAt),
                );
            },
        );

        when(
            () => query.hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'load-latest-company-archive-status-error',
                    props: {
                        title: t`Company Archive Status Error`,
                        description: t`Failed to load company archive status. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    }

    /**
     * Specific getters for PRE_AUDIT category.
     */
    get preAuditArchiveStatusData(): CompanyArchiveDownloadLinkResponseDto | null {
        return this.preAuditArchiveStatusQuery.data;
    }

    get preAuditArchiveStatusIsLoading(): boolean {
        return this.preAuditArchiveStatusQuery.isLoading;
    }

    get preAuditArchiveStatusHasError(): boolean {
        return this.preAuditArchiveStatusQuery.hasError;
    }

    get isPreAuditArchiveStatusDataPending(): boolean {
        return (
            this.preAuditArchiveStatusData?.companyArchiveStatus === 'PENDING'
        );
    }

    get preAuditArchiveStatusDataFailed(): boolean {
        return (
            this.preAuditArchiveStatusData?.companyArchiveStatus === 'FAILED'
        );
    }

    /**
     * Specific getters for CONTROL_EVIDENCE category.
     */
    get controlEvidenceArchiveStatusData(): CompanyArchiveDownloadLinkResponseDto | null {
        return this.controlEvidenceArchiveStatusQuery.data;
    }

    get controlEvidenceArchiveStatusIsLoading(): boolean {
        return this.controlEvidenceArchiveStatusQuery.isLoading;
    }

    get controlEvidenceArchiveStatusHasError(): boolean {
        return this.controlEvidenceArchiveStatusQuery.hasError;
    }
}

export const sharedCompanyArchiveStatusQueryController =
    new CompanyArchiveStatusQueryController();
