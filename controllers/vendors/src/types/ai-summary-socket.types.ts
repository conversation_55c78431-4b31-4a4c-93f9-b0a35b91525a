import type { QuestionnaireSummaryResponseDto } from '@globals/api-sdk/types';

export type LoadingPhase = 'IDLE' | 'REQUESTING' | 'PROCESSING' | 'DONE';

export interface SocketCallbackData {
    vendorDocument?: SummaryEventData;
    errorMessage?: string;
    questionnaireResponse?: QuestionnaireResponseSocketData;
}

export interface SummaryEventData {
    id: string;
    summary: {
        executionId: string;
        summary: string;
        errorCode: string;
    };
}

export interface QuestionnaireResponseSocketData {
    id: number;
    completedBy: string;
    file: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    questionnaireSent: {
        id: number;
        formId: string;
        createdAt: string;
        updatedAt: string;
        deletedAt: string | null;
        trackingId: string;
    };
    questionnaireSummary: Pick<
        QuestionnaireSummaryResponseDto,
        'executionId' | 'summary'
    > & {
        createdAt: string;
        deletedAt: string | null;
    };
}

export type SummaryEventCallback = (data: SocketCallbackData) => void;
