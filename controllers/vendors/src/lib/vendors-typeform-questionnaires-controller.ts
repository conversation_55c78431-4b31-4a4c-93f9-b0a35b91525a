import { isEmpty, isObject, isString } from 'lodash-es';
import type {
    VendorQuestionnaireCategoryType,
    VendorQuestionnaireRiskType,
    VendorQuestionnaireStatus,
} from '@components/vendor-questionnaires';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import {
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import {
    questionnaireVendorSecurityTypeformControllerCreateVendorSecurityTypeformMutation,
    questionnaireVendorSecurityTypeformControllerDeleteVendorSecurityTypeformMutation,
    questionnaireVendorSecurityTypeformControllerGetVendorSecurityTypeformOptions,
    questionnaireVendorSecurityTypeformControllerGetVendorSecurityTypeformPdfOptions,
    questionnaireVendorSecurityTypeformControllerGetVendorsQuestionnairesOptions,
} from '@globals/api-sdk/queries';
import type { QuestionnaireVendorResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    action,
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    runInAction,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { downloadBlob } from '@helpers/download-file';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import {
    closeCopyQuestionnaireModal,
    openCopyQuestionnaireModal,
} from '@views/vendors-questionnaires-add';
import { mapApiShortAnswerTypeToFormType } from './helpers/questionnaire-field-mapping.helper';
import { sharedVendorsTypeformQuestionnaireController } from './vendors-typeform-questionnaire-controller';

/**
 * Helper function to safely extract filter value.
 */
const getFilterValue = (filter: unknown): string | undefined => {
    if (filter && isObject(filter) && 'value' in filter) {
        const filterObj = filter as { value: unknown };

        if (
            filterObj.value &&
            isObject(filterObj.value) &&
            'value' in filterObj.value
        ) {
            const nestedValue = (filterObj.value as { value: unknown }).value;

            return isString(nestedValue) ? nestedValue : undefined;
        }
    }

    return undefined;
};

/**
 * Handle copy questionnaire action - extracted to outer scope for linting.
 */
const handleCopyQuestionnaire = function (
    this: VendorsTypeformQuestionnairesController,
    questionnaireId: number,
): void {
    // Try to find in the main list first
    let currentQuestionnaire = this.allVendorsQuestionnaires.find(
        (q) => q.id === questionnaireId,
    );

    // If not found in main list, try to get from the individual questionnaire controller
    if (!currentQuestionnaire) {
        const individualData =
            sharedVendorsTypeformQuestionnaireController
                .vendorsQuestionnaireQuery.data;

        if (individualData && individualData.id === questionnaireId) {
            currentQuestionnaire = individualData;
        }
    }

    if (!currentQuestionnaire) {
        snackbarController.addSnackbar({
            id: 'questionnaire-copy-error',
            props: {
                title: t`Couldn't copy questionnaire`,
                description: t`Questionnaire not found`,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });

        return;
    }

    const { title } = currentQuestionnaire;
    const defaultName = t`Copy of ${title}`;

    // Open the copy modal using helper
    openCopyQuestionnaireModal({
        defaultName,
        questionnaireId,
    });
};

class VendorsTypeformQuestionnairesController {
    #lastSearchQuery = '';
    hasFilters = false;

    constructor() {
        makeAutoObservable(this);
    }

    allVendorsQuestionnairesQuery = new ObservedQuery(
        questionnaireVendorSecurityTypeformControllerGetVendorsQuestionnairesOptions,
    );

    deleteQuestionnaireMutation = new ObservedMutation(
        questionnaireVendorSecurityTypeformControllerDeleteVendorSecurityTypeformMutation,
        {
            onSuccess: () => {
                this.allVendorsQuestionnairesQuery.invalidate();
            },
        },
    );

    createQuestionnaireMutation = new ObservedMutation(
        questionnaireVendorSecurityTypeformControllerCreateVendorSecurityTypeformMutation,
        {
            onSuccess: () => {
                this.allVendorsQuestionnairesQuery.invalidate();
            },
        },
    );

    getQuestionnaireQuery = new ObservedQuery(
        questionnaireVendorSecurityTypeformControllerGetVendorSecurityTypeformOptions,
    );

    get allVendorsQuestionnaires(): QuestionnaireVendorResponseDto[] {
        return this.allVendorsQuestionnairesQuery.data?.data ?? [];
    }

    get isLoading(): boolean {
        return this.allVendorsQuestionnairesQuery.isLoading;
    }

    get total(): number {
        return this.allVendorsQuestionnairesQuery.data?.total ?? 0;
    }

    get hasNextPage(): boolean {
        const { data } = this.allVendorsQuestionnairesQuery;

        if (!data) {
            return false;
        }

        return data.page < data.total;
    }

    get formattedQuestionnaireOptions(): {
        id: string;
        label: string;
        value: string;
    }[] {
        return this.allVendorsQuestionnaires.map((questionnaire) => ({
            id: questionnaire.id.toString(),
            label: questionnaire.title,
            value: questionnaire.id.toString(),
        }));
    }

    loadQuestionnaires = (params: FetchDataResponseParams): void => {
        const { pagination, globalFilter } = params;
        const { search = '', filters = {} } = globalFilter;
        const { pageSize, page = 1 } = pagination;

        this.#lastSearchQuery = search;
        this.hasFilters = !isEmpty(search.trim()) || !isEmpty(filters);

        // Extract filter values
        const statusFilter =
            filters['vendors-questionnaires-table-filter-status'];
        const categoryFilter =
            filters['vendors-questionnaires-table-filter-category'];
        const riskFilter = filters['vendors-questionnaires-table-filter-risk'];

        const queryParams = {
            q: search || undefined,
            prioritizeCategory: false,
            page,
            limit: pageSize,
            status: getFilterValue(statusFilter) as
                | VendorQuestionnaireStatus
                | undefined,
            categories: getFilterValue(categoryFilter) as
                | VendorQuestionnaireCategoryType
                | undefined,
            riskLevels: getFilterValue(riskFilter) as
                | VendorQuestionnaireRiskType
                | undefined,
        };

        this.allVendorsQuestionnairesQuery.load({
            query: queryParams,
        });
    };

    handleFetchOptions = (params: {
        search?: string;
        increasePage?: boolean;
        filters?: Record<string, unknown>;
    }) => {
        const { search, increasePage, filters = {} } = params;

        if (increasePage) {
            this.loadNextPage({ search, filters });
        } else {
            this.loadQuestionnaires({
                pagination: { page: 1, pageSize: DEFAULT_PAGE_SIZE },
                globalFilter: { search: search || '', filters },
            } as FetchDataResponseParams);
        }
    };

    loadNextPage = ({
        search,
        filters = {},
    }: {
        search?: string;
        filters?: Record<string, unknown>;
    }): void => {
        if (search !== this.#lastSearchQuery) {
            this.loadQuestionnaires({
                pagination: { page: 1, pageSize: DEFAULT_PAGE_SIZE },
                globalFilter: { search: search || '', filters },
            } as FetchDataResponseParams);

            return;
        }

        if (this.hasNextPage) {
            const currentPage =
                this.allVendorsQuestionnairesQuery.data?.page ?? 1;

            this.loadQuestionnaires({
                pagination: {
                    page: currentPage,
                    pageSize: DEFAULT_PAGE_SIZE,
                },
                globalFilter: { search: this.#lastSearchQuery, filters },
            } as FetchDataResponseParams);
        }
    };

    handleDeleteQuestionnaire = (questionnaireId: number): void => {
        openConfirmationModal({
            title: t`Delete Questionnaire`,
            body: t`Confirm that you'd like to delete this questionnaire. This action cannot be undone.`,
            confirmText: t`Yes, delete questionnaire`,
            cancelText: t`Cancel`,
            type: 'danger',
            size: 'md',
            onConfirm: () => {
                runInAction(() => {
                    this.deleteQuestionnaireMutation
                        .mutateAsync({
                            path: { id: questionnaireId },
                        })
                        .then(() => {
                            snackbarController.addSnackbar({
                                id: 'questionnaire-delete-success',
                                hasTimeout: true,
                                props: {
                                    title: t`Questionnaire deleted`,
                                    severity: 'success',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                            closeConfirmationModal();

                            // Navigate back to questionnaires list
                            const { currentWorkspace } =
                                sharedWorkspacesController;

                            if (currentWorkspace?.id) {
                                sharedProgrammaticNavigationController.navigateTo(
                                    `/workspaces/${currentWorkspace.id}/vendors/questionnaires`,
                                );
                            }
                        })
                        .catch((error) => {
                            closeConfirmationModal();
                            snackbarController.addSnackbar({
                                id: 'questionnaire-delete-error',
                                props: {
                                    title: t`Couldn't delete questionnaire`,
                                    description: isEmpty(error)
                                        ? t`Please try again later`
                                        : (error as Error).message,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                        });
                });
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };

    deleteQuestionnaire = action(this.handleDeleteQuestionnaire);

    copyQuestionnaire = action(handleCopyQuestionnaire);

    /**
     * Copy questionnaire with a specific name (called from modal).
     */
    handleCopyQuestionnaireWithName = (
        questionnaireId: number,
        newName: string,
        onComplete?: () => void,
    ): void => {
        runInAction(() => {
            // Load the complete questionnaire with fields using GET
            this.getQuestionnaireQuery.load({
                path: { id: questionnaireId },
            });

            // Wait for the questionnaire to load, then copy it
            when(
                () => !this.getQuestionnaireQuery.isLoading,
                () => {
                    const currentQuestionnaire =
                        this.getQuestionnaireQuery.data;

                    if (!currentQuestionnaire) {
                        snackbarController.addSnackbar({
                            id: 'questionnaire-copy-error',
                            props: {
                                title: t`Couldn't copy questionnaire`,
                                description: t`Questionnaire not found`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        onComplete?.();

                        return;
                    }

                    this.createQuestionnaireMutation.mutate({
                        body: {
                            title: newName.trim(),
                            status: 'ACTIVE', // Create copy as active
                            categories: currentQuestionnaire.categories,
                            riskLevels: currentQuestionnaire.riskLevels,
                            fields: currentQuestionnaire.fields.map(
                                (field, index) => ({
                                    ref: `question-${Date.now()}-${index}`,
                                    title: field.title,
                                    type: field.type, // Use original API type directly
                                    required: field.required,
                                    shortAnswerType:
                                        mapApiShortAnswerTypeToFormType(
                                            field.shortAnswerType,
                                        ),
                                    choices: field.choices,
                                    followUpQn: field.followUpQn,
                                    allowOtherChoice: field.allowOtherChoice,
                                    includeFollowUpQn: field.includeFollowUpQn,
                                    followUpQnTrigger: field.followUpQnTrigger,
                                }),
                            ),
                        },
                    } as unknown as Parameters<
                        typeof this.createQuestionnaireMutation.mutate
                    >[0]); // DTO is outdated, API accepts all types

                    when(
                        () => !this.createQuestionnaireMutation.isPending,
                        () => {
                            if (this.createQuestionnaireMutation.hasError) {
                                // Handle error
                                snackbarController.addSnackbar({
                                    id: 'questionnaire-copy-error',
                                    props: {
                                        title: t`Couldn't copy questionnaire`,
                                        description: t`Please try again later`,
                                        severity: 'critical',
                                        closeButtonAriaLabel: t`Close`,
                                    },
                                });
                            } else {
                                snackbarController.addSnackbar({
                                    id: 'questionnaire-copy-success',
                                    hasTimeout: true,
                                    props: {
                                        title: t`Questionnaire copied`,
                                        severity: 'success',
                                        closeButtonAriaLabel: t`Close`,
                                    },
                                });

                                runInAction(() => {
                                    const workspaceId =
                                        sharedWorkspacesController.currentWorkspaceId;

                                    if (workspaceId) {
                                        sharedProgrammaticNavigationController.navigateTo(
                                            `/workspaces/${workspaceId}/vendors/questionnaires`,
                                        );
                                    }
                                });

                                closeCopyQuestionnaireModal();
                            }

                            // Call the completion callback if provided
                            if (onComplete) {
                                onComplete();
                            }
                        },
                    );
                },
            );
        });
    };

    copyQuestionnaireWithName = action(this.handleCopyQuestionnaireWithName);

    /**
     * Let's try using ObservedQuery with custom configuration.
     */
    questionnairePdfDownloadQuery = new ObservedQuery(
        questionnaireVendorSecurityTypeformControllerGetVendorSecurityTypeformPdfOptions,
    );

    downloadQuestionnairePdf = (questionnaireId: number): void => {
        // Load questionnaire details if not already loaded
        this.getQuestionnaireQuery.load({
            path: { id: questionnaireId },
        });

        // Try loading with custom parseAs option
        this.questionnairePdfDownloadQuery.load({
            path: { id: questionnaireId },
        });

        when(
            () =>
                !this.questionnairePdfDownloadQuery.isLoading &&
                !this.getQuestionnaireQuery.isLoading,
            () => {
                const response = this.questionnairePdfDownloadQuery.data;

                if (!response) {
                    snackbarController.addSnackbar({
                        id: 'questionnaire-pdf-download-error',
                        props: {
                            title: t`Couldn't download questionnaire PDF`,
                            description: t`Please try again later`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Get questionnaire title from multiple sources
                const questionnaireFromList =
                    this.allVendorsQuestionnaires.find(
                        (q) => q.id === questionnaireId,
                    );
                const questionnaireFromDetails =
                    this.getQuestionnaireQuery.data;

                const questionnaireTitle =
                    questionnaireFromDetails?.title ||
                    questionnaireFromList?.title ||
                    'Untitled';

                /**
                 * YYYY-MM-DD format.
                 */
                const currentDate = new Date().toISOString().split('T')[0];
                const fileName = `_VendorQuestionnaire_${questionnaireTitle}_${questionnaireId}${currentDate}.pdf`;

                // Handle the response as binary data

                // Create blob - this might need adjustment based on response format
                const blob = new Blob([response as unknown as BlobPart], {
                    type: 'application/pdf',
                });

                downloadBlob(blob, fileName);

                snackbarController.addSnackbar({
                    id: 'questionnaire-pdf-download-success',
                    hasTimeout: true,
                    props: {
                        title: t`Questionnaire PDF downloaded`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedVendorsTypeformQuestionnairesController =
    new VendorsTypeformQuestionnairesController();
