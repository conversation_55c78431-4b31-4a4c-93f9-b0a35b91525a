import { beforeEach, describe, expect, test, vi } from 'vitest';
import { dismissBanner, showSeverityBanner } from '@controllers/banner-service';
import { VendorsQuestionnaireAddController } from './vendors-questionnaire-add-controller';

// Mock the banner service
vi.mock('@controllers/banner-service', () => ({
    BannerLocation: {
        PAGE_HEADER: 'PAGE_HEADER',
    },
    BannerPersistenceType: {
        TIMER_BASED: 'TIMER_BASED',
    },
    showSeverityBanner: vi.fn(),
    dismissBanner: vi.fn(),
}));

// Mock other dependencies
vi.mock('@controllers/snackbar', () => ({
    snackbarController: {
        addSnackbar: vi.fn(),
    },
}));

vi.mock('@globals/api-sdk/queries', () => ({
    questionnaireVendorSecurityTypeformControllerCreateVendorSecurityTypeformMutation:
        vi.fn(),
    questionnaireVendorSecurityTypeformControllerQuestionnaireTitleCheckOptions:
        vi.fn(),
    questionnaireVendorSecurityTypeformControllerUpdateVendorSecurityTypeformMutation:
        vi.fn(),
}));

vi.mock('@globals/i18n/macro', () => ({
    t: (strings: TemplateStringsArray) => strings[0],
}));

vi.mock('@models/vendors-questionnaire-add', () => ({
    VendorsQuestionnaireAddModel: class {
        canSubmit = true;
        resetForm = vi.fn();
        formData = { questions: [] };
    },
}));

vi.mock('./vendors-typeform-questionnaires-controller', () => ({
    sharedVendorsTypeformQuestionnairesController: {},
}));

describe('vendorsQuestionnaireAddController - Banner Service Integration', () => {
    let controller: VendorsQuestionnaireAddController;
    const mockShowSeverityBanner = vi.mocked(showSeverityBanner);
    const mockDismissBanner = vi.mocked(dismissBanner);

    beforeEach(() => {
        vi.clearAllMocks();
        controller = new VendorsQuestionnaireAddController();
    });

    describe('showImportBanner', () => {
        test('should call showSeverityBanner with correct parameters for success', () => {
            const mockBannerId = 'test-banner-id';

            mockShowSeverityBanner.mockReturnValue(mockBannerId);

            controller.showImportBanner(
                'Success!',
                'Import completed',
                'success',
            );

            expect(mockShowSeverityBanner).toHaveBeenCalledWith(
                'success',
                'Success!',
                {
                    body: 'Import completed',
                    location: 'PAGE_HEADER',
                    persistenceType: 'TIMER_BASED',
                    autoHideDuration: 5000,
                },
            );

            expect(controller.currentImportBannerId).toBe(mockBannerId);
        });

        test('should call showSeverityBanner with correct parameters for warning', () => {
            const mockBannerId = 'test-warning-banner-id';

            mockShowSeverityBanner.mockReturnValue(mockBannerId);

            controller.showImportBanner(
                'Warning!',
                'Some issues found',
                'warning',
            );

            expect(mockShowSeverityBanner).toHaveBeenCalledWith(
                'warning',
                'Warning!',
                {
                    body: 'Some issues found',
                    location: 'PAGE_HEADER',
                    persistenceType: 'TIMER_BASED',
                    autoHideDuration: 5000,
                },
            );

            expect(controller.currentImportBannerId).toBe(mockBannerId);
        });

        test('should clear existing banner before showing new one', () => {
            const firstBannerId = 'first-banner';
            const secondBannerId = 'second-banner';

            mockShowSeverityBanner
                .mockReturnValueOnce(firstBannerId)
                .mockReturnValueOnce(secondBannerId);

            // Show first banner
            controller.showImportBanner('First', 'First message', 'success');
            expect(controller.currentImportBannerId).toBe(firstBannerId);

            // Show second banner - should dismiss first
            controller.showImportBanner('Second', 'Second message', 'warning');

            expect(mockDismissBanner).toHaveBeenCalledWith(firstBannerId);
            expect(controller.currentImportBannerId).toBe(secondBannerId);
        });
    });

    describe('clearImportBanner', () => {
        test('should dismiss banner and clear ID when banner exists', () => {
            const mockBannerId = 'test-banner-id';

            controller.currentImportBannerId = mockBannerId;

            controller.clearImportBanner();

            expect(mockDismissBanner).toHaveBeenCalledWith(mockBannerId);
            expect(controller.currentImportBannerId).toBeNull();
        });

        test('should do nothing when no banner exists', () => {
            controller.currentImportBannerId = null;

            controller.clearImportBanner();

            expect(mockDismissBanner).not.toHaveBeenCalled();
            expect(controller.currentImportBannerId).toBeNull();
        });
    });

    describe('resetForNewQuestionnaire', () => {
        test('should clear import banner when resetting', () => {
            const mockBannerId = 'test-banner-id';

            controller.currentImportBannerId = mockBannerId;

            controller.resetForNewQuestionnaire();

            expect(mockDismissBanner).toHaveBeenCalledWith(mockBannerId);
            expect(controller.currentImportBannerId).toBeNull();
        });
    });
});
