import { debounce, isNumber } from 'lodash-es';
import {
    BannerLocation,
    type BannerOptions,
    BannerPersistenceType,
    dismissBanner,
    showSeverityBanner,
} from '@controllers/banner-service';
import { snackbarController } from '@controllers/snackbar';
import {
    questionnaireVendorSecurityTypeformControllerCreateVendorSecurityTypeformMutation,
    questionnaireVendorSecurityTypeformControllerQuestionnaireTitleCheckOptions,
    questionnaireVendorSecurityTypeformControllerUpdateVendorSecurityTypeformMutation,
} from '@globals/api-sdk/queries';
import type {
    QuestionnaireRequestDto,
    QuestionnaireVendorResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    runInAction,
    toJS,
    when,
} from '@globals/mobx';
import {
    type QuestionnaireStatus,
    VendorsQuestionnaireAddModel,
} from '@models/vendors-questionnaire-add';
import { mapApiShortAnswerTypeToFormType } from './helpers/questionnaire-field-mapping.helper';
import { sharedVendorsTypeformQuestionnairesController } from './vendors-typeform-questionnaires-controller';

// Type for question types as they come from API (snake_case)
type ApiQuestionType =
    | 'short_answer'
    | 'long_text'
    | 'multiple_choice'
    | 'checkboxes'
    | 'yes_no'
    | 'date'
    | 'file_upload';

export class VendorsQuestionnaireAddController {
    formModel = new VendorsQuestionnaireAddModel();

    titleValidationQuery = new ObservedQuery(
        questionnaireVendorSecurityTypeformControllerQuestionnaireTitleCheckOptions,
    );

    createQuestionnaireMutation = new ObservedMutation(
        questionnaireVendorSecurityTypeformControllerCreateVendorSecurityTypeformMutation,
        {
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'questionnaire-create-error',
                    props: {
                        title: t`Failed to create questionnaire`,
                        description: t`Please try again later`,
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            },
        },
    );

    updateQuestionnaireMutation = new ObservedMutation(
        questionnaireVendorSecurityTypeformControllerUpdateVendorSecurityTypeformMutation,
        {
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'questionnaire-update-error',
                    props: {
                        title: t`Failed to update questionnaire`,
                        description: t`Please try again later`,
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            },
        },
    );

    lastValidatedTitle = '';

    /**
     * Track if we're editing an existing questionnaire.
     */
    editingQuestionnaireId: number | null = null;

    /**
     * Track title validation error for display in input.
     */
    titleValidationErrorMessage: string | null = null;

    /**
     * Track current import banner ID for dismissal if needed.
     */
    currentImportBannerId: string | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    get isSubmitting(): boolean {
        return (
            this.createQuestionnaireMutation.isPending ||
            this.updateQuestionnaireMutation.isPending
        );
    }

    get titleValidationError(): string | null {
        return this.titleValidationErrorMessage;
    }

    /**
     * Clear title validation error.
     */
    clearTitleValidationError(): void {
        runInAction(() => {
            this.titleValidationErrorMessage = null;
        });
    }

    /**
     * Show import banner using the new Banner Service.
     */
    showImportBanner(
        title: string,
        description: string,
        severity: 'success' | 'warning',
    ): void {
        // Clear any existing banner first
        this.clearImportBanner();

        // Show new banner using Banner Service
        const bannerOptions: BannerOptions = {
            body: description,
            location: BannerLocation.PAGE_HEADER,
            persistenceType: BannerPersistenceType.TIMER_BASED,
            autoHideDuration: 5000, // Auto-dismiss after 5 seconds
        };

        runInAction(() => {
            this.currentImportBannerId = showSeverityBanner(
                severity,
                title,
                bannerOptions,
            );
        });
    }

    /**
     * Clear import banner using the new Banner Service.
     */
    clearImportBanner(): void {
        if (!this.currentImportBannerId) {
            return;
        }

        dismissBanner(this.currentImportBannerId);
        runInAction(() => {
            this.currentImportBannerId = null;
        });
    }

    get canSubmit(): boolean {
        return this.formModel.canSubmit && !this.isSubmitting;
    }

    /**
     * Validate questionnaire title.
     */
    handleValidateTitle(title: string): void {
        runInAction(() => {
            if (!title.trim()) {
                this.lastValidatedTitle = '';

                return;
            }

            const trimmedTitle = title.trim();

            this.lastValidatedTitle = trimmedTitle;

            this.titleValidationQuery.load({
                query: { title: trimmedTitle },
            });
        });
    }

    validateTitle = debounce(this.handleValidateTitle.bind(this), 300);

    /**
     * Validate questionnaire title on submit.
     */
    async validateTitleOnSubmit(): Promise<{
        isValid: boolean;
        error?: string;
    }> {
        // Access formData within reactive context
        let title = '';

        runInAction(() => {
            title = this.formModel.formData.title.trim();
            // Clear previous error
            this.titleValidationErrorMessage = null;
        });

        if (!title) {
            const errorMessage = t`Please enter a name.`;

            runInAction(() => {
                this.titleValidationErrorMessage = errorMessage;
            });

            return {
                isValid: false,
                error: errorMessage,
            };
        }

        try {
            // Access editingQuestionnaireId within reactive context
            const excludeIds = runInAction(() => {
                return this.editingQuestionnaireId
                    ? [this.editingQuestionnaireId]
                    : [];
            });

            // Load the validation query and wait for it to complete
            // In edit mode, exclude the current questionnaire ID from the search
            this.titleValidationQuery.load({
                query: {
                    title,
                    'excludeIds[]': excludeIds,
                },
            });

            // Wait for the query to complete
            await when(() => !this.titleValidationQuery.isLoading);

            // Access query results within reactive context
            return runInAction(() => {
                if (this.titleValidationQuery.error) {
                    // Allow submission if validation fails due to network issues
                    return { isValid: true };
                }

                if (this.titleValidationQuery.data?.isFound) {
                    const errorMessage = t`The questionnaire name you entered is already in use.`;

                    this.titleValidationErrorMessage = errorMessage;

                    return {
                        isValid: false,
                        error: errorMessage,
                    };
                }

                return { isValid: true };
            });
        } catch {
            // Allow submission if validation fails due to network issues
            return { isValid: true };
        }
    }

    /**
     * Submit the form with specified status.
     */
    async submitForm(status: QuestionnaireStatus = 'ACTIVE'): Promise<void> {
        const formData = toJS(this.formModel.formData);

        await runInAction(async () => {
            if (!this.canSubmit || this.isSubmitting) {
                return;
            }

            // Validate title before submitting
            const titleValidation = await this.validateTitleOnSubmit();

            if (!titleValidation.isValid) {
                // Error is already shown in the input field
                return;
            }

            const formDataToSubmit = {
                title: formData.title,
                status,
                categories: formData.categories,
                riskLevels: formData.riskLevels,
                fields: formData.questions.map((question, index: number) => ({
                    ref: question.id || `question-${index}`,
                    title: question.title,
                    type: question.type,
                    required: question.required,
                    shortAnswerType: question.shortAnswerType ?? 'TEXT',
                    choices: question.choices ?? [],
                    followUpQn: question.followUpQn ?? '',
                    allowOtherChoice: question.allowOtherChoice ?? false,
                    includeFollowUpQn: question.includeFollowUpQn,
                    followUpQnTrigger: question.followUpQnTrigger ?? false,
                })),
            };

            // Use the appropriate mutation based on whether we're editing or creating
            if (this.editingQuestionnaireId) {
                // Update existing questionnaire
                this.updateQuestionnaireMutation.mutate({
                    path: { id: this.editingQuestionnaireId },
                    body: formDataToSubmit,
                });
            } else {
                // Create new questionnaire
                this.createQuestionnaireMutation.mutate({
                    body: formDataToSubmit,
                });
            }
        });
    }

    /**
     * Submit the form and navigate on success.
     */
    async submitFormAndNavigate(
        onSuccess: () => void,
        status: QuestionnaireStatus = 'ACTIVE',
    ): Promise<void> {
        const editingId = this.editingQuestionnaireId;
        const isEditing = editingId !== null;
        const activeMutation = isEditing
            ? this.updateQuestionnaireMutation
            : this.createQuestionnaireMutation;

        const formData = toJS(this.formModel.formData);
        let formDataToSubmit: QuestionnaireRequestDto | undefined;

        await runInAction(async () => {
            // Check submission state within reactive context
            if (!this.canSubmit || this.isSubmitting) {
                return;
            }
            // Validate title before submitting
            const titleValidation = await this.validateTitleOnSubmit();

            if (!titleValidation.isValid) {
                // Error is already shown in the input field
                return;
            }

            formDataToSubmit = {
                title: formData.title,
                status,
                categories: formData.categories,
                riskLevels: formData.riskLevels,
                fields: formData.questions.map((question, index: number) => ({
                    ref: question.id || `question-${index}`,
                    title: question.title,
                    type: question.type,
                    required: question.required,
                    shortAnswerType: question.shortAnswerType ?? 'TEXT',
                    choices: question.choices ?? [],
                    followUpQn: question.followUpQn ?? '',
                    allowOtherChoice: question.allowOtherChoice ?? false,
                    includeFollowUpQn: question.includeFollowUpQn,
                    followUpQnTrigger: question.followUpQnTrigger ?? false,
                })),
            };
        });

        // Only proceed with mutation if we have valid form data
        if (formDataToSubmit) {
            runInAction(() => {
                // Use the appropriate mutation based on whether we're editing or creating
                if (isEditing && isNumber(editingId)) {
                    // Update existing questionnaire
                    this.updateQuestionnaireMutation.mutate({
                        path: { id: editingId },
                        body: formDataToSubmit,
                    });
                } else {
                    // Create new questionnaire
                    this.createQuestionnaireMutation.mutate({
                        body: formDataToSubmit,
                    });
                }
            });
        }

        // Wait for mutation to complete outside of runInAction
        await when(() => !activeMutation.isPending);

        // Access mutation results within reactive context
        runInAction(() => {
            if (activeMutation.hasError || !activeMutation.response) {
                return;
            }

            // Show success snackbar with appropriate message
            let message: string;

            if (status === 'DRAFT') {
                message = isEditing
                    ? t`The vendor questionnaire draft has been successfully updated!`
                    : t`The vendor questionnaire draft has been successfully created!`;
            } else {
                message = isEditing
                    ? t`The vendor questionnaire has been successfully updated!`
                    : t`The vendor questionnaire has been successfully created!`;
            }

            snackbarController.addSnackbar({
                id: isEditing
                    ? 'questionnaire-update-success'
                    : 'questionnaire-create-success',
                hasTimeout: true,
                props: {
                    title: message,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            // Reset form after successful operation
            this.formModel.resetForm();

            // Invalidate questionnaires list to refresh the table
            sharedVendorsTypeformQuestionnairesController.allVendorsQuestionnairesQuery.invalidate();

            // Execute navigation callback
            onSuccess();
        });
    }

    /**
     * Load existing questionnaire data into the form.
     */
    loadQuestionnaireData(data: QuestionnaireVendorResponseDto): void {
        runInAction(() => {
            // Set the editing questionnaire ID to indicate we're in edit mode
            this.editingQuestionnaireId = data.id;

            const mappedQuestions = data.fields.map((field, index) => ({
                id: field.ref || `question-${index}`,
                title: field.title,
                type: field.type as ApiQuestionType, // API type (snake_case)
                required: field.required,
                shortAnswerType: mapApiShortAnswerTypeToFormType(
                    field.shortAnswerType,
                ),
                choices: field.choices,
                followUpQn: field.followUpQn,
                allowOtherChoice: field.allowOtherChoice,
                includeFollowUpQn: field.includeFollowUpQn,
                followUpQnTrigger: field.followUpQnTrigger,
            }));

            this.formModel.formData = {
                title: data.title,
                categories: data.categories,
                riskLevels: data.riskLevels,
                questions:
                    mappedQuestions as unknown as typeof this.formModel.formData.questions, // DTO types are outdated
                markAllAsRequired: false, // Default value since it's not in the response
            };

            this.lastValidatedTitle = data.title;
        });
    }

    /**
     * Reset the controller for creating a new questionnaire.
     */
    resetForNewQuestionnaire(): void {
        runInAction(() => {
            this.editingQuestionnaireId = null;
            this.formModel.resetForm();
            this.lastValidatedTitle = '';
            this.titleValidationErrorMessage = null;
            this.clearImportBanner();
        });
    }
}

export const sharedVendorsQuestionnaireAddController =
    new VendorsQuestionnaireAddController();
