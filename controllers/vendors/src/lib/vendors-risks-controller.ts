import { isEmpty, isNil, isString } from 'lodash-es';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import {
    riskManagementControllerGetRiskSettingsOptions,
    vendorsRiskManagementControllerGetVendorRiskDashboardOptions,
    vendorsRiskManagementControllerGetVendorRisksListOptions,
} from '@globals/api-sdk/queries';
import type {
    DashboardResponseDto,
    RiskSettingsResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import {
    COLUMN_NAMES_TO_SORT_IDS_MAP,
    DEFAULT_SORT_COLUMN,
    DEFAULT_SORT_ORDER,
} from './constants/vendors-profile-risks-controller.constants';
import {
    VENDORS_RISKS_FILTER_RISK_POSTURE_ID,
    VENDORS_RISKS_FILTER_STATUS_ID,
} from './constants/vendors-risks-filters.constants';
import { getRiskPostureScoreRange } from './helpers/risk-posture-score-range.helper';
import { adaptRiskScoreSeverity } from './helpers/vendors-risks-adapter.helper';
import type { VendorsRisksTableItem } from './types/vendor-risk.type';

class VendorsRisksController {
    hasFilter = false;

    constructor() {
        makeAutoObservable(this);
    }

    riskSettingsQuery = new ObservedQuery(
        riskManagementControllerGetRiskSettingsOptions,
    );
    risksQuery = new ObservedQuery(
        vendorsRiskManagementControllerGetVendorRisksListOptions,
    );
    riskDashboardQuery = new ObservedQuery(
        vendorsRiskManagementControllerGetVendorRiskDashboardOptions,
    );

    get settings(): RiskSettingsResponseDto | null {
        return this.riskSettingsQuery.data ?? null;
    }

    get risks(): VendorsRisksTableItem[] {
        return (this.risksQuery.data?.data ?? []).map((risk) =>
            adaptRiskScoreSeverity(risk, this.settings?.thresholds ?? []),
        );
    }

    get dashboard(): DashboardResponseDto | null {
        return this.riskDashboardQuery.data ?? null;
    }

    get isLoading(): boolean {
        return this.risksQuery.isLoading;
    }

    get isSettingsLoading(): boolean {
        return this.riskSettingsQuery.isLoading;
    }

    loadSettings = () => {
        this.riskSettingsQuery.load();
    };

    loadRisks = (params: FetchDataResponseParams) => {
        const { pagination, globalFilter, sorting } = params;
        const { page, pageSize } = pagination;
        const { search, filters } = globalFilter;

        const riskPosture = filters[VENDORS_RISKS_FILTER_RISK_POSTURE_ID];
        const statusFilter = filters[VENDORS_RISKS_FILTER_STATUS_ID];

        this.hasFilter = !isEmpty(search) || !isEmpty(filters);

        type Query = Required<
            Parameters<
                typeof vendorsRiskManagementControllerGetVendorRisksListOptions
            >
        >[0]['query'];

        const query: Query = {
            page,
            limit: pageSize,
            q: search ?? '',
            sort: DEFAULT_SORT_COLUMN,
            sortDir: DEFAULT_SORT_ORDER,
            onlyVendors: true,
        };

        // Apply risk posture filter if present
        if (
            !isNil(riskPosture) &&
            isString(riskPosture.value) &&
            this.settings?.thresholds
        ) {
            const { minScore, maxScore } = getRiskPostureScoreRange(
                riskPosture.value,
                this.settings.thresholds,
            );

            if (minScore && maxScore) {
                query.minScore = minScore;
                query.maxScore = maxScore;
            }
        }

        // Apply status filter if present
        if (!isNil(statusFilter) && isString(statusFilter.value)) {
            // TODO: https://drata.atlassian.net/browse/ENG-65657 -- when datatable callback generics are supported, `as` can be removed.
            query['status[]'] = statusFilter.value as
                | 'ACTIVE'
                | 'ARCHIVED'
                | 'CLOSED';
        }

        if (!isEmpty(sorting)) {
            // ToDo: https://drata.atlassian.net/browse/ENG-65657 -- when datatable callback generics are supported, `as` can be removed.
            const sortId = sorting[0].id;

            if (sortId in COLUMN_NAMES_TO_SORT_IDS_MAP) {
                const mappedSort =
                    COLUMN_NAMES_TO_SORT_IDS_MAP[
                        sortId as keyof typeof COLUMN_NAMES_TO_SORT_IDS_MAP
                    ];

                // ToDo: https://drata.atlassian.net/browse/ENG-65657 -- when datatable callback generics are supported, `as` can be removed.
                query.sort = mappedSort as 'IDENTIFIED_DATE';
            }
            query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
        }

        this.risksQuery.load({
            query,
        });
    };

    loadDashboard = () => {
        // Using type assertion to handle the 'status[]' property
        // which is expected by the API but not properly typed in the TypeScript definitions
        this.riskDashboardQuery.load({
            query: {
                'status[]': ['ACTIVE'],
                classifications: [
                    'CONTRACTS',
                    'EXCEPTIONS',
                    'GENERAL_RISK',
                    'REGULATIONS',
                ],
            },
        });
    };

    loadVendorRisks = () => {
        this.loadDashboard();
        this.loadSettings();
        this.loadRisks({
            pagination: {
                page: 1,
                pageSize: DEFAULT_PAGE_SIZE,
                pageIndex: 0,
                pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
            },
            globalFilter: {
                search: '',
                filters: {},
            },
            sorting: [],
        });
    };
}

export const sharedVendorsRisksController = new VendorsRisksController();
