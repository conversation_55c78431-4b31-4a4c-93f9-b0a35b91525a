import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { makeAutoObservable } from '@globals/mobx';

class VendorsCriteriaController {
    tableParams?: FetchDataResponseParams;
    hasFilters = false;

    /**
     * Map to store data for each vendor criteria.
     */
    vendorCriteriaData = new Map<number, unknown>();

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Get loading state for the criteria table.
     */
    get isLoading(): boolean {
        // TODO: Return actual loading state
        return false;
    }

    /**
     * Get total count of criteria.
     */
    get total(): number {
        // TODO: Return actual total
        return 0;
    }

    /**
     * Get criteria data array.
     */
    get criteriaData(): { id?: string | number }[] {
        // TODO: Return actual criteria data
        return [];
    }
}

export const sharedVendorsCriteriaController = new VendorsCriteriaController();
