import { isEmpty, uniqueId } from 'lodash-es';
import {
    ADD_SOC_REVIEW_MODAL_ID,
    UPLOAD_REVIEW_REPORT_MODAL_ID,
} from '@components/vendors-security-reviews';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import {
    questionnairesControllerGenerateQuestionnaireSummaryMutation,
    vendorsDocumentsControllerGetPdfVendorDocumentDownloadUrlOptions,
    vendorsDocumentsControllerUploadVendorDocumentMutation,
    vendorsSecurityReviewsControllerCreateSecurityReviewDocumentMutation,
    vendorsSecurityReviewsControllerDeleteSecurityReviewDocumentMutation,
    vendorsSecurityReviewsControllerGetSecurityReviewDocumentsOptions,
} from '@globals/api-sdk/queries';
import type {
    SignedUrlResponseDto,
    VendorsDocumentsControllerUploadVendorDocumentData,
    VendorSecurityReviewDocumentRequestDto,
    VendorSecurityReviewDocumentResponseDto,
    VendorSecurityReviewRequestDto,
} from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    runInAction,
    when,
} from '@globals/mobx';
import type { NavigateFunction } from '@remix-run/react';
import { VENDORS_DETAILS_TABS_DEFAULT_PARAMS } from './constants/vendors-profile-documents.constants';
import {
    transformDocumentsToFiles,
    transformDocumentsToQuestionnaires,
    transformToDocuments,
} from './helpers/vendors-security-review-documents-adapter.helper';
import type {
    Document,
    DocumentFiles,
    QuestionnaireFiles,
} from './types/vendor-security-review.type';
import { sharedVendorsCurrentSecurityReviewsController } from './vendors-current-security-reviews-controller';
import { sharedVendorsDetailsController } from './vendors-details-controller';

export class VendorsSecurityReviewDocumentsController {
    constructor() {
        makeAutoObservable(this);
    }

    vendorId: number | null = null;

    hasRetriedQuestionnairesLoad = false;

    setVendorId = (vendorId: number): void => {
        this.vendorId = vendorId;
        // Reset retry flag when vendor changes
        this.resetQuestionnairesRetryFlag();
    };

    get currentVendorId(): number | null {
        return this.vendorId;
    }

    securityReviewDocumentsQuery = new ObservedQuery(
        vendorsSecurityReviewsControllerGetSecurityReviewDocumentsOptions,
    );

    pdfDownloadUrlQuery = new ObservedQuery(
        vendorsDocumentsControllerGetPdfVendorDocumentDownloadUrlOptions,
    );

    uploadFileMutation = new ObservedMutation(
        vendorsDocumentsControllerUploadVendorDocumentMutation,
    );
    createSecurityReviewDocumentMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerCreateSecurityReviewDocumentMutation,
    );

    deleteSecurityReviewDocumentMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerDeleteSecurityReviewDocumentMutation,
    );

    generateQuestionnaireSummaryMutation = new ObservedMutation(
        questionnairesControllerGenerateQuestionnaireSummaryMutation,
    );

    get isGeneratingSummary(): boolean {
        return this.generateQuestionnaireSummaryMutation.isPending;
    }

    get hasGenerationError(): boolean {
        return this.generateQuestionnaireSummaryMutation.hasError;
    }

    get socDocument(): VendorSecurityReviewDocumentResponseDto | undefined {
        return this.allSecurityReviewDocuments.find(
            ({ type }) => type === 'SOC_REPORT',
        );
    }

    get reviewDocument(): VendorSecurityReviewDocumentResponseDto | undefined {
        return this.allSecurityReviewDocuments.find(
            ({ type }) => type === 'REVIEW',
        );
    }

    get allSecurityReviewDocuments(): VendorSecurityReviewDocumentResponseDto[] {
        return this.securityReviewDocumentsQuery.data?.data ?? [];
    }

    get pdfDownloadUrl(): SignedUrlResponseDto | null {
        if (!this.socDocument?.id) {
            return null;
        }

        return this.pdfDownloadUrlQuery.data;
    }

    get isPdfDownloadLoading(): boolean {
        return this.pdfDownloadUrlQuery.isLoading;
    }

    get isLoading(): boolean {
        return this.securityReviewDocumentsQuery.isLoading;
    }

    get isAddingFiles(): boolean {
        return (
            this.uploadFileMutation.isPending ||
            this.createSecurityReviewDocumentMutation.isPending
        );
    }

    get securityReviewDocuments(): Document[] {
        return transformToDocuments(
            this.securityReviewDocumentsQuery.data?.data ?? [],
        );
    }

    get files(): DocumentFiles[] {
        return transformDocumentsToFiles(
            this.allSecurityReviewDocuments.filter(
                (item) => item.type === 'DOCUMENT',
            ),
        );
    }

    get questionnaires(): QuestionnaireFiles[] {
        return transformDocumentsToQuestionnaires(
            this.allSecurityReviewDocuments.filter(
                (item) => item.type === 'QUESTIONNAIRE',
            ),
        );
    }

    get bridgeLetterDocuments(): VendorSecurityReviewDocumentResponseDto[] {
        return this.allSecurityReviewDocuments.filter(
            ({ type }) => type === 'BRIDGE_LETTER',
        );
    }

    loadSecurityReviewDocuments = (
        options: Parameters<typeof this.securityReviewDocumentsQuery.load>[0],
    ): void => {
        this.securityReviewDocumentsQuery.load(options);
    };

    loadSecurityReviewSOCDocument = (
        options: Parameters<typeof this.securityReviewDocumentsQuery.load>[0],
    ): void => {
        this.securityReviewDocumentsQuery.load(options);
        when(
            () => !this.securityReviewDocumentsQuery.isLoading,
            () => {
                const data: VendorSecurityReviewDocumentResponseDto[] =
                    this.allSecurityReviewDocuments;

                if (isEmpty(data)) {
                    return;
                }

                if (!this.socDocument?.documentId) {
                    return;
                }

                this.loadPdfDownloadUrl(
                    this.vendorId ?? 0,
                    this.socDocument.documentId,
                );
            },
        );
    };

    loadPdfDownloadUrl = (vendorId: number, docId: number): void => {
        if (!docId || !vendorId) {
            return;
        }

        this.pdfDownloadUrlQuery.load({
            path: { id: vendorId, docId },
        });
    };

    uploadSecurityReviewDocument = (
        navigate: NavigateFunction,
        documentType: VendorsDocumentsControllerUploadVendorDocumentData['body']['type'],
        securityReviewStatus: VendorSecurityReviewRequestDto['securityReviewStatus'],
        securityReviewType: VendorSecurityReviewRequestDto['securityReviewType'],
        linkDocumentType: VendorSecurityReviewDocumentRequestDto['type'],
        file: File,
        vendorId: number | null,
    ): void => {
        if (!vendorId) {
            return;
        }

        this.uploadFileMutation.mutate({
            body: {
                file,
                type: documentType,
            },
            path: { id: vendorId },
        });

        when(
            () => !this.uploadFileMutation.isPending,
            () => {
                const {
                    response: responseUploadFile,
                    hasError: hasErrorUploadFile,
                } = this.uploadFileMutation;

                if (hasErrorUploadFile) {
                    snackbarController.addSnackbar({
                        id: 'upload-security-review-document-error',
                        props: {
                            title: t`Error while uploading the file`,
                            description: t`Please try again later`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const documents = responseUploadFile?.documents;

                if (documents) {
                    const documentsLength = documents.length;

                    sharedVendorsCurrentSecurityReviewsController.createNewSecurityReview(
                        navigate,
                        securityReviewStatus,
                        securityReviewType,
                        linkDocumentType,
                        documents[documentsLength - 1].id,
                    );
                } else {
                    logger.error({
                        message: 'Error while uploading the file',
                        additionalInfo: {
                            responseUploadFile,
                        },
                    });
                }
            },
        );
    };

    createReviewDocument = async (
        documentId: number,
        securityReviewId: number,
    ): Promise<void> => {
        if (!documentId || !securityReviewId) {
            return;
        }

        await this.createSecurityReviewDocumentMutation.mutateAsync({
            body: {
                documentId,
                type: 'REVIEW',
            },
            path: { id: securityReviewId },
        });
    };

    uploadFileAndLinkToSecurityReview = (
        file: File,
        securityReviewId: number,
        workspaceId: number,
        securityReviewType: VendorSecurityReviewRequestDto['securityReviewType'],
        linkDocumentType: VendorSecurityReviewDocumentRequestDto['type'],
        onError?: () => void,
    ): void => {
        if (!this.currentVendorId) {
            onError?.();

            return;
        }

        this.uploadFileMutation.mutate({
            body: {
                file,
                type: 'SOC_DOCUMENT',
            },
            path: { id: this.currentVendorId },
        });

        when(
            () => !this.uploadFileMutation.isPending,
            () => {
                const { response, hasError } = this.uploadFileMutation;

                if (hasError) {
                    onError?.();

                    return;
                }

                const documents = response?.documents;

                if (documents && !isEmpty(documents)) {
                    const documentId = documents.find(
                        (doc) => doc.type === 'SOC_DOCUMENT',
                    )?.id;

                    if (!documentId) {
                        onError?.();

                        return;
                    }

                    this.createNewSecurityReviewWithDocuments(
                        documentId,
                        securityReviewId,
                        workspaceId,
                        securityReviewType,
                        linkDocumentType,
                        this.currentVendorId,
                    );
                } else {
                    onError?.();
                }
            },
        );
    };

    createNewSecurityReviewWithDocuments = (
        documentId: number,
        securityReviewId: number,
        workspaceId: number,
        securityReviewType: VendorSecurityReviewRequestDto['securityReviewType'],
        linkDocumentType: VendorSecurityReviewDocumentRequestDto['type'],
        vendorId: number | null,
        navigate?: NavigateFunction,
    ): void => {
        this.createSecurityReviewDocumentMutation.mutate({
            body: {
                documentId,
                type: linkDocumentType,
                isAddedFromLibrary: false,
            },
            path: { id: securityReviewId },
        });
        when(
            () => !this.createSecurityReviewDocumentMutation.isPending,
            () => {
                const { hasError: hasErrorDocumentReview } =
                    this.createSecurityReviewDocumentMutation;

                if (hasErrorDocumentReview) {
                    snackbarController.addSnackbar({
                        id: 'create-security-review-error',
                        props: {
                            title: t`Error while creating the security review SOC document`,
                            description: t`Please try again later`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    sharedVendorsCurrentSecurityReviewsController.activeProcess = false;

                    return;
                }

                if (securityReviewType === 'SOC_REPORT') {
                    this.securityReviewDocumentsQuery.invalidate();
                    if (vendorId && documentId) {
                        this.pdfDownloadUrlQuery.load({
                            path: { id: vendorId, docId: documentId },
                        });
                    }
                    modalController.closeModal(ADD_SOC_REVIEW_MODAL_ID);
                    navigate?.(
                        `workspaces/${workspaceId}/vendors/${
                            sharedVendorsDetailsController.isProspectiveVendor
                                ? 'prospective'
                                : 'current'
                        }/${vendorId}/security-reviews/soc/${securityReviewId}`,
                    );
                } else {
                    modalController.closeModal(UPLOAD_REVIEW_REPORT_MODAL_ID);
                    snackbarController.addSnackbar({
                        id: 'create-security-review-success',
                        props: {
                            title: t`Security review created successfully`,
                            description: t`The security review has been created successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    sharedVendorsCurrentSecurityReviewsController.loadPaginatedSecurityReviews(
                        VENDORS_DETAILS_TABS_DEFAULT_PARAMS,
                    );
                }

                sharedVendorsCurrentSecurityReviewsController.activeProcess = false;
            },
        );
    };

    /**
     * Add a document from the library to a security review.
     */
    addDocumentFromLibrary = (
        documentId: number,
        securityReviewId: number,
        onSuccess?: () => void,
        onError?: () => void,
    ): void => {
        if (!securityReviewId || !documentId) {
            logger.error('Missing security review ID or document ID');
            onError?.();

            return;
        }

        this.createSecurityReviewDocumentMutation.mutate({
            body: {
                documentId,
                type: 'DOCUMENT',
                isAddedFromLibrary: true,
            },
            path: { id: securityReviewId },
        });

        when(
            () => !this.createSecurityReviewDocumentMutation.isPending,
            () => {
                if (this.createSecurityReviewDocumentMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: `add-document-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to add document from library`,
                            description: t`Please try again later`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    onError?.();

                    return;
                }

                this.securityReviewDocumentsQuery.invalidate();
                onSuccess?.();
            },
        );
    };

    /**
     * Upload a file from computer and link it to a security review.
     */
    uploadFileFromComputer = (
        file: File,
        securityReviewId: number,
        onSuccess?: () => void,
        onError?: () => void,
    ): void => {
        if (!securityReviewId) {
            logger.error('Missing security review ID');
            onError?.();

            return;
        }

        if (!this.currentVendorId) {
            snackbarController.addSnackbar({
                id: `missing-vendor-id-error-${uniqueId()}`,
                props: {
                    title: t`Unable to upload file`,
                    description: t`Vendor information is not available. Please refresh the page and try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
            onError?.();

            return;
        }

        // First upload the file
        this.uploadFileMutation.mutate({
            body: {
                file,
                type: 'COMPLIANCE_REPORT',
            },
            path: { id: this.currentVendorId },
        });

        when(
            () => !this.uploadFileMutation.isPending,
            () => {
                if (this.uploadFileMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: `upload-file-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to upload file`,
                            description: t`Please try again later`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    onError?.();

                    return;
                }

                const uploadResponse = this.uploadFileMutation.response;

                if (!uploadResponse) {
                    logger.error({
                        message: 'No upload response received',
                        additionalInfo: {
                            uploadResponse,
                        },
                    });
                    onError?.();

                    return;
                }

                const { documents } = uploadResponse;

                if (isEmpty(documents)) {
                    logger.error({
                        message: 'No documents returned from upload',
                        additionalInfo: {
                            documents,
                        },
                    });
                    onError?.();

                    return;
                }

                const documentId = documents[documents.length - 1]?.id;

                if (!documentId) {
                    logger.error('No document ID found in upload response');
                    onError?.();

                    return;
                }

                // Link the document to the security review
                this.createSecurityReviewDocumentMutation.mutate({
                    body: {
                        documentId,
                        type: 'DOCUMENT',
                    },
                    path: { id: securityReviewId },
                });

                when(
                    () => !this.createSecurityReviewDocumentMutation.isPending,
                    () => {
                        if (
                            this.createSecurityReviewDocumentMutation.hasError
                        ) {
                            snackbarController.addSnackbar({
                                id: `link-document-error-${uniqueId()}`,
                                props: {
                                    title: t`Failed to link document to security review`,
                                    description: t`Please try again later`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                            onError?.();

                            return;
                        }

                        this.securityReviewDocumentsQuery.invalidate();
                        onSuccess?.();
                    },
                );
            },
        );
    };

    /**
     * Upload multiple files from computer and link them to a security review.
     * Processes files sequentially to avoid shared MobX mutation instance conflicts.
     */
    uploadFilesFromComputer = async (
        files: File[],
        securityReviewId: number,
        onSuccess?: () => void,
        onError?: () => void,
    ): Promise<void> => {
        if (isEmpty(files)) {
            onError?.();

            return;
        }

        if (!securityReviewId || !this.currentVendorId) {
            logger.error('Missing security review ID or vendor ID');
            onError?.();

            return;
        }

        let successCount = 0;
        let failureCount = 0;

        // Process files sequentially to avoid shared mutation instance conflicts
        for (const file of files) {
            try {
                await new Promise<void>((resolve, reject) => {
                    runInAction(() => {
                        this.uploadFileFromComputer(
                            file,
                            securityReviewId,
                            resolve,
                            reject,
                        );
                    });
                });
                successCount = successCount + 1;
            } catch (error) {
                logger.error({
                    message: `Failed to upload file ${file.name}:`,
                    additionalInfo: {
                        error,
                    },
                });
                failureCount = failureCount + 1;
            }
        }

        const totalCount = files.length;

        // Show appropriate snackbar messages based on results
        if (successCount === totalCount) {
            // Complete success scenario
            const successMessage =
                successCount === 1
                    ? t`Security review document uploaded successfully`
                    : t`${successCount} security review documents uploaded successfully`;

            snackbarController.addSnackbar({
                id: `upload-files-success-${uniqueId()}`,
                props: {
                    title: successMessage,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        } else if (successCount > 0) {
            // Partial success scenario
            const successMessage =
                successCount === 1
                    ? t`Security review document uploaded successfully`
                    : t`${successCount} security review documents uploaded successfully`;

            const errorMessage =
                failureCount === 1
                    ? t`Failed to upload 1 document`
                    : t`Failed to upload ${failureCount} documents`;

            snackbarController.addSnackbar({
                id: `upload-files-success-${uniqueId()}`,
                props: {
                    title: successMessage,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            snackbarController.addSnackbar({
                id: `upload-files-error-${uniqueId()}`,
                props: {
                    title: errorMessage,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        } else {
            // Complete failure scenario
            snackbarController.addSnackbar({
                id: `upload-files-error-${uniqueId()}`,
                props: {
                    title: t`Failed to upload files`,
                    description: t`All files failed to upload. Please try again later.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }

        // Invalidate queries and call callbacks
        runInAction(() => {
            if (successCount > 0) {
                this.securityReviewDocumentsQuery.invalidate();
                onSuccess?.();
            } else {
                onError?.();
            }
        });
    };

    /**
     * Resets the retry flag for questionnaires loading.
     * Should be called when starting a fresh AI summary generation process.
     */
    resetQuestionnairesRetryFlag = (): void => {
        this.hasRetriedQuestionnairesLoad = false;
    };

    /**
     * Generates AI summary for a specific questionnaire by responseId.
     */
    generateQuestionnaireSummary = (
        responseId: number,
        onSuccess?: () => void,
    ): void => {
        // Check if AI is enabled and user has permissions
        if (!sharedFeatureAccessModel.isVendorAISummaryEnabled) {
            return;
        }

        this.generateQuestionnaireSummaryMutation.mutate({
            path: { id: responseId },
        });

        when(
            () => !this.isGeneratingSummary,
            () => {
                if (this.hasGenerationError) {
                    snackbarController.addSnackbar({
                        id: `summary-generation-error-${uniqueId()}`,
                        props: {
                            title: t`AI Summary Generation Failed`,
                            description: t`Unable to generate AI summary. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                onSuccess?.();
            },
        );
    };
}

export const sharedVendorsSecurityReviewDocumentsController =
    new VendorsSecurityReviewDocumentsController();
