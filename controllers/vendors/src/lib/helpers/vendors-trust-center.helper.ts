import { isArray, isObject, isString } from 'lodash-es';
import type { IconName } from '@cosmos/components/icon';
import type { VendorTrustCenterItemsByCategoryResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    ICON_MAP,
    type ItemMaturityLevel,
} from '@views/vendors-profile-trust-center';

export const normalizeWebsiteUrl = (
    website: string | undefined,
): string | undefined => {
    if (!website) {
        return undefined;
    }

    if (website.startsWith('http://') || website.startsWith('https://')) {
        return website;
    }

    return `https://${website}`;
};

export const normalizeSecurityGrade = (
    item: VendorTrustCenterItemsByCategoryResponseDto['items'][number],
): VendorTrustCenterItemsByCategoryResponseDto['items'][number]['additionalFields'] => {
    return {
        ...item.additionalFields,
        listEntries: isArray(item.additionalFields?.listEntries)
            ? item.additionalFields.listEntries.map((entry) => {
                  if (isObject(entry) && 'url' in entry) {
                      return {
                          ...entry,
                          url: normalizeWebsiteUrl(
                              isString(entry.url) ? entry.url : undefined,
                          ),
                      };
                  }

                  return entry as object;
              })
            : [],
    };
};

export const getMaturityIcon = (
    maturityLevel: ItemMaturityLevel,
): {
    name: IconName;
    colorScheme: 'success';
    backgroundType: 'round';
} => ({
    name: ICON_MAP[maturityLevel],
    colorScheme: 'success' as const,
    backgroundType: 'round' as const,
});

export const getMaturityLevelTooltip = (
    maturityLevel: ItemMaturityLevel,
): string => {
    switch (maturityLevel) {
        case 'full': {
            return t`Fully Implemented`;
        }
        case 'in_progress': {
            return t`In Progress`;
        }
        default: {
            return t`N/A`;
        }
    }
};
