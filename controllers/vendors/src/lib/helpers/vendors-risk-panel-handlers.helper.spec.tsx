import { beforeEach, describe, expect, test, vi } from 'vitest';
import { panelController } from '@controllers/panel';
import type { ButtonProps } from '@cosmos/components/button';
import type { VendorsRisksTableItem } from '../types/vendor-risk.type';
import {
    generatePanelPaginationOptions,
    getRiskOverviewPanelActions,
} from './vendors-risk-panel-handlers.helper';

vi.mock('@controllers/panel', () => ({
    panelController: {
        openPanel: vi.fn(),
        closePanel: vi.fn(),
    },
}));

describe('vendors-risk-panel-handlers.helper', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('getRiskOverviewPanelActions', () => {
        const mockNavigate = vi.fn();
        const riskId = 'RISK-123';

        test('returns correct stack structure', () => {
            const stacks = getRiskOverviewPanelActions(riskId, () => {
                mockNavigate(
                    `/risk/management/registers/1/register-risks/${riskId}/overview`,
                );

                return undefined;
            });

            expect(stacks).toHaveLength(2);
            expect(stacks[0].id).toBe('left-stack');
            expect(stacks[1].id).toBe('right-stack');
        });

        test('left stack contains metadata with risk ID', () => {
            const stacks = getRiskOverviewPanelActions(riskId, () => {
                mockNavigate(
                    `/risk/management/registers/1/register-risks/${riskId}/overview`,
                );

                return undefined;
            });
            const leftStack = stacks[0];

            expect(leftStack.actions).toHaveLength(1);
            expect(leftStack.actions[0].actionType).toBe('text');
            expect(leftStack.actions[0].id).toBe('badge-stack');
        });

        test('right stack contains open button', () => {
            const stacks = getRiskOverviewPanelActions(riskId, () => {
                mockNavigate(
                    `/risk/management/registers/1/register-risks/${riskId}/overview`,
                );

                return undefined;
            });
            const rightStack = stacks[1];

            expect(rightStack.actions).toHaveLength(1);
            expect(rightStack.actions[0].actionType).toBe('button');
            expect(rightStack.actions[0].id).toBe('open-button');
        });

        test('clicking open button closes panel and navigates to risk overview', () => {
            const closePanelSpy = vi.spyOn(panelController, 'closePanel');
            const stacks = getRiskOverviewPanelActions(riskId, () => {
                mockNavigate(
                    `/risk/management/registers/1/register-risks/${riskId}/overview`,
                );

                return undefined;
            });
            const openButton = stacks[1].actions[0];

            const mockEvent = {
                preventDefault: () => undefined,
                stopPropagation: () => undefined,
            } as React.MouseEvent<HTMLButtonElement>;

            (openButton.typeProps as ButtonProps).onClick?.(mockEvent);

            expect(closePanelSpy).toHaveBeenCalled();
            expect(mockNavigate).toHaveBeenCalledWith(
                `/risk/management/registers/1/register-risks/${riskId}/overview`,
            );
        });
    });

    describe('generatePanelPaginationOptions', () => {
        const mockPanelContent = vi.fn();
        const mockRisks = [
            { riskId: 'RISK-1' },
            { riskId: 'RISK-2' },
            { riskId: 'RISK-3' },
        ] as VendorsRisksTableItem[];

        test('returns correct pagination for middle item', () => {
            const options = generatePanelPaginationOptions(
                'RISK-2',
                mockRisks,
                mockPanelContent,
            );

            expect(options.currentItem).toBe(2);
            expect(options.totalItems).toBe(3);
            expect(typeof options.onNextPageClick).toBe('function');
            expect(typeof options.onPrevPageClick).toBe('function');
        });

        test('handles first item', () => {
            const options = generatePanelPaginationOptions(
                'RISK-1',
                mockRisks,
                mockPanelContent,
            );

            expect(options.currentItem).toBe(1);
            expect(options.totalItems).toBe(3);
            expect(options.onPrevPageClick).toStrictEqual(expect.any(Function));
        });

        test('handles last item', () => {
            const options = generatePanelPaginationOptions(
                'RISK-3',
                mockRisks,
                mockPanelContent,
            );

            expect(options.currentItem).toBe(3);
            expect(options.totalItems).toBe(3);
            expect(options.onNextPageClick).toStrictEqual(expect.any(Function));
        });

        test('handles invalid risk ID', () => {
            const options = generatePanelPaginationOptions(
                'INVALID-ID',
                mockRisks,
                mockPanelContent,
            );

            expect(options.currentItem).toBe(0);
            expect(options.totalItems).toBe(3);
        });
    });
});
