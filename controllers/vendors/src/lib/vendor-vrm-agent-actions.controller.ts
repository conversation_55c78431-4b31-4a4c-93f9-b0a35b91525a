import { routeController } from '@controllers/route';
import { snackbarController } from '@controllers/snackbar';
import { makeAutoObservable } from '@globals/mobx';
import { VRM_AGENT_ACTION_MAPPING } from './constants/vrm-agent-actions-mapping.constants';
import {
    VRM_ASSESSMENT_MISSING_IMPACT_LEVEL_ERROR,
    VRM_ASSESSMENT_STARTED_SUCCESS,
} from './constants/vrm-agent-snackbars.constants';
import type { VrmAgentActionId } from './types/vendor-vrm-agent.types';
import { sharedVendorsDetailsController } from './vendors-details-controller';
import { sharedVendorsSecurityReviewDetailsController } from './vendors-security-review-details-controller';

export class VendorVrmAgentActionsController {
    constructor() {
        makeAutoObservable(this);
    }

    executeAction = (actionId: string, vendorId: string): void => {
        if (!(actionId in VRM_AGENT_ACTION_MAPPING)) {
            console.warn(`Unknown action ID: ${actionId}`);

            return;
        }

        const handlerName =
            VRM_AGENT_ACTION_MAPPING[actionId as VrmAgentActionId];
        const handler = this.getActionHandler(handlerName);

        if (handler) {
            handler(vendorId);
        } else {
            console.warn(`No handler found for action: ${actionId}`);
        }
    };

    getActionHandler = (
        handlerName: string,
    ): ((vendorId: string) => void) | null => {
        switch (handlerName) {
            case 'handleAssessmentStart': {
                return this.handleAssessmentStart;
            }
            case 'handleStopAnalysis': {
                return this.handleStopAnalysis;
            }
            case 'handleSendQuestionnaire': {
                return this.handleSendQuestionnaire;
            }
            case 'handleAutoSendQuestionnaire': {
                return this.handleAutoSendQuestionnaire;
            }
            case 'handleFinalizeAssessment': {
                return this.handleFinalizeAssessment;
            }
            default: {
                return null;
            }
        }
    };
    handleAssessmentStart = (vendorId: string): void => {
        console.info('🚀 Starting VRM assessment for vendor:', vendorId);

        // First, validate that the vendor has an impact level assigned
        const { vendorDetails } = sharedVendorsDetailsController;

        if (!vendorDetails?.impactLevel) {
            snackbarController.addSnackbar(
                VRM_ASSESSMENT_MISSING_IMPACT_LEVEL_ERROR,
            );

            return;
        }

        // Check if we're on a specific security review route
        const { currentParams } = routeController;
        const routeSecurityReviewId =
            currentParams.securityReviewId || currentParams.reviewId;

        // Only use controller data if we're on a specific security review route
        const securityReviewId = routeSecurityReviewId
            ? sharedVendorsSecurityReviewDetailsController.securityReviewDetails
                  ?.id
            : undefined;

        console.info('📍 Security review detection:', {
            routeSecurityReviewId,
            securityReviewId,
            hasSecurityReviewId: Boolean(securityReviewId),
        });

        // Always call the same endpoint - backend will handle security review creation
        this.startVrmAssessment(Number(vendorId), securityReviewId);
    };

    handleStopAnalysis = (vendorId: string): void => {
        console.info('Stopping VRM analysis for vendor:', vendorId);
    };
    handleSendQuestionnaire = (vendorId: string): void => {
        console.info('Sending questionnaire for vendor:', vendorId);
    };
    handleAutoSendQuestionnaire = (vendorId: string): void => {
        console.info('Enabling auto-send questionnaires for vendor:', vendorId);
    };

    handleFinalizeAssessment = (vendorId: string): void => {
        console.info('Finalizing assessment for vendor:', vendorId);
    };

    startVrmAssessment = (
        vendorId: number,
        securityReviewId?: number,
    ): void => {
        console.info('🎯 Starting VRM assessment:', {
            vendorId,
            securityReviewId,
        });

        // Mock the unified endpoint call
        this.mockStartVrmAssessment(vendorId, securityReviewId);
    };

    mockStartVrmAssessment = (
        vendorId: number,
        securityReviewId: number | undefined,
    ): void => {
        console.info('📤 MOCK: Calling unified VRM assessment endpoint:', {
            vendorId,
            securityReviewId,
        });

        // Simulate API response
        setTimeout(() => {
            console.info('✅ MOCK: VRM assessment started successfully:', {
                vendorId,
                securityReviewId,
                created: securityReviewId
                    ? 'Using existing security review'
                    : 'Security review created by backend',
            });

            snackbarController.addSnackbar(VRM_ASSESSMENT_STARTED_SUCCESS);
        }, 1000);
    };

    hasHandler = (actionId: string): boolean => {
        return actionId in VRM_AGENT_ACTION_MAPPING;
    };
}

export const sharedVendorVrmAgentActionsController =
    new VendorVrmAgentActionsController();
