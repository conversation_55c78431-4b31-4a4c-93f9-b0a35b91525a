import { isEmpty } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export interface AuditCreationWizardData {
    conductAuditOption: string;
    auditDetails?: {
        name?: string;
        description?: string;
        framework?: string;
    };
    assignedAuditors?: string[];
}

class AuditCreationWizardController {
    wizardData: AuditCreationWizardData = {
        conductAuditOption: '',
    };

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Conduct audit step methods.
     */
    setConductAuditOption = (option: string): void => {
        this.wizardData.conductAuditOption = option;
    };

    get conductAuditOption(): string {
        return this.wizardData.conductAuditOption;
    }

    /**
     * Validation methods.
     */
    validateConductAuditStep = (): boolean => {
        return !isEmpty(this.wizardData.conductAuditOption);
    };

    validateAuditDetailsStep = (): boolean => {
        // TODO: Implement audit details validation in https://drata.atlassian.net/browse/ENG-72178
        return true;
    };

    validateAssignAuditorsStep = (): boolean => {
        // TODO: Implement auditors validation in https://drata.atlassian.net/browse/ENG-72181
        return true;
    };

    /**
     * Reset wizard data.
     */
    resetWizardData = (): void => {
        this.wizardData = {
            conductAuditOption: '',
        };
    };

    completeWizard = (): void => {
        // TODO: Implement actual audit creation API call in https://drata.atlassian.net/browse/ENG-72181
        const workspaceId = sharedWorkspacesController.currentWorkspaceId;

        sharedProgrammaticNavigationController.navigateTo(
            `/workspaces/${workspaceId}/compliance/audits`,
        );
    };

    cancelWizard = (): void => {
        const workspaceId = sharedWorkspacesController.currentWorkspaceId;

        sharedProgrammaticNavigationController.navigateTo(
            `/workspaces/${workspaceId}/compliance/audits`,
        );
    };
}

export const sharedAuditCreationWizardController =
    new AuditCreationWizardController();
