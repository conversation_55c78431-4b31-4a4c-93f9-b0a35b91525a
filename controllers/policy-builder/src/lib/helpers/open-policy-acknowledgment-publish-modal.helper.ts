import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import { PolicyAcknowledgmentPublishModal } from '../components/policy-acknowledgment-publish-modal.component';

const POLICY_ACKNOWLEDGMENT_PUBLISH_MODAL_ID =
    'policy-acknowledgment-publish-modal';

export const openPolicyAcknowledgmentPublishModal = action((): void => {
    modalController.openModal({
        id: POLICY_ACKNOWLEDGMENT_PUBLISH_MODAL_ID,
        content: PolicyAcknowledgmentPublishModal,
        size: 'lg',
        centered: true,
        disableClickOutsideToClose: true,
    });
});

export const closePolicyAcknowledgmentPublishModal = action((): void => {
    modalController.closeModal(POLICY_ACKNOWLEDGMENT_PUBLISH_MODAL_ID);
});
