import { modalController } from '@controllers/modal';
import { PolicyRequestChangesModal } from '../components/policy-request-changes-modal.component';

const POLICY_REQUEST_CHANGES_MODAL_ID = 'policy-request-changes-modal';

export const closePolicyRequestChangesModal = (): void => {
    modalController.closeModal(POLICY_REQUEST_CHANGES_MODAL_ID);
};

export const openPolicyRequestChangesModal = (): void => {
    modalController.openModal({
        id: POLICY_REQUEST_CHANGES_MODAL_ID,
        content: PolicyRequestChangesModal,
        centered: true,
        disableClickOutsideToClose: true,
        size: 'md',
    });
};
