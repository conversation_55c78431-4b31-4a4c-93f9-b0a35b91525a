import { t } from '@globals/i18n/macro';
import { PolicyPublishConfirmationContent } from '../components/policy-publish-confirmation-content.component';
import type { PolicyBuilderModalConfig } from '../types/policy-builder-modal-config.type';

interface CreatePolicyPublishConfirmationModalParams {
    isLoading: () => boolean;
    onConfirm: () => void;
    onCancel: () => void;
}

export const createPolicyPublishConfirmationModal = ({
    isLoading,
    onConfirm,
    onCancel,
}: CreatePolicyPublishConfirmationModalParams): PolicyBuilderModalConfig => {
    return {
        getTitle: () => t`Publish policy`,
        getContent: () => (
            <PolicyPublishConfirmationContent data-id="policy-publish-confirmation" />
        ),
        getActions: () => [
            {
                label: t`Cancel`,
                level: 'tertiary',
                onClick: onCancel,
                cosmosUseWithCaution_isDisabled: isLoading(),
            },
            {
                label: t`Confirm`,
                level: 'primary',
                onClick: () => {
                    onConfirm();
                },
                isLoading: isLoading(),
                a11yLoadingLabel: t`Publishing policy`,
            },
        ],
        handleClose: () => {
            if (isLoading()) {
                return;
            }

            onCancel();
        },
    };
};
