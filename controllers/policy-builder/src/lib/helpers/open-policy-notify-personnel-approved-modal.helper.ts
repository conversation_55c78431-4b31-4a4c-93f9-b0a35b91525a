import { modalController } from '@controllers/modal';
import { PolicyNotifyPersonnelApprovedModal } from '../components/policy-notify-personnel-approved-modal.component';

const POLICY_NOTIFY_PERSONNEL_APPROVED_MODAL_ID =
    'policy-notify-personnel-approved-modal';

export const openPolicyNotifyPersonnelApprovedModal = (): void => {
    modalController.openModal({
        id: POLICY_NOTIFY_PERSONNEL_APPROVED_MODAL_ID,
        content: PolicyNotifyPersonnelApprovedModal,
        centered: true,
        disableClickOutsideToClose: true,
        size: 'md',
    });
};

export const closePolicyNotifyPersonnelApprovedModal = (): void => {
    modalController.closeModal(POLICY_NOTIFY_PERSONNEL_APPROVED_MODAL_ID);
};
