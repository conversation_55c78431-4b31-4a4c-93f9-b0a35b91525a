import { modalController } from '@controllers/modal';
import { PolicyOverrideApprovalModal } from '../components/policy-override-approval-modal.component';

const POLICY_OVERRIDE_APPROVAL_MODAL_ID = 'policy-override-approval-modal';

export const closePolicyOverrideApprovalModal = (): void => {
    modalController.closeModal(POLICY_OVERRIDE_APPROVAL_MODAL_ID);
};

export const openPolicyOverrideApprovalModal = (): void => {
    modalController.openModal({
        id: POLICY_OVERRIDE_APPROVAL_MODAL_ID,
        content: PolicyOverrideApprovalModal,
        centered: true,
        disableClickOutsideToClose: true,
        size: 'md',
    });
};
