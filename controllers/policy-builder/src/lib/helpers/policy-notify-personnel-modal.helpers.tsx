import type * as React from 'react';
import type { ButtonProps } from '@cosmos/components/button';
import { t } from '@globals/i18n/macro';
import type { FormValues, UseFormSubmitReturn } from '@ui/forms';
import { PolicyNotifyPersonnelConfirmationContent } from '../components/policy-notify-personnel-confirmation-content.component';
import { PolicyNotifyPersonnelQuestionsContent } from '../components/policy-notify-personnel-questions-content.component';
import type { PolicyNotifyPersonnelModalStep } from '../controllers/policy-notify-personnel-modal.controller';

interface GetPolicyNotifyPersonnelModalTitleParams {
    modalStep: PolicyNotifyPersonnelModalStep;
    includeAcknowledgmentQuestion?: boolean;
}

export function getPolicyNotifyPersonnelModalTitle({
    modalStep,
    includeAcknowledgmentQuestion,
}: GetPolicyNotifyPersonnelModalTitleParams): string {
    switch (modalStep) {
        case 'confirmation': {
            return t`Drata notification confirmation`;
        }
        case 'question':
        default: {
            if (includeAcknowledgmentQuestion) {
                return t`Publish policy`;
            }

            return t`Notify Personnel`;
        }
    }
}

interface GetPolicyNotifyPersonnelModalContentParams {
    modalStep: PolicyNotifyPersonnelModalStep;
    personnelCount: number;
    currentShouldNotifyEmployeesChoice: string;
    currentAcknowledgmentChoice?: string;
    includeAcknowledgmentQuestion?: boolean;
    formRef: UseFormSubmitReturn['formRef'];
    isLoading: () => boolean;
    processNotificationChoice: (formValues: FormValues) => void;
}

export function getPolicyNotifyPersonnelModalContent({
    modalStep,
    personnelCount,
    currentShouldNotifyEmployeesChoice,
    currentAcknowledgmentChoice = '',
    includeAcknowledgmentQuestion = false,
    formRef,
    isLoading,
    processNotificationChoice,
}: GetPolicyNotifyPersonnelModalContentParams): React.JSX.Element {
    switch (modalStep) {
        case 'confirmation': {
            return (
                <PolicyNotifyPersonnelConfirmationContent
                    data-id="policy-notify-personnel-confirmation"
                    personnelCount={personnelCount}
                />
            );
        }
        case 'question':
        default: {
            return (
                <PolicyNotifyPersonnelQuestionsContent
                    data-id="policy-notify-personnel-questions"
                    formRef={formRef}
                    currentAcknowledgmentChoice={currentAcknowledgmentChoice}
                    isLoading={isLoading}
                    currentShouldNotifyEmployeesChoice={
                        currentShouldNotifyEmployeesChoice
                    }
                    includeAcknowledgmentQuestion={
                        includeAcknowledgmentQuestion
                    }
                    onSubmit={processNotificationChoice}
                />
            );
        }
    }
}

interface GetPolicyNotifyPersonnelModalActionsParams {
    modalStep: PolicyNotifyPersonnelModalStep;
    isLoading: () => boolean;
    isLoadingPersonnelCount: boolean;
    returnToQuestionModal: () => void;
    confirmAutomatedNotification: () => void;
    cancelNotificationProcess: () => void;
    triggerSubmit: UseFormSubmitReturn['triggerSubmit'];
}

export function getPolicyNotifyPersonnelModalActions({
    modalStep,
    isLoading,
    isLoadingPersonnelCount,
    returnToQuestionModal,
    confirmAutomatedNotification,
    cancelNotificationProcess,
    triggerSubmit,
}: GetPolicyNotifyPersonnelModalActionsParams): ButtonProps[] {
    const isLoadingState = isLoading() || isLoadingPersonnelCount;

    switch (modalStep) {
        case 'confirmation': {
            return [
                {
                    label: t`Cancel`,
                    level: 'tertiary',
                    onClick: returnToQuestionModal,
                    cosmosUseWithCaution_isDisabled: isLoadingState,
                },
                {
                    label: t`Confirm`,
                    level: 'primary',
                    onClick: confirmAutomatedNotification,
                    isLoading: isLoadingState,
                    a11yLoadingLabel: t`Confirming notification`,
                },
            ];
        }
        case 'question':
        default: {
            return [
                {
                    label: t`Cancel`,
                    level: 'tertiary',
                    onClick: cancelNotificationProcess,
                    cosmosUseWithCaution_isDisabled: isLoadingState,
                },
                {
                    label: t`Confirm`,
                    level: 'primary',
                    onClick: () => {
                        triggerSubmit();
                    },
                    isLoading: isLoadingState,
                    a11yLoadingLabel: t`Processing notification choice`,
                },
            ];
        }
    }
}
