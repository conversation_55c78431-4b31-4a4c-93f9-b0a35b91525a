import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import { PolicySaveChangesModal } from '../components/policy-save-changes-modal.component';

const POLICY_SAVE_CHANGES_MODAL_ID = 'policy-save-changes-modal';

export const openPolicySaveChangesModal = action((): void => {
    modalController.openModal({
        id: POLICY_SAVE_CHANGES_MODAL_ID,
        content: PolicySaveChangesModal,
        centered: true,
        disableClickOutsideToClose: true,
        size: 'lg',
    });
});

export const closePolicySaveChangesModal = action((): void => {
    modalController.closeModal(POLICY_SAVE_CHANGES_MODAL_ID);
});
