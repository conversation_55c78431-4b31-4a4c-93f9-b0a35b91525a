import { modalController } from '@controllers/modal';
import { PolicyCancelApprovalModal } from '../components/policy-cancel-approval-modal.component';

const POLICY_CANCEL_APPROVAL_MODAL_ID = 'policy-cancel-approval-modal';

export const closePolicyCancelApprovalModal = (): void => {
    modalController.closeModal(POLICY_CANCEL_APPROVAL_MODAL_ID);
};

export const openPolicyCancelApprovalModal = (): void => {
    modalController.openModal({
        id: POLICY_CANCEL_APPROVAL_MODAL_ID,
        content: PolicyCancelApprovalModal,
        centered: true,
        disableClickOutsideToClose: true,
        size: 'md',
    });
};
