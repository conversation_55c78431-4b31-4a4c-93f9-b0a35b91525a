import { modalController } from '@controllers/modal';
import { PolicyFinalizeModal } from '../components/policy-finalize-modal.component';

const POLICY_FINALIZE_MODAL_ID = 'policy-finalize-modal';

export const openPolicyFinalizeModal = (): void => {
    modalController.openModal({
        id: POLICY_FINALIZE_MODAL_ID,
        content: PolicyFinalizeModal,
        centered: true,
        disableClickOutsideToClose: true,
        size: 'lg',
    });
};

export const closePolicyFinalizeModal = (): void => {
    modalController.closeModal(POLICY_FINALIZE_MODAL_ID);
};
