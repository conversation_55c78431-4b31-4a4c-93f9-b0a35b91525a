import type { UseFormSubmitReturn } from '@ui/forms';
import {
    PolicyNotifyPersonnelModalController,
    type PolicyNotifyPersonnelModalControllerParams,
} from '../controllers/policy-notify-personnel-modal.controller';
import type { PolicyBuilderModalConfig } from '../types/policy-builder-modal-config.type';
import {
    getPolicyNotifyPersonnelModalActions,
    getPolicyNotifyPersonnelModalContent,
    getPolicyNotifyPersonnelModalTitle,
} from './policy-notify-personnel-modal.helpers';

interface CreatePolicyNotifyPersonnelModalParams
    extends PolicyNotifyPersonnelModalControllerParams {
    includeAcknowledgmentQuestion?: boolean;
    formRef: UseFormSubmitReturn['formRef'];
    triggerSubmit: UseFormSubmitReturn['triggerSubmit'];
}

export const createPolicyNotifyPersonnelModal = ({
    includeAcknowledgmentQuestion = false,
    formRef,
    isLoading = () => false,
    onConfirm,
    onCancel,
    triggerSubmit,
}: CreatePolicyNotifyPersonnelModalParams): PolicyBuilderModalConfig => {
    const controller = new PolicyNotifyPersonnelModalController({
        includeAcknowledgmentQuestion,
        isLoading,
        onConfirm,
        onCancel,
    });

    return {
        getTitle: () =>
            getPolicyNotifyPersonnelModalTitle({
                modalStep: controller.modalStep,
                includeAcknowledgmentQuestion:
                    controller.includeAcknowledgmentQuestion,
            }),
        getContent: () =>
            getPolicyNotifyPersonnelModalContent({
                modalStep: controller.modalStep,
                personnelCount: controller.personnelCount,
                currentShouldNotifyEmployeesChoice:
                    controller.currentShouldNotifyEmployeesChoice,
                currentAcknowledgmentChoice:
                    controller.currentAcknowledgmentChoice,
                includeAcknowledgmentQuestion:
                    controller.includeAcknowledgmentQuestion,
                formRef,
                isLoading: controller.isLoading,
                processNotificationChoice: controller.processNotificationChoice,
            }),
        getActions: () =>
            getPolicyNotifyPersonnelModalActions({
                modalStep: controller.modalStep,
                isLoadingPersonnelCount: controller.isLoadingPersonnelCount,
                isLoading: controller.isLoading,
                returnToQuestionModal: controller.returnToQuestionModal,
                confirmAutomatedNotification:
                    controller.confirmAutomatedNotification,
                cancelNotificationProcess: controller.cancelNotificationProcess,
                triggerSubmit,
            }),
        handleClose: controller.handleClose,
    };
};
