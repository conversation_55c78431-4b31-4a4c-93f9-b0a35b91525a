import { t } from '@globals/i18n/macro';
import { runInAction } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import type { UseFormSubmitReturn } from '@ui/forms';
import { PolicyFinalizeQuestionsContent } from '../components/policy-finalize-questions-content.component';
import { PolicyFinalizeModalController } from '../controllers/policy-finalize-modal.controller';
import { sharedPolicyHeaderDraftActions } from '../policy-header-actions.controller';
import type { PolicyBuilderModalConfig } from '../types/policy-builder-modal-config.type';
import { createPolicyNotifyPersonnelModal } from './create-policy-notify-personnel-modal.helper';
import { createPolicyPublishConfirmationModal } from './create-policy-publish-confirmation-modal.helper';
import { closePolicyFinalizeModal } from './open-policy-finalize-modal.helper';

interface CreatePolicyFinalizeModalParams {
    formRef: UseFormSubmitReturn['formRef'];
    triggerSubmit: UseFormSubmitReturn['triggerSubmit'];
}

const isFinalizingDraft = (): boolean => {
    return sharedPolicyHeaderDraftActions.isFinalizingDraft;
};

export const createPolicyFinalizeModal = ({
    formRef,
    triggerSubmit,
}: CreatePolicyFinalizeModalParams): PolicyBuilderModalConfig => {
    const controller = new PolicyFinalizeModalController({
        isLoading: isFinalizingDraft,
        onFinalize: sharedPolicyHeaderDraftActions.handleFinalizeDraftFromModal,
    });

    const policyNotifyPersonnelModal = createPolicyNotifyPersonnelModal({
        isLoading: isFinalizingDraft,
        formRef,
        triggerSubmit,
        onConfirm: ({ shouldNotifyEmployees }) => {
            controller.confirmFinalization(shouldNotifyEmployees);
        },
        onCancel: controller.goBackToQuestions,
    });

    const policyPublishConfirmationModal = createPolicyPublishConfirmationModal(
        {
            isLoading: isFinalizingDraft,
            onConfirm: () => {
                runInAction(() => {
                    controller.confirmFinalization(false);
                });
            },
            onCancel: controller.goBackToQuestions,
        },
    );

    const handleClose = (): void => {
        if (isFinalizingDraft()) {
            return;
        }

        switch (controller.currentStep) {
            case 'notification': {
                policyNotifyPersonnelModal.handleClose();
                break;
            }
            case 'publish': {
                policyPublishConfirmationModal.handleClose();
                break;
            }
            case 'questions':
            default: {
                closePolicyFinalizeModal();
                break;
            }
        }
    };

    return {
        getTitle: () => {
            switch (controller.currentStep) {
                case 'notification': {
                    return policyNotifyPersonnelModal.getTitle();
                }
                case 'publish': {
                    return policyPublishConfirmationModal.getTitle();
                }
                case 'questions':
                default: {
                    return t`Finalize Draft`;
                }
            }
        },
        getContent: () => {
            switch (controller.currentStep) {
                case 'notification': {
                    return policyNotifyPersonnelModal.getContent();
                }
                case 'publish': {
                    return policyPublishConfirmationModal.getContent();
                }
                case 'questions':
                default: {
                    return (
                        <PolicyFinalizeQuestionsContent
                            data-id="policy-finalize-questions"
                            formRef={formRef}
                            rawFormValues={controller.rawFormValues}
                            isLoading={isFinalizingDraft}
                            versions={{
                                nextMajorVersion:
                                    sharedPolicyBuilderModel.nextMajorVersion,
                                nextMinorVersion:
                                    sharedPolicyBuilderModel.nextMinorVersion,
                                currentMajorVersion:
                                    sharedPolicyBuilderModel.currentMajorVersion,
                            }}
                            onSubmit={controller.submitFinalizeQuestions}
                        />
                    );
                }
            }
        },
        getActions: () => {
            switch (controller.currentStep) {
                case 'notification': {
                    return policyNotifyPersonnelModal.getActions();
                }
                case 'publish': {
                    return policyPublishConfirmationModal.getActions();
                }
                case 'questions':
                default: {
                    return [
                        {
                            label: t`Cancel`,
                            level: 'tertiary',
                            onClick: closePolicyFinalizeModal,
                            cosmosUseWithCaution_isDisabled:
                                isFinalizingDraft(),
                        },
                        {
                            label: t`Finalize`,
                            level: 'primary',
                            onClick: () => {
                                triggerSubmit();
                            },
                            isLoading: isFinalizingDraft(),
                            a11yLoadingLabel: t`Finalizing draft`,
                        },
                    ];
                }
            }
        },
        handleClose,
    };
};
