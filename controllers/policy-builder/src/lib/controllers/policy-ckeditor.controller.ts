import { policyVersionControllerUpdatePolicyHtmlMutation } from '@globals/api-sdk/queries';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import { openPolicyCloseEditModal } from '../helpers/policy-close-edit-modal.helper';
import { sharedPolicyBuilderController } from '../policy-builder.controller';

class PolicyCkEditorController {
    isCkEditorInEditMode = false;
    editingHtmlContent = '';
    lastSavedContent = '';
    isAutoSaving = false;

    constructor() {
        makeAutoObservable(this);
    }

    updateHtmlMutation = new ObservedMutation(
        policyVersionControllerUpdatePolicyHtmlMutation,
        {
            onSuccess: () => {
                this.lastSavedContent = this.editingHtmlContent;
                this.isAutoSaving = false;
            },
            onError: () => {
                this.isAutoSaving = false;
            },
        },
    );

    private setIsCkEditorInEditMode(value: boolean): void {
        this.isCkEditorInEditMode = value;

        if (value) {
            const currentHtml = sharedPolicyBuilderModel.currentVersionHtml;
            const { isAuthoredPolicy } = sharedPolicyBuilderModel;

            if (currentHtml) {
                this.editingHtmlContent = currentHtml;
                this.lastSavedContent = currentHtml;
            } else if (isAuthoredPolicy) {
                this.editingHtmlContent = '';
                this.lastSavedContent = '';
            } else {
                this.isCkEditorInEditMode = false;
                this.editingHtmlContent = '';
                this.lastSavedContent = '';
            }
        } else {
            this.editingHtmlContent = '';
            this.lastSavedContent = '';
        }
    }

    private autoSave(): void {
        const { isDraft, policyId } = sharedPolicyBuilderModel;

        if (!policyId || !this.editingHtmlContent) {
            return;
        }

        if (!isDraft) {
            return;
        }

        this.isAutoSaving = true;
        this.updateHtmlMutation.mutate({
            path: { id: policyId },
            body: {
                html: this.editingHtmlContent,
            },
        });
    }

    get htmlContent(): string {
        if (this.isCkEditorInEditMode) {
            return this.editingHtmlContent;
        }

        return sharedPolicyBuilderModel.currentVersionHtml;
    }

    get hasUnsavedChanges(): boolean {
        return this.editingHtmlContent !== this.lastSavedContent;
    }

    get canEnterEditMode(): boolean {
        const { isAuthoredPolicy, currentVersionHtml } =
            sharedPolicyBuilderModel;
        const hasContentOrIsAuthored =
            isAuthoredPolicy || Boolean(currentVersionHtml);

        return (
            hasContentOrIsAuthored &&
            !sharedPolicyBuilderController.isPolicyLoading &&
            !sharedPolicyBuilderController.isVersionLoading
        );
    }

    setHtmlContent(content: string): void {
        this.editingHtmlContent = content;

        if (
            this.isCkEditorInEditMode &&
            sharedPolicyBuilderModel.isDraft &&
            content !== this.lastSavedContent
        ) {
            this.autoSave();
        }
    }

    enterEditMode(): void {
        if (this.canEnterEditMode) {
            this.setIsCkEditorInEditMode(true);
        }
    }

    exitEditMode(): void {
        this.setIsCkEditorInEditMode(false);
    }

    cancelEdit(): void {
        this.editingHtmlContent = '';
        this.lastSavedContent = '';
        this.setIsCkEditorInEditMode(false);
    }

    handleNavigationAttempt(callback: () => void): void {
        if (this.isCkEditorInEditMode && this.hasUnsavedChanges) {
            openPolicyCloseEditModal(() => {
                this.exitEditMode();
                callback();
            });
        } else {
            callback();
        }
    }
}

export const sharedPolicyCkEditorController = new PolicyCkEditorController();
