import { policyVersionControllerGetPersonnelCountForPolicyScopeOptions } from '@globals/api-sdk/queries';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import type { FormValues } from '@ui/forms';

export type PolicyNotifyPersonnelModalStep = 'question' | 'confirmation';

export interface PolicyNotifyPersonnelModalControllerParams {
    includeAcknowledgmentQuestion?: boolean;
    isLoading: () => boolean;
    onConfirm: (data: {
        shouldNotifyEmployees: boolean;
        requiresAcknowledgment: boolean;
    }) => void;
    onCancel: () => void;
}

export class PolicyNotifyPersonnelModalController {
    modalStep: PolicyNotifyPersonnelModalStep = 'question';
    shouldUseDrataNotification = false;
    requiresAcknowledgment = false;
    includeAcknowledgmentQuestion: boolean;
    isLoading: () => boolean;
    onConfirm: (data: {
        shouldNotifyEmployees: boolean;
        requiresAcknowledgment: boolean;
    }) => void;
    onCancel: () => void;

    constructor({
        isLoading,
        onConfirm,
        onCancel,
        includeAcknowledgmentQuestion = false,
    }: PolicyNotifyPersonnelModalControllerParams) {
        this.includeAcknowledgmentQuestion = includeAcknowledgmentQuestion;
        this.isLoading = isLoading;
        this.onConfirm = onConfirm;
        this.onCancel = onCancel;

        makeAutoObservable(this);
    }

    personnelCountQuery = new ObservedQuery(
        policyVersionControllerGetPersonnelCountForPolicyScopeOptions,
    );

    get personnelCount(): number {
        return this.personnelCountQuery.data ?? 0;
    }

    get isLoadingPersonnelCount(): boolean {
        return this.personnelCountQuery.isLoading;
    }

    get currentShouldNotifyEmployeesChoice(): string {
        if (this.shouldUseDrataNotification) {
            return 'true';
        }

        return '';
    }

    get currentAcknowledgmentChoice(): string {
        if (this.includeAcknowledgmentQuestion) {
            return this.requiresAcknowledgment ? 'true' : '';
        }

        return '';
    }

    processNotificationChoice = (formValues: FormValues): void => {
        this.updateFormValues(formValues);

        if (this.shouldSkipConfirmationModal()) {
            this.confirmAndClose();

            return;
        }

        if (this.hasPersonnelCountData()) {
            this.modalStep = 'confirmation';

            return;
        }

        this.loadPersonnelCountForNotification();
    };

    private updateFormValues(formValues: FormValues): void {
        if (this.includeAcknowledgmentQuestion) {
            this.requiresAcknowledgment =
                formValues.requiresAcknowledgment === 'true';
        }

        this.shouldUseDrataNotification =
            formValues.shouldNotifyEmployees === 'true';
    }

    private shouldSkipConfirmationModal(): boolean {
        if (!this.shouldUseDrataNotification) {
            return true;
        }

        return (
            this.includeAcknowledgmentQuestion && !this.requiresAcknowledgment
        );
    }
    private hasPersonnelCountData(): boolean {
        return this.personnelCountQuery.data !== null;
    }

    private confirmAndClose(): void {
        this.onConfirm({
            requiresAcknowledgment: this.requiresAcknowledgment,
            shouldNotifyEmployees: this.shouldUseDrataNotification,
        });
    }

    loadPersonnelCountForNotification(): void {
        const { policyId } = sharedPolicyBuilderModel;

        if (!policyId) {
            return;
        }

        this.personnelCountQuery.load({
            path: { policyId },
        });

        when(
            () =>
                !this.personnelCountQuery.isLoading &&
                this.personnelCountQuery.data !== null,
            () => {
                this.modalStep = 'confirmation';
            },
        );
    }

    confirmAutomatedNotification = (): void => {
        this.onConfirm({
            shouldNotifyEmployees: this.shouldUseDrataNotification,
            requiresAcknowledgment: this.requiresAcknowledgment,
        });
    };

    returnToQuestionModal = (): void => {
        this.modalStep = 'question';
    };

    cancelNotificationProcess = (): void => {
        this.onCancel();
        this.resetNotificationState();
    };

    resetNotificationState = (): void => {
        this.modalStep = 'question';
        this.shouldUseDrataNotification = false;
    };

    handleClose = (): boolean => {
        if (this.isLoading() || this.isLoadingPersonnelCount) {
            return false;
        }

        if (this.modalStep === 'confirmation') {
            this.returnToQuestionModal();

            return false;
        }

        this.cancelNotificationProcess();

        return true;
    };
}
