import { isString } from 'lodash-es';
import { makeAutoObservable } from '@globals/mobx';
import type { FormValues } from '@ui/forms';
import type { PolicyFinalizeData } from '../types/policy-finalize.types';

export type PolicyFinalizeModalStep = 'questions' | 'notification' | 'publish';

export interface PolicyFinalizeModalFormValues {
    isMaterialChange: string;
    requiresApproval: string;
    requiresAcknowledgment: string;
    changesExplanation: string;
}

interface PolicyFinalizeModalControllerParams {
    isLoading: () => boolean;
    onFinalize: (data: PolicyFinalizeData) => void;
}

export class PolicyFinalizeModalController {
    currentStep: PolicyFinalizeModalStep = 'questions';
    formData: PolicyFinalizeData = {
        isMaterialChange: true,
        requiresApproval: true,
        requiresAcknowledgment: null,
        changesExplanation: '',
        shouldNotifyEmployees: false,
    };
    rawFormValues: PolicyFinalizeModalFormValues = {
        isMaterialChange: '',
        requiresApproval: '',
        requiresAcknowledgment: '',
        changesExplanation: '',
    };

    isLoading: () => boolean;
    onFinalize: (data: PolicyFinalizeData) => void;

    constructor({
        isLoading,
        onFinalize,
    }: PolicyFinalizeModalControllerParams) {
        this.isLoading = isLoading;
        this.onFinalize = onFinalize;

        makeAutoObservable(this);
    }

    get needsApproval(): boolean {
        return (
            this.formData.isMaterialChange ||
            this.formData.requiresApproval ||
            false
        );
    }

    submitQuestions = (values: FormValues): void => {
        this.extractRawFormValues(values);

        // Process form values with business logic
        const processedValues = this.processFormValuesWithBusinessLogic();

        // Update form data with processed values
        this.formData = {
            ...processedValues,
            changesExplanation: this.rawFormValues.changesExplanation,
            shouldNotifyEmployees: false,
        };

        this.determineNextStep();
    };

    extractRawFormValues(values: FormValues): void {
        this.rawFormValues = {
            isMaterialChange: this.getStringValue(values, 'isMaterialChange'),
            requiresApproval: this.getStringValue(values, 'requiresApproval'),
            requiresAcknowledgment: this.getStringValue(
                values,
                'requiresAcknowledgment',
            ),
            changesExplanation: this.getStringValue(
                values,
                'changesExplanation',
            ),
        };
    }

    getStringValue(values: FormValues, key: string): string {
        return key in values && isString(values[key]) ? values[key] : '';
    }

    processFormValuesWithBusinessLogic(): Pick<
        PolicyFinalizeData,
        'isMaterialChange' | 'requiresApproval' | 'requiresAcknowledgment'
    > {
        const isMaterialChange = this.rawFormValues.isMaterialChange === 'true';
        const hasApprovalValue = Boolean(this.rawFormValues.requiresApproval);
        const hasAcknowledgmentValue = Boolean(
            this.rawFormValues.requiresAcknowledgment,
        );

        let requiresApproval = this.rawFormValues.requiresApproval === 'true';
        let requiresAcknowledgment: boolean | null =
            this.rawFormValues.requiresAcknowledgment === 'true';

        // Business rule 1: Material changes with no other selections default to requiring both
        if (
            this.shouldDefaultToRequireBoth(
                isMaterialChange,
                hasApprovalValue,
                hasAcknowledgmentValue,
            )
        ) {
            requiresApproval = true;
            requiresAcknowledgment = true;
        }

        // Business rule 2: Non-material changes that require approval don't need acknowledgment
        if (
            this.shouldNullifyAcknowledgment(isMaterialChange, requiresApproval)
        ) {
            requiresAcknowledgment = null;
        }

        return {
            isMaterialChange,
            requiresApproval,
            requiresAcknowledgment,
        };
    }

    shouldDefaultToRequireBoth(
        isMaterialChange: boolean,
        hasApprovalValue: boolean,
        hasAcknowledgmentValue: boolean,
    ): boolean {
        return isMaterialChange && !hasApprovalValue && !hasAcknowledgmentValue;
    }

    shouldNullifyAcknowledgment(
        isMaterialChange: boolean,
        requiresApproval: boolean,
    ): boolean {
        return !isMaterialChange && requiresApproval;
    }

    determineNextStep = (): void => {
        const { isMaterialChange, requiresApproval, requiresAcknowledgment } =
            this.formData;

        if (!isMaterialChange && !requiresApproval && requiresAcknowledgment) {
            this.currentStep = 'notification';
        } else if (
            !isMaterialChange &&
            !requiresApproval &&
            !requiresAcknowledgment
        ) {
            this.currentStep = 'publish';
        }
    };

    goBackToQuestions = (): void => {
        this.currentStep = 'questions';

        this.formData.shouldNotifyEmployees = false;
    };

    submitFinalizeQuestions = (values: FormValues): void => {
        if (this.isLoading()) {
            return;
        }

        this.submitQuestions(values);

        if (this.needsApproval) {
            this.onFinalize(this.formData);
        }
    };

    confirmFinalization = (shouldNotifyEmployees: boolean): void => {
        this.formData.shouldNotifyEmployees = shouldNotifyEmployees;

        this.onFinalize(this.formData);
    };
}
