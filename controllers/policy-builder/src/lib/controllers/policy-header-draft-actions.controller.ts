import { isEmpty } from 'lodash-es';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import type { Action } from '@cosmos/components/action-stack';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import {
    policiesControllerDeletePolicyVersionMutation,
    policiesControllerResetPolicyMutation,
    policyVersionControllerPutPolicyVersionVersionAndStatusMutation,
    policyVersionControllerUpdatePolicyHtmlMutation,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    runInAction,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import {
    closePolicyFinalizeModal,
    openPolicyFinalizeModal,
} from '../helpers/open-policy-finalize-modal.helper';
import { sharedPolicyBuilderController } from '../policy-builder.controller';
import type { PolicyFinalizeData } from '../types/policy-finalize.types';
import { sharedPolicyCkEditorController } from './policy-ckeditor.controller';
import { sharedPolicyHeaderSharedActionsController } from './policy-header-shared-actions.controller';

export class PolicyHeaderDraftActions {
    deleteDraftMutation = new ObservedMutation(
        policiesControllerDeletePolicyVersionMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'delete-draft-success',
                    hasTimeout: true,
                    props: {
                        title: t`Draft deleted successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                closeConfirmationModal();
                this.handlePostDeleteNavigation();
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to delete draft',
                    additionalInfo: {
                        versionId:
                            sharedPolicyBuilderController.currentVersionId,
                        action: 'deleteDraft',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'delete-draft-error',
                    props: {
                        title: t`Failed to delete draft`,
                        description: t`We couldn't delete the draft. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    resetPolicyMutation = new ObservedMutation(
        policiesControllerResetPolicyMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: 'restart-policy-success',
                    hasTimeout: true,
                    props: {
                        title: t`Policy restarted successfully`,
                        description: t`The policy has been reset to the latest template`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                closeConfirmationModal();
                sharedPolicyBuilderController.invalidatePolicyQueries();

                // Update CKEditor content after resetting to template
                // Wait for queries to reload and then update editor
                when(
                    () =>
                        !sharedPolicyBuilderController.isPolicyLoading &&
                        !sharedPolicyBuilderController.isVersionLoading &&
                        sharedPolicyBuilderModel.currentVersionHtml !== '',
                    () => {
                        const newHtmlContent =
                            sharedPolicyBuilderModel.currentVersionHtml;

                        if (
                            sharedPolicyBuilderModel.isPolicyCkEditorInEditMode &&
                            newHtmlContent
                        ) {
                            // Update the editor content with the new template HTML
                            sharedPolicyCkEditorController.setHtmlContent(
                                newHtmlContent,
                            );
                        } else if (
                            sharedPolicyBuilderModel.isPolicyCkEditorInEditMode
                        ) {
                            // If no content, exit edit mode
                            sharedPolicyCkEditorController.exitEditMode();
                        }
                    },
                );
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to reset policy to template',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        action: 'resetPolicy',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'reset-policy-error',
                    props: {
                        title: t`Failed to restart policy`,
                        description: t`We couldn't restart the policy with the template. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    updateHtmlMutation = new ObservedMutation(
        policyVersionControllerUpdatePolicyHtmlMutation,
        {
            onSuccess: () => {
                // HTML updated successfully, now proceed with finalization
                if (this.pendingFinalizeData) {
                    this.proceedWithFinalization();
                }
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to update policy HTML content',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        action: 'updateHtml',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'policy-html-update-error',
                    props: {
                        title: t`Failed to save policy content`,
                        description: t`We couldn't save your changes. Please try again in a few moments.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                this.pendingFinalizeData = null;
            },
        },
    );

    finalizeDraftMutation = new ObservedMutation(
        policyVersionControllerPutPolicyVersionVersionAndStatusMutation,
        {
            onSuccess: () => {
                // First close the modal
                closePolicyFinalizeModal();

                // Exit edit mode if in CKEditor
                sharedPolicyCkEditorController.exitEditMode();

                // Determine the status based on what was selected
                const needsApproval =
                    this.pendingFinalizeData?.isMaterialChange ||
                    this.pendingFinalizeData?.requiresApproval;

                if (needsApproval) {
                    // Policy needs approval - stay on same page and show approval message
                    snackbarController.addSnackbar({
                        id: 'finalize-draft-approval',
                        hasTimeout: true,
                        props: {
                            title: t`Draft sent for approval`,
                            description: t`The policy has been sent for approval and will be published once approved`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    // Reload the policy data to show the new status
                    sharedPolicyBuilderController.invalidatePolicyQueries();
                } else {
                    // Policy is published directly - show publish message and redirect
                    snackbarController.addSnackbar({
                        id: 'finalize-draft-published',
                        hasTimeout: true,
                        props: {
                            title: t`Policy published successfully`,
                            description: this.pendingFinalizeData
                                ?.requiresAcknowledgment
                                ? t`The policy has been published and personnel will be notified`
                                : t`The policy has been published`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    // Navigate to active policies list only when published
                    this.navigateToPoliciesList();
                }

                // Clear pending data
                this.pendingFinalizeData = null;
            },
            onError: (error) => {
                logger.error({
                    message: 'Failed to finalize draft',
                    additionalInfo: {
                        policyId: sharedPolicyBuilderModel.policyId,
                        versionId: sharedPolicyBuilderModel.currentVersionId,
                        action: 'finalizeDraft',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: error.statusCode,
                    },
                });

                snackbarController.addSnackbar({
                    id: 'finalize-draft-error',
                    props: {
                        title: t`Failed to finalize draft`,
                        description: t`We couldn't complete the finalization. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                this.pendingFinalizeData = null;
            },
        },
    );

    pendingFinalizeData: PolicyFinalizeData | null = null;

    /**
     * Modal state for chaining.
     */
    modalState: {
        currentModal: 'finalize' | 'confirmation' | 'notify' | 'warning' | null;
        formValues: {
            isMaterialChange: boolean;
            requiresApproval: boolean;
            requiresAcknowledgment: boolean;
            changesExplanation: string;
        } | null;
    } = {
        currentModal: null,
        formValues: null,
    };

    constructor() {
        makeAutoObservable(this);
    }

    private get shouldShowAuthorPolicy(): boolean {
        return sharedPolicyBuilderModel.isUploadedPolicy;
    }

    private get shouldShowUploadFile(): boolean {
        return (
            sharedPolicyBuilderModel.isAuthoredPolicy ||
            sharedPolicyBuilderModel.isUploadedPolicy
        );
    }

    private get shouldShowPdfPreview(): boolean {
        return sharedPolicyBuilderModel.isAuthoredPolicy;
    }

    get isDeletingDraft(): boolean {
        return this.deleteDraftMutation.isPending;
    }

    get isResettingPolicy(): boolean {
        return this.resetPolicyMutation.isPending;
    }

    get isFinalizingDraft(): boolean {
        return this.finalizeDraftMutation.isPending;
    }

    getActions(): Action[] {
        const actions: Action[] = [];

        const dropdownActions = this.getDropdownActions();

        if (!isEmpty(dropdownActions)) {
            actions.push({
                id: 'draft-dropdown-actions',
                actionType: 'dropdown',
                typeProps: {
                    label: '',
                    level: 'tertiary',
                    colorScheme: 'primary',
                    startIconName: 'HorizontalMenu',
                    items: dropdownActions,
                },
            });
        }

        actions.push(this.finalizeDraftAction);

        return actions;
    }

    private get finalizeDraftAction(): Action {
        return {
            id: 'finalize-draft-button',
            actionType: 'button',
            typeProps: {
                label: t`Finalize draft`,
                level: 'primary',
                colorScheme: 'primary',
                onClick: this.handleFinalizeDraft,
                isLoading: this.isFinalizingDraft,
            },
        };
    }

    private getDropdownActions(): SchemaDropdownItemData[] {
        const actions: SchemaDropdownItemData[] = [];

        if (this.shouldShowPdfPreview) {
            actions.push(
                sharedPolicyHeaderSharedActionsController.pdfPreviewAction,
            );
        }

        if (this.shouldShowUploadFile) {
            actions.push(
                sharedPolicyHeaderSharedActionsController.uploadFileAction,
            );
        }

        if (this.shouldShowAuthorPolicy) {
            actions.push(
                sharedPolicyHeaderSharedActionsController.authorPolicyAction,
            );
        }

        if (this.shouldShowRestartWithTemplate) {
            actions.push({
                id: 'restart-with-template-action',
                type: 'item',
                label: t`Restart with template`,
                value: 'restart-with-template',
                onSelect: this.handleRestartWithTemplate,
                disabled: this.isResettingPolicy,
            });
        }

        if (this.shouldShowDeleteDraft) {
            actions.push({
                id: 'delete-draft-action',
                type: 'item',
                label: t`Delete draft`,
                value: 'delete-draft',
                onSelect: this.handleDeleteDraft,
                disabled: this.isDeletingDraft,
            });
        }

        return actions;
    }

    private get shouldShowRestartWithTemplate(): boolean {
        return sharedPolicyBuilderModel.hasTemplate;
    }

    private get shouldShowDeleteDraft(): boolean {
        if (sharedPolicyBuilderModel.isNewDrataTemplatePolicy) {
            return false;
        }

        return (
            sharedPolicyBuilderModel.isAuthoredPolicy ||
            sharedPolicyBuilderModel.isUploadedPolicy ||
            sharedPolicyBuilderModel.hasNotionOrConfluenceConnection
        );
    }

    get canFinalize(): boolean {
        const { isAuthoredPolicy, hasHtmlContent } = sharedPolicyBuilderModel;

        if (isAuthoredPolicy) {
            return hasHtmlContent;
        }

        return true;
    }

    handleFinalizeDraft = (): void => {
        runInAction(() => {
            if (!this.canFinalize) {
                snackbarController.addSnackbar({
                    id: 'finalize-draft-validation-error',
                    props: {
                        title: t`Unable to finalize draft`,
                        description: t`Please add content to the policy before finalizing.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                return;
            }

            const { hasExpiredRenewalDate, needsApprovalConfiguration } =
                sharedPolicyBuilderModel;

            if (hasExpiredRenewalDate) {
                snackbarController.addSnackbar({
                    id: 'finalize-draft-renewal-date-error',
                    props: {
                        title: t`Unable to finalize draft`,
                        description: t`Finalization cannot proceed because the renewal date is missing or out of date. Please update the renewal date and try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                return;
            }

            if (needsApprovalConfiguration) {
                snackbarController.addSnackbar({
                    id: 'finalize-draft-approval-config-error',
                    props: {
                        title: t`Unable to finalize draft`,
                        description: t`Finalization cannot proceed because approval settings are missing. Please configure approval settings and try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                return;
            }

            // Open modal through helper
            openPolicyFinalizeModal();
        });
    };

    handleFinalizeDraftFromModal = (data: PolicyFinalizeData): void => {
        const { policyId, currentVersionId, isAuthoredPolicy } =
            sharedPolicyBuilderModel;

        if (!policyId || !currentVersionId) {
            logger.error({
                message: 'Missing IDs for finalize draft',
                additionalInfo: {
                    policyId,
                    currentVersionId,
                    action: 'finalizeDraft',
                },
            });

            snackbarController.addSnackbar({
                id: 'finalize-draft-no-ids',
                props: {
                    title: t`Unable to finalize draft`,
                    description: t`Policy information is missing. Please refresh and try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        // Validate content for authored policies - cannot be empty
        const { htmlContent, isPolicyCkEditorInEditMode } =
            sharedPolicyBuilderModel;

        if (isAuthoredPolicy && !htmlContent) {
            logger.error({
                message: 'Empty content for authored policy finalization',
                additionalInfo: {
                    policyId,
                    currentVersionId,
                    isAuthoredPolicy,
                    action: 'finalizeDraft',
                },
            });

            snackbarController.addSnackbar({
                id: 'finalize-draft-empty-content',
                props: {
                    title: t`Unable to finalize draft`,
                    description: t`Authored policy content cannot be empty. Please add content before finalizing.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            closePolicyFinalizeModal();

            return;
        }

        // Store the finalize data for later use
        this.pendingFinalizeData = data;

        // For authored policies, first save the HTML content if in edit mode
        if (isAuthoredPolicy && isPolicyCkEditorInEditMode && htmlContent) {
            this.updateHtmlMutation.mutate({
                path: { id: policyId },
                body: {
                    html: htmlContent,
                },
            });
        } else {
            // For uploaded policies or when not in edit mode, proceed directly with finalization
            this.proceedWithFinalization();
        }
    };

    handleRestartWithTemplate = (): void => {
        openConfirmationModal({
            title: t`Restart from template?`,
            body: t`Restarting from the latest Drata template will override your current policy and restore the Drata policy template. The description, policy owner, renewal date, and SLA (if applicable) will not be changed. Are you sure you'd like to continue?`,
            confirmText: t`Continue`,
            cancelText: t`Cancel`,
            type: 'primary',
            size: 'md',
            disableClickOutsideToClose: true,
            onConfirm: this.confirmRestartWithTemplate,
            onCancel: closeConfirmationModal,
            isLoading: () => this.isResettingPolicy,
        });
    };

    handleDeleteDraft = (): void => {
        openConfirmationModal({
            title: t`Delete draft`,
            body: t`Are you sure you want to delete this draft? This action cannot be undone.`,
            confirmText: t`Delete`,
            cancelText: t`Cancel`,
            type: 'danger',
            size: 'md',
            disableClickOutsideToClose: true,
            onConfirm: this.confirmDeleteDraft,
            onCancel: closeConfirmationModal,
            isLoading: () => this.isDeletingDraft,
        });
    };

    confirmDeleteDraft = (): void => {
        const { currentVersionId } = sharedPolicyBuilderController;

        if (!currentVersionId) {
            return;
        }

        this.deleteDraftMutation.mutate({
            path: { id: currentVersionId },
        });
    };

    handlePostDeleteNavigation = (): void => {
        const { hasPublishedVersion, hasDraftVersion } =
            sharedPolicyBuilderModel;

        if (!hasPublishedVersion && !hasDraftVersion) {
            this.navigateToPoliciesList();
        } else {
            this.loadPublishedVersion();
        }
    };

    navigateToPoliciesList = (): void => {
        const workspaceId = sharedWorkspacesController.currentWorkspaceId;

        sharedProgrammaticNavigationController.navigateTo(
            `/workspaces/${workspaceId}/governance/policies/active`,
        );
    };

    loadPublishedVersion = (): void => {
        const { publishedVersionId, policyId } = sharedPolicyBuilderModel;

        if (publishedVersionId) {
            sharedPolicyBuilderController.loadPolicyWithAllData(
                policyId,
                publishedVersionId,
            );
        }
    };

    confirmRestartWithTemplate = (): void => {
        const { policyId } = sharedPolicyBuilderModel;

        this.resetPolicyMutation.mutate({
            path: { id: policyId },
        });
    };

    proceedWithFinalization = (): void => {
        const { policyId, currentVersionId } = sharedPolicyBuilderModel;

        if (!policyId || !currentVersionId || !this.pendingFinalizeData) {
            return;
        }

        // Now finalize the draft with the form data
        this.finalizeDraftMutation.mutate({
            path: {
                policyId,
                policyVersionId: currentVersionId,
            },
            body: {
                isMaterialChange: this.pendingFinalizeData.isMaterialChange,
                requiresApproval: this.pendingFinalizeData.requiresApproval,
                ...(this.pendingFinalizeData.requiresAcknowledgment !==
                    null && {
                    requiresAcknowledgment:
                        this.pendingFinalizeData.requiresAcknowledgment,
                }),
                explanationOfChanges:
                    this.pendingFinalizeData.changesExplanation,
                shouldNotifyEmployees:
                    this.pendingFinalizeData.shouldNotifyEmployees,
            },
        });
    };
}
