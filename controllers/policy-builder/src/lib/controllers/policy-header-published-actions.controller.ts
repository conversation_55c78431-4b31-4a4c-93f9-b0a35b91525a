import { isEmpty } from 'lodash-es';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { snackbarController } from '@controllers/snackbar';
import type { Action } from '@cosmos/components/action-stack';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import {
    policyVersionControllerPostPolicyVersionMutation,
    policyVersionControllerRenewalCurrentPublishedVersionMutation,
} from '@globals/api-sdk/queries';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import {
    closePolicyRenewalDateModal,
    openPolicyRenewalDateModal,
} from '../helpers/policy-renewal-date-modal.helper';
import { sharedPolicyHeaderSharedActionsController } from './policy-header-shared-actions.controller';

export class PolicyHeaderPublishedActions {
    createNewVersionMutation = new ObservedMutation(
        policyVersionControllerPostPolicyVersionMutation,
        {
            onSuccess: (data) => {
                const newVersionId = data.id;

                closeConfirmationModal();

                if (newVersionId && sharedPolicyBuilderModel.policyId) {
                    sharedProgrammaticNavigationController.navigateTo(
                        `/workspaces/${sharedWorkspacesController.currentWorkspaceId}/governance/policies/${sharedPolicyBuilderModel.policyId}/builder/${newVersionId}`,
                    );
                }
            },
            onError: (error) => {
                snackbarController.addSnackbar({
                    id: 'create-version-error',
                    props: {
                        title: t`Failed to create new version`,
                        description:
                            error.message ||
                            t`An error occurred while creating the version`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    renewalDateMutation = new ObservedMutation(
        policyVersionControllerRenewalCurrentPublishedVersionMutation,
        {
            onSuccess: () => {
                sharedPolicyBuilderController.invalidatePolicyQueries();
                closePolicyRenewalDateModal();

                snackbarController.addSnackbar({
                    id: 'renewal-date-success',
                    props: {
                        title: t`Renewal date updated`,
                        description: t`The policy renewal date has been updated successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: (error) => {
                snackbarController.addSnackbar({
                    id: 'renewal-date-error',
                    props: {
                        title: t`Failed to update renewal date`,
                        description:
                            error.message ||
                            t`An error occurred while updating the renewal date. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    constructor() {
        makeAutoObservable(this);
    }

    getActions(): Action[] {
        const actions: Action[] = [];

        if (this.shouldShowRenewWithoutUpdatesButton) {
            actions.push(this.renewWithoutUpdatesAction);
        }

        if (sharedPolicyBuilderModel.showPublishButtonActions) {
            actions.push(this.goToDraftOrEditPolicyAction);
        }

        const dropdownActions = this.getDropdownActions();

        if (!isEmpty(dropdownActions)) {
            actions.push({
                id: 'published-dropdown-actions',
                actionType: 'dropdown',
                typeProps: {
                    label: '',
                    level: 'tertiary',
                    colorScheme: 'primary',
                    startIconName: 'HorizontalMenu',
                    isIconOnly: true,
                    items: dropdownActions,
                },
            });
        }

        return actions;
    }

    get renewWithoutUpdatesAction(): Action {
        return {
            id: 'renew-without-updates-button',
            actionType: 'button',
            typeProps: {
                label: t`Renew without updates`,
                level: 'secondary',
                colorScheme: 'primary',
                onClick: this.handleRenewWithoutUpdates,
                isLoading: this.renewalDateMutation.isPending,
            },
        };
    }

    get goToDraftOrEditPolicyAction(): Action {
        const label =
            sharedPolicyBuilderModel.isTheLastPolicyVersionInDraftStatus
                ? t`Go to draft`
                : t`Edit policy`;

        return {
            id: 'go-to-draft-or-edit-policy-button',
            actionType: 'button',
            typeProps: {
                label,
                level: 'primary',
                colorScheme: 'primary',
                onClick: this.handleGoToDraftOrEditPolicy,
                isLoading: this.createNewVersionMutation.isPending,
            },
        };
    }

    getDropdownActions(): SchemaDropdownItemData[] {
        const actions: SchemaDropdownItemData[] = [];

        if (!sharedPolicyBuilderModel.showPublishButtonActions) {
            if (this.shouldShowUploadFile) {
                actions.push(
                    sharedPolicyHeaderSharedActionsController.uploadFileAction,
                );
            }

            if (this.shouldShowAuthorPolicy) {
                actions.push(
                    sharedPolicyHeaderSharedActionsController.authorPolicyAction,
                );
            }

            if (this.shouldShowImportFile) {
                actions.push(
                    sharedPolicyHeaderSharedActionsController.importFileAction,
                );
            }
        }

        if (this.shouldShowPdfPreview) {
            actions.push(
                sharedPolicyHeaderSharedActionsController.pdfPreviewAction,
            );
        }

        return actions;
    }

    get shouldShowRenewWithoutUpdatesButton(): boolean {
        const {
            isPolicyVersionOwner,
            hasDraftVersion,
            hasExternalConnection,
            isAuthoredPolicy,
            isUploadedPolicy,
            isNewDrataTemplatePolicy,
        } = sharedPolicyBuilderModel;

        return (
            isPolicyVersionOwner &&
            !hasDraftVersion &&
            !hasExternalConnection &&
            (isAuthoredPolicy || isUploadedPolicy || isNewDrataTemplatePolicy)
        );
    }

    get shouldShowUploadFile(): boolean {
        const { isUploadedPolicy, hasTemplate } = sharedPolicyBuilderModel;

        return isUploadedPolicy || hasTemplate;
    }

    get shouldShowAuthorPolicy(): boolean {
        const { isAuthoredPolicy, isUploadedPolicy, hasTemplate } =
            sharedPolicyBuilderModel;

        return isAuthoredPolicy || isUploadedPolicy || hasTemplate;
    }

    get shouldShowImportFile(): boolean {
        return sharedPolicyBuilderModel.isExternalPolicy;
    }

    get shouldShowPdfPreview(): boolean {
        const hasContent = !isEmpty(sharedPolicyBuilderModel.htmlContent);

        return hasContent && sharedFeatureAccessModel.isDownloadControlEnabled;
    }

    handleRenewWithoutUpdates = (): void => {
        openPolicyRenewalDateModal();
    };

    handleGoToDraftOrEditPolicy = (): void => {
        const {
            policyId,
            latestVersionId,
            isTheLastPolicyVersionInDraftStatus,
        } = sharedPolicyBuilderModel;

        if (!policyId) {
            return;
        }

        if (isTheLastPolicyVersionInDraftStatus && latestVersionId) {
            sharedProgrammaticNavigationController.navigateTo(
                `/workspaces/${sharedWorkspacesController.currentWorkspaceId}/governance/policies/${policyId}/builder/${latestVersionId}`,
            );
        } else {
            openConfirmationModal({
                title: t`Create new draft version?`,
                body: t`This will create a new draft version of this policy. You will be able to make changes and submit for approval when ready.`,
                confirmText: t`Create draft`,
                cancelText: t`Cancel`,
                type: 'primary',
                size: 'sm',
                disableClickOutsideToClose: true,
                onConfirm: this.confirmCreateNewVersion,
                onCancel: closeConfirmationModal,
                isLoading: () => this.createNewVersionMutation.isPending,
            });
        }
    };

    confirmCreateNewVersion = (): void => {
        const { policyId } = sharedPolicyBuilderModel;

        if (!policyId) {
            return;
        }

        this.createNewVersionMutation.mutate({
            path: { id: policyId },
            body: {
                policyType: 'BUILDER',
                policyVersionStatus: 'DRAFT',
            },
        });
    };

    handleRenewalDateSubmit = (renewalDate: string): void => {
        const { policyId, publishedVersionId } = sharedPolicyBuilderModel;

        if (!policyId || !publishedVersionId) {
            return;
        }

        this.renewalDateMutation.mutate({
            path: {
                policyId,
                versionId: publishedVersionId,
            },
            body: {
                renewalDate,
                requiresAcknowledgment: false,
                requiresApproval: false,
                shouldNotifyEmployees: false,
                assignedTo: 'ALL' as const,
                groupIds: [],
                notifyGroups: false,
            },
        });
    };
}
