import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';
import { sharedPolicyHeaderSharedActionsController } from '../controllers/policy-header-shared-actions.controller';
import { closePolicyUploadModal } from '../helpers/policy-upload-modal.helper';

const buildPolicyUploadFormSchema = (): FormSchema => ({
    file: {
        type: 'file',
        label: t`Select Policy File`,
        helpText: t`Upload a PDF, Word document, or text file (max 25MB)`,
        acceptedFormats: ['pdf', 'doc', 'docx', 'txt'],
        isMulti: false,
        oneFileOnly: true,
        selectButtonText: t`Choose File`,
        removeButtonText: t`Remove`,
        innerLabel: t`Or drag and drop your file here`,
    },
});

export const PolicyUploadModal = observer((): React.JSX.Element => {
    const { isUploadingFile } = sharedPolicyHeaderSharedActionsController;
    const { formRef, triggerSubmit } = useFormSubmit();

    const handleSubmit = action((values: FormValues) => {
        const file = values.file as File[];

        sharedPolicyHeaderSharedActionsController.handleFileUpload(file);
    });

    const handleUpload = () => {
        triggerSubmit().catch(console.error);
    };

    return (
        <>
            <Modal.Header
                title={t`Upload Policy File`}
                closeButtonAriaLabel={t`Close upload policy modal`}
                onClose={closePolicyUploadModal}
            />

            <Modal.Body>
                <Form
                    hasExternalSubmitButton
                    ref={formRef}
                    formId="policy-upload-form"
                    schema={buildPolicyUploadFormSchema()}
                    data-id="policy-upload-form"
                    isReadOnly={isUploadingFile}
                    onSubmit={handleSubmit}
                />
            </Modal.Body>

            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'tertiary',
                        onClick: closePolicyUploadModal,
                        cosmosUseWithCaution_isDisabled: isUploadingFile,
                    },
                    {
                        label: t`Upload Policy`,
                        level: 'primary',
                        onClick: handleUpload,
                        isLoading: isUploadingFile,
                    },
                ]}
            />
        </>
    );
});
