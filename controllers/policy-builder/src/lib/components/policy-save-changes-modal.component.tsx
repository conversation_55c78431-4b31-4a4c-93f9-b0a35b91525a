import { useMemo } from 'react';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';
import { closePolicySaveChangesModal } from '../helpers/open-policy-save-changes-modal.helper';
import { sharedPolicyHeaderApprovedActions } from '../policy-header-actions.controller';
import { PolicyExplanationField } from './policy-explanation-field.component';

export const PolicySaveChangesModal = observer((): React.JSX.Element => {
    const { isSavingChanges } = sharedPolicyHeaderApprovedActions;
    const { formRef, triggerSubmit } = useFormSubmit();

    const formSchema = useMemo(
        (): FormSchema => ({
            requiresApproval: {
                type: 'choiceCardGroup',
                label: t`Does this change need approval?`,
                choiceCardInputType: 'radio',
                cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                options: [
                    {
                        value: 'true',
                        label: t`YES, approval is needed`,
                        helpText: t`This will send the existing policy version to be approved again.`,
                    },
                    {
                        value: 'false',
                        label: t`NO, approval is not needed`,
                        helpText: t`This will merge the changes into the current approved version.`,
                    },
                ],
                initialValue: 'true',
            },
            changesExplanation: {
                type: 'custom',
                validateWithDefault: 'textarea',
                label: t`Explanation of changes`,
                placeholder: t`Any details about what has changed in the policy...`,
                rows: 2,
                initialValue:
                    sharedPolicyBuilderModel.currentExplanationOfChanges,
                render: PolicyExplanationField,
                shownIf: {
                    fieldName: 'requiresApproval',
                    operator: 'equals',
                    value: 'true',
                },
            },
        }),
        [],
    );

    const handleSubmit = action((values: FormValues) => {
        sharedPolicyHeaderApprovedActions.handleSaveChangesFromModal(values);
    });

    const handleSave = (): void => {
        triggerSubmit();
    };

    return (
        <>
            <Modal.Header
                closeButtonAriaLabel={t`Close save changes modal`}
                title={t`Approval required?`}
                onClose={closePolicySaveChangesModal}
            />

            <Modal.Body>
                <Form
                    hasExternalSubmitButton
                    data-id="policy-save-changes-form"
                    formId="policy-save-changes-form"
                    ref={formRef}
                    schema={formSchema}
                    isReadOnly={isSavingChanges}
                    onSubmit={handleSubmit}
                />
            </Modal.Body>

            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'tertiary',
                        onClick: closePolicySaveChangesModal,
                        cosmosUseWithCaution_isDisabled: isSavingChanges,
                    },
                    {
                        label: t`Confirm`,
                        level: 'primary',
                        onClick: handleSave,
                        isLoading: isSavingChanges,
                    },
                ]}
            />
        </>
    );
});
