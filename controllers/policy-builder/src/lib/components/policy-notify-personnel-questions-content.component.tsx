import { useMemo } from 'react';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    type UseFormSubmitReturn,
} from '@ui/forms';

interface PolicyNotifyPersonnelQuestionsContentProps {
    currentAcknowledgmentChoice?: string;
    currentShouldNotifyEmployeesChoice: string;
    formRef: UseFormSubmitReturn['formRef'];
    includeAcknowledgmentQuestion?: boolean;
    isLoading: () => boolean;
    onSubmit: (values: FormValues) => void;
}

export const PolicyNotifyPersonnelQuestionsContent = observer(
    ({
        currentShouldNotifyEmployeesChoice,
        formRef,
        isLoading,
        onSubmit,
        includeAcknowledgmentQuestion = false,
        currentAcknowledgmentChoice = '',
    }: PolicyNotifyPersonnelQuestionsContentProps): React.JSX.Element => {
        const formSchema = useMemo((): FormSchema => {
            const shouldNotifyEmployeesField = {
                type: 'choiceCardGroup',
                label: t`Personnel must acknowledge this policy. How would you like to let them know?`,
                choiceCardInputType: 'radio',
                cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                options: [
                    {
                        value: 'false',
                        label: t`I'll send it out myself`,
                        helpText: t`You must manually inform personnel to acknowledge the policy.`,
                    },
                    {
                        value: 'true',
                        label: t`Send now through Drata`,
                        helpText: t`Drata will notify personnel to acknowledge the policy.`,
                    },
                ],
                initialValue: currentShouldNotifyEmployeesChoice,
            } as const satisfies FormSchema['choiceCardGroup'];

            if (includeAcknowledgmentQuestion) {
                return {
                    requiresAcknowledgment: {
                        type: 'choiceCardGroup',
                        label: t`Does this version require personnel acknowledgement?`,
                        choiceCardInputType: 'radio',
                        cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                        options: [
                            {
                                value: 'true',
                                label: t`YES, personnel acknowledgement is required`,
                            },
                            {
                                value: 'false',
                                label: t`NO, personnel acknowledgement is not required`,
                            },
                        ],
                        initialValue: currentAcknowledgmentChoice,
                    },
                    shouldNotifyEmployees: {
                        ...shouldNotifyEmployeesField,
                        shownIf: {
                            fieldName: 'requiresAcknowledgment',
                            operator: 'equals',
                            value: 'true',
                        },
                    },
                };
            }

            return {
                shouldNotifyEmployees: shouldNotifyEmployeesField,
            };
        }, [
            includeAcknowledgmentQuestion,
            currentShouldNotifyEmployeesChoice,
            currentAcknowledgmentChoice,
        ]);

        return (
            <Form
                hasExternalSubmitButton
                data-id="policy-notify-personnel-form"
                formId="policy-notify-personnel-form"
                ref={formRef}
                isReadOnly={isLoading()}
                schema={formSchema}
                onSubmit={onSubmit}
            />
        );
    },
);
