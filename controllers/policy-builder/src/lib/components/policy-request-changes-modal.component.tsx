import { isString } from 'lodash-es';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';
import { closePolicyRequestChangesModal } from '../helpers/open-policy-request-changes-modal.helper';
import { sharedPolicyHeaderNeedsApprovalActions } from '../policy-header-actions.controller';

const buildFormSchema = (): FormSchema => ({
    reason: {
        type: 'textarea',
        label: t`Reason for requesting changes`,
        helpText: t`Note: Your change request will be sent to the policy owner via email and shown in the review and approval details.`,
        rows: 4,
    },
});

export const PolicyRequestChangesModal = observer(() => {
    const { formRef, triggerSubmit } = useFormSubmit();

    const handleSubmit = action((values: FormValues) => {
        const reason = isString(values.reason) ? values.reason : '';

        sharedPolicyHeaderNeedsApprovalActions.handleRequestChangesSubmit(
            reason,
        );
    });

    const handleRequestChanges = () => {
        triggerSubmit();
    };

    return (
        <>
            <Modal.Header
                title={t`Request Changes`}
                closeButtonAriaLabel={t`Close request changes modal`}
                onClose={closePolicyRequestChangesModal}
            />

            <Modal.Body>
                <Form
                    hasExternalSubmitButton
                    data-id="policy-request-changes-form"
                    ref={formRef}
                    formId="policy-request-changes-form"
                    schema={buildFormSchema()}
                    onSubmit={handleSubmit}
                />
            </Modal.Body>

            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: closePolicyRequestChangesModal,
                        cosmosUseWithCaution_isDisabled:
                            sharedPolicyHeaderNeedsApprovalActions
                                .requestChangesMutation.isPending,
                    },
                    {
                        label: t`Request Changes`,
                        level: 'primary',
                        onClick: handleRequestChanges,
                        isLoading:
                            sharedPolicyHeaderNeedsApprovalActions
                                .requestChangesMutation.isPending,
                    },
                ]}
            />
        </>
    );
});
