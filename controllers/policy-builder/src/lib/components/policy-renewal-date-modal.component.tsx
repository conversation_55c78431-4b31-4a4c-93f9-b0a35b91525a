import type React from 'react';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';
import { closePolicyRenewalDateModal } from '../helpers/policy-renewal-date-modal.helper';
import { sharedPolicyHeaderPublishedActions } from '../policy-header-actions.controller';

const buildRenewalDateFormSchema = (): FormSchema => ({
    renewalDate: {
        type: 'date',
        label: t`Renewal date`,
    },
});

export const PolicyRenewalDateModal = observer((): React.JSX.Element => {
    const { renewalDateMutation } = sharedPolicyHeaderPublishedActions;
    const { formRef, triggerSubmit } = useFormSubmit();

    const handleSubmit = action((values: FormValues) => {
        const renewalDate = values.renewalDate as string;

        if (renewalDate) {
            sharedPolicyHeaderPublishedActions.handleRenewalDateSubmit(
                renewalDate,
            );
        }
    });

    const handleSave = () => {
        triggerSubmit().catch(console.error);
    };

    return (
        <Modal
            data-id="policy-renewal-date-modal"
            data-testid="PolicyRenewalDateModal"
            onClose={closePolicyRenewalDateModal}
        >
            <Modal.Header
                title={t`Set renewal date`}
                closeButtonAriaLabel={t`Close`}
                onClose={closePolicyRenewalDateModal}
            />

            <Modal.Body>
                <Form
                    hasExternalSubmitButton
                    ref={formRef}
                    formId="policy-renewal-date-form"
                    schema={buildRenewalDateFormSchema()}
                    data-id="policy-renewal-date-form"
                    isReadOnly={renewalDateMutation.isPending}
                    onSubmit={handleSubmit}
                />
            </Modal.Body>

            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'tertiary',
                        onClick: closePolicyRenewalDateModal,
                        cosmosUseWithCaution_isDisabled:
                            renewalDateMutation.isPending,
                    },
                    {
                        label: t`Save`,
                        level: 'primary',
                        onClick: handleSave,
                        cosmosUseWithCaution_isDisabled:
                            renewalDateMutation.isPending,
                        isLoading: renewalDateMutation.isPending,
                    },
                ]}
            />
        </Modal>
    );
});
