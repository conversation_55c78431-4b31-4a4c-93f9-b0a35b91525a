import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { List } from '@cosmos-lab/components/list';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { closePolicyCancelApprovalModal } from '../helpers/open-policy-cancel-approval-modal.helper';
import { sharedPolicyHeaderNeedsApprovalActions } from '../policy-header-actions.controller';

export const PolicyCancelApprovalModal = observer(() => {
    return (
        <>
            <Modal.Header
                title={t`Cancel approval`}
                closeButtonAriaLabel={t`Close cancel approval modal`}
                onClose={closePolicyCancelApprovalModal}
            />

            <Modal.Body>
                <Stack
                    direction="column"
                    gap="md"
                    data-id="cancel-approval-content"
                >
                    <Text as="p" type="body" size="200" colorScheme="neutral">
                        {t`Cancelling this approval process will result in the following actions:`}
                    </Text>

                    <List
                        type="unordered"
                        size="200"
                        colorScheme="neutral"
                        data-id="cancel-approval-actions-list"
                        items={[
                            t`Any existing approvals will be cancelled`,
                            t`The policy will be removed from approvers task list`,
                            t`All tasks will be removed from approvers task list`,
                            t`A cancellation email will be sent to all approvers`,
                        ]}
                    />

                    <Text as="p" type="body" size="200" colorScheme="neutral">
                        {t`Are you sure you want to proceed?`}
                    </Text>
                </Stack>
            </Modal.Body>

            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: closePolicyCancelApprovalModal,
                        cosmosUseWithCaution_isDisabled:
                            sharedPolicyHeaderNeedsApprovalActions
                                .cancelApprovalMutation.isPending,
                    },
                    {
                        label: t`Confirm`,
                        level: 'primary',
                        onClick:
                            sharedPolicyHeaderNeedsApprovalActions.handleCancelApprovalSubmit,
                        isLoading:
                            sharedPolicyHeaderNeedsApprovalActions
                                .cancelApprovalMutation.isPending,
                    },
                ]}
            />
        </>
    );
});
