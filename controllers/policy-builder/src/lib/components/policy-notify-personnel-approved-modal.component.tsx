import { useMemo } from 'react';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { createPolicyNotifyPersonnelModal } from '../helpers/create-policy-notify-personnel-modal.helper';
import { closePolicyNotifyPersonnelApprovedModal } from '../helpers/open-policy-notify-personnel-approved-modal.helper';
import { sharedPolicyHeaderApprovedActions } from '../policy-header-actions.controller';

export const PolicyNotifyPersonnelApprovedModal = observer(
    (): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();
        const policyNotifyPersonnelFlow = useMemo(
            () =>
                createPolicyNotifyPersonnelModal({
                    formRef,
                    isLoading: () =>
                        sharedPolicyHeaderApprovedActions.isPublishing,
                    onConfirm: ({ shouldNotifyEmployees }) => {
                        sharedPolicyHeaderApprovedActions.handlePublishFromModal(
                            {
                                requiresAcknowledgment: true,
                                shouldNotifyEmployees,
                            },
                        );
                    },
                    onCancel: closePolicyNotifyPersonnelApprovedModal,
                    triggerSubmit,
                }),
            [formRef, triggerSubmit],
        );

        return (
            <>
                <Modal.Header
                    title={policyNotifyPersonnelFlow.getTitle()}
                    closeButtonAriaLabel={t`Close notify personnel modal`}
                    onClose={policyNotifyPersonnelFlow.handleClose}
                />

                <Modal.Body>
                    {policyNotifyPersonnelFlow.getContent()}
                </Modal.Body>

                <Modal.Footer
                    rightActionStack={policyNotifyPersonnelFlow.getActions()}
                />
            </>
        );
    },
);
