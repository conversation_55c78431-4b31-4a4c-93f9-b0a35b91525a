import { useMemo } from 'react';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { createPolicyFinalizeModal } from '../helpers/create-policy-finalize-modal.helper';

export const PolicyFinalizeModal = observer((): React.JSX.Element | null => {
    const { formRef, triggerSubmit } = useFormSubmit();
    const policyFinalizeFlow = useMemo(
        () =>
            createPolicyFinalizeModal({
                formRef,
                triggerSubmit,
            }),
        [formRef, triggerSubmit],
    );

    return (
        <>
            <Modal.Header
                title={policyFinalizeFlow.getTitle()}
                closeButtonAriaLabel={t`Close finalize policy modal`}
                onClose={policyFinalizeFlow.handleClose}
            />

            <Modal.Body>{policyFinalizeFlow.getContent()}</Modal.Body>

            <Modal.Footer rightActionStack={policyFinalizeFlow.getActions()} />
        </>
    );
});
