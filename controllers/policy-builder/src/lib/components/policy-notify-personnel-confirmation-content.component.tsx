import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

interface PolicyNotifyPersonnelConfirmationContentProps {
    personnelCount: number;
}

export const PolicyNotifyPersonnelConfirmationContent = observer(
    ({
        personnelCount,
    }: PolicyNotifyPersonnelConfirmationContentProps): React.JSX.Element => {
        return (
            <Stack gap="md" direction="column" data-id="tthCdApa">
                <Text>
                    <Trans>
                        Publishing this policy will send an email to{' '}
                        {personnelCount} assigned to this policy for
                        acknowledgement
                    </Trans>
                </Text>

                <Text>
                    <Trans>
                        Are you ready to publish and send for acknowledgement?
                    </Trans>
                </Text>
            </Stack>
        );
    },
);
