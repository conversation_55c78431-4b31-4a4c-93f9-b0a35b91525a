import { useMemo } from 'react';
import { Banner } from '@cosmos/components/banner';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    type UseFormSubmitReturn,
} from '@ui/forms';
import type { PolicyFinalizeModalFormValues } from '../controllers/policy-finalize-modal.controller';
import { PolicyExplanationField } from './policy-explanation-field.component';

export interface PolicyVersionNumbers {
    nextMajorVersion: number;
    nextMinorVersion: number;
    currentMajorVersion: number;
}

export interface PolicyFinalizeQuestionsContentProps {
    formRef: UseFormSubmitReturn['formRef'];
    rawFormValues: PolicyFinalizeModalFormValues;
    isLoading: () => boolean;
    onSubmit: (values: FormValues) => void;
    versions: PolicyVersionNumbers;
}

export const PolicyFinalizeQuestionsContent = observer(
    ({
        formRef,
        rawFormValues,
        isLoading,
        onSubmit,
        versions,
    }: PolicyFinalizeQuestionsContentProps): React.JSX.Element => {
        const { nextMajorVersion, nextMinorVersion, currentMajorVersion } =
            versions;

        const formSchema = useMemo(
            (): FormSchema => ({
                isMaterialChange: {
                    type: 'choiceCardGroup',
                    label: t`Does this draft include material changes?`,
                    choiceCardInputType: 'radio',
                    cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                    options: [
                        {
                            value: 'true',
                            label: t`YES, it includes material changes`,
                            helpText: t`This draft becomes Version ${nextMajorVersion}`,
                        },
                        {
                            value: 'false',
                            label: t`NO, it does not include material changes`,
                            helpText: t`This draft becomes Version ${currentMajorVersion}.${nextMinorVersion}`,
                        },
                    ],
                    initialValue: rawFormValues.isMaterialChange,
                },
                materialChangesBanner: {
                    type: 'custom',
                    label: t`Material changes require approval Banner`,
                    render: () => (
                        <Banner
                            severity="primary"
                            title={t`Material changes require approval`}
                            body={t`All material changes require approval before publishing`}
                            data-id="FxRZoJnW"
                        />
                    ),
                    shownIf: {
                        fieldName: 'isMaterialChange',
                        operator: 'equals',
                        value: 'true',
                    },
                },
                requiresApproval: {
                    type: 'choiceCardGroup',
                    label: t`Does this change require approval?`,
                    choiceCardInputType: 'radio',
                    cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                    options: [
                        {
                            value: 'true',
                            label: t`YES, approval is required`,
                            helpText: t`This will require the policy to be approved prior to publishing.`,
                        },
                        {
                            value: 'false',
                            label: t`NO, approval is not required`,
                            helpText: t`This will immediately publish the policy without approval.`,
                        },
                    ],
                    initialValue: rawFormValues.requiresApproval,
                    shownIf: {
                        fieldName: 'isMaterialChange',
                        operator: 'equals',
                        value: 'false',
                    },
                },
                requiresAcknowledgment: {
                    type: 'choiceCardGroup',
                    label: t`Does this change require personnel acknowledgment?`,
                    choiceCardInputType: 'radio',
                    cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                    options: [
                        {
                            value: 'true',
                            label: t`YES, personnel acknowledgement is required`,
                        },
                        {
                            value: 'false',
                            label: t`NO, personnel acknowledgement is not required`,
                        },
                    ],
                    initialValue: rawFormValues.requiresAcknowledgment,
                    shownIf: {
                        operator: 'and',
                        conditions: [
                            {
                                fieldName: 'isMaterialChange',
                                operator: 'equals',
                                value: 'false',
                            },
                            {
                                fieldName: 'requiresApproval',
                                operator: 'equals',
                                value: 'false',
                            },
                        ],
                    },
                },
                changesExplanation: {
                    type: 'custom',
                    validateWithDefault: 'textarea',
                    label: t`Explanation of changes`,
                    placeholder: t`Any details about what has changed in the policy...`,
                    isOptional: true,
                    rows: 2,
                    initialValue: rawFormValues.changesExplanation,
                    render: PolicyExplanationField,
                },
            }),
            [
                currentMajorVersion,
                nextMajorVersion,
                nextMinorVersion,
                rawFormValues.changesExplanation,
                rawFormValues.isMaterialChange,
                rawFormValues.requiresAcknowledgment,
                rawFormValues.requiresApproval,
            ],
        );

        return (
            <Form
                hasExternalSubmitButton
                data-id="policy-finalize-form"
                formId="policy-finalize-form"
                ref={formRef}
                schema={formSchema}
                isReadOnly={isLoading()}
                onSubmit={onSubmit}
            />
        );
    },
);
