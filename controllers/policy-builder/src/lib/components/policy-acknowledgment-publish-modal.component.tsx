import { useMemo } from 'react';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { createPolicyNotifyPersonnelModal } from '../helpers/create-policy-notify-personnel-modal.helper';
import { closePolicyAcknowledgmentPublishModal } from '../helpers/open-policy-acknowledgment-publish-modal.helper';
import { sharedPolicyHeaderApprovedActions } from '../policy-header-actions.controller';

export const PolicyAcknowledgmentPublishModal = observer(
    (): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();
        const policyAcknowledgmentFlow = useMemo(
            () =>
                createPolicyNotifyPersonnelModal({
                    includeAcknowledgmentQuestion: true,
                    formRef,
                    isLoading: () =>
                        sharedPolicyHeaderApprovedActions.isPublishing,
                    onConfirm:
                        sharedPolicyHeaderApprovedActions.handlePublishFromModal,
                    onCancel: closePolicyAcknowledgmentPublishModal,
                    triggerSubmit,
                }),
            [formRef, triggerSubmit],
        );

        return (
            <>
                <Modal.Header
                    title={policyAcknowledgmentFlow.getTitle()}
                    closeButtonAriaLabel={t`Close publish policy modal`}
                    onClose={policyAcknowledgmentFlow.handleClose}
                />

                <Modal.Body>{policyAcknowledgmentFlow.getContent()}</Modal.Body>

                <Modal.Footer
                    rightActionStack={policyAcknowledgmentFlow.getActions()}
                />
            </>
        );
    },
);
