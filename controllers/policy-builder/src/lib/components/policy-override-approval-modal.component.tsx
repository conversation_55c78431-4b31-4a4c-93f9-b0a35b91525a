import { isString } from 'lodash-es';
import { Modal } from '@cosmos/components/modal';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';
import { closePolicyOverrideApprovalModal } from '../helpers/open-policy-override-approval-modal.helper';
import { sharedPolicyHeaderNeedsApprovalActions } from '../policy-header-actions.controller';

const buildFormSchema = (): FormSchema => ({
    justification: {
        type: 'textarea',
        label: t`Reason for override`,
        rows: 4,
    },
});

export const PolicyOverrideApprovalModal = observer(() => {
    const { formRef, triggerSubmit } = useFormSubmit();

    const handleSubmit = action((values: FormValues) => {
        const justification = isString(values.justification)
            ? values.justification
            : '';

        sharedPolicyHeaderNeedsApprovalActions.handleOverrideApprovalSubmit(
            justification,
        );
    });

    const handleOverride = () => {
        triggerSubmit();
    };

    return (
        <>
            <Modal.Header
                title={t`Override approval`}
                description={t`Are you sure you want to override the current approval cycle?`}
                closeButtonAriaLabel={t`Close override approval modal`}
                onClose={closePolicyOverrideApprovalModal}
            />

            <Modal.Body>
                <Form
                    hasExternalSubmitButton
                    data-id="policy-override-approval-form"
                    ref={formRef}
                    formId="policy-override-approval-form"
                    schema={buildFormSchema()}
                    onSubmit={handleSubmit}
                />
            </Modal.Body>

            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: closePolicyOverrideApprovalModal,
                        cosmosUseWithCaution_isDisabled:
                            sharedPolicyHeaderNeedsApprovalActions
                                .overrideApprovalMutation.isPending,
                    },
                    {
                        label: t`Override approval`,
                        level: 'primary',
                        onClick: handleOverride,
                        isLoading:
                            sharedPolicyHeaderNeedsApprovalActions
                                .overrideApprovalMutation.isPending,
                    },
                ]}
            />
        </>
    );
});
