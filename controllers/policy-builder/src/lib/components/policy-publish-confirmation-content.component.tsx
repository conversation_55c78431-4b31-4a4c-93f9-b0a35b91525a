import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedPolicyBuilderModel } from '@models/policy-builder';

export const PolicyPublishConfirmationContent = observer(
    (): React.JSX.Element => {
        const { currentMajorVersion } = sharedPolicyBuilderModel;

        return (
            <>
                <Text>
                    <Trans>
                        You are making a non-material change that does not
                        require personnel acknowledgment. This will immediately
                        publish the changes to the policy.
                    </Trans>
                </Text>

                <Text>
                    <Trans>
                        Personnel will not need to acknowledge this version if
                        they have already accepted another version in this V
                        {currentMajorVersion} series.
                    </Trans>
                </Text>
            </>
        );
    },
);
