import { isError, uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import { companiesControllerChangeCompanySettingAiMutation } from '@globals/api-sdk/queries';
import type { CompanySettingRequestDto } from '@globals/api-sdk/types';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';

class AiSettingsController {
    constructor() {
        makeAutoObservable(this);
    }

    changeAiSettingMutation = new ObservedMutation(
        companiesControllerChangeCompanySettingAiMutation,
        {
            onSuccess: () => {
                sharedCurrentCompanyController.load();
            },
        },
    );

    enableAiSettings = (onSuccess?: () => void): void => {
        const requestData: CompanySettingRequestDto = {
            type: 'AI_ENABLED',
            defaultValue: true,
        };

        this.changeAiSettingMutation.mutate({
            body: requestData,
        });

        when(
            () => !this.changeAiSettingMutation.isPending,
            () => {
                if (this.changeAiSettingMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: uniqueId('ai-settings-enabled-error-'),
                        props: {
                            title: t`Failed to enable AI features`,
                            description: isError(
                                this.changeAiSettingMutation.error,
                            )
                                ? this.changeAiSettingMutation.error.message
                                : t`An error occurred while enabling AI features. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: uniqueId('ai-settings-enabled-success-'),
                        props: {
                            title: t`AI features have been enabled`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    onSuccess?.();
                }
            },
        );
    };

    disableAiSettings = (onSuccess?: () => void): void => {
        const requestData: CompanySettingRequestDto = {
            type: 'AI_ENABLED',
            defaultValue: false,
        };

        this.changeAiSettingMutation.mutate({
            body: requestData,
        });

        when(
            () => !this.changeAiSettingMutation.isPending,
            () => {
                if (this.changeAiSettingMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: uniqueId('ai-settings-disabled-error-'),
                        props: {
                            title: t`Failed to disable AI features`,
                            description: isError(
                                this.changeAiSettingMutation.error,
                            )
                                ? this.changeAiSettingMutation.error.message
                                : t`An error occurred while disabling AI features. Try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: uniqueId('ai-settings-disabled-success-'),
                        props: {
                            title: t`AI features have been disabled`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    onSuccess?.();
                }
            },
        );
    };

    get isLoading(): boolean {
        return this.changeAiSettingMutation.isPending;
    }

    get hasError(): boolean {
        return this.changeAiSettingMutation.hasError;
    }

    get error(): Error | null {
        return this.changeAiSettingMutation.error;
    }
}

export const sharedAiSettingsController = new AiSettingsController();
