import { uniqueId } from 'lodash-es';
import { openNewEvidenceModal } from '@components/control-approval';
import {
    sharedControlEvidenceController,
    sharedControlsDetailsStatsController,
    sharedControlsExternalEvidenceController,
} from '@controllers/controls';
import { panelController } from '@controllers/panel';
import { snackbarController } from '@controllers/snackbar';
import {
    grcControllerDeleteEvidenceMutation,
    grcControllerDeleteReportsMutation,
    grcControllerEditExternalEvidenceMutation,
    grcControllerEditExternalUrlEvidenceMutation,
    grcControllerExternalEvidenceUrlMutation,
    grcControllerPutReportsMutation,
    grcControllerUploadExternalEvidenceMutation,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedEvidenceDetailsModel } from '@models/evidence-library-details';
import type { FormValues } from '@ui/forms';
import { closeAddExternalEvidenceControlModal } from '@views/controls-evidence';
import { buildControlsExternalFileEvidenceRequestDto } from '../helpers/build-control-external-file-evidence-dto.helper';
import { buildControlsExternalUrlEvidenceRequestDto } from '../helpers/build-control-external-url-evidence-dto.helper';

type MapEvidenceBody = NonNullable<
    Required<Parameters<typeof grcControllerPutReportsMutation>>[0]['body']
>;

type UnMapEvidenceBody = NonNullable<
    Required<Parameters<typeof grcControllerDeleteReportsMutation>>[0]['body']
>;

class ControlsExternalEvidenceMutationController {
    createExternalFileEvidenceMutation = new ObservedMutation(
        grcControllerUploadExternalEvidenceMutation,
        {
            onSuccess: () => {
                openNewEvidenceModal();
            },
        },
    );

    createExternalUrlEvidenceMutation = new ObservedMutation(
        grcControllerExternalEvidenceUrlMutation,
        {
            onSuccess: () => {
                openNewEvidenceModal();
            },
        },
    );

    updateExternalFileEvidenceMutation = new ObservedMutation(
        grcControllerEditExternalEvidenceMutation,
    );

    updateExternalUrlEvidenceMutation = new ObservedMutation(
        grcControllerEditExternalUrlEvidenceMutation,
    );

    mapControlEvidenceMutation = new ObservedMutation(
        grcControllerPutReportsMutation,
        {
            onSuccess: () => {
                openNewEvidenceModal();
            },
        },
    );

    unmapControlEvidenceMutation = new ObservedMutation(
        grcControllerDeleteReportsMutation,
    );

    unmapControlExternalEvidenceMutation = new ObservedMutation(
        grcControllerDeleteEvidenceMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get isCreateExternalEvidenceMutationLoading(): boolean {
        return this.createExternalFileEvidenceMutation.isPending;
    }

    get isCreateExternalUrlEvidenceMutationLoading(): boolean {
        return this.createExternalUrlEvidenceMutation.isPending;
    }

    get isMappingEvidenceLoading(): boolean {
        return this.mapControlEvidenceMutation.isPending;
    }

    get isUpdatingExternalEvidenceLoading(): boolean {
        return (
            this.updateExternalFileEvidenceMutation.isPending ||
            this.updateExternalUrlEvidenceMutation.isPending
        );
    }

    createControlExternalFileEvidence = (values: FormValues) => {
        const { currentWorkspace } = sharedWorkspacesController;
        const { controlId } = sharedControlEvidenceController;

        const requestBody = buildControlsExternalFileEvidenceRequestDto(values);

        if (!requestBody || !controlId || !currentWorkspace) {
            snackbarController.addSnackbar({
                id: `external-evidence-creation-error-${uniqueId()}`,
                props: {
                    title: t`Unable to create evidence`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            throw new Error('Unable to create evidence');
        }

        this.createExternalFileEvidenceMutation.mutate({
            body: { ...requestBody, workspaceId: currentWorkspace.id },
            path: {
                controlId,
            },
        });

        when(
            () => !this.createExternalFileEvidenceMutation.isPending,
            () => {
                if (this.createExternalFileEvidenceMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: `external-evidence-creation-error-${uniqueId()}`,
                        props: {
                            title: t`Unable to create evidence`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: `external-evidence-created-successfully-${uniqueId()}`,
                        props: {
                            title: t`External evidence created successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    sharedControlEvidenceController.invalidateControlEvidence();
                    closeAddExternalEvidenceControlModal();
                }
            },
        );
    };

    createControlExternalUrlEvidence = (values: FormValues) => {
        const { currentWorkspace } = sharedWorkspacesController;
        const { controlId } = sharedControlEvidenceController;

        const requestBody = buildControlsExternalUrlEvidenceRequestDto(values);

        if (!requestBody || !controlId || !currentWorkspace) {
            snackbarController.addSnackbar({
                id: `external-evidence-creation-error-${uniqueId()}`,
                props: {
                    title: t`Unable to create evidence`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            throw new Error('Unable to create evidence');
        }

        this.createExternalUrlEvidenceMutation.mutate({
            body: { ...requestBody, workspaceId: currentWorkspace.id },
            path: {
                xProductId: currentWorkspace.id,
                controlId,
            },
        });

        when(
            () => !this.createExternalUrlEvidenceMutation.isPending,
            () => {
                if (this.createExternalUrlEvidenceMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: `external-evidence-creation-error-${uniqueId()}`,
                        props: {
                            title: t`Unable to create evidence`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: `external-evidence-created-successfully-${uniqueId()}`,
                        props: {
                            title: t`External evidence created successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    sharedControlEvidenceController.invalidateControlEvidence();
                    closeAddExternalEvidenceControlModal();
                }
            },
        );
    };

    mapControlEvidence = (
        controlId: number,
        body: Omit<MapEvidenceBody, 'workspaceId'>,
    ) => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        this.mapControlEvidenceMutation.mutate({
            body: {
                workspaceId: currentWorkspace.id,
                ...body,
            },
            path: {
                controlId,
            },
        });

        when(
            () => !this.mapControlEvidenceMutation.isPending,
            () => {
                if (this.mapControlEvidenceMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: uniqueId('external-evidence-mapping-error-'),
                        props: {
                            title: t`Unable to map evidence`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                sharedControlsDetailsStatsController.invalidate();
                sharedControlEvidenceController.invalidateControlEvidence();

                snackbarController.addSnackbar({
                    id: uniqueId('external-evidence-mapped-successfully-'),
                    props: {
                        title: t`Evidence mapped successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    get isUnmapPending(): boolean {
        return (
            this.unmapControlEvidenceMutation.isPending ||
            this.unmapControlExternalEvidenceMutation.isPending
        );
    }

    get hasUnmapError(): boolean {
        return (
            this.unmapControlEvidenceMutation.hasError ||
            this.unmapControlExternalEvidenceMutation.hasError
        );
    }

    unmapControlEvidence = ({
        controlId,
        libraryEvidenceBody,
        externalEvidenceId,
    }: {
        controlId: number;
        libraryEvidenceBody?: Omit<UnMapEvidenceBody, 'workspaceId'>;
        externalEvidenceId?: number;
    }) => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        if (libraryEvidenceBody) {
            this.unmapControlEvidenceMutation.mutate({
                body: {
                    workspaceId: currentWorkspace.id,
                    ...libraryEvidenceBody,
                },
                path: {
                    controlId,
                },
            });
        } else if (externalEvidenceId) {
            this.unmapControlExternalEvidenceMutation.mutate({
                path: {
                    externalEvidenceId,
                },
                query: {
                    workspaceId: currentWorkspace.id,
                },
            });
        }

        when(
            () => !this.isUnmapPending,
            () => {
                if (this.hasUnmapError) {
                    snackbarController.addSnackbar({
                        id: uniqueId('external-evidence-unmapping-error-'),
                        props: {
                            title: t`Unable to unmap evidence`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const isEvidencePanelOpen =
                    panelController.currentPanelId === 'evidence-panel';

                const currentLibraryEvidenceId =
                    sharedEvidenceDetailsModel.evidenceDetails?.id;
                const currentExternalEvidenceId =
                    sharedControlsExternalEvidenceController
                        .controlExternalEvidence?.id;
                const unmappedLibraryEvidenceId =
                    libraryEvidenceBody?.reportIds[0];

                const shouldClosePanel =
                    isEvidencePanelOpen &&
                    ((unmappedLibraryEvidenceId &&
                        currentLibraryEvidenceId ===
                            unmappedLibraryEvidenceId) ||
                        (externalEvidenceId &&
                            currentExternalEvidenceId === externalEvidenceId));

                if (shouldClosePanel) {
                    panelController.closePanel();
                }

                sharedControlsDetailsStatsController.invalidate();
                sharedControlEvidenceController.invalidateControlEvidence();

                snackbarController.addSnackbar({
                    id: uniqueId('external-evidence-unmapped-successfully-'),
                    props: {
                        title: t`Evidence unmapped successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    updateControlExternalEvidence = (
        values: FormValues,
        externalEvidenceId: number,
    ) => {
        const { currentWorkspace } = sharedWorkspacesController;
        const { controlId } = sharedControlEvidenceController;

        const requestBody = buildControlsExternalFileEvidenceRequestDto(values);

        if (!requestBody || !controlId || !currentWorkspace) {
            snackbarController.addSnackbar({
                id: `external-evidence-update-error-${uniqueId()}`,
                props: {
                    title: t`Unable to update evidence`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            throw new Error('Unable to update evidence');
        }

        this.updateExternalFileEvidenceMutation.mutate({
            body: { ...requestBody },
            path: {
                externalEvidenceId,
            },
        });

        when(
            () => !this.updateExternalFileEvidenceMutation.isPending,
            () => {
                if (this.updateExternalFileEvidenceMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: `external-evidence-update-error-${uniqueId()}`,
                        props: {
                            title: t`Unable to update evidence`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: `external-evidence-updated-successfully-${uniqueId()}`,
                        props: {
                            title: t`External evidence updated successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    sharedControlEvidenceController.invalidateControlEvidence();
                    sharedControlsExternalEvidenceController.invalidateControlExternalEvidence();
                    closeAddExternalEvidenceControlModal();
                }
            },
        );
    };

    updateControlExternalUrlEvidence = (
        values: FormValues,
        externalEvidenceId: number,
    ) => {
        const { currentWorkspace } = sharedWorkspacesController;
        const { controlId } = sharedControlEvidenceController;

        const requestBody = buildControlsExternalUrlEvidenceRequestDto(values);

        if (!requestBody || !controlId || !currentWorkspace) {
            snackbarController.addSnackbar({
                id: `external-evidence-update-error-${uniqueId()}`,
                props: {
                    title: t`Unable to update evidence`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            throw new Error('Unable to update evidence');
        }

        this.updateExternalUrlEvidenceMutation.mutate({
            body: { ...requestBody, workspaceId: currentWorkspace.id },
            path: {
                externalEvidenceId,
            },
        });

        when(
            () => !this.updateExternalUrlEvidenceMutation.isPending,
            () => {
                if (this.updateExternalUrlEvidenceMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: `external-evidence-update-error-${uniqueId()}`,
                        props: {
                            title: t`Unable to update evidence`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: `external-evidence-updated-successfully-${uniqueId()}`,
                        props: {
                            title: t`External evidence updated successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                    sharedControlEvidenceController.invalidateControlEvidence();
                    sharedControlsExternalEvidenceController.invalidateControlExternalEvidence();
                    closeAddExternalEvidenceControlModal();
                }
            },
        );
    };
}

export const sharedControlsExternalEvidenceMutationController =
    new ControlsExternalEvidenceMutationController();
