import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Truncation } from '@cosmos-lab/components/truncation';
import { grcControllerSearchControlsInfiniteOptions } from '@globals/api-sdk/queries';
import type {
    ControlListResponseDto,
    GrcControllerSearchControlsData,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedInfiniteQuery } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

type Query = NonNullable<GrcControllerSearchControlsData['query']>;
class ControlsInfiniteListController {
    #lastSearchQuery = '';
    constructor() {
        makeAutoObservable(this);
    }

    controlsListInfiniteQuery = new ObservedInfiniteQuery(
        grcControllerSearchControlsInfiniteOptions,
    );

    get controlsInfiniteList(): ControlListResponseDto[] {
        return (
            this.controlsListInfiniteQuery.data?.pages.flatMap(
                (i) => i?.data ?? [],
            ) ?? []
        );
    }

    loadInfiniteControls = (query?: Query): void => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        this.controlsListInfiniteQuery.load({
            path: { xProductId: currentWorkspace.id },
            query: {
                isArchived: false,
                q: this.#lastSearchQuery,
                ...query,
            },
        });
    };

    get controlsComboboxOptions(): ListBoxItemData[] {
        return this.controlsInfiniteList.map((value) => ({
            id: String(value.id),
            label: value.name,
            value: String(value.id),
            description: (
                <Truncation mode="end" lineClamp={2}>
                    {value.description}
                </Truncation>
            ),
            controlData: {
                id: value.id,
                code: value.code,
            },
        }));
    }

    get hasError(): boolean {
        return this.controlsListInfiniteQuery.hasError;
    }

    get isLoading(): boolean {
        return this.controlsListInfiniteQuery.isLoading;
    }

    get hasNextPage(): boolean {
        return this.controlsListInfiniteQuery.hasNextPage;
    }

    loadNextPage = ({ search }: { search?: string }): void => {
        if (search !== this.#lastSearchQuery) {
            this.controlsListInfiniteQuery.unload();
            this.#lastSearchQuery = search ?? '';
            this.loadInfiniteControls();

            return;
        }

        this.controlsListInfiniteQuery.nextPage();
    };

    onFetchControls = ({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    } = {}): void => {
        const q = search?.trim();

        if (increasePage) {
            this.loadNextPage({ search: q });

            return;
        }
        this.loadInfiniteControls({ q });
    };

    remove = (): void => {
        this.controlsListInfiniteQuery.remove();
    };
}

export const sharedControlsInfiniteListController =
    new ControlsInfiniteListController();
