import { panelController } from '@controllers/panel';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { snackbarController } from '@controllers/snackbar';
import { grcControllerDeletePoliciesMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedControlPoliciesController } from './control-policies.controller';
import { sharedControlsDetailsStatsController } from './controls-details-stats.controller';

type UnmapPolicyBody = NonNullable<
    Required<Parameters<typeof grcControllerDeletePoliciesMutation>>[0]['body']
>;

class ControlsUnmapPolicyMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    unmapPolicyMutation = new ObservedMutation(
        grcControllerDeletePoliciesMutation,
    );

    get isUnmapping(): boolean {
        return this.unmapPolicyMutation.isPending;
    }

    get hasError(): boolean {
        return this.unmapPolicyMutation.hasError;
    }

    unmapPolicyFromControl = (
        controlId: number,
        body: Omit<UnmapPolicyBody, 'workspaceId' | 'updateDisabledControls'>,
    ): void => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        this.unmapPolicyMutation.mutate({
            path: {
                controlId,
            },
            body: {
                updateDisabledControls: false,
                workspaceId: currentWorkspace.id,
                ...body,
            },
        });

        when(
            () => !this.isUnmapping,
            () => {
                if (this.hasError) {
                    snackbarController.addSnackbar({
                        id: 'unmap-policy-error',
                        props: {
                            title: t`Failed to unmap policy`,
                            description: t`An error occurred while unmapping the policy. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const isPolicyPanelOpen =
                    panelController.currentPanelId === 'policy-details-panel';
                const currentPolicyId =
                    sharedPolicyBuilderController.policy?.id;
                const unmappedPolicyId = body.policyIds[0];

                const shouldClosePanel =
                    isPolicyPanelOpen &&
                    unmappedPolicyId &&
                    currentPolicyId === unmappedPolicyId;

                if (shouldClosePanel) {
                    panelController.closePanel();
                }

                sharedControlPoliciesController.invalidate();
                sharedControlsDetailsStatsController.invalidate();

                snackbarController.addSnackbar({
                    id: 'unmap-policy-success',
                    hasTimeout: true,
                    props: {
                        title: t`Policy unmapped successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedControlsUnmapPolicyMutationController =
    new ControlsUnmapPolicyMutationController();
