import { isEmpty, uniqueId } from 'lodash-es';
import { panelController } from '@controllers/panel';
import { sharedRiskPartiallyMutationController } from '@controllers/risk';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import { snackbarController } from '@controllers/snackbar';
import { grcControllerBulkPutControlRisksMutation } from '@globals/api-sdk/queries';
import type { RiskResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedControlRisksController } from './control-risks.controller';

class ControlRisksMutationController {
    associateRiskMutation = new ObservedMutation(
        grcControllerBulkPutControlRisksMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get isMappingPending(): boolean {
        return this.associateRiskMutation.isPending;
    }

    get hasError(): boolean {
        return this.associateRiskMutation.hasError;
    }

    unMapRiskToControl = (controlId: number, risk: RiskResponseDto) => {
        risk.controls = risk.controls.filter(
            (control) => control.id !== controlId,
        );

        sharedRiskPartiallyMutationController.updateRiskPartially(risk.riskId, {
            controls: risk.controls,
        });

        when(
            () => !sharedRiskPartiallyMutationController.isPending,
            () => {
                if (sharedRiskPartiallyMutationController.hasError) {
                    snackbarController.addSnackbar({
                        id: `unmap-risk-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to unmap risk`,
                            description: t`An error occurred while unmapping the risk. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }

                const isRiskPanelOpen =
                    panelController.currentPanelId === 'risk-register-panel';
                const currentRiskId =
                    sharedRiskDetailsController.riskDetails?.riskId;

                const shouldClosePanel =
                    isRiskPanelOpen && currentRiskId === risk.riskId;

                if (shouldClosePanel) {
                    panelController.closePanel();
                }

                sharedControlRisksController.invalidate();

                snackbarController.addSnackbar({
                    id: `unmap-risk-success-${uniqueId()}`,
                    props: {
                        title: t`Risk unmapped`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    mapRisksToControl = (controlId: number, risksCodes: string[]) => {
        if (isEmpty(risksCodes)) {
            return;
        }

        this.associateRiskMutation.mutate({
            path: { controlId },
            body: { riskIds: risksCodes },
        });

        when(
            () => !this.isMappingPending,
            () => {
                if (this.hasError) {
                    snackbarController.addSnackbar({
                        id: `map-risk-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to map risk`,
                            description: t`An error occurred while mapping the risk. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }

                sharedControlRisksController.invalidate();

                snackbarController.addSnackbar({
                    id: `map-risk-success-${uniqueId()}`,
                    props: {
                        title: t`Risk mapped to control`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedControlRisksMutationController =
    new ControlRisksMutationController();
