import { isEmpty, isNil } from 'lodash-es';
import { CHECK_RESULT_STATUS_ENUM } from '@components/events-detail-card';
import { snackbarController } from '@controllers/snackbar';
import {
    aiExecutionGroupControllerGenerateExecutionMutation,
    eventsControllerGetEventDetailsOptions,
    eventsControllerGetEventPdfDownloadUrlOptions,
    eventsControllerGetEventPdfUrlPreviewOptions,
} from '@globals/api-sdk/queries';
import type { EventDetailsResponseDto } from '@globals/api-sdk/types';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    reaction,
    when,
} from '@globals/mobx';
import { isProviderType, providers } from '@globals/providers';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    codeToJSONString,
    downloadBlob,
    sanitizeFileName,
} from '@helpers/download-file';
import { MAX_MEGABYTES_EVENT_SIZE_TO_PROCESS, VALUE_SIZE } from './constants';

class EventsDetailsController {
    constructor() {
        makeAutoObservable(this, { eventsDetails: false });
    }

    eventsDetails = new ObservedQuery(eventsControllerGetEventDetailsOptions);

    getDownloadPDFUrlQuery = new ObservedQuery(
        eventsControllerGetEventPdfDownloadUrlOptions,
    );

    getDownloadPDFUrlPreviewQuery = new ObservedQuery(
        eventsControllerGetEventPdfUrlPreviewOptions,
    );

    generateAiSummaryMutation = new ObservedMutation(
        aiExecutionGroupControllerGenerateExecutionMutation,
    );

    get isLoading(): boolean {
        return this.eventsDetails.isLoading;
    }

    get eventsDetailsData(): EventDetailsResponseDto | null {
        return this.eventsDetails.data;
    }

    get eventsDetailsCode() {
        return this.eventsDetails.data?.metadata;
    }

    get eventsDetailsType() {
        return this.eventsDetails.data?.type;
    }

    get connectionClientType() {
        return this.eventsDetails.data?.connection?.clientType;
    }

    get providerName(): string {
        const clientType = this.connectionClientType;

        if (!clientType) {
            return '';
        }

        if (isProviderType(clientType)) {
            return providers[clientType].name;
        }

        return clientType;
    }

    get isEventDataTooLarge() {
        if (isNil(this.eventsDetails.data?.metadata)) {
            return false;
        }
        const codeLength = new TextEncoder().encode(
            JSON.stringify(this.eventsDetails.data.metadata),
        ).length;

        return (
            codeLength / VALUE_SIZE / VALUE_SIZE >
            MAX_MEGABYTES_EVENT_SIZE_TO_PROCESS
        );
    }

    get isGeneratingAiSummary(): boolean {
        return this.generateAiSummaryMutation.isPending;
    }

    get aiSummaryError(): string | null {
        return this.generateAiSummaryMutation.error?.message ?? null;
    }

    get aiSummaryData() {
        return this.generateAiSummaryMutation.response;
    }

    get hasAiSummary(): boolean {
        return Boolean(this.aiSummaryData?.id);
    }

    get shouldShowAiSummary(): boolean {
        const eventDetails = this.eventsDetailsData;

        return Boolean(
            eventDetails?.controlTestInstance?.runMode === 'AP2' &&
                !isNil(eventDetails.controlTestInstance.ap2EnabledAt) &&
                eventDetails.status === CHECK_RESULT_STATUS_ENUM.FAILED &&
                eventDetails.haveFailedResources,
        );
    }

    loadEventDetails = (eventId: string): void => {
        when(
            () => !isNil(sharedWorkspacesController.currentWorkspace),
            () => {
                const workspaceId =
                    sharedWorkspacesController.currentWorkspace?.id;

                this.eventsDetails.load({
                    path: { id: eventId },
                    query: { workspaceId, includeMetadata: true },
                });
            },
        );
    };

    downloadTXT(): void {
        try {
            if (isEmpty(this.eventsDetailsData)) {
                throw new Error('Event details are not available');
            }
            const { category, type, createdAt, metadata } =
                this.eventsDetailsData;
            const [eventDate] = new Date(createdAt).toISOString().split('T');
            const fileName = sanitizeFileName(
                `${eventDate}-${category}-${type}.txt`,
            );

            // Create a new Blob object with the desired content
            const blob = new Blob([codeToJSONString(metadata)], {
                type: 'text/plain',
            });

            downloadBlob(blob, fileName);
        } catch (error: unknown) {
            const timestamp = new Date().toISOString();

            snackbarController.addSnackbar({
                id: `${timestamp}-cant-download-txt`,
                props: {
                    title: 'Cant download TXT at the moment. Please try again later.',
                    description: 'Please try again later.',
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });
            // TODO: remove this catch block once the logger is implemented.
            console.error(error);
        }
    }

    downloadPDF(): void {
        try {
            if (isEmpty(this.eventsDetailsData)) {
                throw new Error('Event details are not available');
            }

            this.getDownloadPDFUrlQuery.load({
                path: {
                    id: this.eventsDetailsData.id,
                },
            });

            reaction(
                () => !this.getDownloadPDFUrlQuery.hasError,
                () => {
                    throw this.getDownloadPDFUrlQuery.error as Error;
                },
            );
        } catch (error) {
            snackbarController.addSnackbar({
                id: 'cant-download-pdf',
                props: {
                    title: 'Cant download PDF at the moment.',
                    description: 'Please try again later.',
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });
            console.error(error);
        }
    }

    downloadPDFPreview(eventId: string, evidenceId: number) {
        try {
            this.getDownloadPDFUrlPreviewQuery.load({
                path: {
                    id: eventId,
                    evidenceId,
                },
            });

            when(
                () => this.getDownloadPDFUrlPreviewQuery.hasError,
                () => {
                    throw this.getDownloadPDFUrlPreviewQuery.error as Error;
                },
            );
        } catch (error) {
            snackbarController.addSnackbar({
                id: 'cant-download-pdf-preview',
                props: {
                    title: 'Cant download PDF preview at the moment.',
                    description: 'Please try again later.',
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });
            console.error(error);
        }
    }

    generateAiSummary = (eventId: string): void => {
        if (!eventId) {
            throw new Error('Event ID is required for AI summary generation');
        }

        this.generateAiSummaryMutation.mutate({
            body: {
                processType: 'SUMMARY',
                processFeature: 'EVENT_TEST_FAILURE',
                featureId: eventId,
            },
        });
    };

    handleGenerateAiSummary = (): void => {
        const eventId = this.eventsDetailsData?.id;

        if (!eventId) {
            return;
        }

        this.generateAiSummary(eventId);
    };

    clearAiSummary = (): void => {
        this.generateAiSummaryMutation.mutation = null;
    };
}

export const sharedEventsDetailsController = new EventsDetailsController();
