import { isError, isObject } from 'lodash-es';
import { z } from 'zod';
import {
    ImpactLikelihoodFormField,
    RiskLevelDefinitionsFormField,
    ThresholdsFormField,
} from '@components/risk-settings-scoring';
import { createNumericOptions } from '@controllers/risk';
import { sharedRiskSettingsController } from '@controllers/risk-settings';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { riskManagementControllerUpdateRiskSettingsMutation } from '@globals/api-sdk/queries';
import type {
    RiskSettingsRequestDto,
    RiskSettingsResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { getScoreIntensityByThresholds } from '@helpers/risk-score';
import type { FormSchema } from '@ui/forms';

function createLevelDefinitionFields(
    riskSettings: RiskSettingsResponseDto,
): FormSchema {
    const fields: FormSchema = {};

    // Create impact definition fields (1 to impact level)
    const impactLevels = Array.from(
        { length: riskSettings.impact },
        (_, i) => i + 1,
    );

    for (const level of impactLevels) {
        const existingDefinition = riskSettings.levelDefinitions.find(
            (def) => def.level === level && def.type === 'IMPACT',
        );

        fields[`impactDefinition${level}`] = {
            type: 'textarea' as const,
            label: `Impact Level ${level}`,
            initialValue: existingDefinition?.description || '',
            validator: z.string().optional(),
        };
    }

    // Create likelihood definition fields (1 to likelihood level)
    const likelihoodLevels = Array.from(
        { length: riskSettings.likelihood },
        (_, i) => i + 1,
    );

    for (const level of likelihoodLevels) {
        const existingDefinition = riskSettings.levelDefinitions.find(
            (def) => def.level === level && def.type === 'LIKELIHOOD',
        );

        fields[`likelihoodDefinition${level}`] = {
            type: 'textarea' as const,
            label: `Likelihood Level ${level}`,
            initialValue: existingDefinition?.description || '',
            validator: z.string().optional(),
        };
    }

    return fields;
}

/**
 * Create threshold fields dynamically based on current thresholds.
 */
function createThresholdFields(
    thresholds: RiskSettingsResponseDto['thresholds'],
): FormSchema {
    const fields: FormSchema = {};

    for (const threshold of thresholds) {
        fields[`threshold${threshold.id}Name`] = {
            type: 'text' as const,
            label: `Threshold ${threshold.id} Name`,
            initialValue: threshold.name || '',
            validator: z.string().min(1, 'Name is required'),
        };
        fields[`threshold${threshold.id}Description`] = {
            type: 'textarea' as const,
            label: `Threshold ${threshold.id} Description`,
            initialValue: threshold.description || '',
            validator: z.string().optional(),
        };
    }

    return fields;
}

function createFormSchema(
    riskSettings: RiskSettingsResponseDto | null,
): FormSchema {
    if (!riskSettings) {
        return {};
    }

    return {
        impactLikelihoodDisplay: {
            type: 'custom' as const,
            customType: 'object' as const,
            label: 'Impact and Likelihood',
            render: ImpactLikelihoodFormField,
            initialValue: {
                impact: riskSettings.impact,
                likelihood: riskSettings.likelihood,
            },
            fields: {
                impact: {
                    type: 'select' as const,
                    label: 'Impact',
                    options: createNumericOptions(10),
                    initialValue: {
                        id: String(riskSettings.impact),
                        label: String(riskSettings.impact),
                        value: String(riskSettings.impact),
                    },
                    validator: z.object({
                        id: z.string(),
                        label: z.string(),
                        value: z.string(),
                    }),
                },
                likelihood: {
                    type: 'select' as const,
                    label: 'Likelihood',
                    options: createNumericOptions(10),
                    initialValue: {
                        id: String(riskSettings.likelihood),
                        label: String(riskSettings.likelihood),
                        value: String(riskSettings.likelihood),
                    },
                    validator: z.object({
                        id: z.string(),
                        label: z.string(),
                        value: z.string(),
                    }),
                },
            },
        },
        riskLevelDefinitions: {
            type: 'custom' as const,
            customType: 'object' as const,
            label: 'Risk Level Definitions',
            render: RiskLevelDefinitionsFormField,
            initialValue: {},
            fields: createLevelDefinitionFields(riskSettings),
        },
        thresholds: {
            type: 'custom' as const,
            customType: 'object' as const,
            label: 'Thresholds',
            render: ThresholdsFormField,
            initialValue: {},
            fields: createThresholdFields(riskSettings.thresholds),
        },
    };
}

class RiskSettingsScoringController {
    constructor() {
        makeAutoObservable(this);
    }

    updateRiskSettingsMutation = new ObservedMutation(
        riskManagementControllerUpdateRiskSettingsMutation,
        {
            onSuccess: () => {
                sharedRiskSettingsController.riskSettingsQuery.invalidate();

                snackbarController.addSnackbar({
                    id: 'risk-settings-update-success',
                    props: {
                        title: t`Risk settings updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: (error) => {
                snackbarController.addSnackbar({
                    id: 'risk-settings-update-error',
                    props: {
                        title: t`Failed to update risk settings`,
                        description: error.message || t`Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    _currentImpact = 5;

    _currentLikelihood = 5;

    get impactOptions(): ListBoxItemData[] {
        const { riskSettings } = sharedRiskSettingsController;

        return riskSettings ? createNumericOptions(riskSettings.impact) : [];
    }

    get likelihoodOptions(): ListBoxItemData[] {
        const { riskSettings } = sharedRiskSettingsController;

        return riskSettings
            ? createNumericOptions(riskSettings.likelihood)
            : [];
    }

    get currentImpactLevel(): number {
        return this._currentImpact;
    }

    get currentLikelihoodLevel(): number {
        return this._currentLikelihood;
    }

    getCurrentImpactFromForm(impactFieldValue?: { value?: string }): number {
        return impactFieldValue?.value
            ? parseInt(impactFieldValue.value, 10)
            : 5;
    }

    getCurrentLikelihoodFromForm(likelihoodFieldValue?: {
        value?: string;
    }): number {
        return likelihoodFieldValue?.value
            ? parseInt(likelihoodFieldValue.value, 10)
            : 5;
    }

    get currentRiskScore(): number {
        return this._currentImpact * this._currentLikelihood;
    }

    get riskSeverity(): string {
        const { riskSettings } = sharedRiskSettingsController;

        if (!riskSettings?.thresholds) {
            return 'low';
        }

        return getScoreIntensityByThresholds(
            this.currentRiskScore,
            riskSettings.thresholds,
        );
    }

    updateImpactLevel(level: number): void {
        this._currentImpact = Math.max(1, Math.min(level, 10));
    }

    updateLikelihoodLevel(level: number): void {
        this._currentLikelihood = Math.max(1, Math.min(level, 10));
    }

    handleThresholdChange(
        index: number,
        field: string,
        newValue: string,
    ): void {
        const currentSettings = sharedRiskSettingsController.riskSettings;

        if (!currentSettings?.thresholds) {
            logger.error({
                message:
                    'Invalid threshold change request - no settings or thresholds',
                additionalInfo: {
                    index,
                    field,
                    newValue,
                    hasSettings: Boolean(currentSettings),
                    hasThresholds: Boolean(currentSettings?.thresholds),
                },
            });

            return;
        }

        if (index >= currentSettings.thresholds.length) {
            logger.error({
                message:
                    'Invalid threshold change request - index out of bounds',
                additionalInfo: {
                    index,
                    field,
                    newValue,
                    thresholdCount: currentSettings.thresholds.length,
                },
            });

            return;
        }

        if (field === 'delete') {
            logger.info({
                message: 'Threshold deletion requested',
                additionalInfo: {
                    index,
                    thresholdId: currentSettings.thresholds[index].id,
                },
            });

            return;
        }

        if (field === 'boundaries') {
            const boundaries = newValue
                .split(',')
                .map((val) => parseFloat(val.trim()))
                .filter((val) => !isNaN(val));

            logger.info({
                message: 'Threshold boundaries change requested',
                additionalInfo: { index, boundaries },
            });

            return;
        }

        logger.info({
            message: 'Threshold field change requested',
            additionalInfo: { index, field, newValue },
        });
    }

    resetThresholdsToDefaults(): void {
        const currentSettings = sharedRiskSettingsController.riskSettings;

        if (!currentSettings) {
            logger.error({
                message:
                    'Cannot reset thresholds - no current settings available',
            });

            return;
        }

        logger.info({
            message: 'Reset thresholds to defaults requested',
            additionalInfo: {
                currentThresholdCount: currentSettings.thresholds.length,
            },
        });
    }

    handleResetToDefaults = (): void => {
        this.resetThresholdsToDefaults();
    };

    get isSubmitting(): boolean {
        return this.updateRiskSettingsMutation.isPending;
    }

    get hasSubmissionError(): boolean {
        return this.updateRiskSettingsMutation.hasError;
    }

    handleFormSubmit = (values: unknown): void => {
        // Validate form data
        if (!this.isValidFormData(values)) {
            snackbarController.addSnackbar({
                id: 'risk-settings-invalid-data',
                props: {
                    title: t`Invalid form data`,
                    description: t`Please check your input and try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const currentSettings = sharedRiskSettingsController.riskSettings;

        if (!currentSettings) {
            snackbarController.addSnackbar({
                id: 'risk-settings-not-available',
                props: {
                    title: t`Unable to save changes`,
                    description: t`Risk settings are not available. Please refresh the page and try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        try {
            const requestData = this.transformFormData(values, currentSettings);

            this.updateRiskSettingsMutation.mutate({
                body: requestData,
            });
        } catch (error) {
            logger.error({
                message: 'Failed to process form data',
                additionalInfo: {
                    error,
                },
            });

            snackbarController.addSnackbar({
                id: 'risk-settings-processing-error',
                props: {
                    title: t`Failed to process form data`,
                    description: isError(error)
                        ? error.message
                        : t`Please check your input and try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }
    };

    isValidFormData(data: unknown): data is Record<string, unknown> {
        return isObject(data);
    }

    get formSchema() {
        const { riskSettings } = sharedRiskSettingsController;

        return createFormSchema(riskSettings);
    }

    transformFormData(
        formValues: unknown,
        currentSettings: RiskSettingsResponseDto,
    ): RiskSettingsRequestDto {
        if (!this.isValidFormData(formValues)) {
            throw new Error('Invalid form data structure');
        }

        // Extract impact and likelihood from the nested object
        const impactLikelihoodData = formValues.impactLikelihoodDisplay as {
            impact?: { value: string };
            likelihood?: { value: string };
        };

        const impact = impactLikelihoodData.impact?.value
            ? parseInt(impactLikelihoodData.impact.value, 10)
            : currentSettings.impact;

        const likelihood = impactLikelihoodData.likelihood?.value
            ? parseInt(impactLikelihoodData.likelihood.value, 10)
            : currentSettings.likelihood;

        // For now, keep the existing thresholds and level definitions
        // These will be updated when the respective form fields are properly implemented
        return {
            impact,
            likelihood,
            thresholds: currentSettings.thresholds,
            levelDefinitions: currentSettings.levelDefinitions,
        };
    }
}

export const sharedRiskSettingsScoringController =
    new RiskSettingsScoringController();
