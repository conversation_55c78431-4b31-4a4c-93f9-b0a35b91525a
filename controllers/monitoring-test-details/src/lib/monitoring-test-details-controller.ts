import { head, isEmpty, isNil, isString } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedMonitorFindingsController } from '@controllers/monitoring-details';
import { monitorsV2ControllerGetControlTestInstanceOverviewOptions } from '@globals/api-sdk/queries';
import type {
    MonitorInstanceMetadataResponseDto,
    MonitorV2ControlTestInstanceOverviewResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    getMonitorFixNowDestination,
    getPolicyMetaData,
} from './helpers/monitoring-test-details-controller.helpers';

class MonitoringTestDetailsController {
    constructor() {
        makeAutoObservable(this);
    }

    testDetailsQuery = new ObservedQuery(
        monitorsV2ControllerGetControlTestInstanceOverviewOptions,
    );

    get testDetails(): MonitorV2ControlTestInstanceOverviewResponseDto | null {
        return this.testDetailsQuery.data;
    }

    get testName(): string | null {
        return this.testDetails?.name ?? null;
    }

    get category(): string | null {
        return this.testDetails?.checkTypes[0] ?? null;
    }

    get source():
        | MonitorV2ControlTestInstanceOverviewResponseDto['source']
        | undefined {
        return this.testDetails?.source ?? undefined;
    }

    get isLoading(): boolean {
        return this.testDetailsQuery.isLoading;
    }

    get isDraftTest(): boolean {
        return this.testDetails?.draft ?? false;
    }

    get status(): string | null {
        return this.testDetails?.checkStatus ?? null;
    }

    get error(): Error | null {
        return this.testDetailsQuery.error;
    }

    get isCustomTest(): boolean {
        return this.source === 'CUSTOM';
    }

    get checkResultStatus():
        | MonitorV2ControlTestInstanceOverviewResponseDto['checkResultStatus']
        | null {
        return this.testDetails?.checkResultStatus ?? null;
    }

    get getMonitorArticleUrl(): string | null {
        return this.testDetails?.monitorInstances[0]?.url ?? null;
    }

    /**
     * Returns the policy metadata for the first monitor instance.
     */
    get policyMetadata(): {
        policyScope: string | null;
        policyName: string;
        policyGroups: MonitorInstanceMetadataResponseDto['groups'];
    } {
        const monitorInstance = head(this.testDetails?.monitorInstances);
        const policyMetadata = getPolicyMetaData(monitorInstance);

        return policyMetadata;
    }

    get policyGroups(): MonitorInstanceMetadataResponseDto['groups'] {
        return this.policyMetadata.policyGroups;
    }

    get isPolicyScopeNone(): boolean {
        return this.policyMetadata.policyScope === 'NONE';
    }

    get monitorHasPolicyGroups(): boolean {
        return (
            this.policyMetadata.policyScope === 'GROUP' &&
            !isEmpty(this.policyMetadata.policyGroups)
        );
    }

    get helpUrl(): string {
        return this.testDetails?.monitorInstances[0]?.url ?? '';
    }

    get remedyDescription(): string {
        const monitorInstance = head(this.testDetails?.monitorInstances);

        return monitorInstance?.remedyDescription ?? '';
    }

    /**
     * 1.monitor is failing, enabled
     * 2. Monitor doesn't have findings
     * 3. Monitor DOES HAVE a fix now path.
     */
    get isMonitorFailinWithgNoFindingsAndWithFixNowPath(): boolean {
        const destinationProps = getMonitorFixNowDestination({
            testId: this.testDetails?.testId.toString() ?? '',
            policyGroups: this.policyGroups as { id: string }[],
            monitorHasPolicyGroups: this.monitorHasPolicyGroups,
            isPolicyScopeNone: this.isPolicyScopeNone,
        });

        const { failingResources } = sharedMonitorFindingsController;
        const { ticketingConnectionWithWriteAccess } =
            sharedConnectionsController;

        return (
            !isString(destinationProps) &&
            !isNil(destinationProps) &&
            failingResources <= 0 &&
            !isNil(ticketingConnectionWithWriteAccess)
        );
    }

    loadTest = (testId?: number): void => {
        if (isNil(testId)) {
            throw new Error('Test ID is required');
        }

        when(
            () => sharedWorkspacesController.isLoaded,
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                if (!currentWorkspace) {
                    return;
                }

                this.testDetailsQuery.load({
                    path: { testId, workspaceId: currentWorkspace.id },
                });
            },
        );
    };
}

export const sharedMonitoringTestDetailsController =
    new MonitoringTestDetailsController();
