import {
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import {
    riskManagementControllerGetDashboardOptions,
    riskManagementControllerGetRiskCategoriesInfiniteOptions,
    riskManagementControllerGetRisksListOptions,
    riskManagementControllerGetRisksOwnersInfiniteOptions,
    riskManagementControllerGetStatisticsOptions,
} from '@globals/api-sdk/queries';
import type {
    DashboardResponseDto,
    RiskWithCustomFieldsResponseDto,
    StatisticsResponseDto,
} from '@globals/api-sdk/types';
import {
    makeAutoObservable,
    ObservedInfiniteQuery,
    ObservedQuery,
} from '@globals/mobx';

type RiskManagementDashboardQuery = NonNullable<
    NonNullable<
        Parameters<typeof riskManagementControllerGetDashboardOptions>[0]
    >['query']
>;

class RiskManagementController {
    constructor() {
        makeAutoObservable(this);
    }

    riskManagementListQuery = new ObservedQuery(
        riskManagementControllerGetRisksListOptions,
    );

    get isLoading(): boolean {
        return this.riskManagementListQuery.isLoading;
    }

    get total(): number {
        return this.riskManagementListQuery.data?.total ?? 0;
    }

    get risks(): RiskWithCustomFieldsResponseDto[] {
        return this.riskManagementListQuery.data?.data ?? [];
    }

    loadRiskManagement = (params: FetchDataResponseParams) => {
        this.riskManagementListQuery.load({
            query: {
                page: params.pagination.page,
                limit: params.pagination.pageSize,
                q: params.globalFilter.search ?? '',
            },
        });
    };

    riskManagementStatisticsQuery = new ObservedQuery(
        riskManagementControllerGetStatisticsOptions,
    );

    loadStatistics = () => {
        this.riskManagementStatisticsQuery.load();
    };

    get statistics(): StatisticsResponseDto | null {
        return this.riskManagementStatisticsQuery.data;
    }

    riskManagementDashboardQuery = new ObservedQuery(
        riskManagementControllerGetDashboardOptions,
    );

    riskFilter: RiskManagementDashboardQuery['riskFilter'];

    scoreType: RiskManagementDashboardQuery['scoreType'] = 'RESIDUAL';

    loadDashboard = () => {
        this.riskManagementDashboardQuery.load({
            query: {
                'status[]': ['ACTIVE'],
                isScored: true,
                riskFilter: this.riskFilter,
                scoreType: this.scoreType,
            },
        });
    };

    get dashboard(): DashboardResponseDto | null {
        return this.riskManagementDashboardQuery.data;
    }

    risksOwnersInfiniteOptionsQuery = new ObservedInfiniteQuery(
        riskManagementControllerGetRisksOwnersInfiniteOptions,
    );

    get risksOwners() {
        return (
            this.risksOwnersInfiniteOptionsQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    risksOwnersLoadFirstPage = (search?: string): void => {
        this.risksOwnersInfiniteOptionsQuery.load({
            query: {
                page: 1,
                limit: DEFAULT_PAGE_SIZE,
                q: search ?? '',
            },
        });
    };

    risksOwnersLoadNextPage = (): void => {
        if (this.risksOwnersInfiniteOptionsQuery.hasNextPage) {
            this.risksOwnersInfiniteOptionsQuery.nextPage();
        }
    };

    get risksOwnersHasNextPage(): boolean {
        return this.risksOwnersInfiniteOptionsQuery.hasNextPage;
    }

    riskCategoriesInfiniteOptionsQuery = new ObservedInfiniteQuery(
        riskManagementControllerGetRiskCategoriesInfiniteOptions,
    );

    get riskCategories() {
        return (
            this.riskCategoriesInfiniteOptionsQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    riskCategoriesLoadFirstPage = (search?: string): void => {
        this.riskCategoriesInfiniteOptionsQuery.load({
            query: {
                page: 1,
                limit: DEFAULT_PAGE_SIZE,
                q: search ?? '',
            },
        });
    };

    riskCategoriesLoadNextPage = (): void => {
        if (this.riskCategoriesInfiniteOptionsQuery.hasNextPage) {
            this.riskCategoriesInfiniteOptionsQuery.nextPage();
        }
    };

    get riskCategoriesHasNextPage(): boolean {
        return this.riskCategoriesInfiniteOptionsQuery.hasNextPage;
    }
}

export const sharedRiskManagementController = new RiskManagementController();
