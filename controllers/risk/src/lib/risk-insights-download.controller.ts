import type { RefObject } from 'react';
import { snackbarController } from '@controllers/snackbar';
import { riskManagementControllerGetRiskDashboardReportMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    toJS,
    when,
} from '@globals/mobx';
import { sharedSnapdomController } from '@globals/snapdom';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedRiskInsightsScoreTypeModel } from '@models/risk-insights';
import {
    type DownloadSource,
    requestBuilders,
} from './helpers/risk-insights-download.helper';
import { sharedRiskInsightsController } from './insights/risk-insights.controller';

class RiskInsightsDownloadController {
    constructor() {
        makeAutoObservable(this);
    }

    lastPostureRef: RefObject<HTMLElement> | null = null;

    riskInsightsReportMutation = new ObservedMutation(
        riskManagementControllerGetRiskDashboardReportMutation,
    );

    get isDownloadLoading(): boolean {
        return this.riskInsightsReportMutation.isPending;
    }

    get hasError(): boolean {
        return this.riskInsightsReportMutation.hasError;
    }

    downloadAll = () => {
        if (!this.lastPostureRef) {
            return;
        }

        sharedSnapdomController
            .captureElementAsBase64(toJS(this.lastPostureRef))
            .then(() => {
                const { riskInsights } = sharedRiskInsightsController;

                if (!riskInsights) {
                    return;
                }

                if (!sharedSnapdomController.base64Result) {
                    logger.error('Failed to capture element as base64');
                    snackbarController.addSnackbar({
                        id: 'risk-download-error',
                        props: {
                            title: t`Failed to capture image`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const requestBody = {
                    riskPosture: sharedSnapdomController.base64Result,
                    scoreType: sharedRiskInsightsScoreTypeModel.scoreType,
                };

                this.riskInsightsReportMutation.mutate({
                    body: requestBody,
                });

                when(
                    () => !this.riskInsightsReportMutation.isPending,
                    () => {
                        if (!this.riskInsightsReportMutation.hasError) {
                            return;
                        }

                        logger.error(
                            'Failed to download risk insights report from reference',
                        );
                        snackbarController.addSnackbar({
                            id: 'risk-download-error',
                            props: {
                                title: t`Failed to submit report request`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    },
                );
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'risk-download-error',
                    props: {
                        title: t`Failed to download report`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                logger.error('Failed to download Heatmap report');
            });
    };

    downloadRiskInsightsReportFromHtml = (htmlRef: HTMLElement) => {
        sharedSnapdomController
            .captureElementFromHtmlAsBase64(htmlRef)
            .then(() => {
                const { riskInsights } = sharedRiskInsightsController;

                if (!riskInsights) {
                    return;
                }

                if (!sharedSnapdomController.base64Result) {
                    logger.error('Failed to capture element as base64');
                    snackbarController.addSnackbar({
                        id: 'risk-download-error',
                        props: {
                            title: t`Failed to capture image`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const requestBody = {
                    riskPosture: sharedSnapdomController.base64Result,
                    scoreType: sharedRiskInsightsScoreTypeModel.scoreType,
                };

                this.riskInsightsReportMutation.mutate({
                    body: requestBody,
                });

                when(
                    () => !this.riskInsightsReportMutation.isPending,
                    () => {
                        if (!this.riskInsightsReportMutation.hasError) {
                            return;
                        }

                        logger.error(
                            'Failed to download risk insights report from reference',
                        );
                        snackbarController.addSnackbar({
                            id: 'risk-download-error',
                            props: {
                                title: t`Failed to submit report request`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    },
                );
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'risk-download-error',
                    props: {
                        title: t`Failed to download report`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                logger.error('Failed to download Heatmap report');
            });
    };

    downloadRiskInsightsReportFromReference = (
        captureAreaRef: RefObject<HTMLElement>,
    ) => {
        sharedSnapdomController
            .captureElementAsBase64(captureAreaRef)
            .then(() => {
                const { riskInsights } = sharedRiskInsightsController;

                if (!riskInsights) {
                    return;
                }

                if (!sharedSnapdomController.base64Result) {
                    logger.error('Failed to capture element as base64');
                    snackbarController.addSnackbar({
                        id: 'risk-download-error',
                        props: {
                            title: t`Failed to capture image`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                const requestBody = {
                    riskPosture: sharedSnapdomController.base64Result,
                    scoreType: sharedRiskInsightsScoreTypeModel.scoreType,
                };

                this.riskInsightsReportMutation.mutate({
                    body: requestBody,
                });

                when(
                    () => !this.riskInsightsReportMutation.isPending,
                    () => {
                        if (!this.riskInsightsReportMutation.hasError) {
                            return;
                        }

                        logger.error(
                            'Failed to download risk insights report from reference',
                        );
                        snackbarController.addSnackbar({
                            id: 'risk-download-error',
                            props: {
                                title: t`Failed to submit report request`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    },
                );
            })
            .catch(() => {
                snackbarController.addSnackbar({
                    id: 'risk-download-error',
                    props: {
                        title: t`Failed to download report`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                logger.error('Failed to download Heatmap report');
            });
    };

    downloadRiskInsightsReport = (source: DownloadSource) => {
        const { currentWorkspace } = sharedWorkspacesController;
        const { riskInsights } = sharedRiskInsightsController;

        if (!currentWorkspace || !riskInsights) {
            return;
        }

        const builder = requestBuilders[source];

        const requestBody = builder(toJS(riskInsights));

        this.riskInsightsReportMutation.mutate({
            body: requestBody,
        });

        when(
            () => !this.isDownloadLoading,
            () => {
                if (this.hasError) {
                    snackbarController.addSnackbar({
                        id: 'risk-download-error',
                        props: {
                            title: t`Failed to download report`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    };
}

export const sharedRiskInsightsDownloadController =
    new RiskInsightsDownloadController();
