import { isString } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    riskManagementControllerCreateCategoryMutation,
    riskManagementControllerDeleteRiskCategoriesMutation,
    riskManagementControllerGetRiskCategoriesInfiniteOptions,
} from '@globals/api-sdk/queries';
import type {
    RiskCategoryResponseDto,
    RiskManagementControllerGetRiskCategoriesData,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedInfiniteQuery,
    ObservedMutation,
    runInAction,
} from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { MAX_CATEGORY_NAME_LENGTH } from '../constants/risk-categories.constants';

export class RiskCategoriesController {
    _pendingCategory: {
        name: string;
        onSuccess?: (newCategory: ListBoxItemData) => void;
    } | null = null;

    newCategoryName = '';
    validationError: string | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    categoriesInfiniteQuery = new ObservedInfiniteQuery(
        riskManagementControllerGetRiskCategoriesInfiniteOptions,
    );

    createCategoryMutation = new ObservedMutation(
        riskManagementControllerCreateCategoryMutation,
        {
            onSuccess: (newCategory) => {
                // Invalidate the query to refresh data
                this.categoriesInfiniteQuery.invalidate();

                // Call the pending callback with the new category
                if (this._pendingCategory?.onSuccess) {
                    const categoryOption = {
                        id: String(newCategory.id),
                        label: newCategory.name,
                        value: String(newCategory.id),
                    };

                    this._pendingCategory.onSuccess(categoryOption);
                    this._pendingCategory = null;
                }

                // Clear form state and show success message
                runInAction(() => {
                    this.newCategoryName = '';
                    this.validationError = null;
                });

                snackbarController.addSnackbar({
                    id: 'risk-category-created',
                    props: {
                        title: t`Category created`,
                        description: t`The new risk category has been created successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'category-api-error',
                    props: {
                        title: t`Error creating category`,
                        description: t`Failed to create the category. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    deleteCategoriesMutation = new ObservedMutation(
        riskManagementControllerDeleteRiskCategoriesMutation,
        {
            onSuccess: () => {
                this.categoriesInfiniteQuery.invalidate();

                snackbarController.addSnackbar({
                    id: 'risk-category-deleted',
                    props: {
                        title: t`Category deleted`,
                        description: t`The risk category has been deleted successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'delete-category-error',
                    props: {
                        title: t`Error deleting category`,
                        description: t`Failed to delete the category. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    get hasNextPage(): boolean {
        return this.categoriesInfiniteQuery.hasNextPage;
    }

    get categories(): RiskCategoryResponseDto[] {
        return (
            this.categoriesInfiniteQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get hasError(): boolean {
        return this.categoriesInfiniteQuery.hasError;
    }

    get isLoading(): boolean {
        return this.categoriesInfiniteQuery.isLoading;
    }

    get isFetching(): boolean {
        return this.categoriesInfiniteQuery.isFetching;
    }

    loadNextPage = (): void => {
        this.categoriesInfiniteQuery.nextPage();
    };

    get requirementCategoriesOptions(): ListBoxItemData[] {
        // Transform API categories data into ListBoxItemData format
        return this.categories.map((category) => ({
            id: String(category.id),
            label: category.name,
            value: String(category.id),
        }));
    }

    get totalCount(): number {
        // Get the total count from the first page of the API response
        const firstPage = this.categoriesInfiniteQuery.data?.pages[0];

        return firstPage?.total ?? 0;
    }

    loadCategories = (searchTerm?: string): void => {
        const query: RiskManagementControllerGetRiskCategoriesData['query'] = {
            page: 1,
        };

        if (searchTerm) {
            query.q = searchTerm;
        }

        this.categoriesInfiniteQuery.load({
            query,
        });
    };

    onFetchCategories = ({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    } = {}): void => {
        if (increasePage) {
            this.loadNextPage();

            return;
        }
        this.loadCategories(search?.trim());
    };

    initialize = (): void => {
        // Load categories if not already loaded
        if (!this.categoriesInfiniteQuery.data) {
            this.loadCategories();
        }
    };

    addCategory = (
        categoryName: string,
        onSuccess?: (newCategory: ListBoxItemData) => void,
    ): void => {
        const existingCategory = this.requirementCategoriesOptions.find(
            (option) => {
                if (!isString(option.label) || !isString(categoryName)) {
                    return false;
                }

                return (
                    option.label
                        .toLowerCase()
                        .trim()
                        .localeCompare(categoryName.toLowerCase().trim()) === 0
                );
            },
        );

        if (existingCategory) {
            // Category already exists, call success callback immediately
            onSuccess?.(existingCategory);

            return;
        }

        // Store the pending callback
        this._pendingCategory = { name: categoryName, onSuccess };

        // Create the category via API
        this.createCategoryMutation.mutate({
            body: { name: categoryName },
        });
    };

    get isCreatingCategory(): boolean {
        return this.createCategoryMutation.isPending;
    }

    get createCategoryError(): Error | null {
        return this.createCategoryMutation.error;
    }

    get isDeletingCategory(): boolean {
        return this.deleteCategoriesMutation.isPending;
    }

    get deleteCategoryError(): Error | null {
        return this.deleteCategoriesMutation.error;
    }

    validateCategoryName = (name: string): string | null => {
        if (!name.trim()) {
            return t`Category name is required`;
        }

        if (name.length > MAX_CATEGORY_NAME_LENGTH) {
            return t`Category name must be ${MAX_CATEGORY_NAME_LENGTH} characters or less`;
        }

        return null;
    };

    updateNewCategoryName = (name: string): void => {
        runInAction(() => {
            this.newCategoryName = name;
            // Clear validation error when user starts typing
            if (this.validationError) {
                this.validationError = null;
            }
        });
    };

    addCategoryWithValidation = (): void => {
        runInAction(() => {
            this.validationError = this.validateCategoryName(
                this.newCategoryName,
            );
        });

        if (this.validationError) {
            return;
        }

        this.addCategory(this.newCategoryName.trim());
    };

    deleteCategory = (categoryId: string): void => {
        const categoryIdNumber = Number(categoryId);

        if (isNaN(categoryIdNumber)) {
            snackbarController.addSnackbar({
                id: 'invalid-category-id',
                props: {
                    title: t`Invalid category`,
                    description: t`Unable to delete category. Invalid category ID.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.deleteCategoriesMutation.mutate({
            body: { ids: [categoryIdNumber] },
        });
    };

    deleteCategoryWithConfirmation = (
        categoryId: string,
        categoryName: string,
    ): void => {
        openConfirmationModal({
            title: t`Delete category`,
            body: t`Are you sure you want to delete "${categoryName}"? This action cannot be undone.`,
            confirmText: t`Delete`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: () => {
                this.deleteCategory(categoryId);
                closeConfirmationModal();
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };
}

export const sharedRiskCategoriesController = new RiskCategoriesController();
