import { sharedAuditHubController } from '@controllers/audit-hub';
import { snackbarController } from '@controllers/snackbar';
import { auditorControllerPutAuditorFrameworkTimeframeMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export class ChangeAuditPeriodController {
    updateTimeframeMutation = new ObservedMutation(
        auditorControllerPutAuditorFrameworkTimeframeMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    updateAuditPeriod = async (
        auditId: string,
        startDate: string | null,
        endDate: string | null,
        onSuccess?: () => void,
    ): Promise<void> => {
        if (!startDate || !endDate) {
            snackbarController.addSnackbar({
                id: 'audit-period-validation-error',
                props: {
                    title: t`Please select both start and end dates`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        if (!workspaceId) {
            logger.warn({
                additionalInfo: {
                    auditId,
                    startDate,
                    endDate,
                },
                message:
                    'Failed to update audit period. Workspace ID is missing',
            });

            snackbarController.addSnackbar({
                id: 'audit-period-workspace-error',
                props: {
                    title: t`Failed to update audit period`,
                    description: t`An error occurred while updating the audit period. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        try {
            await this.updateTimeframeMutation.mutateAsync({
                path: { auditorFrameworkId: auditId },
                body: {
                    startDate,
                    endDate,
                    workspaceId,
                },
            });

            snackbarController.addSnackbar({
                id: 'audit-period-update-success',
                props: {
                    title: t`Audit period updated successfully`,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            sharedAuditHubController.auditByIdQuery.invalidate();
            onSuccess?.();
        } catch (error) {
            snackbarController.addSnackbar({
                id: 'audit-period-update-error',
                props: {
                    title: t`Failed to update audit period`,
                    description: t`An error occurred while updating the audit period. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            logger.error({
                message: 'Failed to update audit period',
                additionalInfo: { auditId, startDate, endDate, error },
            });
        }
    };

    get isUpdating(): boolean {
        return this.updateTimeframeMutation.isPending;
    }
}

export const sharedChangeAuditPeriodController =
    new ChangeAuditPeriodController();
