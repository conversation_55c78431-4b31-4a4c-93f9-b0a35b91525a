import { sharedControlsInfiniteListController } from '@controllers/controls';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Truncation } from '@cosmos-lab/components/truncation';
import { auditorFrameworkControllerGetControlsByAuditFrameworkOptions } from '@globals/api-sdk/queries';
import type {
    AuditFrameworkControlResponseDto,
    AuditorFrameworkControllerGetControlsByAuditFrameworkData,
    ControlListResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export interface ControlListBoxItemData extends ListBoxItemData {
    controlData:
        | Partial<ControlListResponseDto>
        | AuditFrameworkControlResponseDto;
}

class LinkControlsController {
    #lastSearchQuery = '';
    #customWorkspaceId: number | null = null;
    #excludedControlIds: number[] = [];
    #excludeTestId: number | undefined = undefined;
    selectedControls: (
        | Partial<ControlListResponseDto>
        | AuditFrameworkControlResponseDto
    )[] = [];
    selectedListItems: Partial<ControlListBoxItemData>[] = [];

    /**
     * Audit framework controls query.
     */
    auditFrameworkControlsQuery = new ObservedQuery(
        auditorFrameworkControllerGetControlsByAuditFrameworkOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get controlOptions(): ControlListBoxItemData[] {
        return sharedControlsInfiniteListController.controlsInfiniteList.map(
            (control) => {
                return {
                    id: String(control.id),
                    label: control.name,
                    description: (
                        <Truncation lineClamp={2}>
                            {control.description}
                        </Truncation>
                    ),
                    value: String(control.id),
                    badge: control.code,
                    controlData: control,
                };
            },
        );
    }

    get isLoading(): boolean {
        return sharedControlsInfiniteListController.isLoading;
    }

    get hasNextPage(): boolean {
        return sharedControlsInfiniteListController.hasNextPage;
    }

    loadControls = (): void => {
        // Reset custom workspace when using default loading
        this.#customWorkspaceId = null;

        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        const excludeIds = [
            ...this.selectedControls.map((control) => control.id),
            ...this.#excludedControlIds,
        ];

        sharedControlsInfiniteListController.controlsListInfiniteQuery.load({
            path: {
                xProductId: currentWorkspace.id,
            },
            query: {
                'excludeIds[]': excludeIds as number[],
                isArchived: false,
                q: this.#lastSearchQuery,
                excludeTestId: this.#excludeTestId,
            },
        });
    };

    loadControlsWithWorkspace = (workspaceId: number): void => {
        this.#customWorkspaceId = workspaceId;

        const excludeIds = [
            ...this.selectedControls.map((control) => control.id),
            ...this.#excludedControlIds,
        ];

        sharedControlsInfiniteListController.controlsListInfiniteQuery.load({
            path: {
                xProductId: workspaceId,
            },
            query: {
                'excludeIds[]': excludeIds as number[],
                isArchived: false,
                q: this.#lastSearchQuery,
            },
        });
    };

    loadNextPage = ({ search }: { search?: string }): void => {
        if (search !== this.#lastSearchQuery) {
            sharedControlsInfiniteListController.controlsListInfiniteQuery.unload();
            this.#lastSearchQuery = search ?? '';

            // Handle search for audit framework controls
            if (this.#auditFrameworkParams) {
                this.loadControlsWithAuditFramework(
                    this.#auditFrameworkParams.auditId,
                    this.#auditFrameworkParams.productId,
                    this.#auditFrameworkParams.excludeRequestId,
                );
            }
            // Use custom workspace if available, otherwise use default loading
            else if (this.#customWorkspaceId) {
                this.loadControlsWithWorkspace(this.#customWorkspaceId);
            } else {
                this.loadControls();
            }

            return;
        }

        // For audit framework controls, we don't have pagination yet, so just return
        if (this.#auditFrameworkParams) {
            return;
        }

        sharedControlsInfiniteListController.controlsListInfiniteQuery.nextPage();
    };

    updateSelectedItems = (values: ControlListBoxItemData[]): void => {
        const uniqueValuesMap = new Map(
            values.map((item) => [item.controlData.id, item]),
        );

        this.selectedListItems = [...uniqueValuesMap.values()];
    };

    updateSelectedControls = (
        values: Partial<ControlListResponseDto>[],
    ): void => {
        const uniqueValuesMap = new Map(values.map((item) => [item.id, item]));

        this.selectedControls = [
            ...uniqueValuesMap.values(),
        ] as Partial<ControlListResponseDto>[];
    };

    addControls = (): void => {
        const controlsToAdd = this.selectedListItems.map(
            (item) => item.controlData,
        );

        this.selectedControls = [
            ...this.selectedControls,
            ...controlsToAdd,
        ] as Partial<ControlListResponseDto>[];
    };

    removeControl = (controlId: number): void => {
        this.selectedControls = this.selectedControls.filter(
            (control) => control.id !== controlId,
        );
    };

    setExcludedControlIds = (excludedIds: number[]): void => {
        this.#excludedControlIds = excludedIds;
    };

    setExcludeTestId = (excludeTestId: number): void => {
        this.#excludeTestId = excludeTestId;
    };

    clearSelectedItems = (): void => {
        this.selectedListItems = [];
        this.#customWorkspaceId = null;
        this.#excludedControlIds = [];
        // Clear audit framework parameters when clearing items
        this.#auditFrameworkParams = null;
    };

    removeAllControls = (): void => {
        this.selectedControls = [];
    };

    loadControlsWithAuditFramework = (
        auditId: string,
        productId: number,
        excludeRequestId?: number,
    ): void => {
        type Query = NonNullable<
            AuditorFrameworkControllerGetControlsByAuditFrameworkData['query']
        >;

        const query: Query = {
            productId,
            q: this.#lastSearchQuery || undefined,
        };

        if (excludeRequestId) {
            query.excludeRequestId = excludeRequestId;
        }

        this.auditFrameworkControlsQuery.load({
            path: {
                auditId,
            },
            query,
        });
    };

    get auditFrameworkControlOptions(): ControlListBoxItemData[] {
        const controls = this.auditFrameworkControlsQuery.data?.data ?? [];

        return controls.map((control: AuditFrameworkControlResponseDto) => {
            const isSelected = this.selectedControls.some(
                (selectedControl) => selectedControl.id === control.id,
            );

            return {
                id: control.id.toString(),
                label: control.name,
                description: (
                    <Truncation lineClamp={2}>{control.description}</Truncation>
                ),
                controlData: control,
                isSelected,
            };
        });
    }

    get isAuditFrameworkControlsLoading(): boolean {
        return this.auditFrameworkControlsQuery.isLoading;
    }

    get hasAuditFrameworkNextPage(): boolean {
        // For now, audit framework controls don't support pagination
        // This can be enhanced later if the API supports it
        return false;
    }

    /**
     * Store audit framework parameters for search functionality.
     */
    #auditFrameworkParams: {
        auditId: string;
        productId: number;
        excludeRequestId?: number;
    } | null = null;

    setAuditFrameworkParams = (
        auditId: string,
        productId: number,
        excludeRequestId?: number,
    ): void => {
        this.#auditFrameworkParams = { auditId, productId, excludeRequestId };
    };

    searchAuditFrameworkControls = (searchQuery: string): void => {
        this.#lastSearchQuery = searchQuery;
        if (this.#auditFrameworkParams) {
            this.loadControlsWithAuditFramework(
                this.#auditFrameworkParams.auditId,
                this.#auditFrameworkParams.productId,
                this.#auditFrameworkParams.excludeRequestId,
            );
        }
    };
}

export const sharedLinkControlsController = new LinkControlsController();
