import { sharedRiskInsightsController } from '@controllers/risk';
import { makeAutoObservable, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    calculatePreviousDate,
    getMonthsForPeriod,
} from './helpers/period-calculations.helper';
import {
    extractAvailableRiskLevels,
    sortAndLimitDataPoints,
    transformRiskOverTimeData,
} from './helpers/risk-data-transformer.helpers';
import type {
    PeriodValue,
    RiskOverTimeData,
} from './risk-insights-over-time.types';

class RiskInsightsOverTimeCrontroller {
    constructor() {
        makeAutoObservable(this);
    }

    timePeriod: PeriodValue = '6months';

    get data(): RiskOverTimeData | null {
        const dashboardData = sharedRiskInsightsController.riskInsights;

        if (!dashboardData) {
            return null;
        }

        const availableRiskLevels = extractAvailableRiskLevels(dashboardData);
        const transformedData = transformRiskOverTimeData(
            dashboardData,
            availableRiskLevels,
        );
        const sortedData = sortAndLimitDataPoints(
            transformedData,
            this.timePeriod,
        );

        return {
            data: sortedData,
            period: this.timePeriod,
        };
    }

    get period(): PeriodValue {
        return this.timePeriod;
    }

    get availableRiskLevels(): string[] {
        const dashboardData = sharedRiskInsightsController.riskInsights;

        return dashboardData ? extractAvailableRiskLevels(dashboardData) : [];
    }

    initialize = (): void => {
        when(
            () => sharedWorkspacesController.isReady,
            () => {
                this.load();
            },
        );
    };

    setPeriod = (period: PeriodValue) => {
        this.timePeriod = period;
        this.load();
    };

    load = () => {
        if (!sharedWorkspacesController.currentWorkspace?.id) {
            return;
        }

        const previousDate = this.calculatePreviousDate(this.timePeriod);

        sharedRiskInsightsController.load({
            isScored: true,
            previousDate,
        });
    };

    calculatePreviousDate = (period: PeriodValue): string => {
        return calculatePreviousDate(period);
    };

    getMonthsForPeriod = (): string[] => {
        return getMonthsForPeriod(this.timePeriod);
    };
}

export const sharedRiskInsightsOverTimeController =
    new RiskInsightsOverTimeCrontroller();
