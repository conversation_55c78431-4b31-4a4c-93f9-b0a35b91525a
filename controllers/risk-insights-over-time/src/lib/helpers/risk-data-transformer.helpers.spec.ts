import { describe, expect, test } from 'vitest';
import type { DashboardResponseDto } from '@globals/api-sdk/types';
import {
    extractAvailableRiskLevels,
    sortAndLimitDataPoints,
    transformRiskOverTimeData,
} from './risk-data-transformer.helpers';

const createMockDashboardData = (
    overrides: Partial<DashboardResponseDto> = {},
): DashboardResponseDto => ({
    riskPosture: {},
    riskOverTime: {},
    treatmentOverview: {},
    riskHeatmap: [],
    categoryBreakdown: [],
    scored: 0,
    remaining: 0,
    ...overrides,
});

describe('risk data transformer helpers', () => {
    describe('extractAvailableRiskLevels', () => {
        test('returns empty array when no risk data exists', () => {
            const dashboardData = createMockDashboardData();

            const result = extractAvailableRiskLevels(dashboardData);

            expect(result).toStrictEqual([]);
        });

        test('extracts risk levels from riskPosture only', () => {
            const dashboardData = createMockDashboardData({
                riskPosture: {
                    HIGH_ID_123: 5,
                    MEDIUM_ID_456: 3,
                },
            });

            const result = extractAvailableRiskLevels(dashboardData);

            expect(result).toStrictEqual(['high', 'medium']);
        });

        test('extracts risk levels from riskOverTime only', () => {
            const dashboardData = createMockDashboardData({
                riskOverTime: {
                    '2024': {
                        '1': {
                            CRITICAL_ID_789: 2,
                            LOW_ID_101: 1,
                        },
                    },
                },
            });

            const result = extractAvailableRiskLevels(dashboardData);

            expect(result).toStrictEqual(['critical', 'low']);
        });

        test('combines and deduplicates risk levels from both sources', () => {
            const dashboardData = createMockDashboardData({
                riskPosture: {
                    HIGH_ID_123: 5,
                    MEDIUM_ID_456: 3,
                },
                riskOverTime: {
                    '2024': {
                        '1': {
                            HIGH_ID_789: 2,
                            CRITICAL_ID_101: 1,
                        },
                    },
                },
            });

            const result = extractAvailableRiskLevels(dashboardData);

            expect(result).toStrictEqual(['critical', 'high', 'medium']);
        });

        test('ignores invalid key formats', () => {
            const dashboardData = createMockDashboardData({
                riskPosture: {
                    HIGH_ID_123: 5,
                    INVALID_KEY: 3,
                    MEDIUM_ID_: 2,
                    _ID_456: 1,
                },
            });

            const result = extractAvailableRiskLevels(dashboardData);

            expect(result).toStrictEqual(['high']);
        });
    });

    describe('transformRiskOverTimeData', () => {
        test('returns empty array when riskOverTime is empty', () => {
            const dashboardData = createMockDashboardData();
            const availableRiskLevels = ['high', 'medium'];

            const result = transformRiskOverTimeData(
                dashboardData,
                availableRiskLevels,
            );

            expect(result).toStrictEqual([]);
        });

        test('transforms risk over time data correctly', () => {
            const dashboardData = createMockDashboardData({
                riskOverTime: {
                    '2024': {
                        '1': {
                            HIGH_ID_123: 5,
                            HIGH_ID_456: 3,
                            MEDIUM_ID_789: 2,
                        },
                    },
                },
            });
            const availableRiskLevels = ['high', 'medium', 'low'];

            const result = transformRiskOverTimeData(
                dashboardData,
                availableRiskLevels,
            );

            expect(result).toStrictEqual([
                {
                    month: 'Jan',
                    year: 2024,
                    high: 8,
                    medium: 2,
                    low: 0,
                },
            ]);
        });

        test('handles multiple months and years', () => {
            const dashboardData = createMockDashboardData({
                riskOverTime: {
                    '2023': {
                        '12': {
                            HIGH_ID_123: 3,
                        },
                    },
                    '2024': {
                        '1': {
                            MEDIUM_ID_456: 2,
                        },
                    },
                },
            });
            const availableRiskLevels = ['high', 'medium'];

            const result = transformRiskOverTimeData(
                dashboardData,
                availableRiskLevels,
            );

            expect(result).toStrictEqual([
                {
                    month: 'Dec',
                    year: 2023,
                    high: 3,
                    medium: 0,
                },
                {
                    month: 'Jan',
                    year: 2024,
                    high: 0,
                    medium: 2,
                },
            ]);
        });
    });

    describe('sortAndLimitDataPoints', () => {
        test('sorts data points chronologically', () => {
            const dataPoints = [
                { month: 'Mar', year: 2024, high: 1 },
                { month: 'Jan', year: 2024, high: 2 },
                { month: 'Dec', year: 2023, high: 3 },
            ];

            const result = sortAndLimitDataPoints(dataPoints, '1year');

            expect(result).toStrictEqual([
                { month: 'Dec', year: 2023, high: 3 },
                { month: 'Jan', year: 2024, high: 2 },
                { month: 'Mar', year: 2024, high: 1 },
            ]);
        });

        test('limits data points to specified period', () => {
            const dataPoints = [
                { month: 'Jan', year: 2024, high: 1 },
                { month: 'Feb', year: 2024, high: 2 },
                { month: 'Mar', year: 2024, high: 3 },
                { month: 'Apr', year: 2024, high: 4 },
                { month: 'May', year: 2024, high: 5 },
                { month: 'Jun', year: 2024, high: 6 },
                { month: 'Jul', year: 2024, high: 7 },
            ];

            const result = sortAndLimitDataPoints(dataPoints, '3months');

            expect(result).toStrictEqual([
                { month: 'Apr', year: 2024, high: 4 },
                { month: 'May', year: 2024, high: 5 },
                { month: 'Jun', year: 2024, high: 6 },
                { month: 'Jul', year: 2024, high: 7 },
            ]);
        });
    });
});
