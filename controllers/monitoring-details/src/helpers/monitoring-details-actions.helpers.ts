import { sharedMonitoringDeleteTestMutationController } from '@controllers/monitoring-details';
import type { Action } from '@cosmos/components/action-stack';
import type { ColorScheme } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { MONITORING_DETAILS_ACTION_IDS } from '../constants/monitoring-details-actions.constants';

const createDeleteConfirmationModal = (type: 'draft' | 'published') => {
    const isDraft = type === 'draft';

    openConfirmationModal({
        title: t`Are you sure?`,
        body: isDraft
            ? t`You can not recover this draft once it is deleted.`
            : t`You can not recover this test once it is deleted. If this published test is linked to a draft version, the linked draft will also be deleted. This action can not be undone.`,
        confirmText: isDraft ? t`Yes, delete draft` : t`Yes, delete test`,
        cancelText: t`No, take me back`,
        type: 'danger',
        onConfirm: () => {
            sharedMonitoringDeleteTestMutationController.deleteTest();
        },
        onCancel: closeConfirmationModal,
        isLoading: () =>
            sharedMonitoringDeleteTestMutationController.isDeleting,
    });
};

const onSelectDeletePublished = () => {
    createDeleteConfirmationModal('published');
};

const onSelectDeleteDraft = () => {
    createDeleteConfirmationModal('draft');
};

export const createHorizontalMenuItems = (): Record<
    string,
    {
        id: string;
        label: string;
        colorScheme?: ColorScheme;
        onSelect: () => void;
    }
> => ({
    publishDraft: {
        id: MONITORING_DETAILS_ACTION_IDS.PUBLISH_DRAFT_OPTION,
        label: t`Publish draft`,
        onSelect: () => {
            // TODO: Implement publish draft logic
        },
    },
    deleteDraft: {
        id: MONITORING_DETAILS_ACTION_IDS.DELETE_DRAFT_OPTION,
        label: t`Delete draft`,
        colorScheme: 'critical' as ColorScheme,
        onSelect: onSelectDeleteDraft,
    },
    deleteTest: {
        id: MONITORING_DETAILS_ACTION_IDS.DELETE_TEST_OPTION,
        label: t`Delete test`,
        colorScheme: 'critical' as ColorScheme,
        onSelect: onSelectDeletePublished,
    },
    goToEvidence: {
        id: MONITORING_DETAILS_ACTION_IDS.GO_TO_EVIDENCE_OPTION,
        label: t`Go to evidence`,
        onSelect: () => {
            // TODO: Implement go to evidence logic
        },
    },
});

export const createPageHeaderItems = (): Record<string, Action> => ({
    publishDraft: {
        actionType: 'button',
        id: MONITORING_DETAILS_ACTION_IDS.PUBLISH_DRAFT_BUTTON,
        typeProps: {
            label: t`Publish draft`,
            level: 'tertiary',
            onClick: () => {
                // TODO: Implement publish draft logic
            },
        },
    },
    testNow: {
        actionType: 'button',
        id: MONITORING_DETAILS_ACTION_IDS.TEST_NOW_BUTTON,
        typeProps: {
            label: t`Test now`,
            level: 'tertiary',
            onClick: () => {
                // TODO: Implement test now logic
            },
        },
    },
    deleteTest: {
        actionType: 'button',
        id: MONITORING_DETAILS_ACTION_IDS.DELETE_TEST_BUTTON,
        typeProps: {
            label: t`Delete test`,
            level: 'tertiary',
            colorScheme: 'danger',
            onClick: () => {
                // TODO: Implement delete test logic
            },
        },
    },
    goToEvidence: {
        actionType: 'button',
        id: MONITORING_DETAILS_ACTION_IDS.GO_TO_EVIDENCE_BUTTON,
        typeProps: {
            label: t`Go to evidence`,
            level: 'tertiary',
            onClick: () => {
                // TODO: Implement go to evidence logic
            },
        },
    },
});
