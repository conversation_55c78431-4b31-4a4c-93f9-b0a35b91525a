import {
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { monitorsV2ControllerGetMonitorControlsOptions } from '@globals/api-sdk/queries';
import type { ControlMonitorResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { activeMonitoringController } from './monitoring-controller';

class MonitoringDetailsControlsController {
    constructor() {
        makeAutoObservable(this);
    }

    monitoringDetailsControls = new ObservedQuery(
        monitorsV2ControllerGetMonitorControlsOptions,
    );

    get monitoringDetailsControlsData(): ControlMonitorResponseDto[] {
        return this.monitoringDetailsControls.data?.data ?? [];
    }

    get isLoading(): boolean {
        return (
            this.monitoringDetailsControls.isLoading ||
            this.monitoringDetailsControls.isFetching
        );
    }

    get monitoringDetailsControlsTotal(): number {
        return this.monitoringDetailsControls.data?.total || 0;
    }

    invalidate = () => {
        this.monitoringDetailsControls.invalidate();
    };

    loadMonitoringDetailsControls = ({
        params,
        monitorTestId,
    }: {
        params?: FetchDataResponseParams;
        monitorTestId?: number;
    } = {}) => {
        const { monitor } = activeMonitoringController;

        const testId = monitorTestId ?? monitor?.testId;

        if (testId === undefined) {
            return;
        }

        const query = {
            page: params?.pagination.page ?? 1,
            limit: params?.pagination.pageSize ?? DEFAULT_PAGE_SIZE,
        };

        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace),
            () => {
                const workspaceId = sharedWorkspacesController.currentWorkspace
                    ?.id as number;

                this.monitoringDetailsControls.load({
                    query,
                    path: {
                        testId,
                        workspaceId,
                    },
                });
            },
        );
    };
}

export const sharedMonitoringDetailsControlsController =
    new MonitoringDetailsControlsController();
