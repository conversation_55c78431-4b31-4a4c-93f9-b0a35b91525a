import {
    sharedControlsDetailsStatsController,
    sharedControlsInfiniteListController,
} from '@controllers/controls';
import {
    sharedMonitorsController,
    sharedMonitorsInfiniteController,
} from '@controllers/monitors';
import { snackbarController } from '@controllers/snackbar';
import { grcControllerBulkPutControlTestsMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedMonitoringControlTestComparisonController } from './monitoring-control-test-comparsion-controller';
import { sharedMonitoringDetailsControlsController } from './monitoring-details-controls-controller';

class MonitoringMapTestsMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    mapTestsMutation = new ObservedMutation(
        grcControllerBulkPutControlTestsMutation,
        {
            onSuccess: () => {
                sharedMonitoringControlTestComparisonController.invalidate();
                sharedControlsDetailsStatsController.invalidate();
                sharedMonitorsController.invalidate();
                sharedMonitorsInfiniteController.clearSelectedMonitors();
                sharedMonitoringDetailsControlsController.invalidate();
                sharedControlsInfiniteListController.remove();

                snackbarController.addSnackbar({
                    id: 'map-tests-success',
                    props: {
                        title: t`Successfully mapped test(s) to control(s)`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'map-tests-error',
                    props: {
                        title: t`Failed to map test(s) to control(s)`,
                        description: t`An error occurred while mapping test(s) to control(s). Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    get isMapping(): boolean {
        return this.mapTestsMutation.isPending;
    }

    get hasError(): boolean {
        return this.mapTestsMutation.hasError;
    }

    mapTestsToControl = async (
        controlIds: number[],
        testIds: number[],
    ): Promise<void> => {
        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        await this.mapTestsMutation.mutateAsync({
            path: { xProductId: workspaceId as number },
            body: { testIds, controlIds },
        });
    };
}

export const sharedMonitoringMapTestsMutationController =
    new MonitoringMapTestsMutationController();
