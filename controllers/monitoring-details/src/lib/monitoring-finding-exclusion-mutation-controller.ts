import { isEmpty } from 'lodash-es';
import type { FindingForBulkExclusion } from '@components/monitoring-bulk-exclusion-modal';
import { closeMonitoringRemoveExclusionConfirmationModal } from '@components/monitoring-remove-exclusions-confirmation-modal';
import { snackbarController } from '@controllers/snackbar';
import {
    monitorExclusionsBulkControllerBulkReincludeMonitorInstanceExclusionsMutation,
    monitorsControllerCreateMonitorExclusionMutation,
} from '@globals/api-sdk/queries';
import type {
    FindingItemResponseDto,
    MonitorInstanceExclusionsRequestDto,
    MonitorV2FindingResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedMonitoringDetailsExclusionsController } from './monitoring-details-exclusions';
import { sharedFindingsController } from './monitoring-finding-controller';

class FindingExclusionMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    createExclusionMutation = new ObservedMutation(
        monitorsControllerCreateMonitorExclusionMutation,
    );

    removeExclusionsBulkMutation = new ObservedMutation(
        monitorExclusionsBulkControllerBulkReincludeMonitorInstanceExclusionsMutation,
    );

    get isRemovingExclusionsBulk(): boolean {
        return this.removeExclusionsBulkMutation.isPending;
    }

    get hasRemoveExclusionsBulkError(): boolean {
        return this.removeExclusionsBulkMutation.hasError;
    }

    createExclusion = (
        finding: MonitorV2FindingResponseDto,
        reason: string,
    ): void => {
        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        if (!workspaceId) {
            return;
        }

        const { metadata } = finding;
        /**
         * This is the regionalId.
         */
        const targetId = metadata.id as string;
        /**
         * This is the resource name.
         */
        const targetName = metadata.name as string;

        if (!targetId || !targetName) {
            logger.error({
                message: 'Failed to exclude finding. Missing required data',
                additionalInfo: {
                    targetId,
                    targetName,
                },
            });

            return;
        }

        const { connectionId } = finding;

        const requestBody: MonitorInstanceExclusionsRequestDto = {
            monitorInstanceExclusionsByConnection: [
                {
                    connectionId,
                    monitorInstanceExclusions: [
                        {
                            targetId,
                            targetName,
                        },
                    ],
                },
            ],
            exclusionReason: reason,
        };

        const { testId } = finding;

        this.createExclusionMutation.mutate({
            body: requestBody,
            path: {
                testId,
                xProductId: workspaceId,
            },
        });

        when(
            () => !this.createExclusionMutation.isPending,
            () => {
                if (this.createExclusionMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'finding-excluded-error',
                        props: {
                            title: t`Failed to exclude finding`,
                            description: t`Please try again`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else if (this.createExclusionMutation.response) {
                    snackbarController.addSnackbar({
                        id: 'finding-excluded-success',
                        hasTimeout: true,
                        props: {
                            title: t`Finding excluded successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    sharedFindingsController.findingsListQuery.invalidate();
                }
            },
        );
    };

    createBulkExclusion = (
        findings: FindingForBulkExclusion[],
        reason: string,
        onSuccess?: () => void,
    ): void => {
        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        if (!workspaceId) {
            return;
        }

        if (isEmpty(findings)) {
            snackbarController.addSnackbar({
                id: 'bulk-exclusion-no-findings',
                props: {
                    title: t`No findings selected`,
                    description: t`Please select findings to exclude`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const groupedFindings: Record<
            string,
            {
                testId: number;
                connectionId: number;
                findings: {
                    findingId: string;
                    testId: number;
                    connectionId: number;
                    metadata: FindingItemResponseDto;
                }[];
            }
        > = {};

        for (const finding of findings) {
            const key = `${finding.testId}-${finding.connectionId}`;

            if (!(key in groupedFindings)) {
                groupedFindings[key] = {
                    testId: finding.testId,
                    connectionId: finding.connectionId,
                    findings: [],
                };
            }
            groupedFindings[key].findings.push(finding);
        }

        Object.values(groupedFindings).forEach((group) => {
            const monitorInstanceExclusions = group.findings
                .map((finding) => {
                    const { metadata } = finding;
                    const targetId = metadata.id;
                    const targetName = metadata.name;

                    if (!targetId || !targetName) {
                        logger.error({
                            message:
                                'Failed to exclude finding. Missing required data',
                            additionalInfo: {
                                targetId,
                                targetName,
                            },
                        });

                        return null;
                    }

                    return {
                        targetId,
                        targetName,
                    };
                })
                .filter(
                    (
                        exclusion,
                    ): exclusion is { targetId: string; targetName: string } =>
                        exclusion !== null,
                );

            if (isEmpty(monitorInstanceExclusions)) {
                return;
            }

            const requestBody: MonitorInstanceExclusionsRequestDto = {
                monitorInstanceExclusionsByConnection: [
                    {
                        connectionId: group.connectionId,
                        monitorInstanceExclusions,
                    },
                ],
                exclusionReason: reason,
            };

            this.createExclusionMutation.mutate({
                body: requestBody,
                path: {
                    testId: group.testId,
                    xProductId: workspaceId,
                },
            });

            when(
                () => !this.createExclusionMutation.isPending,
                () => {
                    if (this.createExclusionMutation.hasError) {
                        snackbarController.addSnackbar({
                            id: 'bulk-finding-excluded-error',
                            props: {
                                title: t`Failed to exclude findings`,
                                description: t`Please try again`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    } else if (this.createExclusionMutation.response) {
                        snackbarController.addSnackbar({
                            id: 'bulk-finding-excluded-success',
                            hasTimeout: true,
                            props: {
                                title: t`Findings excluded successfully`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        sharedFindingsController.findingsListQuery.invalidate();
                        onSuccess?.();
                    }
                },
            );
        });
    };

    removeExclusionsBulk = ({
        testId,
        targetIds,
        onSuccess,
    }: {
        testId: number;
        targetIds: number[];
        onSuccess?: () => void;
    }): void => {
        if (isEmpty(targetIds)) {
            snackbarController.addSnackbar({
                id: 'bulk-production-remove-exclusions-no-exclusions',
                props: {
                    title: t`No exclusions selected`,
                    description: t`Please select exclusions to remove`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        this.removeExclusionsBulkMutation.mutate({
            path: {
                workspaceId: currentWorkspace.id,
                testId,
            },
            body: {
                // selectAll is set as false since we don't have this functionality at moment
                // if we send `true`, we will receive a 422 error since ids won't match
                selectAll: false,
                targetIds,
            },
        });

        when(
            () => !this.removeExclusionsBulkMutation.isPending,
            () => {
                if (this.removeExclusionsBulkMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'bulk-production-remove-exclusions-error',
                        props: {
                            title: t`Failed to remove exclusions`,
                            description: t`Failed to remove exclusions. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'bulk-production-remove-exclusions-success',
                    hasTimeout: true,
                    props: {
                        title: t`Exclusions removed successfully`,
                        description: t`Exclusions removed and included back in test results`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                sharedMonitoringDetailsExclusionsController.invalidate();
                closeMonitoringRemoveExclusionConfirmationModal();
                onSuccess?.();
            },
        );
    };
}

export const sharedFindingExclusionMutationController =
    new FindingExclusionMutationController();
