import { isEmpty, isString } from 'lodash-es';
import { Button } from '@cosmos/components/button';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { EmptyStateProps } from '@cosmos/components/empty-state';
import { monitorsV2ControllerGetFindingsListOptions } from '@globals/api-sdk/queries';
import type { FindingItemResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedQuery,
    reaction,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { processFiltersForAPI } from '../helpers/filter-processing.helper';
import { activeMonitoringController } from './monitoring-controller';

class FindingsController {
    hasEverHadData = false;
    currentFilters: FetchDataResponseParams['globalFilter'] | null = null;
    currentMonitorId: number | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    findingsListQuery = new ObservedQuery(
        monitorsV2ControllerGetFindingsListOptions,
    );

    get findingsList(): FindingItemResponseDto[] {
        return this.findingsListQuery.data?.data ?? [];
    }

    get findingsListTotal(): number {
        return this.findingsListQuery.data?.total ?? 0;
    }

    get isLoadingFindingsList(): boolean {
        return (
            this.findingsListQuery.isLoading ||
            this.findingsListQuery.isFetching
        );
    }

    get hasActiveFilters(): boolean {
        if (!this.currentFilters) {
            return false;
        }

        const { search, filters } = this.currentFilters;

        return !isEmpty(search) || !isEmpty(filters);
    }

    get shouldShowFirstTimeEmpty(): boolean {
        return !this.hasEverHadData && this.findingsListTotal === 0;
    }

    get emptyState(): EmptyStateProps {
        const { resultStatus, monitor } = activeMonitoringController;

        // Error state takes priority
        if (resultStatus === 'ERROR') {
            return {
                title: t`No findings due to error`,
                description: t`This test was unable to generate findings due to an error that prevented it from running. Please review the details of the error to resolve it and try again.`,
                illustrationName: 'Warning',
                leftAction: (
                    <Button
                        href={`/workspaces/${sharedWorkspacesController.currentWorkspace?.id}/compliance/monitoring/production/${monitor?.testId}/overview`}
                        label={t`View error details`}
                        level="secondary"
                    />
                ),
            };
        }

        if (this.shouldShowFirstTimeEmpty) {
            return {
                title: t`No findings for this test`,
                description: t`Test results you need to address will appear here.`,
                illustrationName: 'AddCircle',
            };
        }

        return {
            title: t`No findings found`,
            description: t`Please update your filters or refine your search.`,
            illustrationName: 'Warning',
        };
    }

    markDataLoaded(): void {
        if (this.hasEverHadData || this.findingsListTotal === 0) {
            return;
        }

        this.hasEverHadData = true;
    }

    resetForNewMonitor(monitorId: number): void {
        if (this.currentMonitorId === monitorId) {
            return;
        }

        this.hasEverHadData = false;
        this.currentFilters = null;
        this.currentMonitorId = monitorId;
    }

    load = (
        params: FetchDataResponseParams & { testId: number | string },
    ): void => {
        // Store current filters for empty state logic
        this.currentFilters = params.globalFilter;

        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace),
            () => {
                const testId = isString(params.testId)
                    ? parseInt(params.testId)
                    : params.testId;

                if (isNaN(testId)) {
                    throw new TypeError(
                        'Invalid testId: must be a valid number',
                    );
                }

                // Reset tracking when monitor changes
                this.resetForNewMonitor(testId);
                const { pagination, globalFilter, sorting } = params;
                const { page, pageSize } = pagination;
                const { search, filters } = globalFilter;

                // Process filters generically
                const processedFilters = processFiltersForAPI(filters);

                const query: Record<string, unknown> = {
                    page,
                    limit: pageSize,
                    ...(!isEmpty(search) && { q: search }),
                    ...(!isEmpty(filters) && processedFilters),
                };

                if (isEmpty(sorting)) {
                    query.sortDir = 'DESC';
                } else {
                    query.sort = sorting[0].id;
                    query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
                }

                this.findingsListQuery.load({
                    path: {
                        testId,
                        workspaceId:
                            sharedWorkspacesController.currentWorkspace?.id ??
                            1,
                    },
                    query,
                });
            },
        );
    };
}

export const sharedFindingsController = new FindingsController();

// Auto-update data flag when findings count changes (for empty state logic)
reaction(
    () => sharedFindingsController.findingsListTotal,
    () => {
        sharedFindingsController.markDataLoaded();
    },
);

// Reset tracking when monitor changes
reaction(
    () => activeMonitoringController.monitor?.testId,
    (testId) => {
        if (testId) {
            sharedFindingsController.resetForNewMonitor(testId);
        }
    },
);
