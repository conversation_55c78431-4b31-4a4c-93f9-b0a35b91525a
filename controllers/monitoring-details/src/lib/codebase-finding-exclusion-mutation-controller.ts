import { isEmpty } from 'lodash-es';
import { closeMonitoringRemoveExclusionConfirmationModal } from '@components/monitoring-remove-exclusions-confirmation-modal';
import { sharedMonitoringCodeExclusionsController } from '@controllers/monitoring';
import { snackbarController } from '@controllers/snackbar';
import {
    findingExclusionControllerExcludeFindingsMutation,
    monitorExclusionsBulkControllerBulkReincludeMonitorCodebaseExclusionsMutation,
    monitorExclusionsBulkControllerBulkUpdateMonitorCodebaseExclusionsReasonMutation,
    monitorExclusionsControllerUpdateCodebaseFindingExclusionReasonMutation,
} from '@globals/api-sdk/queries';
import type {
    ExcludeFindingListRequestDto,
    ExcludeFindingRequestDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedFindingsController } from './monitoring-finding-controller';

export interface CodebaseFindingForExclusion {
    findingId: string;
    testId: number;
    repositoryName: string;
}

class CodebaseFindingExclusionMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    createExclusionMutation = new ObservedMutation(
        findingExclusionControllerExcludeFindingsMutation,
    );

    updateExclusionReasonMutation = new ObservedMutation(
        monitorExclusionsControllerUpdateCodebaseFindingExclusionReasonMutation,
    );

    updateExclusionReasonBulkMutation = new ObservedMutation(
        monitorExclusionsBulkControllerBulkUpdateMonitorCodebaseExclusionsReasonMutation,
    );

    removeExclusionsBulkMutation = new ObservedMutation(
        monitorExclusionsBulkControllerBulkReincludeMonitorCodebaseExclusionsMutation,
    );

    get isUpdatingReason(): boolean {
        return this.updateExclusionReasonMutation.isPending;
    }

    get isUpdatingReasonBulk(): boolean {
        return this.updateExclusionReasonBulkMutation.isPending;
    }

    get hasUpdateReasonError(): boolean {
        return this.updateExclusionReasonMutation.hasError;
    }

    get hasBulkUpdateReasonError(): boolean {
        return this.updateExclusionReasonBulkMutation.hasError;
    }

    get isRemovingExclusionsBulk(): boolean {
        return this.removeExclusionsBulkMutation.isPending;
    }

    get hasRemoveExclusionsBulkError(): boolean {
        return this.removeExclusionsBulkMutation.hasError;
    }

    createExclusion = (
        finding: CodebaseFindingForExclusion,
        reason: string,
    ): void => {
        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        if (!workspaceId) {
            return;
        }

        const { findingId, testId, repositoryName } = finding;

        if (!findingId || !repositoryName) {
            logger.error({
                message:
                    'Failed to exclude codebase finding. Missing required data',
                additionalInfo: {
                    findingId,
                    repositoryName,
                    testId,
                },
            });

            return;
        }

        const findingExclusion: ExcludeFindingRequestDto = {
            findingId,
            reason,
            repositoryName,
            testId: Number(testId),
        };

        const requestBody: ExcludeFindingListRequestDto = {
            findingExclusions: [findingExclusion],
            workspaceName: sharedWorkspacesController.currentWorkspace?.name,
        };

        this.createExclusionMutation.mutate({
            body: requestBody,
        });

        when(
            () => !this.createExclusionMutation.isPending,
            () => {
                if (this.createExclusionMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'codebase-finding-excluded-error',
                        props: {
                            title: t`Failed to exclude codebase finding`,
                            description: t`Please try again`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else if (this.createExclusionMutation.response) {
                    snackbarController.addSnackbar({
                        id: 'codebase-finding-excluded-success',
                        hasTimeout: true,
                        props: {
                            title: t`Codebase finding excluded successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    // Invalidate the findings list to refresh the data
                    sharedFindingsController.findingsListQuery.invalidate();
                }
            },
        );
    };

    createBulkExclusion = (
        findings: CodebaseFindingForExclusion[],
        reason: string,
        onSuccess?: () => void,
    ): void => {
        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        if (!workspaceId) {
            return;
        }

        if (isEmpty(findings)) {
            snackbarController.addSnackbar({
                id: 'bulk-codebase-exclusion-no-findings',
                props: {
                    title: t`No findings selected`,
                    description: t`Please select findings to exclude`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const findingExclusions: ExcludeFindingRequestDto[] = findings
            .map((finding) => {
                const { findingId, testId, repositoryName } = finding;

                if (!findingId || !repositoryName) {
                    logger.error({
                        message:
                            'Failed to exclude codebase finding. Missing required data',
                        additionalInfo: {
                            findingId,
                            repositoryName,
                            testId,
                        },
                    });

                    return null;
                }

                return {
                    findingId,
                    reason,
                    repositoryName,
                    testId: Number(testId),
                };
            })
            .filter(
                (exclusion): exclusion is ExcludeFindingRequestDto =>
                    exclusion !== null,
            );

        if (isEmpty(findingExclusions)) {
            snackbarController.addSnackbar({
                id: 'bulk-codebase-exclusion-invalid-data',
                props: {
                    title: t`Invalid data`,
                    description: t`No valid findings to exclude`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const requestBody: ExcludeFindingListRequestDto = {
            findingExclusions,
            workspaceName: sharedWorkspacesController.currentWorkspace?.name,
        };

        this.createExclusionMutation.mutate({
            body: requestBody,
        });

        when(
            () => !this.createExclusionMutation.isPending,
            () => {
                if (this.createExclusionMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'bulk-codebase-finding-excluded-error',
                        props: {
                            title: t`Failed to exclude codebase findings`,
                            description: t`Please try again`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else if (this.createExclusionMutation.response) {
                    snackbarController.addSnackbar({
                        id: 'bulk-codebase-finding-excluded-success',
                        hasTimeout: true,
                        props: {
                            title: t`Codebase findings excluded successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    sharedFindingsController.findingsListQuery.invalidate();
                    onSuccess?.();
                }
            },
        );
    };

    updateExclusionReasonBulk = (
        testId: number,
        targetIds: number[],
        reason: string,
        onSuccess?: () => void,
    ): void => {
        if (!reason.trim()) {
            snackbarController.addSnackbar({
                id: `update-codebase-exclusion-reason-error`,
                props: {
                    title: t`Update failed`,
                    description: t`Invalid parameters provided`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        this.updateExclusionReasonBulkMutation.mutate({
            path: {
                workspaceId: currentWorkspace.id,
                testId,
            },
            body: {
                selectAll: false,
                targetIds,
                reason,
            },
        });

        when(
            () => !this.updateExclusionReasonBulkMutation.isPending,
            () => {
                if (this.updateExclusionReasonBulkMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: `update-codebase-exclusion-reason-bulk-error`,
                        props: {
                            title: t`Update failed`,
                            description: t`Failed to update exclusion reasons. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `update-codebase-exclusion-reason-bulk-success`,
                    props: {
                        title: t`Update successful`,
                        description: t`Exclusion reasons updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                // Invalidate the codebase exclusions query to refresh the data
                sharedMonitoringCodeExclusionsController.monitoringCodeExclusions.invalidate();
                onSuccess?.();
            },
        );
    };

    updateExclusionReason = (
        exclusionId: number,
        testId: number,
        reason: string,
        onSuccess?: () => void,
    ): void => {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!reason.trim()) {
            snackbarController.addSnackbar({
                id: `update-codebase-exclusion-reason-error`,
                props: {
                    title: t`Update failed`,
                    description: t`Invalid parameters provided`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        if (!currentWorkspace) {
            return;
        }

        this.updateExclusionReasonMutation.mutate({
            path: {
                workspaceId: currentWorkspace.id,
                testId,
                findingExclusionId: exclusionId,
            },
            body: {
                reason: reason.trim(),
            },
        });

        when(
            () => !this.updateExclusionReasonMutation.isPending,
            () => {
                if (this.updateExclusionReasonMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: `update-codebase-exclusion-reason-error`,
                        props: {
                            title: t`Update failed`,
                            description: t`Failed to update exclusion reason. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `update-codebase-exclusion-reason-success`,
                    props: {
                        title: t`Update successful`,
                        description: t`Exclusion reason updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                // Invalidate the codebase exclusions query to refresh the data
                sharedMonitoringCodeExclusionsController.monitoringCodeExclusions.invalidate();
                onSuccess?.();
            },
        );
    };

    removeExclusionsBulk = ({
        testId,
        targetIds,
        onSuccess,
    }: {
        testId: number;
        targetIds: number[];
        onSuccess?: () => void;
    }): void => {
        if (isEmpty(targetIds)) {
            snackbarController.addSnackbar({
                id: 'bulk-codebase-remove-exclusions-no-exclusions',
                props: {
                    title: t`No exclusions selected`,
                    description: t`Please select exclusions to remove`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        this.removeExclusionsBulkMutation.mutate({
            path: {
                workspaceId: currentWorkspace.id,
                testId,
            },
            body: {
                // selectAll is set as false since we don't have this functionality at moment
                // if we send `true`, we will receive a 422 error since ids won't match
                selectAll: false,
                targetIds,
            },
        });

        when(
            () => !this.removeExclusionsBulkMutation.isPending,
            () => {
                if (this.removeExclusionsBulkMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'bulk-codebase-remove-exclusions-error',
                        props: {
                            title: t`Failed to remove exclusions`,
                            description: t`Failed to remove exclusions. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'bulk-codebase-remove-exclusions-success',
                    hasTimeout: true,
                    props: {
                        title: t`Exclusions removed successfully`,
                        description: t`Exclusions removed and included back in test results`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                sharedMonitoringCodeExclusionsController.monitoringCodeExclusions.invalidate();
                closeMonitoringRemoveExclusionConfirmationModal();
                onSuccess?.();
            },
        );
    };
}

export const sharedCodebaseFindingExclusionMutationController =
    new CodebaseFindingExclusionMutationController();
