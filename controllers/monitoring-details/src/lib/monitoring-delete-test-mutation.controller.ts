import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { snackbarController } from '@controllers/snackbar';
import { monitorsControllerDeleteDraftTestMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export class MonitoringDeleteTestMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    deleteTestMutation = new ObservedMutation(
        monitorsControllerDeleteDraftTestMutation,
        {
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'monitoring-delete-test-error',
                    props: {
                        title: t`Failed to delete test`,
                        description: t`An error occurred while deleting the test. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    get isDeleting(): boolean {
        return this.deleteTestMutation.isPending;
    }

    get hasError(): boolean {
        return this.deleteTestMutation.hasError;
    }

    get error(): Error | null {
        return this.deleteTestMutation.error;
    }

    deleteTest = (): void => {
        const { testDetails } = sharedMonitoringTestDetailsController;
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!testDetails) {
            logger.error({
                message: 'Failed to delete draft test. Test details not found',
            });

            return;
        }

        if (!currentWorkspaceId) {
            logger.error({
                message: 'Failed to delete draft test. Workspace not found',
            });

            return;
        }

        this.deleteTestMutation.mutate({
            path: {
                testId: testDetails.testId,
                xProductId: currentWorkspaceId,
            },
        });

        when(
            () => !this.deleteTestMutation.isPending,
            () => {
                if (this.deleteTestMutation.hasError) {
                    return;
                }

                const deletedTestId = testDetails.testId;

                snackbarController.addSnackbar({
                    id: 'monitoring-delete-test-success',
                    props: {
                        title: t`The test ${deletedTestId} deleted successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                sharedProgrammaticNavigationController.navigateTo(
                    `${routeController.userPartOfUrl}/compliance/monitoring/production`,
                );
            },
        );
    };
}

export const sharedMonitoringDeleteTestMutationController =
    new MonitoringDeleteTestMutationController();
