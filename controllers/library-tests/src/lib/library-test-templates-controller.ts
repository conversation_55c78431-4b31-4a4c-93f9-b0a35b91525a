import { isEmpty, isNil, isString, omit } from 'lodash-es';
import type {
    DatatableRowSelectionState,
    FetchDataResponseParams,
    FilterProps,
} from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import type {
    ListBoxGroupData,
    ListBoxItemData,
} from '@cosmos/components/list-box';
import {
    libraryTestTemplateControllerGetTestTemplateIdsOptions,
    libraryTestTemplateControllerSearchTemplatesOptions,
} from '@globals/api-sdk/queries';
import type { LibraryTestTemplateResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedQuery,
    runInAction,
    toJS,
    when,
} from '@globals/mobx';
import {
    generateQueryParams,
    type LibraryTestTemplateSearchParams,
} from './helpers/generate-query-params.helper';

class LibraryTestTemplatesController {
    selectedTemplates: LibraryTestTemplateResponseDto[] = [];
    isAllRowsSelected = false;
    queryParams: LibraryTestTemplateSearchParams = {
        page: 1,
        limit: 10,
        q: '',
    };

    constructor() {
        makeAutoObservable(this, {
            loadTestTemplates: false,
        });
    }

    filterPropsObserved: FilterProps = {
        clearAllButtonLabel: t`Reset`,
        triggerLabel: t`Filters`,
        filters: [],
    };

    categoriesOptions: ListBoxItemData[] = [];
    frameworksOptions: ListBoxItemData[] = [];
    connectionsOptions: (ListBoxGroupData | ListBoxItemData)[] = [];
    resourcesOptions: ListBoxItemData[] = [];

    testTemplatesQuery = new ObservedQuery(
        libraryTestTemplateControllerSearchTemplatesOptions,
    );

    testTemplateIdsQuery = new ObservedQuery(
        libraryTestTemplateControllerGetTestTemplateIdsOptions,
    );

    get error(): Error | null {
        return this.testTemplatesQuery.error;
    }

    get isLoading(): boolean {
        return this.testTemplatesQuery.isLoading;
    }

    get rawData() {
        return this.testTemplatesQuery.data;
    }

    get getTestTemplates(): LibraryTestTemplateResponseDto[] {
        return this.rawData?.templates ?? [];
    }

    get getTotal(): number {
        return this.rawData?.counts.searchTotal ?? 0;
    }

    get testsTotals() {
        return (
            this.rawData?.counts ?? {
                total: 0,
                active: 0,
                available: 0,
            }
        );
    }

    get filtersData() {
        return (
            this.rawData?.filters ?? {
                statuses: [],
                ratings: [],
                categories: [],
                frameworks: [],
                connections: [],
                resources: [],
            }
        );
    }

    get filterProps(): FilterProps {
        return toJS(this.filterPropsObserved);
    }

    get generateFilters(): Filter[] {
        const {
            filtersData,
            categoriesOptions,
            frameworksOptions,
            connectionsOptions,
            resourcesOptions,
        } = this;

        const filters: Filter[] = [];

        if (!isEmpty(filtersData.statuses)) {
            filters.push({
                filterType: 'checkbox',
                id: 'statuses',
                label: t`Usage`,
                options: filtersData.statuses.map((status) => ({
                    id: status,
                    label: status,
                    value: status,
                })),
            });
        }

        if (!isEmpty(filtersData.ratings)) {
            filters.push({
                filterType: 'checkbox',
                id: 'ratings',
                label: t`Rating`,
                options: filtersData.ratings.map((rating) => ({
                    id: rating,
                    label: rating,
                    value: rating,
                })),
            });
        }

        if (!isEmpty(filtersData.categories)) {
            filters.push({
                filterType: 'combobox',
                id: 'categories',
                label: t`Categories`,
                isMultiSelect: true,
                placeholder: t`Search categories`,
                options: categoriesOptions,
                onFetchOptions: (params: { search?: string }) => {
                    this.categoriesFilterOptions(params.search);
                },
            });
        }

        if (!isEmpty(filtersData.frameworks)) {
            filters.push({
                filterType: 'combobox',
                id: 'frameworks',
                label: t`Frameworks`,
                isMultiSelect: true,
                placeholder: t`Search frameworks`,
                options: frameworksOptions,
                onFetchOptions: (params: { search?: string }) => {
                    this.frameworksFilterOptions(params.search);
                },
            });
        }

        if (!isEmpty(filtersData.connections)) {
            filters.push({
                filterType: 'combobox',
                id: 'connections',
                label: t`Connections`,
                isMultiSelect: true,
                placeholder: t`Search connections`,
                options: connectionsOptions,
                onFetchOptions: (params: { search?: string }) => {
                    this.connectionsFilterOptions(params.search);
                },
            });
        }

        if (!isEmpty(filtersData.resources)) {
            filters.push({
                filterType: 'combobox',
                id: 'resources',
                label: t`Resources`,
                isMultiSelect: true,
                placeholder: t`Search resources`,
                options: resourcesOptions,
                onFetchOptions: (params: { search?: string }) => {
                    this.resourcesFilterOptions(params.search);
                },
            });
        }

        return filters;
    }

    getTestTemplateIds = async (): Promise<number[]> => {
        this.testTemplateIdsQuery.load({
            query: omit(this.queryParams, ['page', 'limit']),
        });

        await when(() => !this.testTemplateIdsQuery.isLoading);

        return this.testTemplateIdsQuery.data?.ids ?? [];
    };

    loadTestTemplates = (params?: FetchDataResponseParams): void => {
        runInAction(() => {
            const defaultParams: FetchDataResponseParams = {
                pagination: {
                    page: 1,
                    pageSize: 10,
                    pageIndex: 0,
                    pageSizeOptions: [10, 25, 50, 100],
                },
                globalFilter: {
                    search: '',
                    filters: {},
                },
                sorting: [],
            };

            const effectiveParams = params ?? defaultParams;

            this.queryParams = generateQueryParams(effectiveParams);

            const plainQueryParams = toJS(this.queryParams);

            this.testTemplatesQuery.load({
                query: plainQueryParams,
            });

            when(
                () => !this.isLoading && !this.error,
                () => {
                    if (!this.rawData?.filters) {
                        return;
                    }
                    this.seedOptions();
                    this.filterPropsObserved.filters = this.generateFilters;
                },
            );
        });
    };

    seedOptions() {
        this.categoriesFilterOptions();
        this.frameworksFilterOptions();
        this.connectionsFilterOptions();
        this.resourcesFilterOptions();
    }

    categoriesFilterOptions(searchTerm?: string): void {
        const allOptions = this.filtersData.categories.map((category) => ({
            id: category,
            label: category,
            value: category,
        }));

        const newOptions = isNil(searchTerm)
            ? allOptions
            : allOptions.filter((option) =>
                  option.label.toLowerCase().includes(searchTerm.toLowerCase()),
              );

        this.categoriesOptions.splice(
            0,
            this.categoriesOptions.length,
            ...newOptions,
        );
    }

    frameworksFilterOptions(searchTerm?: string): void {
        const allOptions = this.filtersData.frameworks.map((framework) => ({
            id: framework,
            label: framework,
            value: framework,
        }));

        const newOptions = isNil(searchTerm)
            ? allOptions
            : allOptions.filter((option) =>
                  option.label.toLowerCase().includes(searchTerm.toLowerCase()),
              );

        this.frameworksOptions.splice(
            0,
            this.frameworksOptions.length,
            ...newOptions,
        );
    }

    connectionsFilterOptions(searchTerm?: string): void {
        const activeConnections: ListBoxItemData[] = [];
        const inactiveConnections: ListBoxItemData[] = [];

        this.filtersData.connections.forEach((connection) => {
            let connectionType = '';

            if (isString(connection.connectionType)) {
                connectionType = connection.connectionType;
            } else if (isString(connection.type)) {
                connectionType = connection.type;
            }

            const active = Boolean(connection.active);

            const option = {
                id: connectionType,
                label: connectionType,
                value: connectionType,
                description: active ? t`Active` : t`Not Connected`,
            };

            if (active) {
                activeConnections.push(option);
            } else {
                inactiveConnections.push(option);
            }
        });

        const groupedOptions: (ListBoxGroupData | ListBoxItemData)[] = [];

        if (!isEmpty(activeConnections)) {
            groupedOptions.push({
                groupHeader: '',
                items: activeConnections,
            });
        }

        if (!isEmpty(inactiveConnections)) {
            groupedOptions.push({
                groupHeader: '',
                items: inactiveConnections,
            });
        }

        const newGroups = isNil(searchTerm)
            ? groupedOptions
            : groupedOptions
                  .map((group) => {
                      if ('items' in group && group.items) {
                          return {
                              ...group,
                              items: (group.items as ListBoxItemData[]).filter(
                                  (item) =>
                                      item.label
                                          .toLowerCase()
                                          .includes(searchTerm.toLowerCase()),
                              ),
                          };
                      }

                      return group;
                  })
                  .filter(
                      (group) =>
                          !('items' in group) ||
                          !isEmpty((group as ListBoxGroupData).items),
                  );

        this.connectionsOptions.splice(
            0,
            this.connectionsOptions.length,
            ...newGroups,
        );
    }

    resourcesFilterOptions(searchTerm?: string): void {
        const allOptions =
            this.filtersData.resources?.map((resource) => ({
                id: resource,
                label: resource,
                value: resource,
            })) ?? [];

        const newOptions = isNil(searchTerm)
            ? allOptions
            : allOptions.filter((option) =>
                  option.label.toLowerCase().includes(searchTerm.toLowerCase()),
              );

        this.resourcesOptions.splice(
            0,
            this.resourcesOptions.length,
            ...newOptions,
        );
    }

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ) => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;

        const selectedTemplateIds = Object.keys(selectedRows);

        // If no rows are selected and not "select all", clear everything
        if (isEmpty(selectedTemplateIds) && !isAllRowsSelected) {
            this.selectedTemplates = [];
            this.isAllRowsSelected = isAllRowsSelected;

            return;
        }

        // Keep selections from other pages + add current page selections
        const otherPageSelections = this.selectedTemplates.filter(
            (template) =>
                !this.getTestTemplates.some(
                    (current) => current.templateId === template.templateId,
                ),
        );

        const currentPageSelections = this.getTestTemplates.filter((template) =>
            selectedTemplateIds.includes(String(template.templateId)),
        );

        this.isAllRowsSelected = isAllRowsSelected;

        this.selectedTemplates = [
            ...otherPageSelections,
            ...currentPageSelections,
        ];
    };
}

export const libraryTestTemplatesController =
    new LibraryTestTemplatesController();
