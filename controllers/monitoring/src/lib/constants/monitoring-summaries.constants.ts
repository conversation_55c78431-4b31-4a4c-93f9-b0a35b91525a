import { t } from '@globals/i18n/macro';

/**
 * Execution status types for monitoring summaries
 * Based on the original useAIExecution patterns but adapted for multiverse.
 */
export type ExecutionStatus = 'ERROR' | 'COMPLETED' | 'INPROGRESS' | 'PENDING';

/**
 * Summary status types for tracking overall generation state.
 */
export type SummaryStatus = 'LOADING' | 'ERROR' | 'COMPLETED' | 'LLM_ERROR';

/**
 * User-friendly status messages for different states.
 */
export const getStatusMessage = (status: SummaryStatus): string => {
    switch (status) {
        case 'LOADING': {
            return t`Generating summary...`;
        }
        case 'COMPLETED': {
            return t`Summary ready for download`;
        }
        case 'ERROR': {
            return t`Generation failed`;
        }
        case 'LLM_ERROR': {
            return t`AI generation failed`;
        }
        default: {
            return t`Unknown status`;
        }
    }
};

/**
 * Button labels for different states.
 */
export const getButtonLabel = (
    status: SummaryStatus | null,
    isGenerating: boolean,
): string => {
    if (isGenerating) {
        return t`Generating...`;
    }

    if (status === 'COMPLETED') {
        return t`Download CSV`;
    }

    return t`Generate Summary CSV`;
};
