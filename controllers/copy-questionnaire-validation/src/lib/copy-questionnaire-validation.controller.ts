import { debounce } from 'lodash-es';
import { questionnaireVendorSecurityTypeformControllerQuestionnaireTitleCheckOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedQuery,
    runInAction,
    when,
} from '@globals/mobx';

// Simple validation controller for the modal
export class CopyQuestionnaireValidationController {
    titleValidationQuery = new ObservedQuery(
        questionnaireVendorSecurityTypeformControllerQuestionnaireTitleCheckOptions,
    );

    lastValidatedTitle = '';

    /**
     * Track title validation error for display in input.
     */
    titleValidationErrorMessage: string | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    getTitleValidationError(): string | null {
        return this.titleValidationErrorMessage;
    }

    /**
     * Clear title validation error.
     */
    clearTitleValidationError(): void {
        runInAction(() => {
            this.titleValidationErrorMessage = null;
        });
    }

    /**
     * Validate questionnaire title on submit.
     */
    async validateTitleOnSubmit(title: string): Promise<{
        isValid: boolean;
        error?: string;
    }> {
        const trimmedTitle = title.trim();

        // Clear previous error
        runInAction(() => {
            this.titleValidationErrorMessage = null;
        });

        if (!trimmedTitle) {
            const errorMessage = t`Please enter a name.`;

            runInAction(() => {
                this.titleValidationErrorMessage = errorMessage;
            });

            return {
                isValid: false,
                error: errorMessage,
            };
        }

        // Load the validation query and wait for it to complete
        this.titleValidationQuery.load({
            query: { title: trimmedTitle },
        });

        // Wait for the query to complete
        await when(() => !this.titleValidationQuery.isLoading);

        return runInAction(() => {
            if (this.titleValidationQuery.error) {
                return { isValid: false };
            }

            if (this.titleValidationQuery.data?.isFound) {
                const errorMessage = t`The questionnaire name you entered is already in use.`;

                this.titleValidationErrorMessage = errorMessage;

                return {
                    isValid: false,
                    error: errorMessage,
                };
            }

            return { isValid: true };
        });
    }

    handleValidateTitle = (title: string): void => {
        runInAction(() => {
            if (!title.trim()) {
                this.lastValidatedTitle = '';

                return;
            }

            const trimmedTitle = title.trim();

            this.lastValidatedTitle = trimmedTitle;

            this.titleValidationQuery.load({
                query: { title: trimmedTitle },
            });
        });
    };

    validateTitle = debounce(this.handleValidateTitle, 300);
}

export const sharedCopyQuestionnaireValidationController =
    new CopyQuestionnaireValidationController();
