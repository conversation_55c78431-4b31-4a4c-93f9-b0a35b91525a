import { isEmpty, isNil, noop, toPairs } from 'lodash-es';
import type React from 'react';
import type { UseFormReturn } from 'react-hook-form';
import { z } from 'zod';
import {
    COMPARE_CONTROL_FIELDS_DIFFERENCE_KEYS,
    COMPARE_CONTROL_FIELDS_LINK,
    getCompareControlFieldsApplySelectValue,
    getCompareControlFieldsRejectSelectValue,
    getCompareControlFieldsSelectValues,
    handleCloseCompareControlFieldsModal,
} from '@components/controls';
import {
    sharedControlDetailsController,
    sharedControlsGetControlTemplateController,
} from '@controllers/controls';
import type { ButtonProps } from '@cosmos/components/button';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { ControlTemplateResponseDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import type { FormSchema, FormValues } from '@ui/forms';

interface CurrentFormValues {
    name?: string;
    code?: string;
    description?: string;
    question?: string;
    activity?: string;
}

class CompareControlFieldsModalModel {
    readonly fieldsKeysToRender = COMPARE_CONTROL_FIELDS_DIFFERENCE_KEYS;
    currentFormValues: CurrentFormValues = {};
    triggerResetForm: (values: Partial<FormValues>) => void = noop;

    constructor() {
        makeAutoObservable(this);
    }

    get isLoading(): boolean {
        const { isLoading } = sharedControlsGetControlTemplateController;

        return isLoading;
    }

    get templateValues(): ControlTemplateResponseDto | null {
        return sharedControlsGetControlTemplateController.data;
    }

    closeModal = () => {
        handleCloseCompareControlFieldsModal();
    };

    get modalTitle(): string {
        return t`Compare with Drata's template`;
    }

    get hasDefaultState(): boolean {
        const mapIterator = this.fieldsWithDifferencesMap.values();

        for (const hasDifference of mapIterator) {
            if (hasDifference) {
                return false;
            }
        }

        return !this.isLoading;
    }

    get fieldsWithDifferencesMap(): Map<keyof FormValues, boolean> {
        return new Map(
            COMPARE_CONTROL_FIELDS_DIFFERENCE_KEYS.map((fieldKey) => {
                const currentValue = this.currentFormValues[fieldKey];
                const templateValue = this.templateValues?.[fieldKey] ?? '';
                const hasDifference = currentValue !== templateValue;

                return [fieldKey, hasDifference];
            }),
        );
    }

    get emptyStateNode(): React.JSX.Element {
        return (
            <Stack gap="md" direction="column">
                <Text type="body" size="100" colorScheme="neutral">
                    <Trans>
                        We periodically update{' '}
                        <AppLink
                            isExternal
                            size="sm"
                            href={COMPARE_CONTROL_FIELDS_LINK}
                        >
                            {`Drata's template`}
                        </AppLink>{' '}
                        based on industry best practices.
                    </Trans>
                </Text>
                <Text type="title" size="200" colorScheme="neutral">
                    {t`Your're are already using the most recent version of Drata's template.`}
                </Text>
            </Stack>
        );
    }

    get modalParagraph(): React.JSX.Element {
        return (
            <Text as="div">
                <Trans>
                    Review your current control against {"Drata's"} template,
                    which we periodically update based on industry best
                    practices. Choose whether to apply or reject each update,
                    then confirm your selections.{' '}
                    <AppLink isExternal href={COMPARE_CONTROL_FIELDS_LINK}>
                        {`Learn about Drata's control templates`}
                    </AppLink>
                </Trans>
            </Text>
        );
    }

    getFooterActions = (
        triggerSubmit: () => Promise<boolean>,
    ): ButtonProps[] => {
        const baseActions = [
            {
                label: this.hasDefaultState ? t`Close` : t`Cancel`,
                level: 'secondary',
                onClick: this.closeModal,
            },
        ] as const satisfies ButtonProps[];

        if (this.hasDefaultState) {
            return baseActions;
        }

        return [
            ...baseActions,
            {
                label: t`Confirm selections`,
                level: 'primary',
                type: 'button',
                onClick: () => {
                    triggerSubmit();
                },
            },
        ] as const satisfies ButtonProps[];
    };

    loadCompareControlFieldsData = (
        currentFormValues: FormValues,
        triggerResetForm: (values: Partial<FormValues>) => void,
    ) => {
        this.triggerResetForm = triggerResetForm;
        this.currentFormValues =
            currentFormValues as object as CurrentFormValues;

        const { controlDetails } = sharedControlDetailsController;

        if (!controlDetails?.fk_control_template_id) {
            return;
        }

        sharedControlsGetControlTemplateController.load(
            controlDetails.fk_control_template_id,
        );
    };

    handleResetDefaultsControlFieldsValues = (values: FormValues) => {
        const updatedValues: CurrentFormValues = {};

        this.fieldsKeysToRender.forEach((fieldKey) => {
            const fieldValue = values[fieldKey] as ListBoxItemData | undefined;
            const isChangeAccepted =
                fieldValue?.value ===
                getCompareControlFieldsApplySelectValue().value;

            updatedValues[fieldKey] = isChangeAccepted
                ? (this.templateValues?.[fieldKey] ?? '')
                : this.currentFormValues[fieldKey];
        });

        const updatedValuesWithCustomFields = {
            ...this.currentFormValues,
            ...updatedValues,
        };

        this.triggerResetForm(updatedValuesWithCustomFields as FormValues);
        this.closeModal();
    };

    handleApplyAllChange = (
        newCheckedValue: boolean,
        formSetValueFn: UseFormReturn['setValue'],
    ) => {
        const mapIterator = this.fieldsWithDifferencesMap.entries();

        for (const [fieldKey, hasDifference] of mapIterator) {
            if (hasDifference) {
                formSetValueFn(
                    `${fieldKey}`,
                    newCheckedValue
                        ? getCompareControlFieldsApplySelectValue()
                        : getCompareControlFieldsRejectSelectValue(),
                );
            }
        }
    };

    getApplyAll = (formValues: FormValues): boolean => {
        const entries = toPairs(formValues);

        if (isEmpty(entries)) {
            return false;
        }

        for (const [, value] of entries) {
            const fieldValue = value as ListBoxItemData | undefined;

            const isRejectValue =
                isNil(fieldValue) ||
                fieldValue.value ===
                    getCompareControlFieldsRejectSelectValue().value;

            if (isRejectValue) {
                return false;
            }
        }

        return true;
    };

    buildSchema = (): FormSchema => {
        const mapIterator = this.fieldsWithDifferencesMap.entries();
        const schema: FormSchema = {};

        for (const [fieldKey, hasDifference] of mapIterator) {
            if (hasDifference) {
                schema[fieldKey] = {
                    type: 'select',
                    label: t`Value selection: ${fieldKey}`,
                    validator: z.object({
                        id: z.string(),
                        label: z.string(),
                        value: z.string(),
                    }),
                    isOptional: false,
                    options: getCompareControlFieldsSelectValues(),
                    shouldHideLabel: true,
                };
            }
        }

        return schema;
    };
}

export const sharedCompareControlFieldsModalModel =
    new CompareControlFieldsModalModel();
