import { sharedRiskInsightsController } from '@controllers/risk';
import type { DashboardResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';

const DEFAULT_CATEGORY_BREAKDOWN_PAGE_SIZE = 5;

class CategoryBreakdownPaginationModel {
    DEFAULT_PAGE = 1;
    pageSize = DEFAULT_CATEGORY_BREAKDOWN_PAGE_SIZE;

    constructor() {
        makeAutoObservable(this);
    }

    get defaultCategoryBreakdownPageSize(): number {
        return this.pageSize;
    }

    get totalCategories(): number {
        const { riskInsights } = sharedRiskInsightsController;

        /**
         * TODO: The DashboardResponseDto has categoryBreakdown marked as non-null, but it can be undefined https://drata.atlassian.net/browse/ENG-73598.
         */
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- categoryBreakdown can be undefined
        return riskInsights?.categoryBreakdown?.length ?? 0;
    }

    get allCategories(): DashboardResponseDto['categoryBreakdown'] {
        const { riskInsights } = sharedRiskInsightsController;

        return riskInsights?.categoryBreakdown ?? [];
    }

    get paginatedCategories(): DashboardResponseDto['categoryBreakdown'] {
        const { riskInsights } = sharedRiskInsightsController;
        const categories = riskInsights?.categoryBreakdown ?? [];

        const startIndex = (this.DEFAULT_PAGE - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;

        return categories.slice(startIndex, endIndex);
    }

    categoryBreakdownPaginationOnPageChange = (page: number) => {
        this.DEFAULT_PAGE = page;
    };
}

export const sharedCategoryBreakdownPaginationModel =
    new CategoryBreakdownPaginationModel();
