import { isArray, isEmpty, isNil, isNumber, isObject } from 'lodash-es';
import {
    sharedAuditHubAuditController,
    sharedAuditHubController,
} from '@controllers/audit-hub';
import {
    sharedAuditEvidenceQueryController,
    sharedAuditHubAuditorClientEvidenceStatusController,
    sharedCompanyArchiveStatusQueryController,
    sharedCompanyStatsQueryController,
    sharedCompanySummaryQueryController,
    sharedTotalEvidenceQueryController,
} from '@controllers/audit-hub-auditor-client-actions';
import { sharedAuditHubAuditorClientAuditController } from '@controllers/audit-hub-auditor-client-audit';
import { sharedAuditHubAuditorClientMethodsAssetsController } from '@controllers/audit-hub-auditor-client-methods-assets';
import { sharedAuditHubAuditorClientMethodsConnectionsController } from '@controllers/audit-hub-auditor-client-methods-connections';
import { sharedAuditHubAuditor<PERSON>lientMethods<PERSON>ontrolMapping<PERSON>ontroller } from '@controllers/audit-hub-auditor-client-methods-control-mapping';
import { sharedAuditHubAuditorClientMethodsEvidenceLibraryController } from '@controllers/audit-hub-auditor-client-methods-evidence-library';
import { sharedAuditHubAuditorClientMethodsHumanResourcesController } from '@controllers/audit-hub-auditor-client-methods-human-resources';
import { sharedAuditHubAuditorClientMethodsInfrastructureController } from '@controllers/audit-hub-auditor-client-methods-infrastructure';
import { sharedAuditHubAuditorClientMethodsRequestPackageController } from '@controllers/audit-hub-auditor-client-methods-request-package';
import { sharedAuditHubAuditorClientMethodsVendorsController } from '@controllers/audit-hub-auditor-client-methods-vendors';
import { sharedAuditHubAuditorClientMethodsVersionControlController } from '@controllers/audit-hub-auditor-client-methods-version-control';
import { sharedAuditHubAuditorRefreshPackageController } from '@controllers/audit-hub-auditor-refresh-package';
import { sharedAuditHubAuditorValidatePersonnelController } from '@controllers/audit-hub-auditor-validate-personnel';
import { sharedAuditHubFedRampController } from '@controllers/audit-hub-fed-ramp-controller';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import { Banner } from '@cosmos/components/banner';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Grid } from '@cosmos/components/grid';
import { Icon } from '@cosmos/components/icon';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { dimensionSm } from '@cosmos/constants/tokens';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import {
    FrameworkBadge,
    type FrameworkBadgeName,
} from '@cosmos-lab/components/framework-badge';
import type {
    AuditListResponseDto,
    AuditorFrameworkSummaryResponseDto,
    CompanyArchiveDownloadLinkResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, runInAction } from '@globals/mobx';
import {
    areSampleDatesWithinAuditorFrameworkPeriod,
    getIsArchiveExpired,
    openHandleModal,
} from '@models/auditor-client-audit';
import { MAX_EVIDENCE_DIRECT_DOWNLOAD } from './auditor-client-audit-model-constants';
import { openHandleRequestModal } from './auditor-client-audit-request-modal';
import { openHandleViewPastRequestModal } from './auditor-client-audit-view-past-request-modal';
import { openAuditorStatusModal } from './auditor-client-auditor-status-modal';
import { openChangeSampleEvidenceModal } from './change-sample-evidence-modal';

const isValidSummaryData = (
    data: AuditorFrameworkSummaryResponseDto | null,
): data is AuditorFrameworkSummaryResponseDto => {
    return !isNil(data) && isObject(data) && 'auditorFramework' in data;
};

interface DownloadOption {
    id: string;
    label: string;
    showItem: boolean;
    type: string;
    onClick?: () => void;
}

interface PreAuditOptions {
    label: string;
    startSlot: React.JSX.Element | null;
    items?: DownloadOption[];
    type: string;
    onClick?: () => void;
    id: string;
}

interface DownloadAllCompanyFilesButton {
    label: string;
    onClick?: () => void;
}
export class AuditorClientAuditModel {
    constructor() {
        makeAutoObservable(this);
    }

    get areSampleDatesWithinPeriod(): boolean {
        const summaryData = sharedAuditorController.auditSummaryByIdData;

        if (!isValidSummaryData(summaryData)) {
            return false;
        }
        const { auditorFramework } = summaryData;

        return areSampleDatesWithinAuditorFrameworkPeriod(
            auditorFramework.startDate,
            auditorFramework.endDate,
            sharedAuditHubAuditorClientAuditController.auditDatesData,
        );
    }

    get arePersonnelWithinPeriod(): boolean {
        const { validationData } =
            sharedAuditHubAuditorValidatePersonnelController;

        return validationData?.arePersonnelWithinAuditPeriod ?? false;
    }

    get auditStarted(): boolean {
        const summaryData = sharedAuditorController.auditSummaryByIdData;

        if (!isValidSummaryData(summaryData)) {
            return false;
        }
        const { totalRequests } = summaryData;
        let auditStarted = false;

        if (
            (!this.isDownloadOnlyAudit && totalRequests > 0) ||
            this.isDownloadOnlyAudit
        ) {
            auditStarted = true;
        }

        return auditStarted;
    }

    get isFileGenerationPending(): boolean {
        const { companyArchiveStatus } = sharedAuditEvidenceQueryController;

        const isPendingByPing = companyArchiveStatus === 'PENDING';

        return (
            this.auditStarted &&
            isPendingByPing &&
            !this.isGenerateButtonVisible
        );
    }

    get isEvidenceExpiredByStatus(): boolean {
        const { isEvidenceExpired } =
            sharedAuditHubAuditorClientEvidenceStatusController;

        return isEvidenceExpired;
    }

    get isGenerateButtonVisibleConsistentEvidenceExpired(): boolean {
        return (
            this.auditStarted &&
            this.isEvidenceExpiredByStatus &&
            !this.hasEvidenceControlFileGenerationFailed
        );
    }

    get isGenerateButtonVisible(): boolean {
        const { isEvidenceExpired } =
            sharedAuditHubAuditorClientEvidenceStatusController;

        const isEvidenceExpiredByStatus = isEvidenceExpired;

        return (
            this.auditStarted &&
            isEvidenceExpiredByStatus &&
            !this.hasEvidenceControlFileGenerationFailed
        );
    }

    get hasFileGenerationFailed(): boolean {
        const { companyArchiveStatus } = sharedAuditEvidenceQueryController;

        return companyArchiveStatus === 'FAILED';
    }

    get isGenerateButtonVisibleForPreAudit(): boolean {
        const { isPreAuditExpired } = sharedCompanyArchiveStatusQueryController;

        return isPreAuditExpired && !this.hasFileGenerationFailed;
    }

    get hasFileGenerationSucceededForPreAudit(): boolean {
        const { preAuditArchiveStatusData } =
            sharedCompanyArchiveStatusQueryController;

        const isSuccesByStatus =
            preAuditArchiveStatusData?.companyArchiveStatus === 'SUCCESS';

        return isSuccesByStatus && !this.isGenerateButtonVisibleForPreAudit;
    }

    get hasFileGenerationSucceeded(): boolean {
        const { companyArchiveStatus } = sharedAuditEvidenceQueryController;

        return (
            this.auditStarted &&
            companyArchiveStatus === 'SUCCESS' &&
            !this.isGenerateButtonVisibleConsistentEvidenceExpired
        );
    }

    get allFilesFailedStatus(): boolean {
        const { preAuditArchiveStatusDataFailed } =
            sharedCompanyArchiveStatusQueryController;
        const { isFileGenerationFailed } =
            sharedAuditHubAuditorClientEvidenceStatusController;

        const missingCompanyArchiveTimestamp =
            (this.archiveData && !this.archiveData.companyArchiveUpdatedAt) ||
            false;

        return (
            preAuditArchiveStatusDataFailed ||
            missingCompanyArchiveTimestamp ||
            (this.archiveData?.companyArchiveUpdatedAt &&
                getIsArchiveExpired(
                    new Date(this.archiveData.companyArchiveUpdatedAt),
                )) ||
            isFileGenerationFailed
        );
    }

    get archiveData(): CompanyArchiveDownloadLinkResponseDto | null {
        const { preAuditArchiveStatusData } =
            sharedCompanyArchiveStatusQueryController;

        return preAuditArchiveStatusData;
    }

    get isGeneratePreAuditRequestButtonVisible(): boolean {
        const { isEvidenceExpired } =
            sharedAuditHubAuditorClientEvidenceStatusController;

        return isEvidenceExpired && !this.hasFileGenerationFailed;
    }

    get hasEvidenceControlFileGenerationFailed(): boolean {
        return this.auditStarted && this.hasFileGenerationFailed;
    }

    get isDownloadOnlyAudit(): boolean {
        const { auditByIdData } = sharedAuditHubController;
        const { framework } = auditByIdData ?? {};

        return framework?.auditType === 'DOWNLOAD_ONLY_AUDIT';
    }

    get downloadAllCompanyFilesButton(): DownloadAllCompanyFilesButton {
        const { isFileGenerationSuccess } =
            sharedAuditHubAuditorClientEvidenceStatusController;

        const { preAuditArchiveStatusData: archiveData } =
            sharedCompanyArchiveStatusQueryController;

        if (this.allFilesFailedStatus) {
            return {
                label: t`Request new package`,
                onClick: () => {
                    openHandleModal();
                },
            };
        }

        if (
            isFileGenerationSuccess &&
            archiveData?.companyArchiveUpdatedAt &&
            !getIsArchiveExpired(new Date(archiveData.companyArchiveUpdatedAt))
        ) {
            return {
                label: t`Download all`,
                onClick: () => {
                    runInAction(() => {
                        sharedAuditHubAuditorClientMethodsRequestPackageController.downloadPreAuditPackage(
                            'PRE_AUDIT',
                        );
                    });
                },
            };
        }

        return {
            label: t`Request new package`,
            onClick: () => {
                openHandleModal();
            },
        };
    }

    get subMenuItems(): DownloadOption[] {
        const { preAuditArchiveStatusDataFailed: preAuditPackageStatusFailed } =
            sharedCompanyArchiveStatusQueryController;

        const isHumanResourcesDataValid =
            !isNil(sharedCompanySummaryQueryController.companySummary) &&
            isArray(sharedCompanySummaryQueryController.companySummary) &&
            !isEmpty(sharedCompanySummaryQueryController.companySummary);

        const { stats } = sharedCompanyStatsQueryController;

        const { totalEvidenceByFrameworkIdData } =
            sharedTotalEvidenceQueryController;

        if (preAuditPackageStatusFailed) {
            return [
                {
                    id: 'try-again-option',
                    showItem: true,
                    type: 'item',
                    label: t`Try again`,
                    onClick: () => {
                        runInAction(() => {
                            if (
                                !isNumber(
                                    sharedAuditHubController.auditByIdData
                                        ?.framework.productId,
                                )
                            ) {
                                return;
                            }
                            // we need to call loadAllCompanies again to refetch the data
                            // run invalidate does not work here as often user can reach this page before query is called
                            sharedAuditHubAuditController.loadAllCompanies(
                                sharedAuditHubController.auditByIdData.framework
                                    .productId,
                            );
                        });
                    },
                },
            ];
        }

        return [
            {
                id: 'download-all-option',
                label: this.downloadAllCompanyFilesButton.label,
                showItem: true,
                type: 'item',
                onClick: this.downloadAllCompanyFilesButton.onClick,
            },
            {
                id: 'control-mapping-option',
                label: t`Control mapping`,
                type: 'item',
                onClick: () => {
                    runInAction(() => {
                        sharedAuditHubAuditorClientMethodsControlMappingController.loadGrcControllerDownloadControlsOptions();
                    });
                },
                showItem: true,
            },
            {
                id: 'connections-option',
                label: t`Connections`,
                type: 'item',
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsConnectionsController.handleDownloadConnections();
                },
                showItem: true,
            },
            {
                id: 'human-resources-option',
                label: t`Human Resources`,
                type: 'item',
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsHumanResourcesController.handleHumanResourcesDownload();
                },
                showItem: isHumanResourcesDataValid,
            },
            {
                id: 'vendors-option',
                label: t`Vendors`,
                type: 'item',
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsVendorsController.handleDownloadVendors();
                },
                showItem: !isNil(stats?.vendors) && stats.vendors > 0,
            },
            {
                id: 'assets-option',
                label: t`Assets`,
                type: 'item',
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsAssetsController.handleDownloadAssets();
                },
                showItem: !isNil(stats?.assets) && stats.assets > 0,
            },
            {
                id: 'evidence-library-option',
                label: t`Evidence library`,
                type: 'item',
                description: t`Manual evidence only`,
                onClick: () => {
                    if (
                        !isNil(totalEvidenceByFrameworkIdData?.total) &&
                        isNumber(
                            sharedAuditHubController.auditByIdData?.framework
                                .productId,
                        )
                    ) {
                        totalEvidenceByFrameworkIdData.total >
                        MAX_EVIDENCE_DIRECT_DOWNLOAD
                            ? sharedAuditHubAuditorClientMethodsEvidenceLibraryController.handleSendAllEvidenceByEmail(
                                  sharedAuditHubController.auditByIdData
                                      .framework.productId,
                              )
                            : sharedAuditHubAuditorClientMethodsEvidenceLibraryController.handleDownloadAllEvidenceDirectly();
                    }
                },
                showItem:
                    !isNil(totalEvidenceByFrameworkIdData?.total) &&
                    totalEvidenceByFrameworkIdData.total > 0,
            },
            {
                id: 'infrastructure-access-option',
                label: t`Evidence library`,
                type: 'item',
                description: t`Infrastructure access`,
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsInfrastructureController.loadUserIdentitiesControllerGetUserInfrastructureIdentities();
                },
                showItem:
                    !isNil(stats?.infrastructureUserIdentities) &&
                    stats.infrastructureUserIdentities > 0,
            },
            {
                id: 'version-control-option',
                label: t`Evidence library`,
                type: 'item',
                description: t`Version control`,
                onClick: () => {
                    sharedAuditHubAuditorClientMethodsVersionControlController.loadUserVersionControlIdentities();
                },
                showItem:
                    !isNil(stats?.versionControlUserIdentities) &&
                    stats.versionControlUserIdentities > 0,
            },
        ].filter((items) => items.showItem);
    }

    get auditStatus(): AuditListResponseDto['status'] {
        if (sharedAuditorController.auditSummaryByIdQuery.hasError) {
            return 'ACTIVE';
        }

        const { auditSummaryByIdData: summaryData } = sharedAuditorController;
        const { auditorFramework } = summaryData ?? {};

        if (
            isValidSummaryData(summaryData) &&
            auditorFramework?.status === 'COMPLETED'
        ) {
            return 'COMPLETED';
        }

        return 'ACTIVE';
    }

    get evidenceButton(): SchemaDropdownItemData | null {
        const { auditId } = sharedCustomerRequestsController;

        const {
            isAuditorRefreshControlEvidencePackageLoading,
            isSocketPending,
        } = sharedAuditHubAuditorRefreshPackageController;

        const isNotDownloadingOnlyAndControlEvidencePending =
            !this.isDownloadOnlyAudit && this.isFileGenerationPending;

        const isGeneratingControlEvidence =
            (!this.isDownloadOnlyAudit && this.isFileGenerationPending) ||
            (isNotDownloadingOnlyAndControlEvidencePending &&
                isAuditorRefreshControlEvidencePackageLoading) ||
            isAuditorRefreshControlEvidencePackageLoading ||
            isSocketPending;

        if (this.hasFileGenerationSucceeded) {
            return {
                id: `audit-request-control-evidence-`,
                label: t`Request control evidence`, // Download
                type: 'item',
                disabled: true,
                onClick: () => {
                    runInAction(() => {
                        sharedAuditHubAuditorClientMethodsRequestPackageController.downloadPreAuditPackage(
                            'CONTROL_EVIDENCE',
                        );
                    });
                },
                startSlot: <Icon name="Download" size="200" />,
            };
        }

        if (!this.auditStarted && !this.isDownloadOnlyAudit) {
            return {
                id: `evidence-available-after-samples`,
                label: t`Control evidence will be available after the auditor sets samples.`,
                type: 'item',
                startSlot: <Icon name="Download" size="200" />,
            };
        }

        if (
            this.hasEvidenceControlFileGenerationFailed &&
            !isGeneratingControlEvidence
        ) {
            return {
                id: `audit-request-control-evidence-try-again`,
                label: t`Couldn't create control evidence. Try again`,
                type: 'item',
                onClick: () => {
                    runInAction(() => {
                        sharedAuditHubAuditorRefreshPackageController.postRefreshEvidencePackage(
                            auditId,
                            sharedAuditHubController.auditByIdData?.framework
                                .productId as number,
                        );
                    });
                },
                startSlot: <Icon name="Download" size="200" />,
            };
        }

        if (isGeneratingControlEvidence) {
            return {
                id: `audit-request-control-evidence-check-back`,
                label: t`Please wait while we generate your control evidence...`,
                type: 'item',
                startSlot: <Icon name="Download" size="200" />,
            };
        }

        if (this.isGenerateButtonVisible) {
            return {
                id: `audit-request-control-evidence`,
                label: t`Request evidence`,
                type: 'item',
                onClick: () => {
                    openHandleRequestModal();
                },
                startSlot: <Icon name="Download" size="200" />,
            };
        }

        return {
            id: `audit-request-control-evidence-try-again`,
            label: t`Couldn't create control evidence. Try again`,
            type: 'item',
            onClick: () => {
                runInAction(() => {
                    sharedAuditHubAuditorRefreshPackageController.postRefreshEvidencePackage(
                        auditId,
                        sharedAuditHubController.auditByIdData?.framework
                            .productId as number,
                    );
                });
            },
            startSlot: <Icon name="Download" size="200" />,
        };
    }

    get fedRampButton(): SchemaDropdownItemData | null {
        const { auditSummaryByIdData: summaryData } = sharedAuditorController;
        const { auditByIdData } = sharedAuditHubController;
        const { framework } = auditByIdData ?? {};

        if (
            !isValidSummaryData(summaryData) ||
            framework?.type !== 'FEDRAMP20X'
        ) {
            return null;
        }
        const { totalRequests } = summaryData;

        const fedRampAuditStarted = totalRequests > 0;

        const { isFedRamp20xEnabled } = sharedFeatureAccessModel;

        if (!isFedRamp20xEnabled) {
            return null;
        }

        const fedRampDescription = () => {
            if (!fedRampAuditStarted) {
                return t`FedRAMP 20X KSI validation will be available after the auditor sets samples.`;
            }

            return t`FedRAMP 20x KSI validation`;
        };

        return {
            id: 'fedramp-20x-button',
            label: t`Download`,
            type: 'item',
            description: fedRampDescription(),
            onClick: () => {
                if (!fedRampAuditStarted) {
                    return;
                }
                runInAction(() => {
                    sharedAuditHubFedRampController.loadFedRamp20x();
                });
            },
            startSlot: <Icon name="Download" size="200" />,
        };
    }

    get preAuditOptions(): PreAuditOptions {
        const {
            preAuditArchiveStatusDataFailed,
            isPreAuditArchiveStatusDataPending,
        } = sharedCompanyArchiveStatusQueryController;

        const { getAllCompaniesIsLoading } = sharedAuditHubAuditController;

        const isFileGenerationPendingForPreAudit =
            (isPreAuditArchiveStatusDataPending &&
                !this.isGenerateButtonVisibleForPreAudit) ||
            getAllCompaniesIsLoading;

        const { isPreAuditPackageReady } = sharedAuditHubAuditController;

        if (
            (this.hasFileGenerationSucceededForPreAudit &&
                isPreAuditPackageReady) ||
            (this.isGenerateButtonVisibleForPreAudit && isPreAuditPackageReady)
        ) {
            return {
                id: `audit-pre-audit-package-items`,
                label: t`Pre audit package`,
                startSlot: <Icon name="Download" size="200" />,
                items: this.subMenuItems,
                type: 'subMenu',
            };
        }

        if (isFileGenerationPendingForPreAudit || !isPreAuditPackageReady) {
            return {
                id: `audit-pre-audit-package-wait`,
                label: t`Please wait while we generate your pre-audit packages...`,
                startSlot: <Icon name="Download" size="200" />,
                type: 'item',
            };
        }

        if (preAuditArchiveStatusDataFailed) {
            return {
                id: `audit-pre-audit-package-try-again`,
                label: t`Couldn't create pre-audit packages. Try again`,
                startSlot: <Icon name="Download" size="200" />,
                type: 'item',
                onClick: () => {
                    runInAction(() => {
                        if (
                            !isNumber(
                                sharedAuditHubController.auditByIdData
                                    ?.framework.productId,
                            )
                        ) {
                            return;
                        }

                        // we need to call loadAllCompanies again to refetch the data
                        // running invalidate does not work here as often user can reach this page before query is called
                        sharedAuditHubAuditController.loadAllCompanies(
                            sharedAuditHubController.auditByIdData.framework
                                .productId,
                        );
                    });
                },
            };
        }

        return {
            id: `audit-pre-audit-package-items`,
            label: t`Pre audit package`,
            startSlot: <Icon name="Download" size="200" />,
            items: this.subMenuItems,
            type: 'subMenu',
        };
    }

    get auditActions(): Action[] {
        const getAuditButton = () => {
            if (this.auditStatus === 'COMPLETED') {
                return {
                    id: 'audit-mark-as-active',
                    actionType: 'button' as const,
                    typeProps: {
                        label: t`Mark as active`,
                        level: 'secondary' as const,
                        startIconName: 'CheckCircle' as const,
                        onClick: () => {
                            openAuditorStatusModal('ACTIVE');
                        },
                    },
                };
            }

            return {
                id: 'audit-complete-audit',
                actionType: 'button' as const,
                typeProps: {
                    label: t`Complete audit`,
                    level: 'secondary' as const,
                    onClick: () => {
                        openAuditorStatusModal('COMPLETED');
                    },
                },
            };
        };

        return [
            {
                id: 'audit-actions-dropdown',
                actionType: 'dropdown',
                typeProps: {
                    label: '',
                    level: 'tertiary',
                    startIconName: 'HorizontalMenu',
                    isIconOnly: true,
                    items: [
                        {
                            id: `audit-pre-audit-package`,
                            label: this.preAuditOptions.label,
                            type: this.preAuditOptions.type,
                            startSlot: this.preAuditOptions.startSlot,
                            items: this.preAuditOptions.items,
                            onClick: this.preAuditOptions.onClick,
                        },
                        ...(this.evidenceButton ? [this.evidenceButton] : []),
                        ...(this.fedRampButton ? [this.fedRampButton] : []),
                        {
                            id: `audit-view-past-downloads`,
                            label: t`View past downloads`,
                            type: 'item',
                            onClick: () => {
                                openHandleViewPastRequestModal();
                            },
                            startSlot: <Icon name="Visible" size="200" />,
                        },
                        {
                            id: `change-sample-evidence`,
                            label: t`Change sample evidence`,
                            type: 'item',
                            onClick: () => {
                                const { auditorFrameworkId } =
                                    sharedCustomerRequestDetailsController;

                                if (auditorFrameworkId) {
                                    openChangeSampleEvidenceModal(
                                        auditorFrameworkId,
                                    );
                                }
                            },
                            startSlot: <Icon name="Sync" size="200" />,
                        },
                    ],
                },
            },
            getAuditButton(),
        ];
    }

    get breadcrumbs(): Breadcrumb[] {
        const { clientId } = sharedCustomerRequestDetailsController;

        if (!clientId) {
            console.warn(
                'clientId is missing from sharedCustomerRequestDetailsController',
            );

            return [];
        }

        const { company } = sharedCurrentCompanyController;

        return [
            {
                label: t`Client List`,
                pathname: '/audit-hub/clients',
            },
            {
                label: `${company?.name}`,
                pathname: `/audit-hub/clients/${clientId}/audits`,
            },
        ];
    }

    get actionStack(): React.JSX.Element | undefined {
        return (
            <ActionStack
                isFullWidth
                gap={dimensionSm}
                maxWidth="100%"
                actions={this.auditActions}
            />
        );
    }

    get title(): string {
        return '';
    }

    get banner(): React.JSX.Element | undefined {
        const { auditSummaryByIdData: summaryData } = sharedAuditorController;

        if (!isValidSummaryData(summaryData)) {
            return undefined;
        }
        const { auditorFramework } = summaryData;

        const start = new Intl.DateTimeFormat('en-US').format(
            new Date(auditorFramework.startDate),
        );

        const end = new Intl.DateTimeFormat('en-US').format(
            new Date(auditorFramework.endDate),
        );

        if (!this.areSampleDatesWithinPeriod && this.arePersonnelWithinPeriod) {
            return (
                <Banner
                    data-id="choose-new-personnel-banner"
                    displayMode="section"
                    severity="critical"
                    title={t`You need to choose new personnel`}
                    body={
                        <>
                            {t`Some personnel ${start} and ${end} dates have changed.
                                You need to choose a new personnel to ensure
                                your samples aren't out of date.`}
                            <Button
                                label={t`Request new samples`}
                                level="secondary"
                                data-id="choose-new-personnel-button"
                                onClick={() => {
                                    if (auditorFramework.id) {
                                        openChangeSampleEvidenceModal(
                                            auditorFramework.id,
                                        );
                                    }
                                }}
                            />
                        </>
                    }
                />
            );
        }

        if (!this.areSampleDatesWithinPeriod) {
            return (
                <Banner
                    data-id="request-new-samples-banner"
                    displayMode="section"
                    severity="critical"
                    title={t`You need to request new samples`}
                    body={
                        <>
                            {t`Someone changed the audit period from
                                ${start} to
                                ${end} so you'll need to request a new
                                sample to ensure your samples aren't out of
                                date.`}
                            <Button
                                label={t`Request new samples`}
                                level="secondary"
                                data-id="request-new-samples-button"
                                onClick={() => {
                                    if (auditorFramework.id) {
                                        openChangeSampleEvidenceModal(
                                            auditorFramework.id,
                                        );
                                    }
                                }}
                            />
                        </>
                    }
                />
            );
        }

        return undefined;
    }

    get slot(): React.JSX.Element | undefined {
        const { auditDatesData } = sharedAuditHubAuditorClientAuditController;
        const { auditSummaryByIdData } = sharedAuditorController;
        const { company } = sharedCurrentCompanyController;

        const { name: companyName } = company ?? {};

        const { relatedFramework } =
            auditSummaryByIdData?.auditorFramework.auditorFrameworkType ?? {};

        if (!relatedFramework || !companyName) {
            return undefined;
        }

        const { name: frameworkName, tag: frameworkTag } = relatedFramework;

        const label = `${frameworkName} - ${companyName}`;

        return (
            <Box>
                <Grid
                    gap="4x"
                    data-testid="AuditorClientAuditDetailsViewHeader"
                    columns="1fr auto auto"
                >
                    <Stack gap="md" align="center">
                        {!isNil(label) && (
                            <FrameworkBadge
                                badgeName={frameworkTag as FrameworkBadgeName}
                            />
                        )}
                        <Text type="title" size="400">
                            {label}
                        </Text>
                    </Stack>
                </Grid>
                <Grid
                    gap="4x"
                    data-testid="AuditorClientAuditDetailsKeyValuePair"
                    columns="1fr auto auto"
                >
                    <KeyValuePair
                        type="REACT_NODE"
                        label=""
                        value={
                            <Stack direction="column" gap="sm" mt="lg">
                                <Text type="title" size="100">
                                    {t`Audit Period`}
                                </Text>
                                <Text type="body" size="100">
                                    {auditDatesData.join(' - ')}
                                </Text>
                            </Stack>
                        }
                    />
                </Grid>
            </Box>
        );
    }
}
