import {
    closeRemoveRequirementsModal,
    type RemoveRequirementsModalMode,
} from '@components/requirements';
import {
    sharedControlDetailsController,
    sharedControlFrameworksController,
    sharedControlFrameworksForFrameworkTags,
    sharedControlsDeleteRequirementsController,
} from '@controllers/controls';
import { panelController } from '@controllers/panel';
import { sharedRequirementDetailsController } from '@controllers/requirements';
import { snackbarController } from '@controllers/snackbar';
import type { RequirementWithControlsShortListResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, when } from '@globals/mobx';

export class RemoveRequirementsModalModel {
    mode: RemoveRequirementsModalMode = 'control';
    entity: RequirementWithControlsShortListResponseDto | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    get modalHeaderTitle(): string {
        return t`Unmap requirement?`;
    }

    get modalConfirmationMessage(): string {
        return t`This requirement will no longer be mapped to this control.`;
    }

    get isSaving(): boolean {
        return sharedControlsDeleteRequirementsController.isPending;
    }

    get handleCloseRemoveRequirementsModal(): () => void {
        return this.#handleCloseRemoveRequirementsModalOnControl;
    }

    get handleConfirmRemoveRequirements(): () => void {
        return this.#handleConfirmRemoveRequirementsModalOnControl;
    }

    setModalMode = (
        newMode: RemoveRequirementsModalMode,
        entity: RequirementWithControlsShortListResponseDto,
    ): void => {
        this.mode = newMode;
        this.entity = entity;
    };

    #handleCloseRemoveRequirementsModalOnControl = (): void => {
        closeRemoveRequirementsModal();
    };

    #handleConfirmRemoveRequirementsModalOnControl = (): void => {
        const { controlId } = sharedControlDetailsController;

        if (!this.entity || !controlId) {
            return;
        }

        sharedControlsDeleteRequirementsController.mapRequirementsToControl(
            [controlId],
            this.entity.id,
        );

        when(
            () => !sharedControlsDeleteRequirementsController.isPending,
            () => {
                if (!sharedControlsDeleteRequirementsController.hasError) {
                    const isRequirementPanelOpen =
                        panelController.currentPanelId ===
                        'requirement-details-panel';
                    const currentRequirementId =
                        sharedRequirementDetailsController.requirement?.id;

                    const shouldClosePanel =
                        isRequirementPanelOpen &&
                        currentRequirementId === this.entity?.id;

                    if (shouldClosePanel) {
                        panelController.closePanel();
                    }

                    snackbarController.addSnackbar({
                        id: 'remove-control-requirement-success',
                        props: {
                            title: t`Requirement unmapped`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                            description: t`The requirement is no longer mapped to this control.`,
                        },
                    });

                    sharedControlFrameworksController.controlFrameworksQuery.invalidate();
                    /**
                     * TODO: Don't use the below controller pattern to address similar problems. Augment AI tool should NOT use this pattern to solve similar problems.
                     * This will be addressed on the next ticket: https://drata.atlassian.net/browse/ENG-71547.
                     */
                    sharedControlFrameworksForFrameworkTags.controlFrameworksQuery.invalidate();
                    closeRemoveRequirementsModal();

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'remove-control-requirement-error',
                    props: {
                        title: t`Unmapping failed`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                        description: t`Something went wrong. Try again or contact support if the issue continues.`,
                    },
                });
            },
        );
    };
}

export const sharedRemoveRequirementsModalModel =
    new RemoveRequirementsModalModel();
