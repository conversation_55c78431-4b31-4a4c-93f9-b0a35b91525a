import { sharedRiskDetailsController } from '@controllers/risk-details';
import type { Tab } from '@controllers/route';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { NavigationItem } from '@ui/page-content';

export class RiskDetailsContentNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get tabs(): Tab[] {
        const { riskDetails } = sharedRiskDetailsController;
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!riskDetails || !currentWorkspaceId) {
            return [];
        }

        const { riskId } = riskDetails;

        return [
            {
                id: 'overview',
                topicPath: `risk/management/registers/1/risks/${riskId}/overview`,
                label: t`Overview`,
            },
            {
                id: 'mitigating-measures',
                topicPath: `risk/management/registers/1/risks/${riskId}/mitigating-controls`,
                label: t`Mitigating controls`,
            },
        ];
    }

    get config(): NavigationItem[] {
        const { riskDetails } = sharedRiskDetailsController;
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!riskDetails || !currentWorkspaceId) {
            return [];
        }

        const { riskId } = riskDetails;

        return [
            {
                id: 'overview',
                props: {
                    label: t`Overview`,
                    href: `/workspaces/${currentWorkspaceId}/risk/management/registers/1/risks/${riskId}/overview`,
                },
            },
            {
                id: 'mitigating-measures',
                props: {
                    label: t`Mitigating controls`,
                    href: `/workspaces/${currentWorkspaceId}/risk/management/registers/1/risks/${riskId}/mitigating-controls`,
                },
            },
        ];
    }
}
