import { capitalize, isEmpty } from 'lodash-es';
import { sharedRiskSettingsController } from '@controllers/risk';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import { RiskScore } from '@cosmos-lab/components/risk-score';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { getFullName, getInitials } from '@helpers/formatters';
import { getRiskTreatmentLabel } from '@helpers/risk-treatment';
import { calculateRiskMetrics } from '@views/risk-register-overview';

export class RiskDetailsContentHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    get title(): string {
        return `${sharedRiskDetailsController.riskDetails?.title}`;
    }

    get slot(): React.JSX.Element {
        return (
            <Metadata
                colorScheme="neutral"
                label={`${sharedRiskDetailsController.riskDetails?.riskId}`}
                type="tag"
            />
        );
    }

    get breadcrumbs(): Breadcrumb[] {
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!currentWorkspaceId) {
            return [];
        }

        return [
            {
                label: t`Register`,
                pathname: `/workspaces/${currentWorkspaceId}/risk/management/registers/1/register-risks`,
            },
        ];
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { riskDetails } = sharedRiskDetailsController;
        const { riskSettings } = sharedRiskSettingsController;

        const inheritMetrics = calculateRiskMetrics(
            riskDetails?.score ?? 0,
            riskSettings,
        );

        const residualMetrics = calculateRiskMetrics(
            riskDetails?.residualScore ?? 0,
            riskSettings,
        );

        return [
            {
                id: 'treatment',
                'data-id': 'treatment',
                label: t`Treatment`,
                value: riskDetails?.treatmentPlan
                    ? getRiskTreatmentLabel(riskDetails.treatmentPlan)
                    : '—',
                type: 'TEXT',
            },
            {
                id: 'score',
                'data-id': 'score',
                label: t`Score`,
                value: (
                    <RiskScore
                        intensity="moderate"
                        severity={inheritMetrics.severity}
                        scoreNumber={riskDetails?.score}
                        label={capitalize(inheritMetrics.threshold?.name)}
                    />
                ),
                type: 'REACT_NODE',
            },
            {
                id: 'residual-score',
                'data-id': 'residual-score',
                label: t`Residual score`,
                value: (
                    <RiskScore
                        intensity="moderate"
                        severity={residualMetrics.severity}
                        scoreNumber={riskDetails?.residualScore}
                        label={capitalize(residualMetrics.threshold?.name)}
                    />
                ),
                type: 'REACT_NODE',
            },
            {
                id: 'owner',
                'data-id': 'owner',
                label: t`Owner`,
                value: (() => {
                    if (!riskDetails?.owners || isEmpty(riskDetails.owners)) {
                        return null;
                    }

                    if (riskDetails.owners.length === 1) {
                        const owner = riskDetails.owners[0];
                        const { firstName, lastName, id, avatarUrl } = owner;
                        const fullName = getFullName(firstName, lastName);
                        const fallbackText = getInitials(fullName);

                        return (
                            <AvatarIdentity
                                key={id}
                                primaryLabel={fullName}
                                fallbackText={fallbackText}
                                imgSrc={avatarUrl}
                                data-id="xTMIBGty"
                            />
                        );
                    }

                    return (
                        <AvatarStack
                            data-id="xTMIBGty"
                            avatarData={riskDetails.owners.map((owner) => {
                                const { firstName, lastName, avatarUrl } =
                                    owner;
                                const fullName = getFullName(
                                    firstName,
                                    lastName,
                                );
                                const fallbackText = getInitials(fullName);

                                return {
                                    primaryLabel: fullName,
                                    fallbackText,
                                    imgSrc: avatarUrl,
                                };
                            })}
                        />
                    );
                })(),
                type: 'REACT_NODE',
            },
        ];
    }
}
