import { sharedRiskInsightsController } from '@controllers/risk';
import type { DashboardResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';

class RiskInsightsAssessmentProgressModel {
    constructor() {
        makeAutoObservable(this);
    }

    get riskInsights(): DashboardResponseDto | null {
        return sharedRiskInsightsController.riskInsights;
    }

    get isLoading(): boolean {
        return sharedRiskInsightsController.isLoading;
    }

    get assessmentScored(): number {
        return this.riskInsights?.scored ?? 0;
    }

    get assessmentRemaining(): number {
        return this.riskInsights?.remaining ?? 0;
    }

    get downloadDisabled(): boolean {
        return this.assessmentScored === 0 && this.assessmentRemaining === 0;
    }
}

export const sharedRiskInsightsAssessmentProgressModel =
    new RiskInsightsAssessmentProgressModel();
