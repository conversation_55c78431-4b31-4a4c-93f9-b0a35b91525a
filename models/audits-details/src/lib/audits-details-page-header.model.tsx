import { ChangeAuditPeriodModal } from '@components/change-audit-period-modal';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedAuditorController } from '@controllers/auditor';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import { Banner } from '@cosmos/components/banner';
import { Button } from '@cosmos/components/button';
import { Icon } from '@cosmos/components/icon';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import {
    FrameworkBadge,
    getFrameworkBadge,
} from '@cosmos-lab/components/framework-badge';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { formatDate } from '@helpers/date-time';
import { AppLink } from '@ui/app-link';

export class AuditDetailsPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    openChangeAuditPeriodModal = (): void => {
        const auditFramework =
            sharedAuditHubController.auditByIdData?.framework;

        if (!auditFramework) {
            logger.warn({
                message: 'Failed to open change audit period modal',
                additionalInfo: {
                    auditData: sharedAuditHubController.auditByIdData,
                },
            });

            snackbarController.addSnackbar({
                id: 'change-audit-period-error',
                props: {
                    title: t`Failed to open change audit period modal`,
                    description: t`Audit framework is missing`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const handleClose = () => {
            modalController.closeModal('change-audit-period-modal');
        };

        modalController.openModal({
            id: 'change-audit-period-modal',
            content: () => (
                <ChangeAuditPeriodModal
                    auditFramework={auditFramework}
                    data-id="u09WgV5R"
                    onClose={handleClose}
                />
            ),
            centered: true,
            size: 'md',
        });
    };

    get actionStack(): React.JSX.Element {
        const { auditSummaryByIdData: summaryData } = sharedAuditorController;
        const { auditorFramework } = summaryData ?? {};
        const isCompleted = auditorFramework?.status === 'COMPLETED';

        return (
            <Stack direction="row" gap="lg" align="center">
                <SchemaDropdown
                    isIconOnly
                    label={t`More audit details action options`}
                    startIconName="HorizontalMenu"
                    level="tertiary"
                    data-id="audit-details-header-action-1"
                    side="left"
                    items={[
                        {
                            id: 'audit-details-header-subaction-1',
                            label: t`Pre audit package`,
                            startSlot: <Icon name="Download" size="200" />,
                            type: 'subMenu',
                            items: [
                                {
                                    id: 'audit-details-header-subaction-1-submenu-1',
                                    label: t`Request package`,
                                    type: 'item',
                                },
                                {
                                    id: 'audit-details-header-subaction-1-submenu-2',
                                    label: t`Control Mapping`,
                                    type: 'item',
                                },
                                {
                                    id: 'audit-details-header-subaction-1-submenu-3',
                                    label: t`Connections`,
                                    type: 'item',
                                },
                                {
                                    id: 'audit-details-header-subaction-1-submenu-4',
                                    label: t`Vendors`,
                                    type: 'item',
                                },
                                {
                                    id: 'audit-details-header-subaction-1-submenu-5',
                                    label: t`Assets`,
                                    type: 'item',
                                },
                                {
                                    id: 'audit-details-header-subaction-1-submenu-6',
                                    label: t`Evidence Library`,
                                    description: t`Manual evidence only`,
                                    type: 'item',
                                },
                                {
                                    id: 'audit-details-header-subaction-1-submenu-7',
                                    label: t`Infrastructure access`,
                                    type: 'item',
                                },
                                {
                                    id: 'audit-details-header-subaction-1-submenu-8',
                                    label: t`Version control`,
                                    type: 'item',
                                },
                            ],
                        },
                        {
                            id: 'audit-details-header-subaction-2',
                            label: t`Request control evidence`,
                            startSlot: <Icon name="Download" size="200" />,
                            type: 'item',
                        },
                        {
                            id: 'audit-details-header-subaction-3',
                            label: t`View past downloads`,
                            startSlot: <Icon name="Visible" size="200" />,
                            type: 'item',
                        },
                        {
                            id: 'audit-details-header-subaction-4',
                            type: 'group',
                            items: [
                                {
                                    id: 'audit-details-header-subaction-4-group-1-group-action-1',
                                    label: t`Delete audit`,
                                    startSlot: (
                                        <Icon
                                            name="Trash"
                                            size="200"
                                            colorScheme="critical"
                                        />
                                    ),
                                    colorScheme: 'critical',
                                    type: 'item',
                                },
                            ],
                        },
                    ]}
                />
                <Button
                    label={isCompleted ? t`Mark as active` : t`Complete audit`}
                    level="secondary"
                    data-id="audit-details-header-action-2"
                />
            </Stack>
        );
    }

    get backLink(): React.JSX.Element {
        const { currentWorkspace } = sharedWorkspacesController;
        const allAuditsPath = `/workspaces/${currentWorkspace?.id}/compliance/audits/all`;

        return (
            <Stack gap="md" align="center">
                <AppLink href={allAuditsPath} size="sm">
                    {t`Audits`}
                </AppLink>
                <Text>/</Text>
            </Stack>
        );
    }

    get title(): string {
        return `${
            sharedAuditHubController.auditByIdData?.framework.frameworkType
                .label
        }`;
    }

    get slot(): React.JSX.Element {
        const frameworkType =
            sharedAuditHubController.auditByIdData?.framework.type || 'CUSTOM';

        return <FrameworkBadge badgeName={getFrameworkBadge(frameworkType)} />;
    }

    get keyValuePairs(): KeyValuePairProps[] {
        if (sharedAuditHubController.auditByIdIsLoading) {
            return [];
        }

        const auditFrameworkDateType =
            sharedAuditHubController.auditByIdData?.framework.frameworkType
                .dateType;
        const formatMode =
            auditFrameworkDateType === 'SINGLE' ? 'field' : 'field_range';

        return [
            {
                id: 'audit-period',
                label: t`Audit period`,
                value: formatDate(
                    formatMode,
                    sharedAuditHubController.auditByIdData?.framework.startDate,
                    auditFrameworkDateType === 'SINGLE'
                        ? undefined
                        : sharedAuditHubController.auditByIdData?.framework
                              .endDate,
                ),
                iconName: 'Edit',
                iconSize: '200',
                onClick: () => {
                    this.openChangeAuditPeriodModal();
                },
                ariaLabel: t`Edit audit period`,
            },
        ];
    }

    get banner(): React.JSX.Element | undefined {
        return (
            <Banner
                title={t`Audit pages can also be accessed by assigned auditors`}
                severity="primary"
                displayMode="section"
                data-id="audit-pages-banner"
                body={
                    <AppLink
                        isExternal
                        href="https://help.drata.com/en/articles/6928357-audit-hub"
                        data-id="audit-pages-learn-more-link"
                    >
                        {t`Learn about audit pages`}
                    </AppLink>
                }
            />
        );
    }
}
