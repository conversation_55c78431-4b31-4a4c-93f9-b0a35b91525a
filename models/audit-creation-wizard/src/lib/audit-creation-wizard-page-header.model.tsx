import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { AppLink } from '@ui/app-link';

export class AuditCreationWizardPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'audit-creation-wizard-page-header';

    get title(): string {
        return t`Create Audit`;
    }

    get isCentered(): boolean {
        return true;
    }

    get workspaceId(): number | null {
        return sharedWorkspacesController.currentWorkspaceId;
    }

    get backLink(): React.JSX.Element | undefined {
        if (!this.workspaceId) {
            return undefined;
        }

        return (
            <AppLink
                href={`/workspaces/${this.workspaceId}/compliance/audits`}
                label={t`Back to audits`}
                size="sm"
            />
        );
    }
}
