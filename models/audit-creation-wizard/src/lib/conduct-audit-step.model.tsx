import { z } from 'zod';
import drataIconWhite from '@assets/drata/misc/img/drata-icon-white.png';
import { Organization } from '@cosmos-lab/components/organization';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';

export class ConductAuditStepModel {
    constructor() {
        makeAutoObservable(this);
    }

    get formSchema(): FormSchema {
        return {
            conductAuditOption: {
                type: 'choiceCardGroup',
                label: t`Choose how to conduct an audit`,
                choiceCardInputType: 'radio',
                validator: z
                    .string({
                        required_error: t`Please choose an option to continue`,
                    })
                    .min(1),
                cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                options: [
                    {
                        label: t`Conduct audit in Drata`,
                        value: 'conduct-in-drata',
                        helpText: t`Invite an auditor to manage the audit end-to-end in Drata`,
                        slot: (
                            <Organization
                                imgAlt="D"
                                imgSrc={drataIconWhite}
                                size="lg"
                                data-id="conduct-audit-option-drata-logo"
                                data-testid="ConductAuditOptionDrataLogo"
                                fallbackText="D"
                            />
                        ),
                    },
                    {
                        label: t`Download only`,
                        value: 'download-only',
                        helpText: t`Only download material to conduct an audit outside of Drata`,
                    },
                ],
            },
        };
    }
}

export const sharedConductAuditStepModel = new ConductAuditStepModel();
