import {
    openApproveControlModal,
    openRequestChangesModal,
} from '@components/control-approval';
import {
    sharedControlApprovalReviewersController,
    sharedControlApprovalsController,
    sharedControlApprovalsReviewersMutationController,
    sharedControlOwnersController,
} from '@controllers/controls';
import type { Action } from '@cosmos/components/action-stack';
import { ApprovalStatus } from '@drata/enums';
import { sharedCurrentUserController } from '@globals/current-user';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';

class ControlDetailsApproversActionsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get state() {
        const { currentControlApproval } = sharedControlApprovalsController;
        const { controlApprovalsReviewers } =
            sharedControlApprovalReviewersController;
        const { user } = sharedCurrentUserController;
        const { isUpdating, updateCurrentControlApproval } =
            sharedControlApprovalsReviewersMutationController;

        return {
            currentControlApproval,
            controlApprovalsReviewers,
            user,
            isUpdating,
            updateCurrentControlApproval,
            isCurrentUserApprover: Boolean(
                controlApprovalsReviewers.some(
                    (approver) => approver.id === user?.id,
                ),
            ),
            approvalStatus: currentControlApproval?.approvalStatus,
            hasApprovalWithoutOwners:
                !sharedControlOwnersController.controlHasOwners &&
                currentControlApproval,
        };
    }

    get actionConfigs() {
        const { isUpdating, updateCurrentControlApproval } = this.state;

        return {
            sendToApprovers: {
                id: 'send-to-approvers-button',
                actionType: 'button' as const,
                typeProps: {
                    label: t`Send to approvers`,
                    level: 'primary' as const,
                    isLoading: isUpdating,
                    onClick: action(() => {
                        updateCurrentControlApproval({
                            status: ApprovalStatus.READY_FOR_REVIEWS,
                        });
                    }),
                },
            },
            approve: {
                id: 'approve-button',
                actionType: 'button' as const,
                typeProps: {
                    label: t`Approve`,
                    level: 'primary' as const,
                    onClick: openApproveControlModal,
                    isLoading: isUpdating,
                },
            },
            requestChanges: {
                id: 'request-changes-button',
                actionType: 'button' as const,
                typeProps: {
                    label: t`Request changes`,
                    level: 'secondary' as const,
                    onClick: openRequestChangesModal,
                    isLoading: isUpdating,
                },
            },
        };
    }

    getActionKeysForStatus(
        status: ApprovalStatus | undefined,
        isApprover: boolean,
    ): string[] {
        if (!status) {
            return [];
        }

        switch (status) {
            case ApprovalStatus.PREPARE_FOR_REVIEWS:
            case ApprovalStatus.CHANGES_REQUESTED: {
                return ['sendToApprovers'];
            }

            case ApprovalStatus.READY_FOR_REVIEWS: {
                return isApprover ? ['requestChanges', 'approve'] : [];
            }

            case ApprovalStatus.APPROVED: {
                return isApprover ? ['requestChanges'] : [];
            }

            default: {
                return [];
            }
        }
    }

    buildActions = (actionKeys: string[]): Action[] => {
        const configs = this.actionConfigs;

        return actionKeys
            .map((key) => configs[key as keyof typeof configs])
            .filter(Boolean);
    };

    get hasEditPermission(): boolean {
        const {
            hasWriteControlPermission,
            isControlManagerWithRestrictedView,
        } = sharedFeatureAccessModel;

        return !hasWriteControlPermission || isControlManagerWithRestrictedView;
    }

    /**
     * Main actions (request changes first, then approve).
     */
    get actions(): Action[] {
        const {
            approvalStatus,
            isCurrentUserApprover,
            hasApprovalWithoutOwners,
        } = this.state;

        if (hasApprovalWithoutOwners || this.hasEditPermission) {
            return [];
        }
        const actionKeys = this.getActionKeysForStatus(
            approvalStatus,
            isCurrentUserApprover,
        );

        return this.buildActions(actionKeys);
    }

    /**
     * Card actions (approve first, then request changes).
     */
    get actionsForCard(): Action[] {
        const {
            approvalStatus,
            isCurrentUserApprover,
            hasApprovalWithoutOwners,
        } = this.state;

        if (hasApprovalWithoutOwners || this.hasEditPermission) {
            return [];
        }

        const actionKeys = this.getActionKeysForStatus(
            approvalStatus,
            isCurrentUserApprover,
        );

        // For card view, prioritize approve button when both actions are present
        if (
            approvalStatus === ApprovalStatus.READY_FOR_REVIEWS &&
            actionKeys.length === 2
        ) {
            return this.buildActions(['approve', 'requestChanges']);
        }

        return this.buildActions(actionKeys);
    }
}

export const sharedControlDetailsApproversActionsModel =
    new ControlDetailsApproversActionsModel();
