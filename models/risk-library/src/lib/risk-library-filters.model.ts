import type { ComponentProps } from 'react';
import { sharedRiskCategoriesFilterController } from '@controllers/risk';
import type { Datatable } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

export type RiskLibraryFilterProps = ComponentProps<
    typeof Datatable
>['filterProps'];

class RiskLibraryFiltersModel {
    constructor() {
        makeAutoObservable(this);
    }

    get filters(): RiskLibraryFilterProps {
        const { options, hasNextPage, isFetching, isLoading, loadNextPage } =
            sharedRiskCategoriesFilterController;

        return {
            clearAllButtonLabel: t`Reset`,
            filters: [
                {
                    isMultiSelect: true,
                    filterType: 'combobox',
                    id: 'categories',
                    label: t`Categories`,
                    placeholder: t`Search categories...`,
                    hasMore: hasNextPage,
                    isLoading: isFetching && isLoading,
                    options,
                    onFetchOptions: loadNextPage,
                    clearSelectedItemButtonLabel: t`Clear`,
                    removeAllSelectedItemsLabel: t`Remove all`,
                },
            ],
        };
    }
}

export const sharedRiskLibraryFiltersModel = new RiskLibraryFiltersModel();
