import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import { dimension3x } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

export class SingleRegisterPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'risk-management-page';
    title = t`Risk management`;

    get actions(): Action[] {
        return [
            {
                actionType: 'button',
                id: 'risk-configure-register-button',
                typeProps: {
                    label: t`Configure register`,
                    level: 'tertiary',
                    startIconName: 'Settings',
                    onClick: () => {
                        // TODO: Configure register settings action(s)
                    },
                },
            },
            {
                actionType: 'dropdown',
                id: 'risk-management-actions-dropdown',
                typeProps: {
                    label: t`Add`,
                    level: 'primary',
                    endIconName: 'ChevronDown',
                    align: 'end',
                    items: [
                        {
                            id: 'create-risk',
                            label: t`Create risk`,
                            description: t`Track a custom risk`,
                            type: 'item',
                            startIconName: 'Plus',
                            onSelect: () => {
                                sharedProgrammaticNavigationController.navigateTo(
                                    `${routeController.userPartOfUrl}/risk/management/registers/1/create-risk`,
                                );
                            },
                        },
                        // TODO: hide action based on entitlement + FF
                        {
                            id: 'divider',
                            type: 'group',
                            items: [
                                {
                                    id: 'create-register',
                                    label: t`Create register`,
                                    description: t`Build a new risk register`,
                                    type: 'item',
                                    startIconName: 'Plus',
                                    onSelect: () => {
                                        sharedProgrammaticNavigationController.navigateTo(
                                            `${routeController.userPartOfUrl}/risk/management/create-register`,
                                        );
                                    },
                                },
                                {
                                    id: 'bulk-import',
                                    label: t`Bulk import`,
                                    description: t`Import risks from csv`,
                                    type: 'item',
                                    startIconName: 'Settings',
                                    onSelect: () => {
                                        // TODO: Implement bulk import risks action
                                    },
                                },
                            ],
                        },
                    ],
                },
            },
        ];
    }

    get actionStack(): React.JSX.Element {
        return (
            <ActionStack
                data-id="risk-management-page-action-stack"
                gap={dimension3x}
                stacks={[
                    {
                        actions: this.actions,
                        id: 'risk-management-actions-stack',
                    },
                ]}
            />
        );
    }
}
