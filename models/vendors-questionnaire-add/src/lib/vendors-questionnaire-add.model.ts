import { isEmpty } from 'lodash-es';
import type { QuestionnaireRequestDto } from '@globals/api-sdk/types';
import { computed, makeAutoObservable } from '@globals/mobx';

/**
 * Update all questions required state.
 */
const updateAllQuestionsRequired = (
    questions: QuestionnaireFormData['questions'],
    required: boolean,
): void => {
    questions.forEach((question) => {
        question.required = required;
    });
};

type QuestionnaireFormData = Omit<
    QuestionnaireRequestDto,
    'status' | 'fields'
> & {
    markAllAsRequired: boolean;
    questions: (Omit<QuestionnaireRequestDto['fields'][0], 'ref'> & {
        id?: string;
        required: boolean;
        includeFollowUpQn: boolean;
    })[];
};

export class VendorsQuestionnaireAddModel {
    formData: QuestionnaireFormData = {
        title: 'New Questionnaire',
        categories: [],
        riskLevels: [],
        markAllAsRequired: false,
        questions: [
            {
                id: `question-${Date.now()}`,
                title: 'New Question',
                type: 'LONG_ANSWER',
                required: false,
                shortAnswerType: 'TEXT',
                choices: [
                    {
                        ref: `choice-${Date.now()}`,
                        label: '',
                    },
                ],
                followUpQn: '',
                allowOtherChoice: false,
                includeFollowUpQn: false,
                followUpQnTrigger: false,
            },
        ],
    };

    constructor() {
        makeAutoObservable(this, {
            canSubmit: computed,
        });
    }

    get canSubmit(): boolean {
        return (
            !isEmpty(this.formData.title.trim()) &&
            !isEmpty(this.formData.categories) &&
            !isEmpty(this.formData.riskLevels) &&
            !isEmpty(this.formData.questions) &&
            this.formData.questions.every(
                (question) =>
                    !isEmpty(question.title.trim()) &&
                    this.isQuestionValid(question),
            )
        );
    }

    /**
     * Validate individual question fields.
     */
    private isQuestionValid(
        question: QuestionnaireFormData['questions'][0],
    ): boolean {
        // For SHORT_ANSWER questions, shortAnswerType is required
        // Note: DTO types are outdated, but API accepts all types
        const questionType = question.type as string;

        if (
            questionType === 'SHORT_ANSWER' ||
            questionType === 'short_answer'
        ) {
            return !isEmpty(question.shortAnswerType);
        }

        // For MULTIPLE_CHOICE and CHECKBOXES questions, at least one choice with content is required
        if (
            questionType === 'MULTIPLE_CHOICE' ||
            questionType === 'CHECKBOXES'
        ) {
            return Boolean(
                question.choices?.some((choice) => choice.label.trim() !== ''),
            );
        }

        return true;
    }

    /**
     * Update form data for a specific field.
     */
    updateFormField = <K extends keyof QuestionnaireFormData>(
        field: K,
        value: QuestionnaireFormData[K],
    ): void => {
        this.formData[field] = value;
    };

    /**
     * Update a specific question property.
     */
    updateQuestion = <K extends keyof QuestionnaireFormData['questions'][0]>(
        index: number,
        field: K,
        value: QuestionnaireFormData['questions'][0][K],
    ): void => {
        if (this.formData.questions[index]) {
            this.formData.questions[index][field] = value;
        }
    };

    /**
     * Toggle mark all as required.
     */
    toggleMarkAllAsRequired = (): void => {
        this.formData.markAllAsRequired = !this.formData.markAllAsRequired;
        // Update all questions to match the new required state
        updateAllQuestionsRequired(
            this.formData.questions,
            this.formData.markAllAsRequired,
        );
    };

    /**
     * Add a new question to the questionnaire.
     */
    addQuestion = (): void => {
        const newQuestion = {
            id: `question-${Date.now()}`,
            title: `New Question`,
            type: 'LONG_ANSWER' as const,
            required: this.formData.markAllAsRequired,
            shortAnswerType: 'TEXT' as const,
            choices: [
                {
                    ref: `choice-${Date.now()}`,
                    label: '',
                },
            ],
            followUpQn: '',
            allowOtherChoice: false,
            includeFollowUpQn: false,
            followUpQnTrigger: false,
        };

        this.formData.questions.push(newQuestion);
    };

    /**
     * Remove a question from the questionnaire.
     */
    removeQuestion = (index: number): void => {
        if (this.formData.questions.length > 1) {
            this.formData.questions.splice(index, 1);
        }
    };

    /**
     * Reorder questions (for drag and drop).
     */
    reorderQuestions = (startIndex: number, endIndex: number): void => {
        const result = [...this.formData.questions];
        const [removed] = result.splice(startIndex, 1);

        result.splice(endIndex, 0, removed);
        this.formData.questions = result;
    };

    /**
     * Reset form to initial state.
     */
    resetForm = (): void => {
        this.formData = {
            title: '',
            categories: [],
            riskLevels: [],
            markAllAsRequired: false,
            questions: [
                {
                    id: `question-${Date.now()}`,
                    title: 'New Question',
                    type: 'LONG_ANSWER',
                    required: false,
                    shortAnswerType: 'TEXT',
                    choices: [
                        {
                            ref: `choice-${Date.now()}`,
                            label: '',
                        },
                    ],
                    followUpQn: '',
                    allowOtherChoice: false,
                    includeFollowUpQn: false,
                    followUpQnTrigger: false,
                },
            ],
        };
    };
}
