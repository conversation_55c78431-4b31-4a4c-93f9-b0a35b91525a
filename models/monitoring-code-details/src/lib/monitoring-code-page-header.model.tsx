import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { ActionStack } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { dimensionSm } from '@cosmos/constants/tokens';
import { makeAutoObservable } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { getTestResultData } from '@views/monitoring-code';
import { buildCategoryLabel } from './config/monitoring-code-header.config';

export class MonitoringCodePageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'monitoring-code-overview-page';

    get actionStack(): React.JSX.Element {
        return (
            <ActionStack
                data-id="page-header-action-stack"
                gap={dimensionSm}
                data-testid="MonitoringBuilderHeaderActionStack"
                actions={[
                    {
                        id: 'secondary-action-button',
                        actionType: 'button',
                        typeProps: {
                            label: 'Test now',
                            level: 'secondary',
                        },
                    },
                ]}
            />
        );
    }

    get title(): string {
        return sharedMonitoringTestDetailsController.testName ?? 'LOADING...';
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { checkResultStatus, testDetails, category, source } =
            sharedMonitoringTestDetailsController;

        return [
            {
                id: 'monitoring-header-latest-test-result',
                'data-id': 'monitoring-header-status',
                label: 'Latest test result',
                value: getTestResultData(checkResultStatus ?? ''),
                type: 'TAG',
            },
            {
                id: 'monitoring-header-creation-date',
                'data-id': 'monitoring-header-creation-date',
                label: 'Latest test run',
                value: formatDate(
                    'field_time',
                    testDetails?.lastCheck ?? undefined,
                ),
                type: 'TEXT',
            },
            {
                id: 'monitoring-header-test-lifecycle',
                'data-id': 'monitoring-header-test-lifecycle',
                label: 'Test lifecycle',
                value: source === 'ACORN' ? 'Codebase' : '-',
                type: 'TEXT',
            },
            {
                id: 'monitoring-header-category',
                'data-id': 'monitoring-header-category',
                label: 'Category',
                value: buildCategoryLabel(category),
                type: 'TEXT',
            },
        ];
    }
}
