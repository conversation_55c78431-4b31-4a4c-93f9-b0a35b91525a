import { isNil } from 'lodash-es';
import { sharedMonitoringCodeExclusionsController } from '@controllers/monitoring';
import { sharedFindingsController } from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { makeAutoObservable } from '@globals/mobx';
import type { NavigationItem } from '@ui/page-content';

export class MonitoringCodeDetailsNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get config(): NavigationItem[] {
        const { testDetails } = sharedMonitoringTestDetailsController;
        const { monitoringCodeExclusionsTotal } =
            sharedMonitoringCodeExclusionsController;

        const { findingsListTotal } = sharedFindingsController;

        if (isNil(testDetails)) {
            return [];
        }

        const { testId: id } = testDetails;

        return [
            {
                id: 'overview',
                props: {
                    label: 'Overview',
                    href: `/compliance/monitoring/code/details/${id}/overview`,
                },
            },
            {
                id: 'findings',
                props: {
                    label: 'Findings',
                    href: `/compliance/monitoring/code/details/${id}/findings`,
                    ...(findingsListTotal > 0 && {
                        metadata: {
                            label: findingsListTotal.toString(),
                            type: 'number',
                            colorScheme: 'critical',
                        },
                    }),
                },
            },
            {
                id: 'exclusions',
                props: {
                    label: 'Exclusions',
                    href: `/compliance/monitoring/code/details/${id}/exclusions`,
                    ...(monitoringCodeExclusionsTotal > 0 && {
                        metadata: {
                            label: monitoringCodeExclusionsTotal.toString(),
                            colorScheme: 'neutral',
                            type: 'number',
                        },
                    }),
                },
            },
            {
                id: 'controls',
                props: {
                    label: 'Controls',
                    href: `/compliance/monitoring/code/details/${id}/controls`,
                },
            },
        ];
    }
}
