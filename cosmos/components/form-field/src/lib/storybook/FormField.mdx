import { Controls, Description, Meta, Primary, Title } from '@storybook/addon-docs/blocks';
import * as FormFieldStories from './FormField.stories';
import { Banner } from '@cosmos/components/banner';

<Meta of={FormFieldStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos form component, you are probably looking for a different component."
/>

<Description />

<br />

<Primary />

<Controls of={FormFieldStories.Playground} />

## Import

```jsx
import { FormField } from '@drata/cosmos-form-field';
```

## 🟢 When to use the component

- **Building Cosmos form components** - Use as the foundational wrapper when creating new form field components for the design system
- **Form field composition** - Combine with FieldLabel, FieldFeedback, and input components to create complete form fields
- **Consistent form layouts** - Ensure uniform spacing, labeling, and feedback patterns across all form components
- **Accessibility compliance** - Provide proper ARIA relationships, labeling, and form field structure

## ❌ When not to use the component

- **Application development** - Use complete form field components (TextField, SelectField, etc.) instead of building with FormField directly
- **Non-form contexts** - Use appropriate layout components for non-form content organization
- **Custom form systems** - This component is specifically designed for Cosmos design system patterns

## 🛠️ How it works

The FormField component provides foundational structure for building Cosmos form components with consistent labeling, feedback, and accessibility patterns.

**Core functionality:**
- **Flexible wrapper** - Uses configurable HTML elements (div, fieldset) based on `role` prop
- **Layout system** - Supports stack, input-left, and input-right patterns with CSS Grid
- **Component integration** - Conditionally renders FieldLabel and FieldFeedback with proper relationships
- **Render prop pattern** - Uses `renderInput` function for flexible input component integration

**Layout options:**
- **Stack layout** - Default vertical layout with label above input and feedback below
- **Input-left/right layouts** - Horizontal layouts with configurable positioning
- **Responsive gaps** - Separate gap controls for each layout type
- **Field width** - Full-width default or fit-content for compact layouts

**Accessibility features:**
- **Semantic HTML** - Appropriate wrapper elements based on field type (div vs fieldset)
- **ARIA relationships** - Automatic `htmlFor`, `aria-describedby`, and `aria-labelledby` connections
- **Role-based rendering** - Renders `<legend>` for radio/checkbox groups, `<label>` for other inputs
- **ID management** - Automatic generation of unique IDs for proper form relationships

### Usability

- **Stack layout** for most form fields where vertical space is available
- **Input-right layout** for checkboxes, toggles, and compact arrangements
- **Input-left layout** for specialized cases where input precedes label
- Automatic required/optional text handling based on `required` prop
- Seamless help text integration with proper ARIA relationships
- Support for visually hidden labels when inputs provide sufficient context

### Content

- Use clear, specific labels that explain the field's purpose
- Maintain consistent terminology across similar form fields
- Use help text to explain complex requirements or provide examples
- Provide context about why information is needed
- Required fields are clearly marked; optional fields show "optional" text when `required={false}`

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper form field relationships using label, fieldset, and legend elements
- Full keyboard navigation support with logical tab order and proper focus management
- Screen reader announcements for labels, help text, validation feedback, and field relationships
- High contrast support that works with system accessibility preferences and meets WCAG AA standards
- Scalable typography and spacing that works with browser zoom up to 200% magnification
- Proper ARIA relationships including `aria-describedby`, `aria-labelledby`, and `htmlFor` attributes

**Development responsibilities:**
- Provide meaningful `label` prop that clearly describes the field's purpose and requirements
- Use appropriate `role` prop (radiogroup, group) for grouped inputs like radio buttons and checkboxes
- Implement proper `renderInput` function that uses all provided accessibility props
- Ensure `formId` and `name` props create proper form associations for data submission
- Handle validation feedback appropriately with clear, actionable error messages
- Use `cosmosUseWithCaution_hideLabelFromScreenReaders` only when inputs provide their own accessible labels

**Design responsibilities:**
- Provide sufficient color contrast for all field states, labels, and feedback across different themes
- Design clear visual hierarchy that shows relationships between labels, inputs, help text, and feedback
- Ensure focus indicators are clearly visible and meet contrast requirements for all interactive elements
- Create consistent visual patterns for required/optional fields, validation states, and field layouts
- Design appropriate spacing and sizing for different layout options (stack, input-left, input-right)
- Ensure feedback styling provides clear visual cues that complement screen reader announcements

