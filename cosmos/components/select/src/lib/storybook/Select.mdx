import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';

import * as SelectStories from './Select.stories';

<Meta of={SelectStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos form component, you are probably looking for SelectField."
/>
<br />

<Description />

<Primary />

<Controls of={SelectStories.Playground} />

## Import

```jsx
import { Select } from '@drata/cosmos-select';
```

## Props

### `defaultValue`

<Canvas of={SelectStories.DefaultValue} />

## 🟢 When to use the component

- **Building Cosmos components** - This is a foundational component intended only for creating other Cosmos form components

## ❌ When not to use the component

- **Standard form fields** - Use SelectField for typical form implementations with labels, validation, and help text
- **Multi-select scenarios** - Use ComboboxField for multiple selection capabilities
- **Searchable dropdowns** - Use ComboboxField when users need to search through options

## 🛠️ How it works

The Select component provides foundational dropdown selection functionality for building other Cosmos form components, allowing users to choose one option from a short list with keyboard navigation and accessibility support.

**Core functionality:**
- **Downshift integration** - Uses `useSelect` hook for robust dropdown state management and accessibility
- **Popover positioning** - Leverages Popover component with Floating UI for intelligent dropdown placement
- **ListBox rendering** - Displays options using the ListBox component with proper ARIA relationships
- **Single selection** - Users can select exactly one option with controlled `value` prop and `onChange` callback
- **Option structure** - Accepts options as `ListBoxItems` array supporting both individual options and option groups
- **Loading support** - `isLoading` prop shows spinner instead of options with customizable `loaderLabel`

### Usability

- **Foundation layer** - Designed as a building block for higher-level form components
- **Controlled usage** - Always use with controlled `value` prop and `onChange` handler
- **Clear labeling** - Each option should have a clear, descriptive label with logical grouping when appropriate
- **Keyboard efficiency** - Component provides full keyboard navigation out of the box

### Content

- **Descriptive labels** - Each option should clearly describe what it represents with consistent grammatical structure
- **Action-oriented placeholders** - Use placeholders that guide user action ("Select an option", "Choose...")
- **Informative loading** - Use descriptive `loaderLabel` text that explains what's loading
- **Logical organization** - Group related options together when using option groups with clear group labels

### Accessibility

**What the design system provides:**
- Complete keyboard navigation with arrow keys for option selection, Enter/Space to select, and Escape to close dropdown
- Semantic HTML structure with proper ARIA roles including `role="listbox"` for options and `role="combobox"` for trigger
- Automatic ARIA relationships with `aria-labelledby`, `aria-describedby`, and `aria-controls` connecting trigger to dropdown
- High contrast support that works with system accessibility preferences and meets WCAG AA standards for all states
- Screen reader announcements for selection changes, dropdown state, and option navigation
- Focus management that maintains logical tab order and clear focus indicators throughout interaction

**Development responsibilities:**
- Provide meaningful `aria-labelledby` prop that connects to an appropriate label element for the select
- Use `aria-describedby` prop to connect help text, instructions, or error messages when present
- Ensure `id` prop is unique and stable across renders for proper ARIA relationship establishment
- Implement proper `onChange` handling that updates application state when selection changes
- Use `required` prop appropriately when selection is mandatory for form submission
- Provide clear, descriptive option labels that work well with screen reader announcement patterns

**Design responsibilities:**
- Provide sufficient color contrast for trigger, dropdown, options, and focus states across all themes
- Design clear visual hierarchy that distinguishes between trigger, dropdown, selected options, and hover states
- Ensure focus indicators are clearly visible and meet contrast requirements for keyboard navigation
- Create consistent visual patterns for select styling that work across different contexts and container sizes
- Design appropriate spacing and sizing that works with browser zoom up to 200% magnification
- Ensure selected, unselected, disabled, loading, and error states are clearly distinguishable through visual design

