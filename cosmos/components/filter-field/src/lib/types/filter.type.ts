import type { CheckboxFieldGroupProps } from '@cosmos/components/checkbox-field-group';
import type { ComboboxFieldProps } from '@cosmos/components/combobox-field';
import type { DatePickerFieldProps } from '@cosmos/components/date-picker-field';
// import type { FileUploadFieldProps } from '@cosmos/components/file-upload-field';
import type { RadioFieldGroupProps } from '@cosmos/components/radio-field-group';
import type { SelectFieldProps } from '@cosmos/components/select-field';
import type { SliderFieldProps } from '@cosmos/components/slider-field';
import type { TextFieldProps } from '@cosmos/components/text-field';
import type { TextareaFieldProps } from '@cosmos/components/textarea-field';
import type { ToggleFieldProps } from '@cosmos/components/toggle-field';

interface BaseFilter {
    id: string;
    label: string;
    parentId?: string;
}

type ComboboxFilterProps = Partial<
    Pick<
        ComboboxFieldProps,
        | 'defaultSelectedOptions'
        | 'defaultValue'
        | 'isMultiSelect'
        | 'itemToString'
        | 'getRemoveIndividualSelectedItemClickLabel'
        | 'getSearchEmptyState'
        | 'onFetchOptions'
        | 'options'
        | 'placeholder'
        | 'removeAllSelectedItemsLabel'
        | 'searchDebounce'
        | 'tagGroupColorScheme'
        | 'clearSelectedItemButtonLabel'
        | 'hasMore'
        | 'isLoading'
    >
>;

type SelectFilterProps = Partial<SelectFieldProps>;

type SliderFilterProps = Partial<
    Pick<
        SliderFieldProps,
        'value' | 'min' | 'max' | 'step' | 'defaultValue' | 'inputLabels'
    >
>;

type DatePickerFilterProps = Pick<
    DatePickerFieldProps,
    | 'locale'
    | 'monthSelectionFieldLabel'
    | 'yearSelectionFieldLabel'
    | 'calendarPlacement'
    | 'defaultValue'
    | 'dateUnavailableText'
    | 'getIsDateUnavailable'
>;

// type FileUploadFilterProps = Pick<
//     FileUploadFieldProps,
//     | 'acceptedFormats'
//     | 'errorCodeMessages'
//     | 'innerLabel'
//     | 'isMulti'
//     | 'maxFileSizeInBytes'
//     | 'removeButtonText'
//     | 'selectButtonText'
//     | 'showDropzone'
//     | 'showFileList'
// >;

type TextFilterProps = Pick<TextFieldProps, 'onFocus' | 'onBlur' | 'onKeyDown'>;

type TextareaFilterProps = Pick<
    TextareaFieldProps,
    'maxCharacters' | 'rows' | 'onBlur' | 'onClick' | 'onFocus' | 'onKeyDown'
>;

type ToggleFilterProps = Pick<
    ToggleFieldProps,
    'checked' | 'defaultChecked' | 'layout'
>;

export type Filter = BaseFilter &
    (
        | {
              filterType: 'checkbox' | 'radio';
              value?:
                  | CheckboxFieldGroupProps['value']
                  | RadioFieldGroupProps['value'];
              options:
                  | CheckboxFieldGroupProps['options']
                  | RadioFieldGroupProps['options'];
          }
        | (ComboboxFilterProps & { filterType: 'combobox' })
        | (SliderFilterProps & { filterType: 'slider' })
        | (DatePickerFilterProps & { filterType: 'date' })
        // NOTE: Disabled because this doesn't make sense, come talk to Tyler before enabling
        // | (FileUploadFilterProps & { filterType: 'file' })
        | (TextFilterProps & { filterType: 'text' })
        | (TextareaFilterProps & { filterType: 'textarea' })
        | (ToggleFilterProps & { filterType: 'toggle' })
        | (SelectFilterProps & { filterType: 'select' })
    );
// TODO: https://drata.atlassian.net/browse/ENG-66359
