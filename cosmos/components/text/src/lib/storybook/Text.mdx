import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as TextStories from './Text.stories';
import { Banner } from '@cosmos/components/banner';

<Meta of={TextStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="Note: The `span` element is not compatible with the `shouldWrap` prop. For text wrapping control, consider using a block-level element like `div`."
/>

<br />
<Banner
    severity="warning"
    title="Important"
    body="Note: The `span` element is not compatible with the `align` prop. For text alignment control, consider using a block-level element like `div`."
/>

<Description />

<Primary />

<Controls of={TextStories.Playground} />

## Import

```jsx
import { Text } from '@cosmos/components/text';
```

## 🟢 When to use the component

- **All text content** - Primary component for displaying any text in the application with consistent typography
- **Semantic hierarchy** - When you need proper heading structure (h1-h6) or body text with semantic meaning
- **Flexible styling** - For text that needs different sizes, colors, or formatting options within design system constraints
- **Accessible text** - When you need text that works properly with screen readers and assistive technology
- **Responsive typography** - For text that needs to adapt appropriately across different screen sizes

## ❌ When not to use the component

- **Interactive elements** - Use Button, Link, or other interactive components instead of styling text to look clickable
- **Form labels** - Use proper form field components that include built-in label functionality
- **Complex formatted content** - Use specialized components like rich text editors for complex formatting needs
- **Data visualization text** - Use chart-specific text components for labels, legends, and data annotations

## 🛠️ How it works

The Text component provides consistent typography rendering across the design system with support for various text types, sizes, and styling options.

**Typography system:**
- **Text types** - Semantic variants (headline, subheadline, title, body, code) with appropriate font weights and families
- **Size scale** - Consistent sizing from 100-600 using design tokens for typography hierarchy
- **Color schemes** - Multiple color options (neutral, faded, etc.) for different semantic meanings
- **Element rendering** - Renders appropriate HTML elements (h1-h6, p, div, span) based on `as` prop

**Content and styling:**
- **Bold text support** - `allowBold` prop enables `<strong>` tag rendering within text content
- **Text wrapping** - `shouldWrap` controls overflow behavior (wrap vs ellipsis truncation)
- **Text alignment** - `align` prop supports left, right, and center alignment for block-level elements
- **Semantic HTML** - Automatically renders appropriate elements for accessibility and SEO

### Usability

**Content patterns:**
- **Selective emphasis** - Use `allowBold` only when you need `<strong>` tags for specific parts of text content
- **Appropriate wrapping** - Consider `shouldWrap={false}` for single-line content that should truncate with ellipsis
- **Element choice** - Use block elements (div, p) for alignment and wrapping control, inline elements (span) for simple text

### Content

**Writing guidelines:**
- **Clear hierarchy** - Use appropriate text types to create scannable content structure
- **Consistent terminology** - Maintain consistent language and tone across similar text elements
- **Appropriate length** - Consider content length when choosing wrapping and alignment options

**Text type usage:**
- **Headlines and titles** - Use for primary headings and important section titles
- **Body text** - Use for main content, descriptions, and general text
- **Code text** - Use for technical content, code snippets, and monospaced text needs

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper heading hierarchy and text elements for screen reader navigation
- High contrast support that works with system preferences and meets WCAG guidelines
- Scalable typography that works with browser zoom and accessibility settings
- Proper color contrast ratios for all color scheme options

**Development responsibilities:**
- Use proper heading hierarchy to ensure logical document structure with appropriate h1-h6 usage
- Structure content logically for screen readers and assistive technology
- Avoid making non-interactive text appear clickable or actionable

**Design responsibilities:**
- Ensure visual hierarchy supports semantic structure with appropriate sizing and spacing
- Maintain sufficient contrast for all text and background combinations across color schemes
- Design responsive typography that maintains readability across different screen sizes

