import type { Meta } from '@storybook/react-vite';
import { DatePickerField } from '../date-picker-field';
import { CALENDAR_PLACEMENT_OPTIONS } from './constants/calendar-placement-options.constant';
import { LABEL_STYLE_OVERRIDES_DETAIL } from './constants/label-style-overrides-detail.constant';
import { LABEL_STYLE_OVERRIDES_SUMMARY } from './constants/label-style-overrides-summary.constant';
import { LOCALE_OPTIONS } from './constants/locale-options.constants';

const meta: Meta<typeof DatePickerField> = {
    tags: ['Stable'],
    title: 'Forms/DatePickerField',
    component: DatePickerField,
    argTypes: {
        'data-id': { control: 'text' },
        calendarPlacement: {
            control: 'select',
            options: CALENDAR_PLACEMENT_OPTIONS,
        },
        defaultValue: { control: 'text' },
        disabled: { control: 'boolean' },
        feedback: { control: 'object' },
        formId: { control: 'text' },
        helpText: { control: 'text' },
        label: { control: 'text' },
        labelStyleOverrides: {
            table: {
                type: {
                    summary: LABEL_STYLE_OVERRIDES_SUMMARY,
                    detail: LABEL_STYLE_OVERRIDES_DETAIL,
                },
            },
        },
        locale: {
            control: 'select',
            options: LOCALE_OPTIONS,
        },
        name: { control: 'text' },
        onChange: {
            control: false,
        },
        optionalText: { control: 'text' },
        required: { control: 'boolean' },
        value: { control: 'text' },
        readOnly: { control: 'boolean' },
        isMulti: { control: 'boolean' },
    },
    args: {
        calendarPlacement: 'bottom start',
        'data-id': 'cosmos-date-picker-field',
        defaultValue: '2024-04-11',
        disabled: false,
        feedback: { type: 'success', message: '' },
        formId: 'employment-record-form',
        helpText: 'Choose a date between April 5, 2024 and April 25, 2024',
        label: 'Employee Start Date',
        labelStyleOverrides: { size: undefined, type: undefined },
        locale: 'en-US',
        getIsDateUnavailable: (date) => {
            // eslint-disable-next-line sonarjs/prefer-single-boolean-return -- need this
            if (new Date(date).getTime() < new Date('2023-01-01').getTime()) {
                return true;
            }

            return false;
        },
        dateUnavailableText:
            'Date unavailable, please choose date within range',
        name: 'employee-start-date',
        onChange: (date) => {
            /* eslint-disable-next-line no-console --
             * Using console for demonstration in storybook
             **/
            console.log('DatePickerField onChange', date);
        },
        optionalText: 'optional',
        required: false,
        monthSelectionFieldLabel: 'Select Month',
        yearSelectionFieldLabel: 'Select Year',
        readOnly: false,
        isMulti: false,
    },
};

export default meta;

// Playground should be first and have all controls enabled so they are accessible on Docs page

export { MultiSelect, Playground } from './stories';
