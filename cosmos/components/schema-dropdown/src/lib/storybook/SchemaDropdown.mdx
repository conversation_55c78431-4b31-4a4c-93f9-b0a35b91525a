import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Link } from '@cosmos/components/link';

import * as DropdownStories from './SchemaDropdown.stories';

<Meta of={DropdownStories} />

<Title />

<Description />

<Primary />

<Controls of={DropdownStories.Playground} />

## Import

```jsx
import { Dropdown } from '@drata/cosmos-dropdown';
```

## Props

### `onSelectGlobalOverride`

```js
const MEAL_CHOICES = [
    {
        id: 'pizza-option',
        type: 'item',
        label: 'Pizza',
        value: 'pizza',
        description: '<EMAIL>',
    },
    {
        id: 'burger-option',
        type: 'item',
        label: 'Burger',
        value: 'burger',
        description: '<EMAIL>',
    },
    {
        id: 'taco-option',
        type: 'item',
        label: 'Taco',
        value: 'taco',
        description: '<EMAIL>',
    },
    {
        id: 'chicken-option',
        type: 'item',
        label: 'Chick',
        value: 'chicken',
        description: '<EMAIL>',
    },
];

function handleSelectGlobalOverride({ id, payload }) {
    switch (id) {
        case 'pizza-options':
            eatPizzaHut();
            break;
        case 'burger-option':
            eatBurgerKing();
            break;
        case 'taco-option':
            eatTacoBell();
            break;
        case 'chicken-option':
            eatKFC();
            break;
        default:
    }
}

function WhatsForDinner() {
    return (
        <SchemaDropdown
            label="Choose your meal"
            items={MEAL_CHOICES}
            onSelectGlobalOverride={handleSelectGlobalOverride}
        />
    );
}
```

## 🟢 When to use the component

- **Single selection**: Use a dropdown when users need to select a single option from a list of options. This is helpful when equally important choices are triggered by primary, secondary, or tertiary buttons. Mostly used to change the label of a button to indicate the status of an object.
- **Short list of options**: Dropdown is great for providing a concise list of options. These options should be related to each other.
- **Non-Critical Choices**: Suitable for selections that are not immediately critical to the user’s workflow.

## ❌ When not to use the component

- **Multiple Selections**: If users need to select multiple options, avoid using Dropdowns. Instead, use a SelectField with a multi-select component or CheckboxFieldGroup.
- **Frequent Actions**: If users need to perform frequent actions quickly
- **Long Lists**: Avoid using Dropdown for long lists as they can become cumbersome to navigate and scan.
- **Select objects from databases:** Dropdowns should not be used for large database selection. Use SelectField or ComboboxField instead.

## 🛠️ How it works

A Dropdown enables users to choose from a predefined list of options or take an action. It includes a popup menu that appears when the button is clicked or activated. Options can have nested options for further functionality.

### Usability

1. **Button or menu item:** Use a button or menu item with the proper labeling to trigger the dropdown
    a. Icon only buttons are allowed with appropriate labeling.
2. **Dropdown as list:** Use an unordered list of options.
3. **Nested options:** Limit to one level of nested options.

### Content

- Apply the <Link href="https://cosmos.drata.com/?path=/docs/components-button--docs" label="button standards" isExternal /> to the Dropdown options.

### Accessibility

SchemaDropdown adheres to the <Link href="https://www.w3.org/WAI/ARIA/apg/patterns/menu-button/" label="Menu Button WAI-ARIA design pattern" isExternal /> and uses <Link href="https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/#kbd_roving_tabindex" label="roving tabindex" isExternal /> to manage focus movement among menu items.

#### Keyboard Support

<table>
    <thead>
        <tr>
            <th>Key</th>
            <th>Function</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <th>
                <kbd>Space</kbd>
            </th>
            <td>
                When focus is on SchemaDropdown, opens the dropdown menu and focuses the first item.

                When focus is on an item, activates the focused item.
            </td>
        </tr>
        <tr>
            <th>
                <kbd>Enter/Return</kbd>
            </th>
            <td>
                When focus is on SchemaDropdown, opens the dropdown menu and focuses the first item.

                When focus is on an item, activates the focused item.
            </td>
        </tr>
        <tr>
            <th>
                <kbd>ArrowDown</kbd>
            </th>
            <td>
                When focus is on SchemaDropdown, opens the dropdown menu.

                When focus is on an item, moves focus to the next item.
            </td>
        </tr>
        <tr>
            <th>
                <kbd>ArrowUp</kbd>
            </th>
            <td>When focus is on an item, moves focus to the previous item.</td>
        </tr>

        <tr>
            <th>
                <kbd>ArrowRight</kbd> or <kbd>ArrowLeft</kbd>
            </th>
            <td>
                When focus is on a Submenu, opens or closes the submenu
                depending on reading direction.
            </td>
        </tr>
        <tr>
            <th>
                <kbd>Esc</kbd>
            </th>
            <td>Closes the dropdown menu and moves focus to Dropdown.</td>
        </tr>
    </tbody>

</table>
