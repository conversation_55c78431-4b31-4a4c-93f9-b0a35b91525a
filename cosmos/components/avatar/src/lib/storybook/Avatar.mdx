import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as AvatarStories from './Avatar.stories';

<Meta of={AvatarStories} />

<Title />

<Description />

<Primary />

<Controls of={AvatarStories.Playground} />

## Import

```jsx
import { Avatar } from '@drata/cosmos-avatar';
```

## Props

### `fallbackText`

The `fallbackText` prop allows you to specify a default text or description that will be displayed if the avatar image is not available or fails to load.

<Canvas of={AvatarStories.FallbackText} />

### `imgSrc`

The `imgSrc` prop on the Avatar component represents the source URL or path for the image that will be displayed as the avatar. If an image is being rendered, it should always be accompanied by `alt` text, provided by the `imgAlt` prop.

<Canvas of={AvatarStories.ImgSrc} />

### `size`

There are four options for the `size` prop, with a default of "md".

<Canvas of={AvatarStories.Size} />

## 🟢 When to use the component

- **Individual identification** - Display user profile images or initials in tables, lists, and cards
- **Owner displays** - Show asset owners, risk owners, or personnel assignments
- **User stacks** - Use with AvatarStack for multiple user displays
- **Identity components** - Use with AvatarIdentity for user information displays

**Current usage patterns:**
- **AvatarStack integration** - Most common usage with `size="xs"` for compact displays
- **AvatarIdentity component** - Used with Identity component for user information displays
- **Table cells** - Used in data tables for owner/personnel identification
- **KeyValuePair integration** - Used in UserValue component for user displays


## ❌ When not to use the component

- **Organization representation** - Use [Organization](https://cosmos.drata.com/?path=/docs/media-imagery-organization--docs) component for company/organization logos
- **Framework representation** - Use [FrameworkBadge](https://cosmos.drata.com/?path=/docs/media-imagery-frameworkbadge--docs) for compliance framework logos
- Use [Icons](https://cosmos.drata.com/?path=/docs/media-imagery-icon--docs) when representing objects, actions, or other communicative symbols.

## 🛠️ How it works

The Avatar component displays user profile images in a circular format with automatic fallback to text when images fail to load.

**Table cell guidelines**
- **Use AvatarStack** - When users are metadata, even if there only will be one user (e.g., control owners where control is the primary information)
- **Use AvatarIdentity** - When there will only ever be one user and the user is the primary information

### Usability

- Circular Avatars are used to represent users.
- In the absence of an uploaded image, utilize up to two letters as a substitute. When the record name comprises two words (e.g., first and last name), employ the initial capitalized letter of each. For records with a single-word name, opt for the first two letters of the word using a combination of one capital and one lowercase letter.
- Don’t use alternative graphics or icons inside an Avatar.
- Don’t scale or change the shape of Avatars. Instead, use the designated sizes and styles available.
- By default, utilize an Avatar to depict a user. However, if it disrupts the visual coherence of the layout or flow, opt for text-only representation.

**Core functionality:**
- **Image display** - Shows user profile image when `imgSrc` is provided and loads successfully
- **Automatic fallback** - Displays `fallbackText` when image is unavailable or fails to load
- **Error handling** - Uses `onError` handler to detect image load failures and switch to fallback state
- **Circular design** - All avatars use consistent circular shape via `borderRadiusCircle` token
- **Responsive sizing** - Four size variants with corresponding text sizes for optimal readability

**Visual design:**
- **Fallback styling** - Dark background (`neutralBackgroundStrong`) with white text (`neutralTextInverted`)
- **Text formatting** - Uses Text component with "title" type and "inherit" colorScheme for fallback text
- **Size consistency** - Each size has corresponding text size for proportional display
- **State management** - Internal `hasError` state tracks image load failures

**Size guidelines:**
- **Extra small (`xs`)** - Used in AvatarStack and compact user displays
- **Small (`sm`)** - Used in AvatarIdentity and table cells
- **Medium (`md`)** - Default size for general usage
- **Large (`lg`)** - Used for prominent user displays and profile sections

### Content

- Use 1-2 character `fallbackText` (typically initials from user's name)
- Provide descriptive `imgAlt` text that identifies the user
- Choose appropriate `size` based on context and available space
- Ensure `imgSrc` URLs are accessible and properly authenticated

### Accessibility

Make sure that the alternative text properly describes the information and function of the avatar image(s). Depending on the situation, it may be helpful to state the user name.

**What the design system provides:**
- Semantic HTML structure with proper image and text elements
- Screen reader support with `imgAlt` text for images and readable fallback text
- High contrast fallback design with dark background and white text for visibility
- Scalable sizing that works at different zoom levels and screen sizes
- Focus management integration when used within interactive components

**Development responsibilities:**
- Always provide `imgAlt` when using `imgSrc` for screen reader accessibility
- Use meaningful `fallbackText` that identifies the user (typically initials)
- Test avatar displays across different user name formats and image availability
- Ensure fallback text remains readable at all size variants
- Consider the context where avatars appear and provide appropriate surrounding labels

**Design responsibilities:**
- Maintain sufficient contrast between fallback background and text colors
- Ensure avatar sizing works within the broader interface layout
- Test avatar appearance with various text lengths and character sets
- Consider the visual hierarchy when avatars appear alongside other user information
- Verify circular shape consistency across all size variants and content types
