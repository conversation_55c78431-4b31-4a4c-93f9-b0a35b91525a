import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import * as ComboboxFieldStories from './ComboboxField.stories';

<Meta of={ComboboxFieldStories} />

<Title />

<Banner
    severity="education"
    title="Use Web Wrappers for Form Fields"
    body={
        <>
            Avoid using form field components directly from Cosmos. Instead, use
            our web wrappers for optimized functionality and consistent
            integration.{' '}
            <Link
                href="/?path=/docs/form-components-about--docs#web-wrappers"
                label="Click here for more details"
                isExternal
            />
            .
        </>
    }
/>

<br />

<Banner
    severity="warning"
    title="Unstable component"
    body={
        <>
            This component is considered <strong>Unstable</strong> because it
            has incomplete test coverage.
        </>
    }
/>

<Description />

<Primary />

<Controls of={ComboboxFieldStories.Playground} />

## Import

```jsx
import { ComboboxField } from '@cosmos/components/combobox-field';
```

## Props

### `defaultValue`

Passing an option object to `defaultValue` will set that option as the initially selected value.

<Canvas of={ComboboxFieldStories.DefaultValue} />

## Examples

### Multi select

Enable with the `isMultiSelect` prop. When enabled, `getRemoveIndividualSelectedItemClickLabel` and `removeAllSelectedItemsLabel` are required props.

Multi select works with Async search.

<Canvas of={ComboboxFieldStories.MultiSelect} />

## Troubleshooting Common Issues

### Why don't the checkboxes stay "checked" after selecting an item in a MultiSelect combobox?

Both issues stem from unstable function references or dependencies in your asynchronous operations. If you are seeing resets or multiple fetch triggers, it is likely that functions like `onChange` or `onFetchOptions` are being re-created on each render, causing unexpected behavior. To address this, you can:

- Use `useCallback` to stabilize function references, such as `onFetchOptions` and `onFilterOptions`.
- Ensure that the component’s state and props (e.g., `options`, `defaultSelectedOptions`, `paginationOptions`) are properly controlled and memoized to prevent re-renders from resetting the state or causing repeated fetch calls.

### How can I enable clearing the selection on Single Select combobox?

To enable clearing the selection on Single Select combobox, you need to set `clearSelectedItemButtonLabel` prop. This prop will render a clear button icon to the right of the selected item.

## 🟢 When to use the component

- **Searchable selections** - When users need to search through a list of options to find and select items
- **Large option sets (10+)** - For dropdown lists with many options that benefit from search filtering
- **Multi-select scenarios** - When users need to select multiple items with clear visual feedback via tags
- **Dynamic option loading** - For scenarios requiring pagination, infinite scroll, or search-based option fetching

## ❌ When not to use the component

- **10 options or fewer** - Use SelectField for simple dropdowns without search
- **2-5 options that should be visible** - Use RadioFieldGroup or CheckboxFieldGroup to show all options at once
- **Static lists** - Use SelectField when options are static and don't require search functionality
- **Complex option layouts** - Use custom implementations when options need rich content beyond label/description

## 🛠️ How it works

The ComboboxField component combines FormField structure with Combobox functionality to provide a complete form input with search capabilities, validation, and accessibility features.

**Core functionality:**
- **FormField wrapper** - Labeling, help text, validation feedback, form integration
- **Combobox core** - Handles search input, dropdown display, option selection, and keyboard navigation
- **Selection modes** - Single-select (`defaultValue`) or multi-select (`isMultiSelect`, `defaultSelectedOptions`)
- **Data loading** - Static options or async with `onFetchOptions`, pagination via `hasMore`
- **Search features** - Debounced search (default 300ms), loading states, custom empty states

### Usability

- Immediate feedback with loading states and search result updates
- Persistent search input available after selection
- Full keyboard navigation and adequate touch targets
- **Single-select clarity** - Clear visual indication of selected item with optional clear button
- **Multi-select management** - Tags show selected items with individual remove buttons and bulk clear option

### Content

- Use clear labels explaining what users are selecting
- Provide helpful placeholder text that indicates what users can search for
- Structure options with consistent data format for effective search
- Use descriptive labels for tag removal actions in multi-select mode
- Ensure copied value matches exactly what users expect based on displayed content

### Accessibility

**What the design system provides:**
- Semantic HTML with proper form relationships, ARIA roles, keyboard navigation
- Screen reader announcements for search results, selections, loading states
- High contrast support, focus management, touch target sizing

**Development responsibilities:**
- Provide descriptive labels and meaningful help text
- Implement proper validation with clear, actionable error messages
- Handle loading/error states with ARIA live region announcements
- Ensure `getRemoveIndividualSelectedItemClickLabel` provides descriptive text

**Design responsibilities:**
- Sufficient color contrast for all states across themes
- Clear visual hierarchy between label, input, selections, help text
- Visible focus indicators and consistent patterns across implementations
- Appropriate spacing and touch targets for all screen sizes
