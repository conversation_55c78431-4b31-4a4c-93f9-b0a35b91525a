import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as RadioFieldGroupStories from './RadioFieldGroup.stories';

<Meta of={RadioFieldGroupStories} />

<Title />

<Description />

<Primary />

<Controls of={RadioFieldGroupStories.Playground} />

## Import

```jsx
import { RadioFieldGroup } from '@drata/cosmos-radio-field-group';
```

## Props

### `defaultValue`

Passing the value of a Radio option to `defaultValue` will set that option as the initially selected value.

<Canvas of={RadioFieldGroupStories.DefaultValue} />

### `cosmosUseWithCaution_forceOptionOrientation`

By default, RadioFieldGroup uses vertical layout for all radio groups to ensure optimal readability and accessibility.

In the rare case that this needs to be overridden, use `cosmosUseWithCaution_forceOptionOrientation` to manually set the orientation.

<Canvas of={RadioFieldGroupStories.USEWITHCAUTION_ForceOptionOrientation} />
<Controls
    of={RadioFieldGroupStories.USEWITHCAUTION_ForceOptionOrientation}
    include={['cosmosUseWithCaution_forceOptionOrientation']}
/>

## 🟢 When to use the component

- **2-5 options ideal** - When you have a small number of mutually exclusive choices that can all be displayed
- **All options visible** - When users should see all available options before making a decision
- **Single choice selections** - When users need to select exactly one option from a predefined set of mutually exclusive choices
- **Preferences or Settings** - When configuring settings that require a single preference selection.

## ❌ When not to use the component

- **Multiple Selections Needed** - If users must select multiple options from a list simultaneously, use checkboxes instead of radio buttons.
- **Many options** - Use SelectField (≤10 options) or ComboboxField (10+ options) for longer lists
- **Binary Choices** - For simple yes/no or on/off choices, use toggle switches or checkboxes, which are more intuitive for binary decisions.

## 🛠️ How it works

The RadioFieldGroup component manages a group of related radio options, allowing users to select only one option from a set of predefined options with vertical layout and complete form field functionality.

**Core functionality:**
- **FormField wrapper** - Provides consistent labeling, help text, validation feedback, and form integration with `role="radiogroup"`
- **Vertical layout** - Uses vertical orientation with 12px gap spacing for optimal readability and accessibility
- **RadioField composition** - Renders individual RadioField components with proper group coordination and ARIA relationships
- **Controlled state** - Uses `value` prop to control which option is selected; `onChange` receives the selected option's value
- **Option structure** - Each option includes required `label` and `value`, plus optional `helpText`, `disabled`, and `readOnly` properties
- **Value normalization** - Uses `normalizeValue` helper to create consistent IDs by removing spaces from option values

### Usability

- **Mutually exclusive choices** - Ensure all options represent truly exclusive alternatives with complete coverage of user scenarios
- **Logical ordering** - Arrange options in logical order (frequency, importance, alphabetical, etc.)
- **Default selection** - Consider providing a sensible default value for better user experience
- **Vertical arrangement** - Provides optimal readability and enables predictable top-to-bottom scanning patterns

### Content

- **Descriptive titles** - Use group labels that clearly explain what users are choosing between
- **Distinctive labels** - Each option should be clearly different, concise, and use consistent grammatical structure
- **Help text usage** - Use group help text to explain the overall choice; individual help text for complex options

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper `role="radiogroup"` and individual radio button elements for full screen reader support
- Complete keyboard navigation including Tab to enter group, arrow keys to navigate between options, and Space to select
- Automatic ARIA relationships with `aria-labelledby` connecting group label and `aria-describedby` for help text and validation feedback
- High contrast support that works with system accessibility preferences and meets WCAG AA standards across all themes
- Scalable typography and spacing that works with browser zoom up to 200% magnification
- Focus management with clear focus indicators and logical tab order through radio options

**Development responsibilities:**
- Provide clear, descriptive `label` prop that explains what choice users are making
- Use meaningful `name` prop that creates proper form field grouping for submission
- Ensure each option has unique, stable `value` props that work with form processing systems
- Implement proper `onChange` handling that updates application state when selection changes
- Provide helpful group-level `helpText` that guides users toward making informed choices
- Handle validation appropriately with clear error messages when selection is required but missing

**Design responsibilities:**
- Provide sufficient color contrast for radio buttons, labels, help text, and validation feedback across all themes
- Design clear visual hierarchy that shows the relationship between group label, individual options, and help text
- Ensure focus indicators are clearly visible and meet contrast requirements for keyboard navigation
- Create consistent visual patterns for radio group styling that work across different orientations and option counts
- Design appropriate spacing and sizing that works across different screen sizes and container widths
- Ensure selected, unselected, disabled, and validation states are clearly distinguishable through visual design

