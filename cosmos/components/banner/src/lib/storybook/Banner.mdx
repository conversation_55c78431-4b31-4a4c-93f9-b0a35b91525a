import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as BannerStories from './Banner.stories';

<Meta of={BannerStories} />

<Title />

<Description />

<Primary />

<Controls of={BannerStories.Playground} />

## Import

```jsx
import { Banner } from '@cosmos/components/banner';
```

## Props

### `Severity`

The `severity` prop controls the color and icon displayed on the banner.

<Canvas of={BannerStories.Severity} />

### `Display Mode - Section`

The `section` option for the Banner's `displayMode` prop is intended to be used inside of other elements.

<Canvas of={BannerStories.DisplayModeSection} />

### `Display Mode - Full`

The `full` option for the Banner's `displayMode` prop is for banners which span the entire page. **The `full` option is only used at the very top of the screen for system announcements such as when Drata support is accessing the tenant or when the customer’s contract is up for renewal.

<Canvas of={BannerStories.DisplayModeFull} />

### `onClose`

The `onClose` prop is a callback which is called when the Close button is clicked. **The Close button only renders if `onClose` is provided.**

<Canvas of={BannerStories.OnClose} />

### `action`

The `action` prop accepts a Button component to provide users with a clear call-to-action related to the banner message. **Use 'sm' size and 'secondary' buttons for best visual consistency.**

<Canvas of={BannerStories.Action} />

### AI Variant

The AI severity provides specialized styling for AI-powered features, including gradient borders and AI-themed colors. **Use this variant when users need to opt-in to AI features, _not_ for displaying AI results.**

<Canvas of={BannerStories.AI} />

## 🟢 When to use the component

- A banner may be a notification that keeps people informed of the status of the system and may or may not require the user to respond. Such notifications may be errors, warnings, and general updates.
- A banner may be a validation message that informs a user they just took an action that needs to be corrected or a confirmation that a task was completed successfully.
- Banner messages are most often persistent, but can be dismissible using the `onClose` prop.

## ❌ When not to use the component

- Don’t use a banner about a destructive action. Use a more intrusive pattern like a modal.
- Don’t use if the message needs to be permanently on the screen (use normal copy instead).
- Don’t use to serve a primary action—primary actions should be built into the page permanently if they are important enough to be primary.

## 🛠️ How it works

### Banner types

- **Primary:** Shown when neutral information is required.
- **Education:** Shown when highlighting help or a new feature.
- **Success**: Shown when something goes right.
- **Warning:** Shown when extra care should be taken.
- **Critical:** Shown when something goes wrong.
- **AI:** Shown for AI-powered features, insights, or recommendations.

### Usability

- Allow a user to dismiss a notification whenever appropriate.
- Don’t overdo it. Too many notifications will either overwhelm or annoy the user and are likely to be ignored.
- Use when the message is temporary (and not a permanent part of the screen).
- Place banner at the top of the page, under the primary navigation or page\section header when possible.
- On forms, always include in-line validation in addition to any error messages that appear at the top of the form.

### Content

- Consider next steps. When the user is required to do something in response to a banner, let them know what they need to do, and make that task as easy as possible. Think about how much context to provide with your message. For example, a notification of a system change may require more contextual information than a validation message. Write the message in concise, human-readable language; avoid jargon and computer code.
- Banners are an opportunity. Users will read a message that helps them resolve an error even if they generally won’t read documentation; include some educational material in your error message.
- [More Banner content standards](https://www.notion.so/Alert-banner-standards-info-warning-error-education-d1883706ce684eb5a2830d2051d8ae05?pvs=21)

### Accessibility

- Make sure the words in the banner indicate the severity of the banner message instantly for users who can’t recognize certain colors.
- Don’t visually hide banner messages and then make them visible when they are needed. Users of older assistive technologies may still be able to perceive the banner messages even if they are not currently applicable.
- Use the proper ARIA role. The ARIA role attribute can notify assistive technologies of time-sensitive and important messages. To elevate the importance of the alert, choose the appropriate role from the ARIA roles table and add it to the banner element.
- Note the use of role="status" and role="alert". Both create an ARIA live region.
- If the content of the note is advisory information not important enough to justify an alert, use role="status". When added to an element, the browser will send out an accessible status event to assistive technologies, which can then notify the user about it.
- For error messages, use role="alert". The alert role should read out content that has changed, or bring the user's attention to it immediately, so it should not be used for static content or used regularly. Alerts, by definition, are disruptive. Lots of alerts at once or unnecessary alerts will create a bad user experience.
