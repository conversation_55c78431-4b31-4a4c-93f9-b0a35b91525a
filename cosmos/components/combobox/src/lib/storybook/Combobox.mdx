import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';

import * as ComboboxStories from './Combobox.stories';

<Meta of={ComboboxStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos form component, you are probably looking for ComboboxField."
/>
<br />

<Description />

<Primary />

<Controls of={ComboboxStories.Playground} />

## Import

```jsx
import { Combobox } from '@cosmos/components/combobox';
```

## 🟢 When to use the component

- **Building form components** - As the foundational component for creating ComboboxField and other select-based form controls
- **Custom dropdown implementations** - When you need full control over dropdown behavior and styling within the design system
- **Advanced search scenarios** - For implementing complex search and filtering functionality with custom logic
- **Multi-select interfaces** - When building components that need sophisticated multi-selection with tag display
- **Async data loading** - For components that need to fetch options dynamically with search debouncing and pagination

## ❌ When not to use the component

- **Standard form fields** - Use ComboboxField for typical form implementations
- **Simple dropdowns** - Use SelectField for basic single-selection dropdowns without search
- **Navigation menus** - Use Dropdown or NavigationMenu components for action-oriented menus
- **Direct application use** - This is a foundational component; use higher-level form components instead

## 🛠️ How it works

The Combobox component provides foundational select functionality with search capabilities, supporting both single and multi-select modes with advanced features like async loading and custom filtering.

**Core functionality:**
- **Search integration** - Built-in input field with configurable debouncing for search queries
- **Selection modes** - Single-select with `defaultValue` or multi-select with `defaultSelectedOptions` array
- **Async data loading** - Optional `onFetchOptions` with support for pagination and search-based filtering
- **Component architecture** - ComboboxTrigger (input), Popover (dropdown), ListBox (options), TagGroup (multi-select tags)

### Usability

**Search and selection:**
- Debounced search prevents excessive API calls during typing
- Single-select shows selected item with optional clear button
- Multi-select displays removable tags with bulk clear functionality
- Keyboard navigation supports arrow keys, Enter, Escape, and type-ahead

### Content

**Option structure:**
- Use clear, descriptive labels with consistent `ListBoxItemData` structure
- Provide meaningful `placeholderText` and `loaderLabel` for accessibility
- Structure searchable content for effective filtering
- Use `getSearchEmptyState` for contextual no-results messages

### Accessibility

**What the design system provides:**
- Semantic HTML with proper ARIA roles (combobox, listbox, option) and relationships
- Full keyboard navigation and screen reader announcements for selection changes and loading states
- High contrast support and focus management with visible indicators

**Development responsibilities:**
- Provide unique `id` prop and meaningful `aria-labelledby` connections
- Ensure `loaderLabel` is descriptive for screen reader users
- Implement proper error handling with accessible feedback
- Use descriptive labels for clear buttons and tag removal actions

**Design responsibilities:**
- Provide sufficient color contrast for all states including focus, selection, disabled, and loading across themes
- Design clear visual hierarchy that distinguishes between input, dropdown, selected items, and interactive elements
- Ensure focus indicators are clearly visible and meet contrast requirements for all focusable elements
- Create consistent visual patterns for similar combobox implementations across the application
- Design appropriate spacing and sizing for touch targets, dropdown positioning, and tag display across screen sizes
- Ensure loading states and empty states provide clear visual feedback that complements screen reader announcements

