import { Controls, Description, Meta, Primary, Title } from '@storybook/addon-docs/blocks';
import * as LinkStories from './Link.stories';

<Meta of={LinkStories} />

<Title />

<Description />

<Primary />

## Import

```tsx
import { Link } from '@cosmos/components/link';
```

<Controls of={LinkStories.Playground} />

## 🟢 When to use the component

Use links when you want users to:

- Navigate to a different page within the application
- Navigate to an entirely different site
- Jump to an element on the same page
- Link to emails or phone numbers

## ❌ When not to use the component

- If the primary action for the user in an experience is a link, you may choose to use <PERSON><PERSON> as the link instead with a label of "Go to…" and with an external LinkOut end icon if the link is external.
- Do not use links for actions that will change data or manipulate how it is displayed, change a state, or trigger an action. Instead, use buttons to guide users to specific actions e.g. Save.
- Do not use images as links.

## 🛠️ How it works

The Link component provides navigation between pages, sections, or external resources with consistent styling and accessibility features.

**Variants:**

| Variant | Usage |
| --- | --- |
| Standalone | Primary link within its own ecosystem (for example: One of our help pages that opens into another one) |
| Inline | Used within a sentence or paragraph and is styled with an underline |

**Opening links in the same tab vs new tab**

Product designer or content designer should specify if the link opens in the same tab or a new tab.

<table>
    <thead>
        <th>Context</th>
        <th>Same Tab</th>
        <th>New Tab</th>
    </thead>
    <tbody>
        <tr>
            <td>Supplementary Link</td>
            <td></td>
            <td>✅</td>
        </tr>
        <tr>
            <td>
                Primary link within its own ecosystem (for example: One of our
                help pages that opens into another one)
            </td>
            <td>✅</td>
            <td></td>
        </tr>
        <tr>
            <td>
                Primary link that leads outside (example: you need to go to
                another site to hook up a connection)
            </td>
            <td></td>
            <td>✅</td>
        </tr>
    </tbody>
</table>

### Usability

- Links are blue and underlined by default.
- Use the inverted style on darker backgrounds.
- If it is an external link, the link text should be followed up the external icon.
    - Internal example: Our help page about policy widgets can help you do all the things.
    - External examples: View Google’s developer tool documentation for more info.

### Content

- Link text should be descriptive of the destination. Descriptive links are linked to a single word or short phrase. This should be as simple as possible but no simpler.
- Generally avoid “Learn more”. It doesn’t make it clear what they’re learning more about.
- If your link is at the end of a sentence or paragraph, make sure that the linked text does not include the full stop.
- Help users know if it’s internal or external
- Avoid using the same link text for different destinations on the same page and try to differentiate between links by using unique text for each.
- Be a complete thing or idea
    - Don’t link to a verb or adjective on their own.

**Sizing**

There are two sizes for the link component: small, and medium. Inline link sizes should match the type size of the text it is inline with. Standalone link sizes should match the default body copy size of the page.

### Accessibility

- For external links, add “opens in a new tab” as sr-only to the end of the link label. This will help screen reader users know what to expect.
- Users can open a link by pressing `Enter` while the link has focus.

