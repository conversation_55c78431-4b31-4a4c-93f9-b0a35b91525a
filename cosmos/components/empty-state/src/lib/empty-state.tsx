import type { ReactNode } from 'react';
import type { SpotIllustrationName } from '@cosmos-lab/components/spot-illustration';
import { EmptyStateColumn, EmptyStateRow } from './components';
import type { IllustrationSize } from './components/EmptyStateIllustration/EmptyStateIllustration';
import { TESTID } from './constants';

export interface EmptyStateProps {
    /** The main title for the empty state, providing a concise label for the empty state content.*/
    title: string;

    /** The description for the empty state, providing a concise description for the empty state content.*/
    description?: React.ReactNode;

    /** An icon or illustration to show at the top of the component. */
    illustrationName?:
        | SpotIllustrationName
        | `no-component-ticket-link=${string}`;

    /** Unique testing ID for this element.*/
    'data-id'?: string;

    /** Left Action button.*/
    leftAction?: ReactNode;

    /** Right Action button.*/
    rightAction?: ReactNode;

    /** Apply a vertical layout to the component.*/
    isStacked?: boolean;

    /** Controls the illustration size (if provided). Defaults to 'sm'.*/
    imageSize?: IllustrationSize;

    /** When true, removes border and padding for inline usage. Defaults to false.*/
    isInline?: boolean;
}

/**
 * The EmptyState component serves as a placeholder when there are no results or information to be displayed.
 * The screen or section is available for use and the user just needs to do something. This answers the question “What should I be doing here?” or “What can I do here?” The primary goals of an empty state region should be to:
 * - Increase feature adoption.
 * - Improve learnability and feature discovery.
 * - Improve usability.
 *
 * [EmptyState in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=45945-102272&t=f1ddBvDspUp2ofEh-4).
 *
 * **Relevant components:**
 * - [EmptyStateIllustration in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=45945-102492&t=f1ddBvDspUp2ofEh-4).
 * - [SpotIllustration in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=46189-292663&t=f1ddBvDspUp2ofEh-4).
 */
export const EmptyState = ({
    title,
    description = undefined,
    illustrationName = undefined,
    'data-id': dataId = TESTID,
    leftAction = null,
    rightAction = null,
    isStacked = false,
    imageSize = 'sm',
    isInline = false,
}: EmptyStateProps): React.JSX.Element => {
    if (isStacked) {
        return (
            <EmptyStateColumn
                title={title}
                data-id={dataId}
                description={description}
                illustrationName={illustrationName}
                leftAction={leftAction}
                rightAction={rightAction}
                imageSize={imageSize}
                isInline={isInline}
            />
        );
    }

    return (
        <EmptyStateRow
            title={title}
            data-Id={dataId}
            description={description}
            illustrationName={illustrationName}
            leftAction={leftAction}
            rightAction={rightAction}
            imageSize={imageSize}
            isInline={isInline}
            data-testid="EmptyState"
            data-id="WoUPUTrX"
        />
    );
};
