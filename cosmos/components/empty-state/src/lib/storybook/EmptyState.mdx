import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as EmptyStateStories from './EmptyState.stories';
import emptyStateEmptyList from './assets/EmptyStateExample-emptylist.png';
import emptyStateLoading from './assets/EmptyStateExample-loading.gif';
import emptyStateSpinner from './assets/EmptyStateExample-spinner.gif';
import emptyStateIllustration from './assets/EmptyStateExample-illustration.png';
import emptyStateUnnecessaryIllustration from './assets/EmptyStateExample-unnecessaryillustration.png';
import addCircle from './assets/add-circle.png';
import configure from './assets/configure.png';
import warning from './assets/warning.png';
import tableBlankSlate from './assets/table-blank-slate.png';
import tableNoResults from './assets/table-no-results.png';
import cardUncontained from './assets/card-uncontained.png';
import utilitiesContained from './assets/utilities-contained.png';
import panelContained from './assets/panel-contained.png';


<Meta of={EmptyStateStories} />

<Title />

<Description />

<Primary />

<Controls of={EmptyStateStories.Playground} />

## Import

```jsx
import { EmptyState } from '@drata/cosmos-empty-state';
```

## Examples

### Illustration, text content, and actions

<Canvas of={EmptyStateStories.Playground} />

### Title only

<Canvas of={EmptyStateStories.JustTitle} />

### Text content only

<Canvas of={EmptyStateStories.ContentText} />

### Text content and single action

<Canvas of={EmptyStateStories.ContentTextAndCTA} />

## Variants

#### Small
- Minimal layout with concise text
- Consider if the illustration is necessary
- Typically used in constrained spaces (utilities, panels, cards)
- Best for contextual emptiness (e.g., “No comments yet”)

#### Medium

- Includes headline, subtext, and optional CTA
- Can include an illustration
- Best for primary screen areas (e.g., full-page dashboard, gallery, or table)

#### Contained or not contained

Some components support **container styling variants**

- Use the **contained** variant when the component needs visual separation or emphasis, like in a large table or gallery space.
- Use the **uncontained** variant when the component should blend with its surroundings or appear inline, like in a card.

## Examples in containers

### Table (Blank slate)
<img src={tableBlankSlate} alt="Example of a table with a blank slate empty state" />

### Table (No results)
<img src={tableNoResults} alt="Example of a table with a no results empty state"  />

### Card (Uncontained)
<img src={cardUncontained} alt="Example of an uncontained empty state in a card" />

### Utilities (Contained)
<img src={utilitiesContained} alt="Example of an contained empty state in the utilities" />

### Panel (Contained)
<img src={panelContained} alt="Example of an contained empty state in a panel"  />

## 🟢 When to use the component

Empty states happen for a variety of reasons, and can require different treatments. Empty states are often treated as an afterthought. When designed thoughtfully, they become an essential part of a smooth user experience, providing just enough information, in context, to allow users to continue working in a productive way.

During the design process, ask yourself these questions:

- What will the pages, tiles, data tables, and side panels look like without content?
- What are all of the steps a user can take to address the situation?
- Is there any useful content that might be available to show?
- How can I turn this situation into something that is engaging and helpful?

During the design phase, explore the full range of options with your team to ensure that the most appropriate and helpful content is created for each empty space.

- **No data empty states** (First-time user experiences with no data yet (e.g., dashboards, reports), When a process has not yet started (e.g., no campaigns created))
    -  These occur during a first time use when there is no data yet. The goal for the content is that the user understands what will be available on the page when the data has been added or is available, and that they understand how to add the data.

- **User action empty states** (When filters or search result in no matches, After a user has cleared a list (e.g., completed tasks))
    - These may result when a user adds too many filters or search terms that return no results. Or when a user completes an action. The text should be specific about what the cause is. The goal for the content is that the user understands how to adjust search terms or filters to continue their search or that they’ve successfully completed a process.

- **Error management empty states**
    - These occur when there are permissions issues, systems issues, or when there is configuration required. The goal of the content is that the user understands the problem and if there are corrective actions available, knows what action to take or has options to correct the issue.
    - When something is amiss or some level of intervention or troubleshooting is required, a higher level of detail and specificity will better support the user.

## ❌ When not to use the component

- To show loading states—use a spinner or skeleton loader
- When content is still being fetched—wait to determine if it's truly empty
- In place of instructional content—use onboarding or helper text instead
- When there is a specific error, support, or in-depth education needed.

### Use cases
#### Loading content:

✅ **Do use an EmptyState with a helpful message and clear action when a list is empty**
<img src={emptyStateEmptyList} alt="Example of an empty state when a list is empty" />

🚫 **Don’t show an EmptyState when the data is still loading**
<img src={emptyStateLoading} alt="Example of an empty state when data is loading" />

✅ **Instead, use a [loader](https://cosmos.drata.com/?path=/docs/feedback-loader--docs) or [skeleton loading](https://cosmos.drata.com/?path=/docs/feedback-skeleton--docs)**
<img src={emptyStateSpinner} alt="Example of using a spinner or skeleton when data is loading" />

#### Illustrations:

✅ **Do use illustrations when they reinforce the message (e.g., some set-up is needed)**
<img src={emptyStateIllustration} alt="Example of an empty state when an illustration is recommended" />

🚫 **Don’t overuse illustrations that distract or feel unnecessary**
<img src={emptyStateUnnecessaryIllustration} alt="Example of an empty state when an illustration is not recommended" />

## 🛠️ How it works

Empty states are a simple yet extremely powerful way to keep a user informed, supported, and on a productive path. They provide opportunities to communicate what the user would see if they had data, while providing constructive guidance about next steps.
Empty states always appear in the otherwise empty space, in the context of the data that’s missing. They can occur anywhere your app can display data, including but not limited to dashboards, data tables, tiles, full pages, and side panels.

- Should be visually unobtrusive, but still noticeable.
- Only include illustrations when they help clarify the message, add appropriate tone, or are the best next action.

#### Considerations

- **Multiple empty states:** Experiences will often need multiple empty states—you might need a null state for when something doesn’t have a integration (No data), after an integration is set up but hasn’t run yet (different no data), when the user needs to perform an action, or when something is in a complete state.

- **Best next action:** Be aware of surrounding page elements. If an empty state is leading a user to the best next action, the rest of the page should likely be more sparse to emphasize the action the user should take. Some pages include a primary button in the header that may need to be hidden when the empty state is active.

### Usability

**Core functionality:**
- **Flexible layout options** - `isStacked` prop switches between vertical (column) and horizontal (row) layouts
- **Illustration support** - Optional `illustrationName` prop displays SpotIllustration components with configurable sizing
- **Dual action support** - `leftAction` and `rightAction` props accommodate primary and secondary actions
- **Size variants** - `imageSize` prop controls illustration size ('sm' or 'md') for different contexts
- **Container flexibility** - `isInline` prop removes borders and padding for seamless integration

### Content

Design for a balance between the situation and the content you’re providing. More content doesn’t necessarily mean it’s a better solution as there is a cognitive cost for having more content on the page. This is especially true when users first engage with your product, so save the more involved educational moments for primary features and more complex situations.

**Empty state content consists of:**

- (Optional) Illustration to add interest and draw the users eye.
- Title that describes the situation. Try to limit to 5 words (not counting articles or prepositions). Don't end with a period.
- Description that explains in full sentences what actions the user can take or offers more context.
- (Optional) Button that serves as a call to action (CTA) and directs the user to take an action.
- (Optional) Link that offers supporting content like support articles.

**An empty state message should:**

- **Provide supporting information**: Use a cosmos link instead of a button to direct a user to things like documentation and resources that support the main action.
- **Be simple and clear**: Use concise and easily understandable language. Also consult the content guidelines word list. Avoid duplicating content elsewhere in the page that's already present in the empty state heading and description.
- **Provide guidance and motivation**: Offer clear instructions on how to proceed or resolve the empty state. Encourage users to take action with motivating language.
- **Align with the brand voice and tone**: decisive, guiding, and relatable.
- **Use visual elements effectively**: Ensure visual elements enhance the message and don't distract from it.

🔗 More detail in the [content guidelines for empty states](https://www.notion.so/Empty-states-591bfbd5e5014cbba9ed3f805554ba2c?pvs=21).


**Image choice considerations**

- The image choice should relate to the situation.
- The size of the space for the empty state should also guide the size of image. If space is limited, use just text.

**Error management empty states**

| Error type | Explain why there is no data | Explain what the user can do |
| --- | --- | --- |
| *Permissions issue* | The user does not have permission to view the data. | Suggest steps or process to request access. |
| *Systems issue* | Problems with a related system are preventing the data from being supplied. | Explain steps the user can take to learn what has happened. For example, viewing an activity log. |
| *Configuration required* | Further configuration may be needed to access the data. | Provide an explanation and the first step for the user to take for the required configuration. |
| *Action not supported* | For example, the user attempts to upload an unsupported file type. | Explain what file types are supported. |

**Multiple empty states**

In situations where there could be multiple empty states showing at once, we recommend using a tertiary button for the call to action. This avoids scenarios with multiple primary action buttons in the UI.

### Common use case’s content:

#### Blank slate

- Appears when no content exists within a configured feature.
- Contains a method for creating content.
- Hides unnecessary UI elements

**Content notes:**

<img src={addCircle} alt="addCircle SpotIllustration" width="100" />

**Image example:** EmptyState illustration with `add-circle` SpotIllustration

**Title:** Sell the benefit of the feature.

**Description:** A sentence that includes an active verb and encourages the user to use a feature.

**Buttons:** One or more buttons that align with the title's verb and empty state's purpose.

#### Configuration required

- Primary action for configuring a feature.
- Secondary action for inviting a member to configure a feature. The secondary action should read as Invite member to configure and trigger the invite modal.

**Content notes:**

<img src={configure} alt="configure SpotIllustration" width="100" />

**Image:** EmptyState illustration with `configure` SpotIllustration

**Title:** Sell the benefit of the feature.

**Description:** A sentence that invites the user to start the configuration for a feature.

**Buttons:** A primary action to start configuration, and optionally a secondary action to delegate configuration to another team member.

#### No results (filters/search too narrow)

A no results empty state appears when there are no search results from a search or from filtering results. This empty state does not contain a CTA and there is no image.

**Content notes:**

**Image:** no image

**Title:** No results found

**Description:** Edit your filters or search and try again.

#### Error (content cannot be displayed)

An error empty state appears when the content cannot be shown due to a backend, network, or permissions-related error.

This empty state should be accompanied with a snackbar that describes why the data could not be loaded. (In the future, we should explore a proper error state component to replace this)

This is content for possible error/warning states, and we should push for the distinction between them — tailoring the content to the actual problem so we can tell the user how to resolve the issue is ideal.

**Content notes:**

<img src={warning} alt="warning SpotIllustration" width="100" />

**Image:** no image or IF using an image use `warning` in EmptyStateIllustration

**Title examples:** Check your connection, You don’t have permission, Couldn’t load content

**Description examples:** We couldn’t reach the server. Make sure you’re online, then try again., This content isn’t available to your role. Contact your admin if you think this is a mistake., Something went wrong. Try again or contact Support if the issue continues.

**Buttons:** If relevant, a primary action to “Refresh” or other relevant next best action to resolve the issue

### Accessibility

#### Interactions

**Mouse**

- Users should be able to click any CTA (button or link)
- No hover-only instructions—always visible or announced

**Keyboard**

- All actions must be reachable with Tab
- Focus should move logically through CTA elements
- No traps: ESC or tab away should exit modal if applicable

**What the design system provides:**

- Semantic structure: headings, paragraph, button roles
- Focus states for all interactive elements
- Contrast-compliant default colors
- ARIA labels as needed for actions

**Development responsibilities:**
- Provide meaningful titles and descriptions that clearly explain the empty state context
- Ensure action buttons have descriptive labels that indicate what will happen when clicked
- Structure content with proper heading hierarchy when embedding EmptyState in larger contexts
- Test keyboard navigation flow to ensure actions are easily discoverable and accessible
- Implement proper error handling and user feedback for failed actions triggered from empty states
- Use appropriate illustration choices that support rather than distract from the message
- Ensure any custom ReactNode content in descriptions maintains accessibility standards

**Design responsibilities:**

- Use clear, readable text with good contrast
- Avoid cramming too much content—prioritize clarity
- If using illustration, ensure it doesn’t obscure message or action
- Don’t rely solely on color or imagery to convey meaning