import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as GridStories from './Grid.stories';

<Meta of={GridStories} />

<Title />

<Description />

<Primary />

<Controls of={GridStories.Playground} />

## Import

```jsx
import { Grid } from '@drata/cosmos-grid';
```

## Props

### `columnWidth`

<Canvas of={GridStories.ColumnWidth} />

### `columnCount`

<Canvas of={GridStories.ColumnCount} />

## 🟢 When to use the component

- **Responsive layouts** - When you need flexible grid systems that adapt to different screen sizes
- **Card collections** - For displaying multiple cards or similar content in organized rows and columns
- **Dashboard layouts** - When building dashboard interfaces with multiple content sections
- **Form layouts** - For organizing form fields in multi-column layouts

## ❌ When not to use the component

- **Simple linear layouts** - Use Stack component for straightforward vertical or horizontal arrangements
- **Single column content** - Use Stack or basic div containers when content naturally flows in one column
- **Table-like data** - Use Datatable component for structured data that needs sorting, filtering, or column-based operations

## 🛠️ How it works

The Grid component provides flexible CSS grid layouts with responsive columns and gaps for organizing content using Radix UI Grid as the foundation.

**Core features:**
- **Column configuration** - Set columns using `columnCount` prop with responsive support
- **Column width control** - Define minimum and maximum column widths with `columnWidth` prop
- **Flexible spacing** - Configurable gaps between grid items
- **Auto-fit/fill behavior** - Control how columns adapt to available space
- **Dimension props** - Supports standard width, height, padding, and margin props

### Usability

**Layout patterns:**
- **Card grids** - Ideal for displaying collections of cards or similar content blocks
- **Dashboard layouts** - Use for organizing dashboard widgets and metrics in responsive grids
- **Gallery displays** - Perfect for image galleries or media collections with consistent item sizing
- **Form layouts** - Organize form sections or input groups in multi-column arrangements

**Responsive considerations:**
- **Mobile-first approach** - Start with single column and add breakpoints for larger screens
- **Content priority** - Ensure most important content remains visible at all screen sizes
- **Touch targets** - Maintain adequate spacing for touch interactions on mobile devices
- **Performance** - Consider lazy loading for large grids with many items

### Content

**Grid item guidelines:**
- **Consistent sizing** - Ensure grid items have similar content density and visual weight
- **Flexible content** - Design grid items to work well at different sizes and aspect ratios
- **Content hierarchy** - Use consistent patterns for titles, metadata, and actions across grid items

**Spacing and alignment:**
- **Consistent gaps** - Use design system spacing tokens for predictable and harmonious layouts
- **Visual rhythm** - Maintain consistent spacing patterns across different grid implementations
- **Content padding** - Ensure grid items have appropriate internal spacing that works with grid gaps

**Responsive content strategy:**
- **Content prioritization** - Show most important content first on smaller screens
- **Progressive disclosure** - Reveal additional content as screen size increases
- **Adaptive layouts** - Adjust content layout within grid items based on available space

### Accessibility

**What the design system provides:**
- Semantic HTML structure with CSS Grid that doesn't interfere with content semantics
- Responsive design that adapts to different screen sizes and zoom levels
- Keyboard navigation that maintains logical tab order through grid items

**Development responsibilities:**
- Ensure grid items are ordered logically in the DOM for screen readers
- Verify grid layouts work well at different zoom levels
- Ensure individual grid items meet accessibility standards

**Design responsibilities:**
- Design clear visual relationships between grid items
- Ensure adequate spacing between grid items for visual clarity
- Design grid layouts that work well across target screen sizes

