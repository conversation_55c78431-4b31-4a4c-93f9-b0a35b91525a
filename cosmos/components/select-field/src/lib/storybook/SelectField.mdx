import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import * as SelectFieldStories from './SelectField.stories';

<Meta of={SelectFieldStories} />

<Title />

<Banner
    severity="education"
    title="Use Web Wrappers for Form Fields"
    body={
        <>
            Avoid using form field components directly from Cosmos. Instead, use
            our web wrappers for optimized functionality and consistent
            integration.{' '}
            <Link
                href="/?path=/docs/form-components-about--docs#web-wrappers"
                label="Click here for more details"
                isExternal
            />
            .
        </>
    }
/>

<Description />

<Primary />

<Controls of={SelectFieldStories.Playground} />

## Import

```jsx
import { SelectField } from '@drata/cosmos-select-field';
```

## Props

### `isLoading`

<Canvas of={SelectFieldStories.Loading} />

### `defaultValue`

<Canvas of={SelectFieldStories.DefaultValue} />

## 🟢 When to use the component

- When you have a list of options from which users need to choose one.
- When the list of options is relatively short (10 options or fewer) and can be displayed without overwhelming the user.
- When you want to conserve space on the interface, as select components collapse the list of options until the user interacts with them.

## ❌ When not to use the component

- **Avoid using select components for long lists of options(10+)**, as it can become cumbersome for users to navigate. In such cases, consider using a ComboboxField which allows users to quickly find items by searching through a list of options.
- **2-5 options that should be visible** Use RadioFieldGroup to show all options at once.
- **Multi-select.** If you need to allow users to choose more than one option at once. Users often don’t understand how to choose multiple items from select elements. Use the CheckboxFieldGroup or ComboboxField instead.

## 🛠️ How it works

The SelectField component combines FormField structure with Select functionality to provide a complete form input with dropdown selection, labeling, validation, and accessibility features.

**Core functionality:**
- **FormField wrapper** - Provides consistent labeling, help text, validation feedback, and form integration
- **Select core** - Handles dropdown display, option selection, and keyboard navigation using Downshift
- **Single selection** - Users select exactly one option from the dropdown list with controlled `value` prop and `onChange` callback
- **ListBoxItems structure** - Accepts options as array supporting individual options and option groups
- **Loading states** - Shows spinner with customizable loader label during async operations
- **Intelligent positioning** - Uses Floating UI for optimal dropdown placement with viewport awareness

### Usability

- **Avoid dependent options.** Avoid making options in one select menu change based on the input to another. Users often don’t understand how choosing an item in one impacts another.
- **Avoid auto-submission.** Don’t automatically submit the form (or do anything else) when an option is chosen. Offer a “submit” button at the end of the form instead. Users often change their choices multiple times. Auto-submission is also less accessible.
- **Actionable default state.** Ensure that the default state of the select component is clear and descriptive, indicating to the user what action is required.

### Content

**Option and field design:**
- **Descriptive labels** - Each option should clearly describe what it represents with consistent grammatical structure
- **Action-oriented labels** - Use field labels that clearly indicate what users should select
- **Clear placeholders** - Use placeholder text that guides user action ("Select an option", "Choose...")
- **Informative loading** - Use descriptive loader labels that explain what's being loaded

**Validation and guidance:**
- **Context-specific help** - Tailor help text to explain the specific selection context
- **Specific error messages** - Provide actionable error messages for validation failures
- **Progressive disclosure** - Consider showing most common options first when appropriate

See SelectField content guidance [here](https://www.notion.so/drata/Select-components-71a8bf84e3684b77b0f2e5011ba781b0?source=copy_link).

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper form field relationships using label elements and ARIA attributes
- Complete keyboard navigation including arrow keys for option selection and Escape to close dropdown
- Screen reader announcements for selection changes, validation feedback, and loading states
- High contrast support that works with system accessibility preferences and meets WCAG AA standards
- Focus management with visible focus indicators and logical tab order through field elements
- Automatic ARIA relationships connecting labels, help text, validation feedback, and dropdown options

**Development responsibilities:**
- Provide meaningful `label` prop that clearly describes the selection being made
- Use `helpText` prop to provide additional context or instructions when needed
- Implement proper `onChange` handling that updates form state when selection changes
- Use `required` prop appropriately when selection is mandatory for form submission
- Provide clear, descriptive option labels that work well with screen reader announcements
- Handle validation states appropriately with meaningful error messages in `feedback` prop
- **Avoid auto-submission.** Don’t automatically submit the form (or do anything else) when an option is selected. Auto-submission disrupts screen readers because they select each option as they read them.

**Design responsibilities:**
- Provide sufficient color contrast for all field states including focus, error, and disabled states
- Design clear visual hierarchy that distinguishes between label, input, help text, and feedback
- Ensure focus indicators are clearly visible and meet contrast requirements for keyboard navigation
- Create consistent visual patterns for form fields that work across different contexts and layouts
- Design appropriate spacing and sizing that works with browser zoom up to 200% magnification
- Ensure validation states are clearly distinguishable through visual design and not rely solely on color

