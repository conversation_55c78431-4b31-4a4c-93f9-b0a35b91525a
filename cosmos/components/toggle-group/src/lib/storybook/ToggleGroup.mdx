import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Source,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import * as ToggleGroupStories from './ToggleGroup.stories';
import { CONTROLLED_TOGGLE_GROUP_EXAMPLE } from './constants';

<Meta of={ToggleGroupStories} />

<Title />

<Description />

<Primary />

<Controls of={ToggleGroupStories.Playground} />

## Import

```jsx
import { ToggleGroup } from '@drata/cosmos-toggle-group';
```

## Props

### `isIconOnly`

If `isIconOnly` is true, then the labels will be hidden and only the icon will be shown.

<Banner
    data-id="label-banner"
    severity="education"
    title="Label requirement"
    body={
        <>
            Even if <code>isIconOnly</code> is <code>true</code>, the{' '}
            <code>label</code> prop is still required. Screenreader users rely
            on the labels to navigate and select options.
        </>
    }
/>

<Canvas of={ToggleGroupStories.IsIconOnly} />

### `orientation`

ToggleGroups can be oriented either vertically or horizontally.

<Canvas of={ToggleGroupStories.Orientation} />

## Examples

### Fully controlled ToggleGroup

Use `onChange` and `selectedOption` to externally control the state of ToggleGroup.

<Canvas of={ToggleGroupStories.ControlledToggleGroup} />

<Source language="tsx" code={CONTROLLED_TOGGLE_GROUP_EXAMPLE} />

## 🟢 When to use the component

- **Icon-based choices** - When options can be effectively represented with icons, especially in space-constrained layouts
- **Immediate selection feedback** - When the selected state should be immediately visible and the choice takes effect right away
- **Single choice from multiple options** - When users need to select exactly one option from a small set of mutually exclusive choices
- **Visual option comparison** - When options benefit from being displayed simultaneously for easy comparison
- **Toggle-style selection** - When you want button-style selection rather than traditional radio buttons

## ❌ When not to use the component

- **Large option sets** - Use SelectField or RadioFieldGroup when you have many options that would make the toggle group unwieldy
- **Form field integration** - Use RadioFieldGroup when you need labels, help text, validation, and form field structure
- **Multiple selections** - Use CheckboxFieldGroup when users can select multiple options
- **Binary choices** - Use ToggleField for simple on/off decisions
- **Complex option descriptions** - Use RadioFieldGroup when options need detailed explanations or help text

## 🛠️ How it works

The ToggleGroup component allows users to select one option from several available choices using toggle-style buttons with support for both controlled and uncontrolled state management.

**Core functionality:**
- **Single selection** - Only one option can be selected at a time, with automatic deselection of other options
- **State management** - Supports both controlled (`selectedOption` + `onChange`) and uncontrolled (`initialSelectedOption`) patterns
- **Icon support** - Optional `startIconName` for each option with `isIconOnly` mode for icon-only display
- **Flexible orientation** - Horizontal (default) or vertical layout options

**Component structure:**
- **StyledToggleGroupRootDiv** - Radix ToggleGroup.Root wrapper providing group behavior and ARIA support
- **ToggleGroupItem components** - Individual toggle buttons with proper labeling and state management
- **Option-based rendering** - Maps through `options` array to create consistent toggle items

**Data structure (ToggleGroupOption):**
- **Required properties** - `label` (string), `value` (string) for identification and display
- **Optional enhancements** - `startIconName` for icons, `disabled` for individual option control
- **Accessibility requirement** - Labels always required even in `isIconOnly` mode for screen readers

### Usability

**Option design:**
- **Clear visual states** - Selected and unselected states should be immediately distinguishable through multiple visual cues, not just color
- **Appropriate sizing** - Use horizontal orientation for short labels, vertical for longer text
- **Logical grouping** - Group related options that represent mutually exclusive choices

**Interaction patterns:**
- **Immediate feedback** - Selection changes should be visually apparent immediately
- **Keyboard navigation** - Full keyboard support with arrow key navigation between options
- **Touch targets** - Ensure adequate spacing and sizing for touch interaction

### Content

**Option setup:**
- **Descriptive labels** - Use clear, concise labels that accurately describe each choice
- **Parallel structure** - Maintain consistent grammatical structure across all option labels
- **Logical ordering** - Arrange options in logical order (frequency, importance, alphabetical)
- **Distinctive options** - Each option should be clearly different and easily distinguishable

**Icon implementation:**
- **Universal clarity** - When using `isIconOnly`, ensure icons clearly represent their options and are universally understandable
- **Label requirement** - Always provide meaningful `label` values for all options, even in `isIconOnly` mode for accessibility
- **Consistent iconography** - Use icons from the same visual family or style

### Accessibility

This component is built using the [Radix Primitives Toggle Group](https://www.radix-ui.com/primitives/docs/components/toggle-group), and uses [roving tab index](https://www.w3.org/WAI/ARIA/apg/patterns/radio/examples/radio/) to manage focus movement among items.

**What the design system provides:**
- Proper ARIA attributes for toggle group role and individual toggle states
- Keyboard navigation with Tab to enter group and arrow keys to navigate between options
- Screen reader support with proper announcements for group context and selection changes
- Focus management with visible focus indicators and roving tab index behavior
- High contrast support maintaining usability across different visual accessibility settings

**Development responsibilities:**
- Implement proper state management through `onChange` callback with clear value handling
- Choose appropriate `orientation` that maintains logical reading and navigation order
- Ensure `initialSelectedOption` or `selectedOption` values match actual option values
- Test keyboard navigation and screen reader announcements in context

**Design responsibilities:**
- Design focus indicators that meet accessibility standards for keyboard navigation
- Maintain consistent toggle sizing and spacing for reliable interaction targets
- Ensure sufficient color contrast and visual distinction between states


