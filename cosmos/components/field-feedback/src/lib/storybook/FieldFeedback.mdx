import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as FieldFeedbackStories from './FieldFeedback.stories';
import { Banner } from '@cosmos/components/banner';

<Meta of={FieldFeedbackStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos form component, you are probably looking for a different component."
/>

<Description />

<Primary />

<Controls of={FieldFeedbackStories.Playground} />

## Import

```jsx
import { FieldFeedback } from '@drata/cosmos-field-feedback';
```

## 🟢 When to use the component

- **Building form field components** - Use as the feedback foundation when creating new Cosmos form components
- **Validation feedback** - Provide success or error messaging for form field validation states

## ❌ When not to use the component

- **Standard form fields** - Use complete form field components that already include feedback functionality
- **General messaging** - Use Feedback component for non-form-related success or error messages
- **Page-level notifications** - Use Banner component for system-wide or page-level messaging
- **Interactive feedback** - Use components with built-in actions when users need to respond to feedback

## 🛠️ How it works

The FieldFeedback component displays success or error feedback with contextual icons for form field validation.

**Core functionality:**
- **Type-driven display** - Success (CheckCircle) or error (WarningDiamond) with matching colors
- **Form integration** - Accepts `id` for ARIA relationships and `gridArea` for positioning
- **Design system styling** - Uses Text component and design tokens for consistency

### Usability

- Designed for use within FormField and form components
- Should appear immediately after validation occurs and remain visible until validation state changes
- Parent components control display timing and message updates
- Maintains consistent positioning across form fields

### Content

- Messages should be clear, specific, and actionable
- Success messages confirm what was accomplished
- Error messages explain problems and guide toward solutions
- Use consistent tone and avoid technical jargon

### Accessibility

**What the design system provides:**
- Semantic HTML with proper text and icon integration for screen readers
- High contrast colors meeting WCAG AA standards for both states
- Scalable design working with browser zoom up to 200%
- Color-independent visual cues through distinct icon shapes

**Development responsibilities:**
- Provide stable `id` prop for ARIA relationships with form inputs
- Ensure clear, actionable feedback messages for all users
- Connect to form inputs using proper ARIA attributes in parent components
- Use meaningful `data-id` props for testing without affecting accessibility

**Design responsibilities:**
- Provide sufficient color contrast across all supported themes
- Design clear visual hierarchy showing input-feedback relationships
- Ensure icons and text work together without relying solely on color
- Create consistent patterns across all form components

