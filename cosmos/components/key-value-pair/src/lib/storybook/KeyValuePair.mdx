import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as KeyValuePairStories from './KeyValuePair.stories';

<Meta of={KeyValuePairStories} />

<Title />

<Description of={KeyValuePairStories} />

<Primary />

<Controls of={KeyValuePairStories.Playground} />

## Import

```jsx
import { KeyValuePair } from '@cosmos/components/key-value-pair';
```

The KeyValuePair component provides a simple way to display labeled information:

```jsx
<KeyValuePair
    label="Status"
    value="Active"
    type="TEXT"
/>
```

## Props

### `type`

The `type` prop determines how the value content is displayed:

<Canvas of={KeyValuePairStories.Type} />

### `truncationMode`

Control how long text values are handled with the `truncationMode` prop. This only applies when `type` is `TEXT`:

<Canvas of={KeyValuePairStories.TruncationMode} />

### Interactive elements

Add interactivity with icons and click handlers. When providing an `onClick` callback, both `iconName` and `ariaLabel` are required:

<Canvas of={KeyValuePairStories.Interactive} />

### `visibleItemsLimit`

Manage display of multiple items with `visibleItemsLimit`. This applies to array values for all types except `TEXT`:

<Canvas of={KeyValuePairStories.ItemsOverflow} />

### Feedback integration

Provide additional context with feedback props:

<Canvas of={KeyValuePairStories.FeedbackExamples} />

## 🟢 When to use the component

Use KeyValuePair when you need to display labeled information in a consistent format. It's ideal for:

- **Small sets of labeled details and metadata** that users need to reference quickly
- **Simple object attributes** that don't require complex formatting or interaction
- **Static information** like product specifications, profile information, or object metadata
- **Structured data presentation** where the relationship between label and value is important
- **Consistent formatting** across different types of content (text, badges, tags, users)

**Perfect for:**
- User profile information with mixed content types
- Object metadata and properties
- Settings displays with various value types
- Summary cards with key details
- Form review sections showing entered data

## ❌ When not to use the component

- **Large datasets require complex structures** - When dealing with extensive data that needs tables, charts, or other complex organizational structures
- **Complex user interactions are needed** - When tasks require functionality beyond simple data display, such as editing, sorting, or filtering
- **Rich media content display** - When displaying images, videos, or other media content that requires specialized presentation
- **Complex data relationships exist** - When data has intricate relationships that cannot be expressed as simple key-value pairs
- **Table cell content** - When displaying data within table cells, use dedicated cell components instead of KeyValuePair to maintain proper table structure and accessibility

## 🛠️ How it works

KeyValuePairs consist of a label (key) and a value with flexible content options.

**Label (Key)**
- Text-based descriptive labels that identify the information being displayed
- Can include interactive elements when click behavior is needed
- Should be planned for minimum and maximum content lengths within KeyValuePair groups

**Value**
- **Text** - Simple string values with optional feedback integration
- **Metadata badges** - One or more status indicators with color schemes and optional icons
- **Tags** - One or more categorical labels for content organization
- **User information** - Avatar and username combinations representing users or owners
- **External links** - Clickable links that open in new tabs or windows
- **React nodes** - Custom content for specialized display needs

### Usability

- **Proper alignment** - Ensure consistent alignment of labels and values for visual clarity
- **Tooltip integration** - Provide tooltips for additional information when necessary, especially for truncated content
- **Limited interactivity** - Avoid inline editing or complex interactions beyond simple click handlers
- **Layout considerations** - Consider spacing and organization when displaying multiple KeyValuePairs, especially with complex values. Limit to 5 key-value pairs maximum in page headers
- **Automatic text handling** - Built-in truncation options for managing long content on smaller screens
- **Flexible content types** - Support for text, badges, tags, users, and React nodes
- **Responsive design** - Content automatically adapts to available space with touch-friendly interaction targets maintained across device sizes
- **Visual hierarchy** - Labels should be visually secondary to values to establish clear information hierarchy, with values as the primary focus
- Group related key-value pairs together

### Content

- **Descriptive labels** - Use clear, meaningful labels that clearly indicate what the value represents
- **Well-formatted values** - Present information clearly and concisely with appropriate formatting for the content type
- **Appropriate content types** - Choose the right type (TEXT, BADGE, TAG, USER, EXTERNAL-LINK, REACT_NODE) for the content
- **Consider truncation** - Use truncation modes that preserve essential information for potentially long values
- **Provide feedback** - Use `feedbackProps` to provide additional context when helpful
- **Consistent labeling patterns** - Use similar labeling approaches across related types of information

**Required Content:**
- `label`: The descriptive text that identifies what the value represents
- `value`: The actual information being displayed

**Optional Content:**
- `type`: Determines how the value is rendered (TEXT, BADGE, TAG, USER, EXTERNAL-LINK, REACT_NODE)
- `iconName`: Adds an icon for interactive elements (required when `onClick` is provided)
- `ariaLabel`: Accessible label for interactive elements (required when `onClick` is provided)
- `truncationMode`: Controls how long text is handled (none, ellipsis, wrap) - only applies to TEXT type
- `visibleItemsLimit`: Limits displayed items for array values
- `feedbackProps`: Provides additional context or validation feedback
- `isLoading`: Shows skeleton loading state
- `showEmptyValue`: Shows em dash for empty values

### Accessibility

**What the design system provides:**
- Semantic structure with proper label-value relationships and appropriate text hierarchy
- Accessible color system with design system tokens ensuring WCAG 2.1 AA contrast compliance for all content types
- Tooltip integration with accessible tooltip components and proper ARIA attributes for truncated content
- Keyboard navigation support with interactive elements that are keyboard accessible and proper focus management
- Screen reader optimization with labels and values properly associated and announced
- Touch target sizing with interactive elements meeting accessibility guidelines with minimum 44px touch targets

**Development responsibilities:**
- Provide meaningful labels that clearly describe what the value represents
- Handle truncation accessibility by ensuring any truncation of the value can be accessed by all users through tooltips or other means
- Use appropriate content types by choosing the right type (TEXT, BADGE, TAG, USER) for the content
- Test with assistive technology to verify label-value relationships are clear to screen readers
- Support keyboard interaction by ensuring interactive elements respond properly to keyboard input
- Add contextual information using `feedbackProps` to provide additional accessible context when needed

**Design responsibilities:**
- Create clear visual hierarchy by designing labels and values with sufficient contrast and visual distinction
- Don't rely on color alone by using text, icons, and other visual cues alongside color for badges and tags
- Ensure sufficient spacing by maintaining adequate spacing between label-value pairs for readability
- Design accessible states with clear visual states for interactive, hover, focus, and disabled elements
- Consider content length by designing for variable content lengths while maintaining accessibility
- Plan for responsive behavior by ensuring touch targets remain accessible across different screen sizes
