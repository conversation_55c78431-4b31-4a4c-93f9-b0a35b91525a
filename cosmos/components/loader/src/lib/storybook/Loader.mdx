import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as LoaderStories from './Loader.stories';

<Meta of={LoaderStories} />

<Title />

<Description />

<Primary />

<Controls of={LoaderStories.Playground} />

## Import

```jsx
import { Loader } from '@drata/cosmos-loader';
```

## Examples

### LoaderMedium

This represents the standard loading experience, akin to what is used when a page is loading.

<Canvas of={LoaderStories.LoaderMedium} />

### SpinnerOnly

The `spinnerOnly` option is used at a more granular level, such as inline with text.

<Canvas of={LoaderStories.SpinnerOnly} />
<Controls of={LoaderStories.ChangePositions} include={['isSpinnerOnly']} />

### ChangePositions

This is an example of how `labelPosition` can be used to move the label around the spinner

<Canvas of={LoaderStories.ChangePositions} />
<Controls of={LoaderStories.ChangePositions} include={['labelPosition']} />

### A11yLabelOverride

This is an example of how a11yLabelOverride can be used to override what is announced to non-visual
users. We use it when we believe that additional context is needed, such as when information is
conveyed by an icon.

Note to "see" this change you will need to inspect the DOM the change to the aria-label or turn on
VoiceOver to hear how this is announced differently than the visual label.

<Canvas of={LoaderStories.A11yLabelOverride} />
<Controls of={LoaderStories.ChangePositions} include={['a11yLabelOverride']} />

## 🟢 When to use the component

- Use inside of pages, sections, and experiences. Use when data is being retrieved but it doesn’t indicate how long it will take to complete.

## ❌ When not to use the component

- Don’t use when an action is instantaneous
- If loading is triggered by an action, consider using the [IsLoading Button](https://cosmos.drata.com/?path=/docs/actions-button--docs#isloading)

## 🛠️ How it works

### Usability

- Only show a spinner if the expected wait time is more than a second.
- There should only be a single spinner on a page at any time.
- When only a portion of a page is being updated, place the spinner in that part of the page.
- If you're unsure where to place the spinner, place it where you want the user's attention to be when loading is finished.
- Use the inverted `colorScheme` variant on dark backgrounds.

### Content

- Provide a concise and accurate label of what is being loaded, using no more than three words.

### Accessibility

- A `label` prop is required when there is no visible `label`. Provide descriptive and informative text or ARIA-live attributes to convey the purpose and status of the spinner to users who rely on screen readers.
- Consider implementing timeouts and error handling mechanisms in case the spinner is indicative of a time-limited operation. Users should be informed of any issues or delays.
- In the case that there is a visible label but do not feel it is enough information for non-sighted users you can provide an alternative via a11yLabelOverride that will be used for the aria-label instead
- If the spinner represents a loading state or a process, include alternative text or a screen reader-friendly message to inform users about the status or purpose of the spinner.
