import { Controls, Description, Meta, Primary, Title } from '@storybook/addon-docs/blocks';
import * as InputStories from './Input.stories';
import { Banner } from '@cosmos/components/banner';

<Meta of={InputStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos form component, you are probably looking for a different component."
/>

<Description />
<br />

<Primary />

<Controls of={InputStories.Playground} />

## Import

```jsx
import { Input } from '@drata/cosmos-input';
```

## 🟢 When to use the component

- **Building Cosmos form components** - Use as the foundational input element when creating new form field components for the design system
- **Low-level input control** - When you need direct control over input behavior while maintaining design system consistency

## ❌ When not to use the component

- **Application development** - Use complete form field components (TextField, NumberField, etc.) instead of building with Input directly
- **Standard text input needs** - Use TextField which provides complete form field functionality
- **Complex input patterns** - Use specialized components like DateField, SelectField, or ComboboxField for specific input types
- **Multi-line text** - Use TextareaField for multi-line text input requirements
- **File uploads** - Use FileUploadField or ImageUploadField for file input functionality

## 🛠️ How it works

The Input component provides foundational input functionality for building other Cosmos form components, handling controlled and uncontrolled states, validation feedback styling, and accessibility features.

**Core functionality:**
- **Forwardable ref** - Uses `forwardRef` to allow parent components to access the underlying input element
- **State management** - Handles both controlled (`value` prop) and uncontrolled (`defaultValue` prop) input patterns
- **Validation styling** - Accepts `feedbackType` for error/success border colors coordinated with validation systems
- **Grid integration** - `gridArea` prop enables positioning within CSS Grid layouts

**Input behavior:**
- **Standard HTML input** - Maintains all native input accessibility features and keyboard interactions
- **Type variations** - Supports all HTML input types (text, email, password, number, etc.)
- **Read-only support** - `readOnly` prop prevents user input while maintaining visual state
- **Change handling** - `onChange` callback works in both controlled and uncontrolled modes

### Usability

- **Controlled usage** - Parent components manage value state and pass to Input via `value` prop
- **Uncontrolled usage** - Input manages its own state when only `defaultValue` is provided
- **Read-only display** - Use `readOnly` prop for displaying values that shouldn't be edited
- **FormField composition** - Designed to work within FormField wrapper for complete form field functionality
- **Validation coordination** - `feedbackType` prop coordinates with validation systems for visual feedback
- **Ref forwarding** - Parent components can access input element for focus management or validation

### Content

- **Appropriate types** - Use correct `type` attribute for input purpose (email, password, tel, etc.)
- **Meaningful placeholders** - Use placeholder text that provides helpful examples or guidance
- **Default values** - Provide sensible default values when appropriate for user experience
- **Label relationships** - Ensure proper label association through parent FormField component
- **ARIA attributes** - Use appropriate ARIA attributes for complex form field relationships
- **Form field composition** - Always use within FormField or similar wrapper for complete functionality

### Accessibility

**What the design system provides:**
- Standard HTML input element with full native accessibility support including keyboard navigation and screen reader compatibility
- High contrast support that works with system accessibility preferences and meets WCAG AA standards for all input states
- Scalable typography and spacing that works with browser zoom up to 200% magnification
- Proper focus indicators that are clearly visible and meet contrast requirements
- Read-only state handling with appropriate `aria-disabled` attribute for assistive technology
- Consistent styling across all input states that maintains accessibility across different themes

**Development responsibilities:**
- Ensure proper label association through parent FormField component using `htmlFor` and `id` attributes
- Implement appropriate ARIA relationships including `aria-describedby` for help text and validation feedback
- Use correct input `type` attribute for the data being collected (email, tel, password, etc.)
- Provide meaningful placeholder text that doesn't replace proper labeling
- Handle validation states appropriately with clear error messaging through parent components
- Ensure `onChange` handlers work correctly in both controlled and uncontrolled modes

**Design responsibilities:**
- Provide sufficient color contrast for all input states including default, focus, error, and read-only across different themes
- Design clear visual hierarchy that shows input state and validation feedback
- Ensure focus indicators are clearly visible and meet contrast requirements for all input variations
- Create consistent visual patterns for input styling across all form components in the design system
- Design appropriate sizing and spacing that works across different screen sizes and form layouts
- Ensure validation feedback styling provides clear visual cues that complement screen reader announcements

