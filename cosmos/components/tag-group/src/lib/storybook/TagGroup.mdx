import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as TagGroupStories from './TagGroup.stories';

<Meta of={TagGroupStories} />

<Title />

<Description />

<Primary />

<Controls of={TagGroupStories.Playground} />

## Import

```jsx
import { TagGroup } from '@cosmos/components/tag-group';
```

## Examples

### Without Clear All

An example of a group of tags without a Clear All button

<Canvas of={TagGroupStories.WithoutClearAll} />

## 🟢 When to use the component

- **Form field collections** - When displaying selected items in form controls like Combobox or multi-select fields
- **Active filter displays** - When showing currently applied filters that users can individually remove
- **Tag collections in data tables** - When displaying multiple tags within a specific column or cell
- **Removable item groups** - When users need to manage collections of selected items with individual removal actions
- **Dynamic content management** - When displaying user-generated or system-assigned tags that can be cleared individually or all at once

## ❌ When not to use the component

- **Status indicators** - Use Metadata or Badge components for displaying status information
- **Single tag display** - Use individual Metadata components when only one tag needs to be shown

## 🛠️ How it works

The TagGroup component manages collections of removable tags with optional bulk clearing functionality and overflow handling.

### Usability

**Core functionality:**
- **Individual tag removal** - Each tag can be removed independently through click actions on Metadata components
- **Bulk clearing** - Optional "Clear all" button removes all tags when `onClearAll` callback is provided
- **Overflow management** - Uses `maxVisibleTags` to limit displayed tags with a "+X more" popover for additional items
- **Flexible content** - Accepts ReactNode children, typically Metadata components with `onClick` handlers

### Content

**Content Guidelines:**
- Use Metadata components as children for consistent tag styling and behavior
- Provide clear, descriptive labels for individual tag removal using `clickLabel` prop
- Keep tag labels concise for better visual organization
- Use "Clear all" for the `clearAllLabel` text

**Overflow behavior:**
- When `maxVisibleTags` is exceeded, remaining tags are shown in a popover triggered by a "+N" button
- The popover displays all overflow tags in a wrapped grid layout with proper spacing
- Overflow tags maintain their interactive functionality within the popover
- Setting `maxVisibleTags` to 0 displays all tags without overflow handling

### Accessibility

**What the design system provides:**
- Semantic container structure with proper spacing and visual organization
- Keyboard navigation support through individual tag components and clear all button
- Focus management with logical tab order through all interactive elements
- Touch target sizing with buttons meeting accessibility guidelines
- Screen reader support through proper labeling of removal actions

**Development responsibilities:**
- Provide descriptive labels for tag removal actions using `clickLabel` prop on Metadata components
- Ensure clear all functionality has meaningful button text that explains the action
- Test with assistive technology to verify all removal actions are accessible
- Handle dynamic content changes by ensuring proper announcements when tags are added or removed
- Implement proper error states and loading indicators when tag operations are in progress

**Design responsibilities:**
- Create clear visual distinction between individual tags and the clear all button
- Ensure sufficient spacing between interactive elements to prevent accidental activation
- Design accessible states for hover, focus, and active interactions
- Consider content length and ensure tags remain readable at different screen sizes
- Provide clear visual feedback for removal actions and loading states

