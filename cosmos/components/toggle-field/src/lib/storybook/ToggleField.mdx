import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import * as ToggleFieldStories from './ToggleField.stories';

<Meta of={ToggleFieldStories} />

<Title />

<Banner
    severity="education"
    title="Use Web Wrappers for Form Fields"
    body={
        <>
            Avoid using form field components directly from Cosmos. Instead, use
            our web wrappers for optimized functionality and consistent
            integration.{' '}
            <Link
                href="/?path=/docs/form-components-about--docs#web-wrappers"
                label="Click here for more details"
                isExternal
            />
            .
        </>
    }
/>

<Description />

<Primary />

<Controls of={ToggleFieldStories.Playground} />

## Import

```jsx
import { ToggleField } from '@drata/cosmos-toggle-field';
```

## 🟢 When to use the component

- **Binary choices** - When users need to make a simple on/off, enabled/disabled, or yes/no decision
- **Form toggles** - When you need a switch-style input with proper form integration, labeling, and validation
- **Immediate state changes** - When the toggle action should take effect immediately and be visually clear

## ❌ When not to use the component

- **Single choice from many options** - Use RadioField or SelectField when users must choose one option from several alternatives
- **Text-based agreement** - Use CheckboxField when the choice is better expressed as agreement with a statement rather than a binary state

## 🛠️ How it works

The ToggleField component combines FormField structure with Toggle functionality, providing a complete switch input with labeling, validation, and accessibility support for binary state management.

**Core functionality:**
- **Binary state management** - Handles checked/unchecked states with controlled (`checked` + `onChange`) and uncontrolled (`defaultChecked`) patterns
- **Form integration** - Uses `formId` and `name` props for proper form association and submission
- **Layout flexibility** - Supports `input-left` (default) and `input-right` layouts for positioning relative to labels

**Component structure:**
- **StyledFormField wrapper** - Provides consistent labeling, help text, and form integration using `renderInput` pattern
- **Toggle element** - Core Toggle component with proper styling, event handling, and ARIA attributes
- **Label association** - Proper HTML label relationships through FormField's `aria-labelledby` and `aria-describedby` attributes

**State and behavior:**
- **Event handling** - `onChange` callback receives boolean value for state management
- **Read-only support** - `readOnly` prop prevents interaction while maintaining visual state
- **Disabled state** - `disabled` prop provides visual and functional disabled state
- **Value handling** - Optional `value` prop for form submission data

### Usability

**Toggle interaction:**
- **Clear state indication** - Toggle position should clearly communicate the current on/off state with immediate visual feedback
- **Appropriate layout** - Choose `input-left` or `input-right` layout based on content flow and design requirements
- **Keyboard navigation** - Full keyboard support with Space and Enter key activation

**Validation and feedback:**
- **Wait to validate** - Only show validation messages after user interaction with the toggle
- **Clear error messaging** - Provide specific feedback when toggle state doesn't meet form requirements

### Content

**Field setup:**
- **Descriptive labels** - Use clear labels that describe what the toggle controls or enables
- **Help text for context** - Explain what happens when the toggle is on or off, especially for complex features
- **Action-oriented language** - Use labels that clearly indicate the action or state being controlled

**State communication:**
- **Clear consequences** - Make it obvious what enabling or disabling the toggle will do
- **Default state guidance** - Choose appropriate `defaultChecked` values that match user expectations
- **Consistent terminology** - Use consistent on/off language throughout your application

### Accessibility

This component is built using the [Radix Primitives Switch](https://www.radix-ui.com/primitives/docs/components/switch), and follows the [Switch WAI-ARIA design pattern](https://www.w3.org/WAI/ARIA/apg/patterns/switch/).

**What the design system provides:**
- FormField integration with proper label associations (`aria-labelledby`), help text relationships (`aria-describedby`), and validation feedback
- Proper ARIA attributes for switch role and state through underlying Toggle component
- Keyboard navigation support (Space and Enter keys) with logical tab order
- Screen reader compatibility with proper announcements for state changes and labels
- Focus management with visible focus indicators and logical navigation flow
- High contrast support maintaining usability across different visual accessibility settings

**Development responsibilities:**
- Provide meaningful `formId`, `name`, and `label` props for proper form association and identification
- Implement proper state management through `checked`/`onChange` or `defaultChecked` patterns
- Choose appropriate `layout` values that maintain logical reading order
- Test keyboard navigation and screen reader announcements in context

**Design responsibilities:**
- Design clear visual states for on/off positions with sufficient contrast
- Ensure toggle states are distinguishable through multiple visual cues, not just color
- Design focus indicators that meet accessibility standards for keyboard navigation
- Maintain consistent toggle sizing and spacing for reliable touch targets

#### Keyboard Support

<table>
    <tbody>
        <tr>
            <th>Key</th>
            <th>Function</th>
        </tr>
        <tr>
            <td>
                <kbd>Space</kbd>
            </td>
            <td>Checks/unchecks the toggle</td>
        </tr>
        <tr>
            <td>
                <kbd>Enter</kbd>
            </td>
            <td>Checks/unchecks the toggle</td>
        </tr>
    </tbody>
</table>


