import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import * as RadioFieldStories from './RadioField.stories';

<Meta of={RadioFieldStories} />

<Title />

<Banner
    severity="education"
    title="Use Web Wrappers for Form Fields"
    body={
        <>
            Avoid using form field components directly from Cosmos. Instead, use
            our web wrappers for optimized functionality and consistent
            integration.{' '}
            <Link
                href="/?path=/docs/form-components-about--docs#web-wrappers"
                label="Click here for more details"
                isExternal
            />
            .
        </>
    }
/>

<br />

<Banner
    severity="warning"
    title="Unstable component"
    body={
        <>
            This component is considered <strong>Unstable</strong> because it
            has incomplete test coverage.
        </>
    }
/>

<Description />

<Primary />

<Controls of={RadioFieldStories.Playground} />

## Import

```jsx
import { RadioField } from '@drata/cosmos-radio-field';
```

## 🟢 When to use the component

- **Single Choice Selections** - When you want users to choose only one option from a list.
- **Preferences or Settings** - When configuring settings that require a single preference selection.

## ❌ When not to use the component

- **Multiple Selections Needed** - If users must select multiple options from a list simultaneously, use checkboxes instead of radio buttons.
- **Long Lists of Options** - If users need to select from a long list of options, use ComboboxField instead.
- **Binary Choices** - For simple yes/no or on/off choices, use toggle switches or checkboxes, which are more intuitive for binary decisions.

## 🛠️ How it works

The RadioField component is an individual radio button used within a RadioFieldGroup that allows users to select only one option from a group of related choices, providing complete form field functionality with labeling, validation, and accessibility.

**Component structure:**
- **FormField wrapper** - Provides consistent labeling, help text, validation feedback, and form integration
- **Radio input** - Core radio button element with custom styling and accessibility features
- **Input-left layout** - Positions radio button to the left of the label with custom gap spacing
- **Controlled state** - Uses `checked` prop for controlled selection state management

**Radio button behavior:**
- **Single selection** - Only one radio button in a group can be selected at a time
- **Group coordination** - Radio buttons with the same `name` prop form a selection group
- **Controlled state** - Parent component manages `checked` state and handles `onChange` events
- **Value association** - Each radio has a unique `value` that's passed to `onChange` when selected
- **Read-only support** - `readOnly` prop prevents user interaction while maintaining visual state

**Form field integration:**
- **Complete labeling** - Integrates with FormField for proper label, help text, and validation display
- **ARIA relationships** - Automatic `aria-labelledby` and `aria-describedby` connections
- **Validation feedback** - Supports field-level validation through FormField feedback system
- **Required field handling** - Standard required/optional field patterns with visual indicators
- **Custom spacing** - Uses `dimensionXs` and `dimensionMd` tokens for optimal radio-to-label spacing

### Usability

- Use the RadioField component within a RadioFieldGroup to create a set of selectable options.
- Create a RadioFieldGroup by defining the options and managing the selected state.
- Group related options logically and in order.
- Manage the selected state and handle changes appropriately.

### Content

- Ensure each RadioField label is clear and descriptive, making it easy for users to understand their choices.
- Ensure the group label clearly describes the set of options.
- Use concise and descriptive labels for each radio button.

### Accessibility

- **Group Label:** Provide a group label that describes the set of radio buttons. This helps users understand the context and purpose of the group. Use the **`label`** and **`helpText`** props to group related radio buttons together with a descriptive legend.
- **Label Association** Each RadioField must have a clear and descriptive label that is programmatically associated with it. This helps screen readers identify the purpose of each radio button. Use the **`label`** prop to provide the text label.
