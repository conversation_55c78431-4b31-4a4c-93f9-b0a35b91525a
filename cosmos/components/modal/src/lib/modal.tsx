import type { ModalBodyProps } from './ModalBody';
import type { ModalFooterProps } from './ModalFooter';
import type { ModalHeaderProps } from './ModalHeader';

type ModalProps = ModalHeaderProps & ModalBodyProps & ModalFooterProps;
/**
 * The Modal component displays supplementary content or interactive elements on top of the main application interface, ensuring a focused and contextually relevant user experience.
 *
 * Modal is a compound component that consists of ModalHeader, ModalBody, and ModalFooter. The Modal component is a wrapper that provides the modal container.
 *
 * This is a workaround for storybook because we don't have single root component to build stories off of.
 * - Combining interfaces so storybook can identify required props
 * - Shell of a component for main storybook story.
 *
 * TODO: build replacement modal container for the ReactStrap Modal currently in use in CL
 * container should be display: flex
 * container will control height and width based on SIZE prop.
 *
 * [Modal in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=46183-175284&t=QHg4TovE4tsvKYCl-4).
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars -- need this
export const Modal = (_props: ModalProps): React.JSX.Element => (
    <div data-testid="Modal" data-id="Sj8Cxgs0" />
);
