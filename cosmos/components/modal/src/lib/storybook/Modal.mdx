import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as ModalStories from './Modal.stories';
import { Banner } from '@cosmos/components/banner';

<Meta of={ModalStories} />

<Title />

<Banner
    severity="critical"
    title="Important"
    body="This package only provides the internal pieces of a modal. You must put them together yourself and use them with the `useModal` hook from `@cosmos/components/modal`. See examples at the bottom of the page."
/>

<Description />

<Primary />

<Banner
    severity="primary"
    title="Note"
    body="The following controls are combined from all sub-components for demonstration purposes. See individual sub-component sections for relevant props."
/>

<Controls of={ModalStories.Playground} sort="requiredFirst" />

## Import

```tsx
import { Modal } from '@cosmos/components/modal';

```

## Components

### ModalHeader

The top section of a modal, containing a title and description text to provide context or information about the content within the modal.

<Canvas of={ModalStories.ModalHeader} />

<Controls of={ModalStories.ModalHeader} sort="requiredFirst" />

### ModalBody

The central area of a modal where the main content or information is displayed, such as text, forms, or multimedia elements.

<Canvas of={ModalStories.ModalBody} />

<Controls of={ModalStories.ModalBody} sort="requiredFirst" />

### ModalFooter

The bottom section of a modal, reserved for buttons allow users to take specific actions related to the modal content, like confirming or canceling an operation.

<Canvas of={ModalStories.ModalFooter} />

<Controls of={ModalStories.ModalFooter} sort="requiredFirst" />

## Examples

### Generic

```js
import { modalController } from '@controllers/modal';
import { Modal } from '@cosmos/components/modal';

...

...

const handleOpen = () => {
    modalController.openModal({
        id: 'modal-id',
        content: () => <YourModalComponent ... />,
        centered: true,
        disableClickOutsideToClose: false,
        size: 'md',
    });
}
```

```tsx
import { Modal } from '@cosmos/components/modal';

...

function YourModalComponent({ onClose, onConfirm }) {
    return (
        <>
            <Modal.Header
                title="REPLACE_ME"
                description="REPLACE_ME"
                closeButtonAriaLabel="REPLACE_ME"
                onClose={onClose}
            />
            <Modal.Body>{/* Your modal content goes here */}</Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: 'REPLACE_ME',
                        level: 'tertiary',
                        onClick: onClose,
                    },
                    {
                        label: 'REPLACE_ME',
                        level: 'primary',
                        onClick: onConfirm,
                    },
                ]}
            />
        </>
    );
}
```

### Form

```tsx
import { Modal } from '@cosmos/components/modal';
...

const StyledForm = styled(Form)`
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
`;

...

function YourModalFormComponent({ onClose, onSubmit }) {
    const handleSubmit = async () => {
        try {
            // post data to API and wait for response
            // trigger onSubmit when response is OK
            onSubmit();
        } catch (error) {
            // form should be re-enabled on error so user can try again
        }
    };

    return (
        <Formik onSubmit={handleSubmit}>
            {(formikProps) => {
                return (
                    <StyledForm noValidate>
                        <Modal.Header
                            title="REPLACE_ME"
                            description="REPLACE_ME"
                            closeButtonAriaLabel="REPLACE_ME"
                            onClose={onClose}
                        />
                        <Modal.Body>
                            {/* Your form components go here */}
                        </Modal.Body>
                        <Modal.Footer
                            rightActionStack={[
                                {
                                    label: 'REPLACE_ME',
                                    level: 'tertiary',
                                    type: 'button',
                                    onClick: onClose,
                                },
                                {
                                    label: 'REPLACE_ME',
                                    level: 'primary',
                                    type: 'submit',
                                    isLoading: formikProps.isSubmitting,
                                },
                            ]}
                        />
                    </StyledForm>
                );
            }}
        </Formik>
    );
}
```

## 🟢 When to use the component

- **Critical warnings and confirmations** - Use for important warnings to prevent or correct critical errors, especially when users' work may be lost or actions have destructive, irreversible consequences (Consider using the ConfirmationModal component)
- **Required information collection** - When missing information prevents the system from continuing a user-initiated process and you need to prompt the user for critical data
- **Contextual explanatory content** - Provide optional information that enhances user understanding without disrupting their main task, such as privacy policies during account creation or detailed explanations during form completion
- **Focused decision making** - When you need to isolate user attention on a specific choice or action that requires immediate resolution
- **Nimble work surfaces** - When users need to perform focused tasks or make quick edits without losing their current context or navigating away from their primary workflow

## ❌ When not to use the component

- **Complex decision making** - Avoid when users need to consult additional sources of information or do complex research that would be blocked by the modal overlay
- **Error messaging** - Don't use for form-related errors, success, or warning messages; display these in context next to relevant fields or use Banner components for page-level messages
- **URL-dependent content** - Don't use when a separate, designated URL is desired for the content or when users need to bookmark or share the specific state
- **Multi-step processes** - Avoid building complex flows in modals; use dedicated pages for multi-step processes instead
- **Automatic displays** - Don't automatically display modals without user action, as this creates surprising and disruptive experiences

## 🛠️ How it works

The Modal component provides a compound structure with ModalHeader, ModalBody, and ModalFooter components that work together to create focused dialog experiences.

**Component structure:**
- **ModalHeader** - Contains title, optional description, and close button with proper ARIA labeling
- **ModalBody** - Scrollable content area that adapts to different modal sizes with appropriate padding
- **ModalFooter** - Action area with left and right action stacks for organizing buttons by importance

**Size variants:**
- **Small (`sm`)** - 386px width, fixed height (264px), for simple confirmations and brief interactions
- **Medium (`md`)** - 548px width, max height 400px, default size for most modal content
- **Large (`lg`)** - 711px width, max height 600px, for complex forms and detailed content
- **Extra Large (`xl`)** - 1440px width, max height 100%, for full-featured interfaces

**Technical behavior:**
- **Focus management** - Automatically traps focus within modal and returns focus to triggering element on close
- **Dismissal options** - Supports Escape key, click outside (configurable), and explicit close button interactions
- **Overlay handling** - Prevents interaction with underlying page content and manages backdrop styling. Click-outside-to-close can be disabled via `disableClickOutsideToClose` prop
- **Context integration** - Uses React Context to share size and close handlers between child components
- **Controller integration** - Works seamlessly with the `modalController` for programmatic modal management and state handling

### Usability

**Interaction patterns:**
- **User-triggered display** - Modals should appear as a result of user actions, never automatically or by surprise
- **Appropriate sizing** - Choose modal size that fits content without requiring excessive scrolling within the modal body
- **Limited actions** - Use primary and secondary actions; reserve tertiary actions for destructive operations like "Delete"
- **Single modal rule** - Never stack modals on top of each other to avoid accessibility problems and user confusion

**Content organization:**
- **Focused content** - Keep modal content relevant to the specific task or decision at hand
- **Minimal scrolling** - Avoid scrollable content within modals when possible; use appropriate sizing instead
- **Action placement** - Place confirmation and submission buttons in the footer, not in the modal body
- **Static background** - Don't reload pages or display new content behind the modal when it's triggered

**User expectations:**
- **Clear purpose** - Users should immediately understand why the modal appeared and what action is required
- **Easy dismissal** - Provide clear and multiple ways to close the modal (Escape key, close button, click outside)
    - Don't put buttons that close the Modal, or confirm and submit content, in the Modal body.
- **Preserved context** - Maintain the user's place in their workflow when the modal closes
- **Immediate feedback** - Provide loading states and clear confirmation when actions are processed

### Content

**Header guidelines:**
- **Descriptive titles** - Use clear, specific titles that explain the modal's purpose (e.g., "Delete Account" not "Confirm Action")
- **Optional descriptions** - Include brief explanatory text when the title alone doesn't provide sufficient context
- **Consistent labeling** - Use aria-labelledby with the header title for proper screen reader announcement

**Body content:**
- **Focused messaging** - Keep content directly relevant to the modal's purpose without unnecessary information
- **Scannable format** - Use clear headings, bullet points, and white space for easy reading
- **Form organization** - Structure form fields logically with appropriate validation and error handling
- **Progressive disclosure** - For complex content, consider breaking information into digestible sections

**Action buttons:**
- **Clear labels** - Use specific, action-oriented button text (e.g., "Save Changes" instead of "OK")
- **Logical ordering** - Place primary actions on the right, secondary actions on the left in the footer
- **Destructive actions** - Use appropriate color schemes (danger) and confirmation patterns for irreversible actions
- **Loading states** - Show loading indicators on buttons during processing to provide feedback

### Accessibility

**What the design system provides:**
- Focus management that automatically traps focus within the modal and returns focus to the triggering element when closed
- Keyboard navigation with full keyboard accessibility and Escape key support for dismissal
- ARIA attributes with proper aria-labelledby and aria-describedby associations with modal content
- Screen reader support that announces modal opening and provides context through proper semantic structure
- Backdrop behavior that prevents interaction with underlying content and manages focus appropriately

**Development responsibilities:**
- Proper labeling to associate modal titles with aria-labelledby and descriptions with aria-describedby
- Keyboard accessibility to ensure all interactive elements within the modal are keyboard accessible
- Focus restoration to verify focus returns to the appropriate element when modal closes
- Content structure using proper heading hierarchy and semantic markup within modal content
- Error handling to provide accessible error messages and validation feedback within forms
- Modals should always be visible - regardless of scrolling, screen size or orientation changes.

**Design responsibilities:**
- Visual focus indicators with clear focus states for all interactive elements within modals
- Sufficient contrast to ensure text and interactive elements meet WCAG contrast requirements against modal backgrounds
- Clear close affordances with obvious visual indicators for how to dismiss the modal
- Backdrop styling that creates appropriate visual separation between modal content and underlying page
- Responsive behavior with modals that work effectively across different screen sizes and orientations

