import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import * as SliderStories from './Slider.stories';

<Meta of={SliderStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos form component, you are probably looking for a different component."
/>

<Description />

<Primary />

<Controls of={SliderStories.Playground} />

## Import

```jsx
import { Slider } from '@drata/cosmos-slider';
```

## Props

### `defaultValue`

Add multiple values to create a range slider.

<Canvas of={SliderStories.CreateRange} />

## 🟢 When to use the component

- **Range selection interfaces** - When building components that need users to select a value or range within defined boundaries (min/max values)
- **Stepped value inputs** - For components requiring discrete value selection with specific step intervals
- **Dual-thumb range pickers** - When creating components that need both minimum and maximum value selection in a single interface

## ❌ When not to use the component

- **Direct application use** - This is a foundational component; use SliderField or other higher-level components for application interfaces
- **Simple numeric inputs** - Use NumberField or TextInput for basic numeric value entry without range visualization
- **Large value ranges** - Avoid for ranges where precise selection is difficult (e.g., 1-10000) without appropriate step values
- **Critical precision inputs** - Don't use when users need to input exact values where slider imprecision could cause issues

## 🛠️ How it works

The Slider component provides foundational range selection functionality using Radix Primitives, supporting dual-thumb configurations for building other Cosmos form components.

**Core functionality:**
- **Value management** - Supports both controlled (`value` + `onValueChange`) and uncontrolled (`defaultValue`) patterns
- **Range configuration** - Configurable `min`, `max`, and `step` properties define the selectable value range and precision
- **Dual-thumb design** - Always renders two thumbs for range selection, accessing `value[0]` and `value[1]`
- **Event handling** - Provides `onValueChange` for real-time updates and `onValueCommit` for final value capture
- **Radix foundation** - Built on Radix Primitives Slider for robust accessibility and interaction handling
- **Value tooltips** - Each thumb displays current value in a tooltip positioned below the thumb for immediate feedback

### Usability

**Implementation patterns:**
- **Controlled vs uncontrolled** - Use controlled pattern (`value` + `onValueChange`) for form integration; uncontrolled (`defaultValue`) for simple cases
- **Step configuration** - Set appropriate step values that match user mental models (e.g., step={5} for percentage ranges, step={1} for counts)
- **Range sizing** - Choose min/max values that provide meaningful granularity without overwhelming precision requirements
- **Thumb sizing** - Use default `sm` thumb size unless building components with specific visual requirements

**Interaction design:**
- **Value feedback** - Tooltips automatically display current values on each thumb during interaction for immediate visual feedback
- **Precision handling** - Consider pairing with numeric inputs for users who need exact value entry
- **Touch targets** - Ensure adequate spacing around slider for touch interaction when building mobile-friendly components
- **Keyboard support** - Leverage built-in arrow key navigation and ensure proper focus management

**Component building:**
- **Label association** - Always associate with proper labels using `aria-labelledby` when building form components
- **Error states** - Use `aria-invalid` and `aria-describedby` to connect with error messaging in parent components
- **Loading states** - Consider disabled state during async operations to prevent user confusion

### Content

**Value communication:**
- **Clear boundaries** - Ensure min/max values are communicated to users through labels, help text, or visual indicators
- **Step indication** - When using non-standard steps, provide guidance about increment behavior
- **Unit specification** - Include units (%, $, etc.) in associated labels or help text for value context
- **Range meaning** - Clearly communicate what the selected range represents in the broader application context

**Integration guidelines:**
- **Consistent labeling** - Use consistent terminology for similar slider implementations across the application
- **Value formatting** - Apply consistent number formatting for displayed values (decimals, thousands separators)
- **Contextual help** - Provide help text explaining the impact of different value selections when not immediately obvious
- **Default values** - Choose sensible defaults that represent common or recommended settings

### Accessibility

This component is built using the [Radix Primitives Slider](https://www.radix-ui.com/primitives/docs/components/slider), and follows the [Slider WAI-ARIA design pattern](https://www.w3.org/WAI/ARIA/apg/patterns/slider-multithumb/).

**What the design system provides:**
- Semantic slider structure following WAI-ARIA Slider design pattern with proper roles and properties
- Keyboard navigation support with arrow keys for value adjustment and Home/End for min/max values
- Screen reader compatibility with value announcements and state changes communicated to assistive technology
- Focus management with visible focus indicators and logical tab order through slider elements
- Touch accessibility with appropriately sized touch targets that meet minimum size requirements
- High contrast support that maintains usability across different visual accessibility settings

**Development responsibilities:**
- Label association using `aria-labelledby` to connect slider with descriptive labels in parent components
- Error state communication through `aria-invalid` and `aria-describedby` for form validation feedback
- Value context by ensuring current values are announced clearly to screen readers during interaction
- Form integration to properly associate slider with form labels, help text, and validation messages
- Loading state management to communicate when slider values are being processed or updated
- Range communication to ensure min/max boundaries and step values are clear to all users

**Design responsibilities:**
- Visual hierarchy with clear relationships between slider, labels, and associated content elements
- Sufficient contrast ratios for all slider elements including track, range, and thumb components
- Focus indicators that provide clear visual feedback meeting WCAG guidelines for keyboard navigation
- Responsive behavior ensuring slider remains usable and accessible across different screen sizes
- Error state styling that clearly communicates validation issues without relying solely on color
- Loading feedback that provides clear visual indication of processing states for all users

#### Keyboard Support

<table>
    <thead>
        <tr>
            <th>Key</th>
            <th>Description</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                <div>
                    <kbd>ArrowRight</kbd>
                </div>
            </td>
            <td>
                <span>
                    Increments/decrements by the{' '}
                    <code data-accent-color="">step</code> value depending on{' '}
                    <code>orientation</code>.
                </span>
            </td>
        </tr>
        <tr>
            <td>
                <div>
                    <kbd>ArrowLeft</kbd>
                </div>
            </td>
            <td>
                <span>
                    Increments/decrements by the{' '}
                    <code data-accent-color="">step</code> value depending on{' '}
                    <code>orientation</code>.
                </span>
            </td>
        </tr>
        <tr>
            <td>
                <div>
                    <kbd>ArrowUp</kbd>
                </div>
            </td>
            <td>
                <span>
                    Increases the value by the{' '}
                    <code data-accent-color="">step</code> amount.
                </span>
            </td>
        </tr>
        <tr>
            <td>
                <div>
                    <kbd>ArrowDown</kbd>
                </div>
            </td>
            <td>
                <span>
                    Decreases the value by the{' '}
                    <code data-accent-color="">step</code> amount.
                </span>
            </td>
        </tr>
        <tr>
            <td>
                <div>
                    <kbd>PageUp</kbd>
                </div>
            </td>
            <td>
                <span>
                    Increases the value by a larger{' '}
                    <code data-accent-color="">step</code>.
                </span>
            </td>
        </tr>
        <tr>
            <td>
                <div>
                    <kbd>PageDown</kbd>
                </div>
            </td>
            <td>
                <span>
                    Decreases the value by a larger{' '}
                    <code data-accent-color="">step</code>.
                </span>
            </td>
        </tr>
        <tr>
            <td>
                <div>
                    <kbd>Shift + ArrowUp</kbd>
                </div>
            </td>
            <td>
                <span>
                    Increases the value by a larger{' '}
                    <code data-accent-color="">step</code>.
                </span>
            </td>
        </tr>
        <tr>
            <td>
                <div>
                    <kbd>Shift + ArrowDown</kbd>
                </div>
            </td>
            <td>
                <span>
                    Decreases the value by a larger{' '}
                    <code data-accent-color="">step</code>.
                </span>
            </td>
        </tr>
        <tr>
            <td>
                <div>
                    <kbd>Home</kbd>
                </div>
            </td>
            <td>
                <span>Sets the value to its minimum.</span>
            </td>
        </tr>
        <tr>
            <td>
                <div>
                    <kbd>End</kbd>
                </div>
            </td>
            <td>
                <span>Sets the value to its maximum.</span>
            </td>
        </tr>
    </tbody>
</table>
