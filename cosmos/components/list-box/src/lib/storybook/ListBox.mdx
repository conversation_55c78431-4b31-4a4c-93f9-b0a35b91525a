import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';

import * as ListBoxStories from './ListBox.stories';

<Meta of={ListBoxStories} />

<Title />

<Banner
    severity="warning"
    title="Important"
    body="This component is only intended to be used to build other Cosmos components.  If you're not building a Cosmos component, you are probably looking for a different component."
/>
<br />

<Description />

<Primary />

<Controls of={ListBoxStories.Playground} />

## Import

```jsx
import { ListBox } from '@cosmos/components/list-box';
```

## Props

### `emptyState`

The `emptyState` prop accepts a ReactNode which will be shown if no items are available.

<Canvas of={ListBoxStories.EmptyState} />

### `isLoading`

If `true`, a loading spinner will render instead of items.

<Canvas of={ListBoxStories.Loading} />

## Examples

### No groups

An example of the ListBox component when no items should be rendered in a group.

<Canvas of={ListBoxStories.NoGroups} />

### Groups only

An example of the ListBox component when all items are part of a group.

<Canvas of={ListBoxStories.GroupsOnly} />

### Infinite scroll

<Canvas of={ListBoxStories.InfiniteScroll} />

### With slot

An example of the ListBox component when the items have slot with custom components.

<Canvas of={ListBoxStories.WithSlot} />

## 🟢 When to use the component

- **Select and combobox components** - As the foundational list component for dropdown selections and autocomplete
- **Option lists** - When displaying a list of selectable items with consistent styling and ARIA support
- **Grouped options** - When options need to be organized into logical groups with headers and dividers
- **Rich list items** - When list items need icons, avatars, descriptions, or metadata in start/end slots
- **Searchable lists** - As part of combobox or autocomplete implementations with proper option roles
- **Multi-select scenarios** - When users need to select multiple items with checkbox indicators
- **Lazy-loaded lists** - For large datasets that load incrementally with loading states and sentinels

## ❌ When not to use the component

- **Simple text lists** - Use the List component for basic unordered or ordered lists without selection
- **Navigation menus** - Use NavigationMenu or Dropdown components for navigation actions
- **Data tables** - Use DataTable for tabular data with sorting and filtering capabilities
- **Action menus** - Use Dropdown component for action-oriented menu items
- **Static content** - When items don't need selection, ARIA roles, or interaction functionality

## 🛠️ How it works

The ListBox component provides a flexible foundation for building accessible selectable lists with rich content support and proper ARIA implementation.

**Component structure:**
- **ListBox** - Root container (`<ul>`) with `listbox` role and lazy loading support
- **ListBoxItem** - Individual selectable items (`<li>`) with `option` role and tooltip integration
- **ListBoxGroup** - Groups related items with optional headers and dividers
- **StructuredListItem** - Content component for consistent item layout with label, description, and slots

**Key features:**
- **Selection states** - Single/multi-select with `aria-selected` and optional checkbox display
- **Rich content** - Start/end slots for icons, avatars, or metadata alongside labels and descriptions
- **Read-only items** - Non-selectable items with explanatory tooltips
- **Lazy loading** - Built-in infinite scroll with `fetchMoreItems`, `hasMore`, and loading states
- **Grouping** - Organize items with headers, dividers, and proper group semantics
- **Empty states** - Configurable content when no items are present

### Usability

**Selection patterns:**
- Use single selection for dropdowns and comboboxes
- Enable `showCheckbox` for multi-select scenarios with visual selection indicators
- Apply `isReadOnly` with explanatory tooltips for non-selectable context items

**Content organization:**
- Group related items with headers and dividers for better scanability
- Use consistent slot patterns (icons, avatars, metadata) across similar item types
- Provide meaningful empty states and loading feedback

### Content

**Item structure:**
- **Labels** - Clear, concise primary text that describes each option
- **Descriptions** - Optional secondary text for additional context
- **Slots** - Use start slots for icons/avatars or anything that provides immediate context, end slots for metadata/actions

**Data patterns:**
- Structure items as `ListBoxItemData` with consistent `id`, `label`, `value` properties
- Group related items using `ListBoxGroupData` with descriptive headers
- Implement lazy loading for large datasets

### Accessibility

**What the design system provides:**
- Semantic HTML with proper `listbox` and `option` roles for screen reader navigation
- ARIA attributes for selection states and group relationships
- Keyboard navigation (Arrow keys, Enter, Space) with focus management

**Development responsibilities:**
- Provide descriptive `aria-labelledby` for the listbox context
- Ensure read-only items have explanatory tooltips
- Test keyboard navigation and screen reader announcements
- Handle loading states accessibly with proper ARIA live regions

**Design responsibilities:**
- Maintain sufficient contrast for all selection states
- Design clear visual hierarchy between items, groups, and states
- Ensure focus indicators are visible and meet accessibility standards

