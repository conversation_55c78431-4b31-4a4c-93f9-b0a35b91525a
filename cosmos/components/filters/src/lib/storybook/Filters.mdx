import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import * as FiltersStories from './Filters.stories';

<Meta of={FiltersStories} />

<Title />

<Banner
    severity="warning"
    title="Unstable component"
    body={
        <>
            This component is considered <strong>Unstable</strong> because it
            has incomplete test coverage.
        </>
    }
/>

<Description />

<Primary />

<Controls of={FiltersStories.Playground} />

## Import

```jsx
import { Filters } from '@cosmos/components/filters';
```

## 🟢 When to use the component

- **Filter large datasets** - When users need to narrow down content from tables or lists
- **Provide multiple filter criteria** - When filtering requires combinations of different field types (checkboxes, radio buttons, select dropdowns, comboboxes)
- **Support complex filtering workflows** - When users need to apply, clear, and modify multiple filter conditions
- **Standalone filtering** - When filters need to be positioned independently from datatables

## ❌ When not to use the component

- **Small datasets** - When the data set is small enough that users can easily scan and find what they need without filtering
- **Datatable integration** - Use Datatable's built-in `filterProps` instead of standalone Filters component

## 🛠️ How it works

The Filters component is a popover that renders the FilterForm component, providing a dropdown interface for filtering content with a customizable trigger button.

**Core functionality:**
- **Popover-based interface** - Filters are contained within a dropdown popover triggered by a customizable button
- **FilterForm integration** - Wraps the FilterForm component to provide consistent filter field rendering and state management
- **Multiple filter types** - Supports checkbox, radio, select, and combobox filter types with extensible architecture for additional types
- **Flexible trigger customization** - Configurable trigger button with custom labels and required `triggerId` for accessibility
- **State persistence** - Maintains filter values and form state across popover open/close cycles
- **View mode toggle** - Optional toggle between pinned and unpinned filter modes with `showViewModeToggle`

**Component structure:**
- **Trigger button** - Neutral-colored button with filter icon that opens the popover using configurable `triggerLabel` and required `triggerId`
- **Popover container** - Houses the FilterForm with bottom-start placement and proper positioning middleware
- **FilterForm wrapper** - Renders individual filter fields based on the `filters` configuration array
- **Clear all functionality** - Provides reset capability through `clearAllButtonLabel` configuration
- **View mode controls** - Optional toggle for switching between pinned/unpinned modes

**Filter configuration:**
- **Filter array** - Each filter object defines `id`, `label`, `filterType`, and type-specific properties like `options` or `placeholder`
- **Filter values** - Current filter state managed through `filterValues` prop with keys matching filter IDs
- **Type-specific props** - Combobox filters support `searchDebounce`, `onFetchOptions`, and `placeholder` for dynamic search functionality

### Usability

*Updated usability guidelines coming soon*

**Filter organization and interaction:**
- **Logical grouping** - Organize filters in order of importance or frequency of use, if there is no difference in importance, organize filters alphabetically
- **Efficient workflows** - Design filter combinations that support common user tasks and decision-making patterns
- **Filter feedback** - Ensure users understand which filters are active and how to modify or remove them
- **Popover interaction** - Design for dropdown-based filtering where space conservation is important

### Content

**Filter setup and labeling:**
- **Descriptive labels** - Use clear, specific labels for both trigger button and individual filters that indicate exactly what each controls
- **Meaningful option labels** - Ensure filter options use language that matches user mental models and business terminology
- **Clear button configuration** - Use "Reset" for the clear button label

**Filter type selection:**
- **Checkbox filters** - Use for multiple selection scenarios where users can choose several options simultaneously
- **Radio filters** - Use for exclusive selection where only one option can be active at a time
- **Select filters** - Use for single selection from a moderate number of options with a dropdown interface
- **Combobox filters** - Use for searchable options with `searchDebounce` and `onFetchOptions`, especially when filter choices are numerous or dynamic

### Accessibility

**What the design system provides:**
- Built-in popover component with proper ARIA attributes, focus management, and keyboard navigation
- Accessible form structure with proper field labeling and validation support
- Individual filter field components (CheckboxFieldGroup, RadioFieldGroup, etc.) include built-in accessibility features
- Full keyboard support for opening/closing popover and navigating filter options

**Development responsibilities:**
- Ensure `triggerId` and filter `id` values are descriptive and unique for screen reader identification
- Ensure screen readers can understand current filter state and active selections
- Provide meaningful feedback when no filter options are available or when filters return no results
- Verify filter interactions and state changes are comprehensible with screen readers and keyboard-only navigation

**Design responsibilities:**
- Use text labels, icons, or other visual indicators in addition to color to distinguish filter states
- Design filter organization that makes sense to users navigating with assistive technology
- Ensure trigger button clearly indicates when filters are active vs inactive
- Avoid overwhelming users with too many filter options that would be difficult to navigate with assistive technology

