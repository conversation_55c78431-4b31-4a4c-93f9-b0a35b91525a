import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as MetadataStories from './Metadata.stories';

<Meta of={MetadataStories} />

<Title />

<Description />

<Primary />

<Controls of={MetadataStories.Playground} />

## Import

```jsx
import { Metadata } from '@cosmos/components/metadata';
```

## Examples

### Status only

<Canvas of={MetadataStories.StatusOnly} />

<Controls of={MetadataStories.StatusOnly} include={['label']} />

### Status with icon

<Canvas of={MetadataStories.StatusWithIcon} />

<Controls of={MetadataStories.StatusWithIcon} include={['iconName']} />

### Number only
<Canvas of={MetadataStories.NumberOnly} />

<Controls of={MetadataStories.NumberOnly} include={['label']} />

### Number with icon

<Canvas of={MetadataStories.NumberWithIcon} />

<Controls of={MetadataStories.NumberWithIcon} include={['iconName']} />

### Color schemes

<Canvas of={MetadataStories.ColorSchemes} />

### Strong variant (Number type only)

Use the **`strong` variant** when:
- The content it represents is **high-priority or time-sensitive**
- The metadata needs to **stand out** in a busy UI
- You need to **alert the user** to take action or notice something important
- The metadata is the **primary way to convey state**, with no supporting label nearby

Don't use the **`strong` variant** when:
- The surrounding context already has **strong visual elements** and the badge doesn't need to compete
- Use `strong` sparingly to maintain hierarchy

## 🟢 When to use the component
- Metadata is for showing a small amount of color-categorized metadata. They're ideal for getting a user's attention. They come in three types “Tags”, “Badge”, and “Number”.

- Use a **Badge** to inform users of the status of an object or of an action that's been taken. Badges are read-only status indicators or labels and are not interactive.
- Use a **Number** to show an important count. For example, this could be used to indicate the number of new or unread items within a container.
- Use a **Tag** to display, assign, delete, or filter attributes to an object. Tags can be dismissible when provided with an `onClick` prop.

## ❌ When not to use the component

- Avoid using Metadata components near buttons
- Don't use Metadata as a feedback mechanism. Metadata is not meant to be used as an error or success message. Instead, use a Banner or Feedback component.
- **Don't use for primary actions** - Metadata should not be the main way users interact with content
- **Avoid for complex information** - When data requires detailed explanation or multiple pieces of related information
- **Avoid overuse** - Too many metadata elements can create visual clutter and reduce their effectiveness

## 🛠️ How it works

### Usability

- Users may mistake Metadata for buttons. Conduct usability testing to ensure that the use of Metadata in a particular situation is not confusing.
- Don't overdo it. If there are too many instances of Metadata on a page, nothing commands unique attention.
- Don't mix interactive and non-interactive Metadata Tags.
- Position Metadata so they are clearly associated with the object they are informing or labeling.
- Use `strong` sparingly to maintain visual hierarchy - only available for number type
- Interactive functionality is only supported for tag type with `onClick` and `clickLabel` props

### Content

- Use short, scannable text for optimal readability
- Choose appropriate types based on content purpose:
  - **Status** - Read-only indicators with rectangular shape and title text styling
  - **Tag** - Categorical labels with border, can be interactive with dismiss functionality
  - **Number** - Count indicators with pill shape, supports strong variant for emphasis

**Interactive Tags**
- Only tag type supports `onClick` functionality
- Provide meaningful `clickLabel` for screen readers when using interactive tags
- Interactive tags display a close icon and are keyboard accessible

### Accessibility

Metadata text is read by screen readers for universal access. Ensure the text clearly conveys sentiment without relying on color alone. Additionally, if only a number is displayed, use the `a11yLabelOverride` prop to add additional context for screen readers (e.g. if there's a 2 next to audit hub, it should convey to the screen reader "2 messages" rather than just the number).

**What the design system provides:**
- Semantic HTML structure with appropriate roles and ARIA attributes for each metadata type
- Accessible color system with design system tokens ensuring WCAG 2.1 AA contrast compliance across all color schemes
- Keyboard navigation support with proper focus management for interactive tag elements
- Screen reader optimization with meaningful text announcements and context
- Touch target sizing with interactive elements meeting minimum 44px accessibility guidelines
- Built-in support for `a11yLabelOverride` to provide additional context for screen readers

**Development responsibilities:**
- Ensure metadata text clearly conveys sentiment without relying on color alone
- Use `a11yLabelOverride` prop to add context for screen readers when displaying numbers (e.g., "2 messages" instead of just "2")
- Provide meaningful `clickLabel` for interactive tags to give screen readers clear context about the action
- Test with assistive technology to verify metadata is properly announced and understood
- Avoid mixing interactive and non-interactive metadata in the same context
- Position metadata so they are clearly associated with the object they are informing or labeling

**Design responsibilities:**
- Design clear visual hierarchy that doesn't rely solely on color to convey meaning
- Ensure sufficient contrast between metadata and background elements
- Create discoverable interactive states for tag elements without making them appear like primary buttons
- Design consistent spacing and alignment patterns for metadata placement
- Consider the visual weight of metadata in relation to surrounding content
- Plan for responsive behavior ensuring touch targets remain accessible across device sizes
