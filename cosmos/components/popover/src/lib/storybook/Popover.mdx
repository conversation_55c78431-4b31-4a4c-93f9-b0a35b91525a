import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import * as PopoverStories from './Popover.stories';

<Meta of={PopoverStories} />

<Title />

<Banner
    severity="warning"
    title="Unstable component"
    body={
        <>
            This component is considered <strong>Unstable</strong> because it
            has incomplete test coverage.
        </>
    }
/>

<Description />

<Primary />

<Controls of={PopoverStories.Playground} />

## Import

```jsx
import { Popover } from '@drata/cosmos-popover';
```

## 🟢 When to use the component

- **Contextual Information:** Use Popovers to display additional details about an element, such as metadata information, or to show more items in overflow.
- **Interactive Elements:** When you need to provide options or actions related to a specific element, such as editing controls or form inputs.
- **Space Efficiency:** When you want to save space on the main interface and only show additional content when necessary.
- **Hover or Click Trigger:** Suitable for elements that require more information or interaction upon hover or click, like a Dropdown or filters.
- **Overflow content display** - When you need to show additional items that don't fit in limited space (e.g., "+N" patterns in stacks, tables, badges)
- **Data visualization details** - For showing detailed information about chart elements, data points, or visualization components

## 🔴 When not to use the component

- **Critical Information** - Avoid using Popovers for necessary information or actions needing persistent visibility or immediate user attention
- **Complex Interactions** - Do not use Popovers for complex, multi-step interactions that may require significant user input or navigation
- **Navigation** - Do not use Popovers for navigation purposes, such as menu systems or links to different application sections, as this can confuse users and disrupt the flow
- **Large content blocks** - Use Modal for content that requires significant space or scrolling
- **Mobile-first interactions** - Consider Modal alternatives on smaller screens where popovers may be difficult to interact with

> **Note:** Popovers can be used as a base component for other navigational items.

## 🛠️ How it works

A Popover is an overlay that appears upon user interaction, providing contextual information or options without navigating away from the current view.

**Positioning system:**
- **Anchor-based** - Positioned relative to a provided anchor element
- **Placement options** - Supports multiple placement positions with FloatingUI middleware

**Content management:**
- **Flexible content** - Accepts any ReactNode content
- **Padding options** - Configurable padding (`none`, `md`, `xl`)
- **Size constraints** - Optional `maxHeight` and `minHeight` with automatic scrolling

**Interaction behavior:**
- **Controlled visibility** - Uses `isOpen` prop for external state management
- **Dismissible** - Closed via Escape key, outside clicks, or `onDismiss` callback
- **Focus management** - Optional focus trapping with `cosmosUseWithCaution_shouldTrapFocus`

**Technical features:**
- **Portal rendering** - Uses Radix UI Popup to avoid z-index issues
- **Accessibility support** - Includes ARIA attributes through FocusScope and DismissableLayer
- **Performance optimized** - Only renders when `isOpen` is true

### Usability

**Common patterns:**
- **Overflow buttons** - "+N" buttons that show remaining items in stacks, tables, or badge collections
- **Contextual details** - Hover or click interactions that reveal additional information about data elements
- **Quick actions** - Small sets of actions related to specific items without full menu complexity
- **Data exploration** - Interactive elements in charts, graphs, or data visualizations

**Interaction design:**
- **Clear triggers** - Ensure trigger elements clearly indicate they will open additional content
- **Consistent behavior** - Use consistent interaction patterns (click vs hover) across similar contexts
- **Easy dismissal** - Provide multiple ways to close popovers (Escape, outside click, explicit close)
- **Mobile considerations** - Ensure popovers work effectively on touch devices

### Content

- **Concise information** - Keep content brief and focused on essential information
- **Contextual relevance** - Ensure content directly relates to the trigger element
- **Actionable items** - When showing lists, make items actionable or informative
- **Consistent formatting** - Use consistent text styles and spacing patterns

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper ARIA attributes for screen reader compatibility
- Keyboard navigation support with Escape key dismissal and focus management
- Focus trap functionality to contain focus within the popover when appropriate
- High contrast support that works with system preferences and accessibility settings
- Touch target sizing that meets accessibility guidelines for interactive elements
- Screen reader announcements for popover open/close state changes
- Proper z-index management to ensure popovers appear above other content
- Dismissible layer integration for consistent outside-click and escape behavior

**Development responsibilities:**
- Trigger element setup with proper `aria-controls`, `aria-expanded`, and `aria-haspopup` attributes
- Focus management to move focus appropriately when popovers open and return focus when closed
- Content structure to ensure popover content is logically organized for screen readers
- Keyboard navigation to verify all interactive elements within popovers are keyboard accessible
- Loading states that maintain semantic structure while content is being fetched
- Error handling that doesn't break assistive technology functionality

**Design responsibilities:**
- Visual hierarchy with clear relationships between trigger elements and popover content
- Sufficient contrast ratios for all text and interactive elements within popovers
- Focus indicators that provide clear visual feedback for keyboard navigation
- Responsive behavior to ensure popovers work effectively across different screen sizes
- Content overflow handling to manage long content lists appropriately


