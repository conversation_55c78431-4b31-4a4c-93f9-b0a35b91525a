import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as FocusScopeStories from './FocusScopeCosmos.stories';

<Meta of={FocusScopeStories} />

<Title />

<Description />

<Primary />

<Controls of={FocusScopeStories.Playground} />

## Import

```jsx
import { FocusScope } from '@cosmos/components/focus-scope';
```

## 🟢 When to use the component

- **Modal dialogs** - When you need to trap focus within a modal interface
- **Dropdown menus** - When focus should remain within an open dropdown
- **Popover content** - When focus needs to be contained within popover interfaces
- **Complex interactive widgets** - When building custom components that need focus containment
- **Accessibility compliance** - When you need to meet WCAG focus management requirements

## ❌ When not to use the component

- **Regular page content** - Don't trap focus in normal page layouts
- **Non-modal interfaces** - Avoid focus trapping in interfaces that aren't modal in nature
- **Simple interactions** - Don't use for basic buttons or links that don't require focus containment
- **Already handled components** - Many Cosmos components already include proper focus management

## 🛠️ How it works

The FocusScope component conditionally wraps children with Radix UI's focus management system, providing focus trapping when `shouldTrapFocus` is true and normal behavior when false.

### Usability

**Core functionality:**
- **Conditional focus trapping** - Only applies focus management when `shouldTrapFocus` is true
- **Radix UI integration** - Uses `@radix-ui/react-focus-scope` and `@radix-ui/react-focus-guards`
- **Loop behavior** - Focus cycles through focusable elements within the scope
- **Automatic restoration** - Focus returns to previous element when scope is removed

**Focus behavior:**
- **Trapped mode** - When `shouldTrapFocus` is true, focus cannot escape the wrapped content
- **Normal mode** - When `shouldTrapFocus` is false, children render without focus constraints
- **Keyboard navigation** - Tab and Shift+Tab cycle through focusable elements
- **Focus restoration** - Focus returns to previous element when scope is removed

### Content

- Ensure children contain focusable elements for proper keyboard navigation
- Use `shouldTrapFocus: true` only for modal-like interfaces
- Test focus behavior with keyboard navigation to ensure proper cycling

### Accessibility

**What the design system provides:**
- WCAG-compliant focus management via Radix UI primitives
- Proper focus guards to prevent focus from escaping. This is useful for modals, popovers, and other components that need to trap focus.
- Keyboard navigation support with Tab and Shift+Tab
- Focus restoration when the scope is removed
- Screen reader compatibility with proper focus announcements

**Development responsibilities:**
- Ensure wrapped content has logical tab order
- Test keyboard navigation thoroughly with Tab and Shift+Tab
- Verify focus restoration works correctly when component unmounts
- Provide escape mechanisms (like Escape key handlers) for trapped focus
- Test with screen readers to ensure focus changes are announced properly

**Design responsibilities:**
- Ensure focused elements have clear visual indicators
- Design logical focus order within trapped areas
- Provide visual cues that focus is contained (e.g., modal overlays)
- Test focus indicators across different background colors and contexts
- Ensure sufficient contrast for focus states in contained areas