import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import * as TextFieldStories from './TextField.stories';

<Meta of={TextFieldStories} />

<Title />

<Banner
    severity="education"
    title="Use Web Wrappers for Form Fields"
    body={
        <>
            Avoid using form field components directly from Cosmos. Instead, use
            our web wrappers for optimized functionality and consistent
            integration.{' '}
            <Link
                href="/?path=/docs/form-components-about--docs#web-wrappers"
                label="Click here for more details"
                isExternal
            />
            .
        </>
    }
/>

<Description />

<Primary />

<Controls of={TextFieldStories.Playground} />

## Import

```jsx
import { TextField } from '@cosmos/components/text-field';
```

## 🟢 When to use the component

- **Single-line text input** - When users need to enter short text content like names or email addresses
- **Unpredictable or freeform responses** - When you can't reasonably predict a user's answer and there might be wide variability in responses
- **Input simplicity** - When using another type of input would make answering more difficult for users

## ❌ When not to use the component

- **Multi-line input** - Use TextareaField for longer text content that spans multiple lines
- **Complex data entry** - Use more specialized input methods that are more efficient, such as DatePickerField for dates or SelectField/ComboboxField for predefined options

## 🛠️ How it works

The TextField component combines FormField structure with Input functionality, providing a complete single-line text input with labeling, validation, and accessibility support.

**Core functionality:**
- **Single-line input** - Native text input element with controlled value state management
- **Form integration** - Uses `formId` and `name` props for proper form association and submission
- **Controlled component** - Requires `value` prop with `onChange` callback for state management
- **Input validation** - Integrates with FormField's feedback system for error/success states through internal `feedbackType` handling

**Component structure:**
- **FormField wrapper** - Provides consistent labeling, help text, validation feedback, and form integration using `renderInput` pattern
- **Input element** - Core Input component with proper styling, event handling, and ARIA attributes
- **Validation styling** - Dynamic styling via internal `feedbackType` prop passed from FormField to Input
- **Label association** - Proper HTML label relationships through FormField's `aria-labelledby` and `aria-describedby` attributes

**State and behavior:**
- **Event handling** - Supports `onChange`, `onFocus`, `onBlur`, and `onKeyDown` with proper event propagation to Input component
- **Read-only support** - `readOnly` prop prevents editing while maintaining visual presentation
- **Disabled state** - `disabled` prop provides visual and functional disabled state
- **Required field handling** - `required` prop integrates with FormField validation and visual indicators

### Usability

**Input patterns:**
- **Appropriate field sizing** - Use fields appropriate to the length of the expected input. The length provides a hint to users as to how much text to write. Do not require users to write paragraphs into a single-line input; use TextareaField instead
- **Keyboard navigation** - Full keyboard support including tab navigation and standard text editing shortcuts
- **Focus management** - Clear focus indicators and logical navigation flow

**Validation and feedback:**
- **Wait to validate** - Only show error validation messages or feedback after a user has interacted with a particular field
- **Progressive feedback** - Provide immediate feedback through FormField's system after initial interaction, with clear success confirmation when requirements are met

### Content

**Field setup:**
- **Descriptive labels** - Use clear, specific labels that describe the expected content type and any formatting requirements
- **Help text for guidance** - Help text helps users understand how to complete the field or indicates needed input formatting. Use for important information that prevents errors
- **Required field expectations** - Consider all text fields as required unless explicitly noted as optional using the `optionalText` prop
- **Clear error messaging** - Provide specific, actionable feedback through FormField's `feedback` prop when input doesn't meet requirements

**Content expectations:**
- **Format clarity** - Make the intended input type and any specific formatting clear through labeling and help text examples
- **Content type guidance** - Communicate the expected content type and length through descriptive labeling

### Accessibility

**What the design system provides:**
- FormField integration with proper label associations (`aria-labelledby`), help text relationships (`aria-describedby`), and validation feedback
- Semantic input element with appropriate ARIA attributes and relationships
- Keyboard navigation support with standard text editing interactions and logical tab order
- Screen reader compatibility with proper announcements for labels, help text, and validation states
- Focus management with visible focus indicators and logical navigation flow
- High contrast support maintaining usability across different visual accessibility settings

**Development responsibilities:**
- Provide meaningful `formId`, `name`, and `label` props for proper form association and identification
- Use descriptive labels and help text that clearly communicate the input's purpose and requirements
- Implement proper error handling and validation feedback through FormField's `feedback` system
- Handle event callbacks (`onChange`, `onFocus`, `onBlur`, `onKeyDown`) appropriately for form state management
- Use `required` prop appropriately to match form validation requirements

**Design responsibilities:**
- Ensure sufficient contrast ratios for input text, borders, labels, and validation feedback
- Design error states that clearly communicate validation issues through multiple visual cues
- Maintain consistent spacing and alignment between input, labels, help text, and feedback
- Ensure responsive behavior that maintains usability across different screen sizes
- Design validation states that provide clear visual feedback without relying solely on color

