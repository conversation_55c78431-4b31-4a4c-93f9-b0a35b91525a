import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import * as DatatableStories from './Datatable.stories';
import sortingFiltering from './assets/sortingFiltering.png';
import twoDataPoint from './assets/twoDataPoint.png';
import pagination from './assets/pagination.png';
import search from './assets/search.png';
import bulkAction from './assets/bulkActions.png';
import rowActions from './assets/rowActions.png';
import dropdownActions from './assets/dropdownActions.png';
import actionStackAction from './assets/actionStackAction.png';
import toggleActions from './assets/toggleActions.png';
import tableActions from './assets/tableActions.png';

<Meta of={DatatableStories} />

<Title />

<Banner
    severity="warning"
    title="Unstable component"
    body={
        <>
            This component is considered <strong>Unstable</strong> because it
            has incomplete test coverage.
        </>
    }
/>

<Description />

<Primary />

<Controls of={DatatableStories.Playground} />

## Props

### Table density options

The Datatable component supports three density options that control the height and padding of table cells:

- **compact**: 2px padding and 24px minimum height.
- **normal**: 8px padding and 48px minimum height.
- **spacious**: 16px padding and 64px minimum height.

The density can be controlled through the `density` prop or via the table settings menu.

<Canvas of={DatatableStories.Density} />

<Controls of={DatatableStories.Density} include={['density']} />

### Visibility options

The Datatable component provides props to control which UI elements are shown:

- **hidePagination**: Hides the pagination controls at the bottom of the table
- **Top bar**: To hide the entire top bar, **do not** supply any of the following:
    - **tableSearchProps.hideSearch**: Set to `true` to hide the search input in the top bar
    - **filterProps**: Omit this props to hide the filters
    - **tableSettingsTriggerProps**: Omit this prop to hide the settings (gear) trigger
    - **tableActions**: Omit this prop to hide all action buttons

These options allow you to create a clean, minimal table when certain features aren't needed.

<Canvas of={DatatableStories.VisibilityOptions} />

<Controls of={DatatableStories.VisibilityOptions} include={['hidePagination', 'tableSearchProps.hideSearch']} />

## Datatable dev guides

- [Columns](/docs/unstable-components-datatable-columns--docs)
- [Data](/docs/unstable-components-datatable-data--docs)
- [Table Actions](/docs/unstable-components-datatable-table-actions--docs)
- [Filters](/docs/unstable-components-datatable-filters--docs)
- [Search](/docs/unstable-components-datatable-search--docs)

## 🟢 When to use the component

- When presenting large sets of structured data that require organization and comparison.
- When you want column headers for your structured content.
- When users need to perform actions such as sorting, filtering, and editing data within the table.
- When displaying data that benefits from being viewed in a tabular format for easy readability and analysis.
- When you need to provide an overview of multiple items and their properties.

## ❌ When not to use the component

- When the data set is small, it may be better displayed using simpler components like lists or cards.
- When the information requires complex visualizations such as charts or graphs.

## 🛠️ How it works

The Datatable component provides comprehensive data table functionality with sorting, filtering, pagination, search, and bulk operations built on TanStack Table.

#### 1. Basic setup

    - Define the column order from most important to least important from left to right.
    - Add up to two data points per cell.
    - Each row can be actionable. Each row should have a “ChevronRight” icon only button to demonstrate the row is actionable and opens a new Side, Panel or Page
    - Configure any additional features like sorting, filtering, pagination, search capability, and bulk actions.

    <img
        src={twoDataPoint}
        alt="two data points"
        width="600px"
    />

#### 2. Setting the current page externally

    - Use only when you need to update the current page externally due to dynamic table data or filtering
    - Keep in mind that pageIndex is 0 based
    - A basic example of how you can set the current page:

```tsx
import React, { useRef } from 'react';
import Datatable from './Datatable';

const ParentComponent = () => {
    const datatableRef = useRef();

    const handleSetPage = () => {
        if (datatableRef?.current) {
            datatableRef.current.setPageIndex(2); // Example to set page to 2
        }
    };

    return (
        <div>
            <button onClick={handleSetPage}>Set Page</button>
            <Datatable customRef={datatableRef} />
        </div>
    );
};

export default ParentComponent;
```

#### 3. Sorting and filtering

    - Enable sorting by setting the sortable property on columns. Only consider sorting where it’s most relevant to the user.

    <img
        src={sortingFiltering}
        alt="sorting and filtering"
        width="600px"
    />

#### 4. Pagination

    - Add pagination for large data sets. It’s important to establish the number of rows per page that are allowed.

    <img
        src={pagination}
        alt="pagination"
        width="600px"
    />

#### 5. Search

    - Add `TopBar` for search capabilities, especially for large data sets

    <img
        src={search}
        alt="search"
        width="600px"
    />

#### 6. Bulk actions

    - Always start with the most important bulk actions, arranged from left to right.
    - Limit the interface to a maximum of three buttons

    1. An optional primary button for main actions for the table.
    2. A secondary button for a less prominent action
    3. A tertiary ‘more’ button for less important actions stacked in a dropdown if needed.”

    <img
        src={tableActions}
        alt="table actions picture"
        width="600px"
    />

#### 7. Row actions

    - For **full screen tables** on index pages that are the core focus of the page experience, almost all row actions are added at the beginning of the row under the Action button. There are exceptions to this:
        - for tables with complex objects that require user actions that are quick and can be completed with a single field, we sometimes include them in the row itself—see Risks table.
        - for simple objects with minimal jobs to be done, we sometimes include the actions as a button in a table cell—see Connections Account Management.
        - more than one action with similar prominence per row, use a Dropdown component.
    - For **inline tables**, which are generally smaller tables that are not the core focus of a full page, actions should appear on the end of the row. Use icon-only buttons only if the actions are common and will be understood without a label.
        - Use secondary or tertiary button depending what other actions on the page.
        - Use an ActionStack component in case there is a need for a more prominent action among another group of actions on a row. The least prominent actions should be in a Dropdown
        - more than one action with similar prominence per row, use a Dropdown component.

#### 8. Table row indicators

    Row indicators are visual left borders on each row that will be tied to content within the row as a supportive highlight. For a given table there may be more than one filter or column that is tied to a row indicator, but only one can be active at a time, so the highest priority indicator will be shown.

    Usage

    - Use row indicators to **support scanning and triage** across dense tables.
    - Ensure indicators are tied to **specific, meaningful row-level attributes**.
    - Only **one indicator per row** can be visually active at a time — prioritize accordingly.
    - Avoid indicator overuse, especially in tables where many rows meet the same condition.
    - Do not use as a **replacement for column data** — indicators are a visual enhancement, not a substitute for content.

    Like other components with an `urgency` prop:

    - `critical` should be used only on rows with unresolved, high-priority issues
    - `warning` should be used for less urgent callouts
    - `primary` can be used for optional review callouts

#### 9. Height and width:
    - Columns will always adjust to the largest data with a cell. Each cell can have predetermined size if necessary. (This size should be determined by product team)

#### 10. Column alignment
    - Header: Table header will always be aligned left to right and top to bottom by default. Only right-aligned cells will make the header left-aligned
    - Cells: Left-aligned and top-aligned by default. Numbers can be right-aligned

#### 11. Contained in a section or a card
    - Use only when you have more than one section on a page
    - Don’t use pinned filters on cards

#### 12. Cell alignment
    - Cell content should be top aligned and match the horizontal alignment of the column header

If an error occurs when loading table content, a snackbar will appear to let the user know what happened and the table should display an EmptyState with further context ([Examples here](https://www.figma.com/design/YBybPS6EnNlfNl88DfUGCS/Cosmos--Stickers?node-id=8138-208825&t=0mFj4EngOdjEFpHW-4)).

### Usability

**Core functionality:**
- **Data display** - Renders structured data with customizable column definitions and cell formatting
- **Sorting and filtering** - Multi-column sorting with advanced filtering capabilities
- **Pagination** - Built-in pagination with configurable page sizes
- **Search** - Global search functionality in the top bar
- **Row selection** - Multi-row selection with bulk operations (requires `getRowId`)
- **Density control** - Three density options: compact (24px), normal (48px), spacious (64px)
- **Column visibility** - Show/hide columns via table settings

**Interactive features:**
- **Table actions** - Global actions displayed in the top bar (max 3 buttons recommended)
- **Bulk actions** - Actions for selected rows, triggered by row selection
- **Row actions** - Per-row actions via dropdown or ActionStack
- **External page control** - Programmatic page setting via `imperativeHandleRef.setPageIndex()`
- **Column resizing** - Optional column width adjustment with `enableColumnResizing`

### Content

- Provide stable `getRowId` function when using row selection
- Limit table actions to 3 buttons maximum for optimal UX
- Use appropriate density based on data complexity and available space
- Provide ` tableId` for proper table identification and state management

### Accessibility

**What the design system provides:**
- Semantic HTML table structure with proper ARIA attributes
- Keyboard navigation for all interactive elements
- Screen reader support with sortable column indicators
- Focus management and high contrast support

**Development responsibilities:**
- Always provide `getRowId` when enabling row selection
- Ensure action buttons have descriptive labels
- Test keyboard navigation and screen reader compatibility
- Provide meaningful column headers and empty state messages

**Design responsibilities:**
- Maintain sufficient contrast between text and backgrounds across all density options
- Ensure interactive elements have clear focus indicators on all background colors
- Design consistent visual hierarchy for headers, data, and action elements
- Test with various data lengths and screen sizes for responsive behavior
- Verify readability and usability across all three density settings


## Row Selection and getRowId

When enabling row selection with `isRowSelectionEnabled`, you **MUST** provide a `getRowId` function to ensure proper row identification and selection state management.

### ✅ Correct usage

```tsx
<Datatable
    isRowSelectionEnabled
    getRowId={(row) => row.id} // Required when row selection is enabled
    data={myData}
    columns={columns}
    onRowSelection={({ selectedRows, isAllRowsSelected }) => {
        console.log('Selected rows:', selectedRows);
    }}
/>
```

### ❌ Incorrect usage (will cause TypeScript error)

```tsx
<Datatable
    isRowSelectionEnabled
    data={myData}
    columns={columns}
    // Missing getRowId - this will fail TypeScript compilation
/>
```

### Common getRowId patterns

- **Objects with string id**: `getRowId={(row) => row.id}`
- **Objects with number id**: `getRowId={(row) => String(row.id)}`

> **⚠️ Important**: `getRowId` must return a string. If your data has numeric IDs, wrap them with `String()` like `getRowId={(row) => String(row.id)}`.

> **💡 Automatic Loading State Handling**: The Datatable has built-in protection for loading states! You can safely use simple property access like `getRowId={(row) => row.id}` without worrying about undefined properties during loading. The component automatically detects loading states and generates safe IDs like `loading-row-0`, `loading-row-1`, etc. This feature is built into the table and requires no additional setup.

### Why getRowId is required

- ✅ Ensures correct row selection behavior
- ✅ Enables proper bulk actions functionality
- ✅ Provides consistent row identification across pagination
