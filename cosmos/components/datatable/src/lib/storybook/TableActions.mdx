import {
    ArgTypes,
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Source,
    Title,
} from '@storybook/addon-docs/blocks';
import * as TableActionsStories from '../docs/table-actions/TableActions.stories';

<Meta of={TableActionsStories} />

<Title />

Datatable table actions include the buttons, dropdowns, and links that are rendered above the table body. They are used to provide actions that are directly related to the table data, such as adding information or downloading table content, and are built using the [ActionStack](https://cosmos.drata.com/?path=/docs/actions-actionstack--docs) component.

<Primary />

## Props

### `tableActions`

To render table actions in the Datatable, define and pass the `tableActions` prop.

<ArgTypes of={TableActionsStories.TableActionsProps} exclude={['table']} />

## 🟢 When to use the component

- **Table-level operations** - When actions affect the whole table rather than individual rows
- **Primary workflows** - When actions represent the main tasks users perform with the table data
- **Global table functions** - When actions apply to all visible or filtered data

## ❌ When not to use table actions

- **Row-specific actions** - Use `rowActionsProps` for actions that apply to individual rows
- **Unrelated functionality** - Avoid actions that don't directly relate to the table data
- **Navigation actions** - Use page-level navigation instead of table actions for general navigation
- **Too many actions** - Use dropdown grouping for additional actions when space is limited

## 🛠️ How it works

Table actions are rendered above the table using the ActionStack component, providing consistent spacing and alignment for buttons and dropdowns that operate on table data.

### Usability

**Core functionality:**
- **Button actions** - Direct action execution with `actionType: 'button'`
- **Dropdown actions** - Grouped actions with `actionType: 'dropdown'`
- **Consistent positioning** - Always rendered above the table body
- **ActionStack integration** - Automatic spacing and responsive behavior

**Action types:**
- **Primary buttons** - Main table actions (typically one per table)
- **Secondary buttons** - Supporting actions with less visual prominence
- **Tertiary dropdowns** - Grouped actions for space efficiency
- **Icon-only actions** - Compact actions using `isIconOnly: true`

**Layout behavior:**
- **Left-to-right ordering** - Actions render in array order
- **Responsive stacking** - ActionStack handles responsive behavior
- **Consistent spacing** - Uses `dimensionLg` gap between actions

### Content

- Use action-oriented labels (e.g., "Add user", "Export data")
- Limit to 3 buttons maximum for optimal usability
- Group related actions in dropdowns when space is limited
- Place most important actions first (left-to-right)

### Accessibility

**What the design system provides:**
- Semantic button and dropdown elements via ActionStack
- Keyboard navigation support for all action types
- Screen reader support with proper labeling
- Focus management for dropdown interactions
- ARIA attributes for dropdown states

**Development responsibilities:**
- Provide descriptive action labels that clearly indicate function
- Ensure proper keyboard navigation between table actions and table content
- Test dropdown interactions with assistive technologies
- Use appropriate color schemes for action importance hierarchy

**Design responsibilities:**
- Maintain visual hierarchy between primary, secondary, and tertiary actions
- Ensure sufficient contrast for all action states
- Design clear visual separation between action groups
- Test action layouts across different screen sizes
- Provide consistent iconography for similar actions across tables

