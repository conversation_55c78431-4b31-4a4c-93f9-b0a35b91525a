import {
    ArgTypes,
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Source,
    Title,
} from '@storybook/addon-docs/blocks';
import * as SearchStories from '../docs/search/Search.stories';
import { SEARCH_PROPS_ARGS } from '../docs/search/constants';

<Meta of={SearchStories} />

<Title />

<Description />
Search allows users to filter table data in real-time with a debounced input. It integrates with the global filter state for consistent filtering, allows setting default value, is customizable, and has a clear button.

## Props

### `tableSearchProps`

To use the Search component in the Datatable, define and pass the `tableSearchProps` prop.

<ArgTypes
    of={SearchStories.TableSearchProps}
    exclude={['table', 'dataId']}
    include={SEARCH_PROPS_ARGS}
/>

## 🟢 When to use the component

- **Large datasets** - When tables contain many rows that benefit from global filtering
- **Cross-column filtering** - When search should apply across all visible table columns
- **Real-time table filtering** - When immediate feedback improves table navigation

## ❌ When not to use table search

- **Small datasets** - Avoid for tables with very few rows where all data is easily scannable
- **Performance concerns** - Avoid on very large datasets without server-side filtering
- **Complex filtering needs** - Use dedicated `filterProps` when users need field-specific or advanced table filtering

## 🛠️ How it works

The table search feature provides real-time global filtering for Datatable rows through a debounced input that integrates with the table's global filter state to show/hide matching rows.

### Usability

**Core functionality:**
- **Global table filtering** - Searches across all visible columns to filter table rows
- **Debounced input** - Delays search execution to optimize table performance (default: 500ms)
- **Real-time row filtering** - Updates visible table rows as user types
- **Clear functionality** - Built-in clear button to reset table filter and show all rows
- **Default values** - Supports initial search values for URL-driven table state

**Table filtering behavior:**
- **Case-insensitive** - Ignores letter case
- **Partial matching** - Shows rows containing the search term
- **Trimmed input** - Automatically removes leading/trailing whitespace
- **Cross-column search** - Searches all accessible column data simultaneously
- **Row visibility control** - Hides/shows rows based on search matches

### Content

**Content guidelines:**
- Use descriptive placeholder text that indicates what can be searched
- Set appropriate debounce delay based on data size and performance needs
- Provide meaningful default values when integrating with URL parameters
- Consider hiding search for very small datasets to reduce UI clutter

### Accessibility

**What the design system provides:**
- Semantic search input with proper labeling via screen reader label
- Clear button with descriptive label for screen readers
- Keyboard navigation support for input and clear functionality
- ARIA attributes connecting search to table (`aria-controls`)
- Focus management for search interactions

**Development responsibilities:**
- Ensure search placeholder text is descriptive and helpful
- Test keyboard navigation between search and table elements
- Verify screen reader announces search results and state changes
- Provide appropriate `aria-controls` relationship to target table
- Test search functionality with various input methods and assistive technologies

**Design responsibilities:**
- Provide descriptive search placeholder text that indicates what can be searched
- Maintain sufficient contrast for search input and clear button
- Ensure clear button is easily discoverable and accessible
- Design consistent visual feedback for search states (empty, active, cleared)
- Test search component across different screen sizes and zoom levels
- Verify search input remains usable at all table density settings
