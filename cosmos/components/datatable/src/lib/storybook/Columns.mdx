import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import {
    ArgTypes,
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Source,
    Title,
} from '@storybook/addon-docs/blocks';
import {
    COLUMN_DEF_ARGS,
    EXAMPLE_COMPLEX_COLUMNS,
    EXAMPLE_SIMPLE_COLUMNS,
    EXAMPLE_HIDDEN_HEADERS,
} from '../docs/columns/constants';
import * as ColumnsStories from '../docs/columns/Columns.stories';

<Meta of={ColumnsStories} />

<Title />

Column definitions define the shape of your table. Which columns render what data, how that data looks, and where it belongs in the table are all defined with the `columns` prop.

<Banner
    severity="education"
    title="The Datatable component is built using Tanstack Table."
    body={
        <>
            For extended details and advanced use-cases, see the{' '}
            <Link
                href="https://tanstack.com/table/latest/docs/guide/column-defs"
                label="Tanstack Column Defs guide"
                isExternal
            />
            .
        </>
    }
/>

<ArgTypes
    of={ColumnsStories.ColumnDefArgTypes}
    exclude={['table']}
    include={COLUMN_DEF_ARGS}
/>

## Simple Columns

Columns that match 1:1 with a piece of data can be defined by providing a column id, header, and an `accessorKey`. The `accessorKey` tells the column which key in the data object it is representing.

If you want to combine data, you can use an accessor function, or `accessorFn`. It must always return a `string`.

<Source code={EXAMPLE_SIMPLE_COLUMNS} />

## Complex Columns

By default, column cells will display their data model value as a `string`. You can override this behavior by providing custom rendering implementations. Cell formatters are provided the `row` and `table` objects, allowing you to customize the cell formatting beyond just the cell value.

<Source code={EXAMPLE_COMPLEX_COLUMNS} />

## Hiding Column headers

You can hide specific column headers while maintaining the column structure using the `hideHeaderLabel` property.

<Source code={EXAMPLE_HIDDEN_HEADERS} />

## 🟢 When to use the component

- **Within Datatable components** - Column definitions are exclusively used as the `columns` prop for Datatable components

## ❌ When not to use the component

- **Outside of Datatable** - Column definitions only work within Datatable components

## 🛠️ How it works

Column definitions are configuration objects passed to the Datatable component's `columns` prop. They specify how data is extracted, displayed, and behaves within the table, providing complete control over table structure and cell rendering.

### Usability

**Core functionality:**
- **Data extraction** - Use `accessorKey` for simple property access or `accessorFn` for computed values
- **Custom rendering** - Override default display with `cell` property for complex formatting
- **Header configuration** - Set column headers with optional sorting capabilities
- **Size control** - Configure column widths with `minSize`, `maxSize`, and `size` properties
- **Interactive behavior** - Control row click behavior with `meta.shouldIgnoreRowClick` for action columns

**Column patterns:**
- **Data columns** - Display row data using `accessorKey` or `accessorFn`
- **Action columns** - Interactive elements (use `meta: { shouldIgnoreRowClick: true }`)
- **Computed columns** - Derived values using `accessorFn` to combine multiple data points
- **Custom columns** - Complex rendering with dedicated cell components

### Content

- Use dedicated cell components instead of inline functions for performance
- Apply `meta: { shouldIgnoreRowClick: true }` to prevent row clicks on interactive elements
- Use `accessorKey` over `accessorFn` when possible for automatic sorting
- Keep cell content concise - limit to 1-2 data points per cell
- Set `isActionColumn: true` for action columns to avoid empty state dashes

### Accessibility

**What the design system provides:**
- Semantic table headers with proper scope attributes
- Sortable column indicators with ARIA labels
- Keyboard navigation support for interactive elements
- Screen reader support for column headers and sorting states

**Development responsibilities:**
- Provide meaningful header text that describes column content
- Ensure custom cell components maintain proper accessibility attributes
- Test keyboard navigation for interactive column elements
- Use semantic HTML elements within custom cells

**Design responsibilities:**
- Maintain consistent visual hierarchy between headers and data
- Ensure sufficient contrast for column headers and sorting indicators
- Design clear visual separation between columns
- Test column layouts across different screen sizes and zoom levels

