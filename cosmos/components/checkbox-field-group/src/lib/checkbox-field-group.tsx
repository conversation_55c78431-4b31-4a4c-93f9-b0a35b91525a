import { useRef } from 'react';
import {
    CheckboxField,
    type CheckboxFieldProps,
} from '@cosmos/components/checkbox-field';
import { FormField } from '@cosmos/components/form-field';
import { t } from '@globals/i18n/macro';
import { DEFAULT_DATA_ID, OPTION_ORIENTATIONS } from './constants';
import { normalizeValue } from './helpers';
import { useCalculatedOrientation } from './hooks';
import { StyledOptionWrapperDiv } from './styles';
import type { CheckboxOption, OptionOrientation } from './types';

type LimitedCheckboxFieldProps = Omit<
    CheckboxFieldProps,
    'aria-labelledby' | 'checked' | 'defaultChecked' | 'onChange' | 'value'
>;

export interface CheckboxFieldGroupProps extends LimitedCheckboxFieldProps {
    /**
     * For exceptional use-cases only - most of the time, if you're using this, something is wrong.
     */
    cosmosUseWithCaution_forceOptionOrientation?: OptionOrientation;
    /** The options from which the user can choose.
     *
     * See [CheckboxField documentation](/?path=/docs/form-components-checkboxfield--docs) for individual option props.
     */
    options: CheckboxOption[];

    /**
     * Determines whether a "Select All" checkbox is displayed at the top of the list.
     * - When `true`, the "Select All" checkbox allows the user to select/deselect all options at once.
     * - When `false`, only the individual checkboxes are displayed.
     * - The state of this checkbox should be controlled via the `value` prop.
     */
    selectAll?: boolean;

    /**
     * Function called when value changes.
     */
    onChange: (selections: string[]) => void;

    /**
     * The value of the controlled checkbox field group.
     */
    value?: string[];
}

/**
 * The CheckboxFieldGroup component manages a group of related checkbox options, allowing users to select multiple values.
 *
 * [CheckboxFieldGroup in Figma](https://www.figma.com/design/E98sepyU91vSTn4VeWnzmt/Cosmos--Foundations?node-id=46189-487860&t=QHg4TovE4tsvKYCl-4).
 */
export const CheckboxFieldGroup = ({
    cosmosUseWithCaution_forceOptionOrientation = undefined,
    'data-id': dataId = DEFAULT_DATA_ID,
    disabled = undefined,
    feedback = undefined,
    formId,
    helpText = undefined,
    shouldHideLabel = false,
    label,
    labelStyleOverrides = undefined,
    name,
    onChange,
    optionalText = undefined,
    options,
    required = false,
    value = [],
    selectAll = false,
}: CheckboxFieldGroupProps): React.JSX.Element => {
    const optionContainerRef = useRef(null);

    const optionOrientation = useCalculatedOrientation({
        forceOptionOrientation: cosmosUseWithCaution_forceOptionOrientation,
        horizontalConfig: OPTION_ORIENTATIONS.horizontal,
        optionContainerRef,
        optionsCount: options.length,
    });

    const { gap } = OPTION_ORIENTATIONS[optionOrientation];

    const isAllSelected = value.length === options.length;

    const handleCheckboxChange = ({
        isChecked,
        optionValue,
    }: {
        isChecked: boolean | 'indeterminate';
        optionValue: string;
    }) => {
        if (isChecked && !value.includes(optionValue)) {
            onChange([...value, optionValue]);
        } else {
            onChange(value.filter((item) => item !== optionValue));
        }
    };

    const handleSelectAllChange = (isChecked: boolean) => {
        if (isChecked) {
            onChange(options.map((option) => option.value));
        } else {
            onChange([]);
        }
    };

    return (
        <FormField
            data-id={dataId}
            feedback={feedback}
            formId={formId}
            helpText={helpText}
            shouldHideLabel={shouldHideLabel}
            label={label}
            labelStyleOverrides={labelStyleOverrides}
            name={name}
            optionalText={optionalText}
            role="group"
            data-testid="CheckboxFieldGroup"
            required={required}
            renderInput={({ describeIds, inputId, inputTestId, labelId }) => {
                return (
                    <>
                        {selectAll && (
                            <CheckboxField
                                aria-describedby={`${inputId}-select-all-description`}
                                aria-labelledby={`${inputId}-select-all-label`}
                                checked={isAllSelected}
                                value="SELECT_ALL"
                                data-id={`${inputTestId}-select-all`}
                                disabled={disabled}
                                formId={`${inputId}-select-all`}
                                label={t`Select all`}
                                name={`${name}-select-all`}
                                onChange={handleSelectAllChange}
                            />
                        )}

                        <StyledOptionWrapperDiv
                            ref={optionContainerRef}
                            $gap={gap}
                            $selectAll={selectAll}
                            $optionOrientation={optionOrientation}
                        >
                            {options.map((option) => {
                                const {
                                    disabled: optionDisabled,
                                    helpText: optionHelpText,
                                    label: optionLabel,
                                    value: optionValue,
                                } = option;

                                const checked = value.includes(optionValue);
                                const optionTestId = `${inputTestId}-${normalizeValue(
                                    optionValue,
                                )}`;

                                const optionId = `${inputId}-${normalizeValue(
                                    optionValue,
                                )}`;

                                return (
                                    <CheckboxField
                                        aria-describedby={describeIds}
                                        aria-labelledby={labelId}
                                        checked={checked}
                                        data-id={optionTestId}
                                        disabled={disabled || optionDisabled}
                                        formId={optionId}
                                        helpText={optionHelpText}
                                        key={optionLabel}
                                        label={optionLabel}
                                        name={name}
                                        required={required}
                                        value={optionValue}
                                        onChange={(isChecked) => {
                                            handleCheckboxChange({
                                                isChecked,
                                                optionValue,
                                            });
                                        }}
                                    />
                                );
                            })}
                        </StyledOptionWrapperDiv>
                    </>
                );
            }}
        />
    );
};
