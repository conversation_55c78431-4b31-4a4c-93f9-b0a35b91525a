import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as CheckboxFieldGroupStories from './CheckboxFieldGroup.stories';

<Meta of={CheckboxFieldGroupStories} />

<Title />

<Description />

<Primary />

<Controls of={CheckboxFieldGroupStories.Playground} />

## Import

```jsx
import { CheckboxFieldGroup } from '@drata/cosmos-checkbox-field-group';
```

## 🟢 When to use the component

- **Multiple selections** - When users need to select zero, one, or multiple options from a predefined list
- **Filtering interfaces** - When users need to apply multiple filter criteria at once
- **Bulk operations** - When users need to select multiple items for batch actions

## ❌ When not to use the component

- **Single selection only** - Use RadioFieldGroup when only one option can be selected
- **Binary choices** - Use a single CheckboxField for simple yes/no decisions
- **Many options (10+)** - Consider ComboboxField with multi-select for larger lists of options
- **Complex option descriptions** - Use ChoiceCardGroup when options need detailed explanations

## 🛠️ How it works

The CheckboxFieldGroup component manages a collection of related checkbox options with shared validation, automatic layout optimization, and optional "Select All" functionality.

**Component structure:**
- **FormField wrapper** - Provides consistent labeling, help text, and validation feedback
- **Select All checkbox** - Optional master checkbox that controls all individual options
- **Individual checkboxes** - Each option renders as a CheckboxField with shared styling and behavior
- **Responsive layout** - Automatic orientation switching between horizontal and vertical based on content and space

**Key features:**
- **Controlled state** - Use `value` prop (string array) with `onChange` callback for external state management
- **Select All behavior** - Automatically manages individual checkbox states with indeterminate state for partial selection
- **Layout optimization** - Component calculates optimal layout based on label length and available space
- **Individual control** - Each checkbox can be disabled independently while maintaining group behavior

### Usability

**Option organization:**
- Group related options that users would naturally consider together
- Use clear, specific labels and arrange in logical order (alphabetical, frequency, or workflow sequence)
- Enable Select All only when users would realistically want to select all options
- Allow automatic orientation to optimize for readability and space usage

**Select All functionality:**
- **Clear purpose** - Only enable Select All when users would realistically want to select all options
- **Obvious behavior** - Ensure it's clear that Select All affects all visible options
- **Partial states** - Users should understand when some options are selected via visual feedback
- **Logical placement** - Select All appears at the top of the option list for clear hierarchy

**Layout considerations:**
- **Natural flow** - Allow automatic orientation to optimize for readability and space usage
- **Scanning patterns** - Horizontal layout works well for short labels; vertical for longer descriptions
- **Touch targets** - Ensure adequate spacing between options for touch interaction
- **Visual hierarchy** - Maintain clear relationship between group label and individual options

### Content

**Group labeling:**
- Use clear group labels that explain what users are selecting
- Frame as questions when appropriate and provide contextual help text
- Use specific option labels that clearly differentiate between choices
- Keep labels concise while providing sufficient context for decision-making

**Option labels:**
- **Specific descriptions** - Use precise labels that clearly differentiate between options
- **Positive phrasing** - Frame options positively rather than as negatives when possible
- **Consistent structure** - Use parallel phrasing across options (e.g., all start with verbs)
- **Appropriate length** - Keep labels concise while providing sufficient context for decision-making

**Help text usage:**
- **Clarifying information** - Use individual option help text to explain complex or technical options
- **Impact description** - Explain what happens when an option is selected
- **Consistent formatting** - Maintain consistent help text patterns across similar groups
- **Complementary content** - Ensure help text adds value rather than repeating the label

### Accessibility

**What the design system provides:**
- Semantic HTML structure with proper fieldset/legend grouping and individual checkbox labeling
- Full keyboard navigation support and screen reader announcements for group context and state changes
- High contrast support and focus management with visible focus indicators and logical tab order

**Development responsibilities:**
- Provide descriptive group labels and meaningful option labels for screen reader users
- Ensure the `name` prop creates proper form association for all checkboxes in the group
- Handle validation errors with clear, actionable feedback announced to screen readers
- Provide stable `value` arrays that don't cause unnecessary re-renders or focus loss

**Design responsibilities:**
- Provide sufficient color contrast for all states, labels, and help text across themes
- Design clear visual hierarchy showing relationship between group label and individual options
- Ensure focus indicators are clearly visible and Select All checkbox is visually distinct but part of the same group
