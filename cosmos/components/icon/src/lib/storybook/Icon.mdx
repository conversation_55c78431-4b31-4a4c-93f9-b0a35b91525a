import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import * as IconStories from './Icon.stories';
import baseIcons from './assets/base-icons.png';
import statusvsactions from './assets/status-vs-action.png';
import warningIcons from './assets/warning-icons.png';
import deletevsremove from './assets/delete-vs-remove.png';


<Meta of={IconStories} />

<Title />

<Description />

<Primary />

<Controls of={IconStories.Playground} />

## Import

```jsx
import { Icon } from '@cosmos/components/icon';
```

## Props

The structure of the variant matrix here is based on levels:

- base icon
- props on the Icon component
    - backgroundType
    - colorScheme
    - size

### Base icons

<img src={baseIcons} alt="Base icons" />

### `backgroundType`

The `backgroundType` prop controls the shape of the Icon's wrapper, padding, and if the wrapper has a visible background color. Minimal will be used in most cases, though the circle and square options have been used in accordions and other containing components to show an overall status and grab attention where the component body give the fill context.

<Canvas of={IconStories.BackgroundType} />

### `colorScheme`

The `colorScheme` prop provides a valid, themed combination of icon color and background color. Color is generally handled by the components that the Icon is embedded in, but the colors align with our core communication use cases in the product.

<Canvas of={IconStories.ColorScheme} />

### `size`

The `size` prop is based on the Cosmos' font-size tokens. Size is generally handled by the components that the Icon is embedded in, but if you are building something new or exploring a new component, there are 8 levels of icon size available.

<Canvas of={IconStories.Size} />

## 🟢 When to use the component

- They **clarify meaning** or reinforce a label (e.g. trash icon for “Delete”).
- The icon is **globally recognizable** and adds clarity (e.g. search, close, settings).
- The action needs **quick visual scannability** (e.g. toolbar or status indicators).

## ❌ When not to use the component

- They're **purely decorative** and add no functional value.
- They **duplicate a clear label** without enhancing it.
- They are added **“just to fill space”** or for stylistic consistency.
- You're using **multiple icons of similar shape/weight** that compete for attention.

## 🛠️ How it works

Icons are used to provide visual cues to users and help to convey meaning quickly and efficiently. They allow users to navigate through an interface with ease and help to make the overall experience more intuitive.
Icons draw attention and aid recognition — but when overused, they create visual noise. Use them with intention to support clarity, not compete for it.

#### Avoid icon overload

Icons are powerful tools for adding visual emphasis, but they should be used **with purpose**.

- **Icons draw attention** — and too many can compete with each other.
- When everything is emphasized, **nothing stands out**.
- Overuse creates **visual noise**, making it harder for users to scan, locate, or act.

#### Content design tip

If an icon and label say the same thing, **remove the icon** unless it improves recognition or speed.

### Usability

**Core functionality:**
- **Visual communication** - Provide immediate visual cues and reinforce actions
- **Status indication** - Communicate states like success, warning, or error
- **Navigation aid** - Help users quickly scan and locate functions
- **Space efficiency** - Convey meaning in minimal space

**Background types:**
- **Minimal** - Icon only, no background (default)
- **Round** - Circular background with padding for emphasis
- **Square** - Square background for contained contexts

**Color schemes:**
- **Neutral** - Gray icons for general use (default)
- **Primary** - Blue icons for primary actions
- **Success/Warning/Critical** - Semantic colors for states
- **Faded** - Muted icons for less prominent elements
- **Inverted** - White icons for dark backgrounds
- **Inherit** - Uses current text color

**Size guidelines:**
- **50** (8px) - Very compact spaces
- **100** (14px) - Default size, inline text
- **200** (16px) - Standard buttons
- **300** (20px) - Prominent buttons
- **400** (26px) - Larger elements
- **500** (28px) - Section headers
- **600** (34px) - Empty states

### Common icon confusion

#### Status vs Actions

For use cases that have a status and an action that are represented by a similar icon, use contained icons for status and non-contained icons for the action.

<img src={statusvsactions} alt="Status vs Actions" />

#### Delete vs Remove

**Delete** is a destructive action that removes something at its source, such as an individual object, a form where the fields aren’t being pulled from elsewhere, etc. Delete is generally the opposite of create. Because delete is destructive and typically non-recoverable, we want to represent the danger of this action with the key color of red. So use in a button for a delete action should use the ‘danger’ colorScheme.

**Remove** is a destructive action that removes an association but does not delete. Remove is the opposite action of add.

<img src={deletevsremove} alt="Delete vs Remove" />

#### Warning icons

Warning icons get pointier as the urgency increases.

<img src={warningIcons} alt="Warning icons" />

#### Components with an icon as a property

If an icon is displayed in a component and it does not have an icon property assigned, then it is not allowed to change. Only components with the that have a built in icon swap property are allowed to be changed.

### Content

- Choose universally recognized icons with established meanings
- Match icon visual metaphor to its function
- Use backgrounds for emphasis on important actions
- Match semantic colors to appropriate states

### Accessibility

**What the design system provides:**
- Semantic SVG structure with `aria-hidden` attribute (decorative by default)
- High contrast color schemes meeting accessibility standards
- Scalable graphics that work across zoom levels and screen sizes
- Consistent sizing system compatible with browser accessibility preferences

**Development responsibilities:**
- Provide context through surrounding text or `aria-label` for interactive icons
- Don't rely on color alone - ensure meaning through shape and context
- Ensure proper focus states and keyboard navigation for clickable icons
- Use familiar icons and provide text labels when meaning might be unclear

**Design responsibilities:**
- Maintain sufficient contrast between icon colors and backgrounds
- Ensure icons remain recognizable across all size variants
- Use consistent stroke weights and visual style across all icons
- Design for colorblind users through shape and pattern, not just color
