import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import * as TextareaFieldStories from './TextareaField.stories';

<Meta of={TextareaFieldStories} />

<Title />

<Banner
    severity="education"
    title="Use Web Wrappers for Form Fields"
    body={
        <>
            Avoid using form field components directly from Cosmos. Instead, use
            our web wrappers for optimized functionality and consistent
            integration.{' '}
            <Link
                href="/?path=/docs/form-components-about--docs#web-wrappers"
                label="Click here for more details"
                isExternal
            />
            .
        </>
    }
/>

<Description />

<Primary />

<Controls of={TextareaFieldStories.Playground} />

## Import

```jsx
import { TextareaField } from '@cosmos/components/textarea-field';
```

## 🟢 When to use the component

- **Multi-line text input** - When users need to enter longer text content that spans multiple lines (comments, descriptions, feedback)
- **Form text areas** - For collecting detailed information in forms with proper labeling, validation, and accessibility
- **Character-limited input** - When you need to display character counts and enforce maximum length constraints
- **Resizable text entry** - For content where users benefit from adjusting the input area size

## ❌ When not to use the component

- **Single-line input** - Use TextField for short, single-line text entries like names or email addresses
- **Rich text editing** - Use a rich text editor when users need formatting capabilities (bold, italic, links)
- **Structured data entry** - Use specific field types (Select, Checkbox, etc.) for predefined options
- **File content editing** - Use specialized code editors for editing file contents or code
- **Read-only text display** - Use Text component for displaying non-editable content

## 🛠️ How it works

The TextareaField component combines FormField structure with Textarea functionality, providing a complete multi-line text input with optional character counting and validation support.

**Core functionality:**
- **Multi-line input** - Native textarea element supporting line breaks and vertical text expansion with configurable `rows` (defaults to 9)
- **Vertical resizing** - Built-in CSS `resize: vertical` capability allowing users to adjust textarea height
- **Controlled input** - Uses `value` prop with `onChange` callback for controlled text state management

**Component structure:**
- **FormField wrapper** - Provides consistent labeling, help text, validation feedback, and form integration using `renderInput` pattern
- **Textarea element** - Core StyledTextarea with proper styling, event handling, and ARIA attributes
- **Character counter** - Optional Text component displaying `{textCount}/{maxCharacters}` with color feedback (`critical` when exceeded, `faded` otherwise)
- **Validation styling** - Dynamic border colors via `feedbackType` prop mapping to `FEEDBACK_BORDER_COLORS`

**State and behavior:**
- **Character tracking** - Internal `textCount` state in Textarea component, synchronized with value changes via `getInitialTextCount` helper
- **Input validation** - Integrates with FormField's feedback system for error/success states through `feedbackType`
- **Read-only support** - `readOnly` prop applies read-only styles with disabled background and border interactions
- **Event handling** - Supports `onChange`, `onFocus`, `onBlur`, `onKeyDown`, and `onClick` with proper event propagation

### Usability

**Text input patterns:**
- **Multi-line support** - Handles line breaks, paragraphs, and longer content naturally with proper character counting including line breaks
- **Resize functionality** - Users can vertically resize the textarea for better content visibility
- **Keyboard navigation** - Full keyboard support including tab navigation and standard text editing shortcuts
- **Paste handling** - Supports pasting content with automatic character count updates

**Character management:**
- **Visual feedback** - Character counter changes from `faded` to `critical` color scheme when exceeding limits
- **Flexible limits** - Character counting only appears when `maxCharacters` prop is provided
- **Accurate counting** - Character count reflects actual textarea content including line breaks and special characters

### Content

**Label and guidance:**
- **Descriptive labels** - Use clear, specific labels that describe the expected content type
- **Help text usage** - Provide examples, formatting guidance, or context through FormField's `helpText` prop
- **Placeholder text** - Use `placeholder` to show format examples or brief instructions
- **Character limits** - Set appropriate `maxCharacters` based on content requirements and storage constraints

**Content expectations:**
- **Purpose clarity** - Make the intended use clear through labeling and help text
- **Format guidance** - Explain any specific formatting requirements or restrictions
- **Length expectations** - Use character limits and help text to communicate expected content length
- **Error messaging** - Provide clear feedback through FormField's `feedback` prop when content doesn't meet requirements

### Accessibility

**What the design system provides:**
- FormField integration with proper label associations (`aria-labelledby`), help text relationships (`aria-describedby`), and validation feedback
- Semantic textarea element with appropriate ARIA attributes and relationships
- Keyboard navigation support with standard text editing interactions and logical tab order
- Screen reader compatibility with proper announcements for labels, help text, and character counts
- Focus management with visible focus indicators and logical navigation flow
- High contrast support maintaining usability across different visual accessibility settings

**Development responsibilities:**
- Provide meaningful `formId`, `name`, and `label` props for proper form association and identification
- Use descriptive labels and help text that clearly communicate the textarea's purpose and requirements
- Implement proper error handling and validation feedback through FormField's `feedback` system
- Choose appropriate `maxCharacters` values that balance user needs with technical constraints
- Handle event callbacks (`onChange`, `onFocus`, `onBlur`) appropriately for form state management

**Design responsibilities:**
- Ensure sufficient contrast ratios for textarea text, borders, labels, and character counter
- Design error states that clearly communicate validation issues through multiple visual cues
- Maintain consistent spacing and alignment between textarea, labels, help text, and character counter
- Ensure responsive behavior that maintains usability across different screen sizes
- Design character counter states that provide clear visual feedback without relying solely on color

