// import type {
//     PanelBodyProps,
//     PanelControlsProps,
//     PanelFooterProps,
//     PanelHeaderProps,
// } from '.';

/**
 * Root Panel component Workaround.
 *
 * This is a workaround for storybook because we don't have single root component to build stories off of.
 * - Combining interfaces so storybook can identify required props
 * - Shell of a component for main storybook story.
 *
 */

// type PanelProps = PanelControlsProps &
//     PanelHeaderProps &
//     PanelBodyProps &
//     PanelFooterProps;

/**
 * The Panel component provides a flexible container with header, body, and footer sections for displaying contextual information and actions without losing the user's current context.
 * - Responds to show info (high level/drive-thru) about a selection that you make e.g. Object. The content for the selected object is the same across all of the app.
 * - Actions associated to the panel allows you go into that object’s detail page.
 * - Panel allows you to change top level info about the object and will reflect in other places of the app.
 * - Panels can be served up in all pages.
 *
 * [Panel in Figma](https://www.figma.com/design/LhHaXLLZ2iyNGFprqPmQKp/Cosmos--Modules?node-id=2356-54579&t=wVzqUNKjP1Zd1yAw-4).
 */

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- need this
export const Panel = () => <div data-testid="Panel" data-id="ZBySpleG" />;
