import { Meta } from '@storybook/addon-docs/blocks';
import {
  dataBarImage,
  dataDonutLargeImage,
  dataDonutMediumImage,
  dataGaugeImage,
  dataHeatmapImage,
  dataLegendImage,
  dataLineImage,
  dataMeterImage,
  dataPostureImage,
  paletteCategorizedImage,
  paletteDivergingImage,
  paletteHighlightingImage,
  statBlockImage,
  tagsImage,
} from './assets/data-visualization';

<Meta title="Patterns/Data Visualization" />

# **Data Visualization**

Use data visualization to help users make faster, more confident decisions by turning raw data into patterns, trends, and anomalies. Our platform prioritizes automation, so data visuals should reduce the cognitive burden—surfacing *actionable* insight with minimal effort.

## **Usage guidelines**

- Use to **communicate patterns** and highlight **meaningful trends or outliers**
- Provide visuals that enable quick scanning and support decision-making
- Always ask: **“What action does this visualization inspire?”**
- Ensure every visualization adds clarity—not clutter—to the experience
- Avoid visuals that require deep analysis or interpretation unless it’s the primary goal

### **When to use**

- Dashboards summarizing risk, compliance, or governance
- Indexes (tables, galleries) that benefit from a visual overview or embedded insight
- Within reports designed to share progress or value with stakeholders
- Inside table cells when space is limited and trends matter more than details

### **When not to use**

- When precise values are more useful than patterns → use a table
- If color is the *only* signal → this excludes some users
- When space is too constrained to present usable information
- For placeholder charts that communicate no new value

### **Best practices**

| **✅ Do** | **🚫 Don’t** |
| --- | --- |
| Use charts that support specific decisions | Don’t separate charts and controls across the layout |
| Use high-contrast, accessible color palettes | Don’t put a chart on the page outside of a card or a table cell |
| Group visualizations in cards with clear labels |  |

## **Key chart types**

### DataBar

<img src={dataBarImage} alt="Databar" />

- Compare categories or show value changes over time
- Useful when time intervals are inconsistent
- Choose horizontal or vertical based on axis legibility

### DataLine

<img src={dataLineImage} alt="Dataline" />

- Show trends across continuous time
- Use when pattern, direction, or rate of change matters
- Avoid line charts with too few points or sparse data

### **DataDonut**

<img src={dataDonutLargeImage} alt="Datadonut large" />

<img src={dataDonutMediumImage} alt="Datadonut medium" />

- Show parts of a whole, especially when highlighting a specific category
- Best when there are no more than 3–6 segments
- Use small for inline stats, medium in tables, large in dashboards
- DataLegend is usually necessary with DataDonut to help delineate the information and provide an accessible alternative
- We don’t use pie charts: donuts have better clarity, better data-to-ink ratio, and everyone likes the person that brings donuts to the meeting 🍩

### DataGauge and DataMeter

<img src={dataGaugeImage} alt="Datagauge" />

<img src={dataMeterImage} alt="Datameter" />

- Show a single value within a range (e.g., readiness or usage)
- Prefer DataGauge when the value represents a milestone tied to a core Drata value proposition (e.g., framework readiness)
- Can indicate sentiment: green is good, red is concerning, rainbow is the achievement of a goal
- Use Diverging palette for thresholds, Categorized palette for neutral data
- Never use as a progress bar (different semantics and accessibility expectations)
- **NOTE:** DataMeter *can* be used inline and does not need to be contained in a card like other larger charts like DataBar and DataGauge

### **Tags**

<img src={tagsImage} alt="Tags" />

- Show quantity and category
- Use when space is tight and minimal representation is sufficient
- Add shape/icon variation when necessary to support colorblind accessibility
- If data density exceeds clarity, roll up to a chart instead

### **DataHeatmap**

<img src={dataHeatmapImage} alt="Dataheatmap" />

- Exclusive to aggregated risk scoring
- Use Diverging palette to convey risk intensity
- When focusing on one area, downplay surrounding cells with the moderate palette

### **DataPosture**

<img src={dataPostureImage} alt="Dataposture" />

- One stacked horizontal bar showing proportional distribution
- Used for visualizing breakdowns mostly in risk
- Use Diverging palette to convey risk intensity

### **StatBlock**

<img src={statBlockImage} alt="Stablock" />

- High-impact single values with or without trend indicators
- Use when a single number says more than a graph
- Often used in dashboard cards or inline highlights
- Can be used as a button to link the user to relevant information or as a filter for a table on the same screen

### **DataLegend**

<img src={dataLegendImage} alt="Datalegend" />

- Always include if the visual relies on color, shape, or pattern
- Keep close to the visual
- Consider interactivity for filtering or toggling where relevant
- Horizontal arrangements are only used above charts. Vertical can be used to the right or below

## **Color**

### **Highlighting Palette**

<img src={paletteHighlightingImage} alt="highlight palette" />

- Use for sharp state boundaries (e.g., pass/fail, high risk/low risk)
- Red = critical; Green = secure; Yellow = attention

### **Diverging Palette**

<img src={paletteDivergingImage} alt="diverging palette" />

- Use to show a scale (e.g., low → high impact)
- Ideal for sentiment or progress toward a threshold

### **Categorized Palette**

<img src={paletteCategorizedImage} alt="categorized palette" />

- **Using the color wheel (vertically on the chart above)**
    - Use to differentiate distinct groups or categories
    - Choose well-separated hues to ensure clarity
- **Monochromatic (horizontally on the chart above)**
    - Use tints of a single hue to compare related subcategories
    - Maintains harmony without losing differentiation

### **Accessibility considerations**

- Never rely on color alone to communicate meaning
- Support every color with clear labels or legends
- Maintain high contrast for readability
- If a visualization cannot be made legible *and* accessible, consider replacing it with a table

## **Tables & Lists**

Tables and Lists are data visualizations too. Use when users need details, precision, or full control. See [Data display](https://www.notion.so/Data-display-1e0095aed4f1801dafd8ca1dddab0195?pvs=21) for more information

---

**FUTURE GOALS**

### **Progress bars**

- Use for known-duration tasks like onboarding or questionnaires
- Do **not** use meters or gauges for this—use semantic progress elements

### **Visual vocabulary audit**

- Align chart components and tokens across product
- Standardize use cases for visual elements to minimize ambiguity

### **Placeholder components**

- Use skeletons for layout previews (loading state)
- Avoid placeholder charts that show meaningless values