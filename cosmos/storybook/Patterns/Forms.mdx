import { Meta } from '@storybook/addon-docs/blocks';

import {
  checkboxGroupImage,
  comboboxMultiImage,
  comboboxMultiDropdownImage,
  comboboxSingleImage,
  comboboxSingleDropdownImage,
  radioGroupImage,
  selectImage,
  selectionImage,
  stackingImage
} from './assets/forms';

<Meta title="Patterns/Forms" />

# Forms

- Selection

    [Radio group](https://www.notion.so/Radio-group-1ca095aed4f1807e836acd34634bd836?pvs=21)

<img src={selectionImage} alt="Selection types" />

- Layout

    [Max width for form fields](https://www.notion.so/Max-width-for-form-fields-189095aed4f180619221f249ffb2c93c?pvs=21)

- Edit cards

## When to Use

- To collect information from users—whether creating, editing, or configuring.
- Anywhere structured user input is needed (e.g., onboarding, modals, wizards).

## Layout & Structure

### Max Width

- All forms should max out at **680px** to ensure readability and reduce cognitive load.
- Center forms on the page or within containers when possible.

### Stacking

- Stack fields vertically using consistent spacing.
- Default vertical spacing between fields: **16px** or use the `dimension.xl` token.
- Use a **Divider** component when:
    - Switching between clearly distinct sections within the same form (e.g., "Company Info" → "Team Setup").
    - There's a change in context, input type, or required attention.
- Avoid dividers between every field—only use when the separation adds clarity.

<img src={stackingImage} alt="Stacking" />

### Field Widths

- Inputs should stretch to 100% of the form container unless explicitly defined otherwise.

### Alignment

- Labels should be top-aligned by default.
- Left-align form content for scanability.

## Selection Components

Use the right input for the job to help users make accurate selections with the least effort.

### Combobox (Single-Select)

- Use when:
    - There are many options (10+).
    - Users may benefit from searching.
- Behavior:
    - Dropdown opens on focus or click.
    - Supports keyboard navigation.
- Example: Assigning a user from a long list.

<img src={comboboxSingleImage} alt="Combobox single select" />

<img src={comboboxSingleDropdownImage} alt="Combobox single select dropdown" />


### Combobox (Multi-Select)

- Use when:
    - Users can select more than one item from a long or searchable list.
    - You need tag-style visual feedback for selected items.
- Behavior:
    - Selected items appear as tokens inside the input.
    - Dropdown remains open after selection (optional).
- Example: Assigning multiple frameworks.

<img src={comboboxMultiImage} alt="Combobox multi select" />

<img src={comboboxMultiDropdownImage} alt="Combobox multi select dropdown" />


### Select

- Use when:
    - There are 10 options or fewer.
    - No search is needed.
    - Only one option can be selected.
- Behavior:
    - Simple dropdown behavior.
- Example: Selecting a priority level or region.

<img src={selectImage} alt="Select field" />


### Radio Group

- Use when:
    - All options (2–5 ideally) can be shown at once.
    - The user should see all options before deciding.
- Behavior:
    - Only one option may be selected.
- Example: Choosing between "Yes" / "No" or pricing tiers.

<img src={radioGroupImage} alt="Radio group" />


### Checkbox Group

- Use when:
    - Multiple options can be selected.
    - Options are not mutually exclusive.
- Behavior:
    - Each checkbox operates independently.
- Example: Selecting notification preferences.

<img src={checkboxGroupImage} alt="Checkbox group" />


## UX Guidelines

- Always indicate when fields are optional with the optional prop by the label.
- Group related fields using visual cues like headings or cards.
    - Use editing prop when editing in cards  the cards we have
- Keep field labels short but clear. Use tooltips for extra context.
- Use helper text for clarification, especially for selections.
- Keep error and confirmation messages short, helpful, and tied to the field.