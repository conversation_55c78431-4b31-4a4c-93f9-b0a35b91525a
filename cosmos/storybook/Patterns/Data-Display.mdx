import { Meta } from '@storybook/addon-docs/blocks';

import {
accordionListImage,
dataTableImage,
KVPImage,
stackedListImage
} from './assets/data-display';

<Meta title="Patterns/Data Display" />


# **Data Display Patterns**

Use this guide to pick the best component for presenting information clearly and consistently.

## **Key Value Pairs**

**Best for:**

- Small sets of labeled details.
- Simple metadata or attributes.
- No need for dynamic expand/collapse.

**When to use:**

- ~1–10 pairs.
- Values are short text or links.
- Typically static (e.g., product specs, profile info).

**When not to use:**

- Repeating items → use Stacked List.
- Structured grid → use Data Table.

<img src={KVPImage} alt="Key value pairs" />

## **Stacked List**

**Best for:**

- Lists of similar items.
- Each item has the same layout: label, details, optional actions.
- Flat structure; no hierarchy.

**When to use:**

- ~3–20 items.
- Scannable rows, optional multi-column content.
- Inline actions per row.
- Optional Pagination.

**When not to use:**

- Nested or collapsible content → use Accordion List.
- Heavy data grid → use Data Table.

<img src={stackedListImage} alt="Stacked list" />

## **Accordion List**

**Best for:**

- Lists where each item has extra, toggleable details.
- Grouping related content in a vertical stack.
- Collapsing dense information to keep pages clean.

**When to use:**

- ~3–10 expandable items.
- Content per item varies in length.
- User needs to open and close sections independently.
- Great for Controls, policies, or any other Drata object.

**When not to use:**

- Flat, short items → use Stacked List.
- Large datasets → use Data Table.

<img src={accordionListImage} alt="Accordion list" />

## **Data Table**

**Best for:**

- Dense, structured data in rows and columns.
- Sorting, filtering, bulk actions.
- Comparing values across multiple columns.

**When to use:**

- Numeric or analytical content.
- Needs export or copy functionality.

**When not to use:**

- Simple labeled facts → use Key Value Pairs.
- Short list without headers → use Stacked List.
- Expandable nested content → consider Accordion List plus Data Table inside.

<img src={dataTableImage} alt="Datatable" />

## **✅ Quick Decision Table**

| **Context** | **Use** |
| --- | --- |
| Small block of labeled facts | **Key Value Pairs** |
| Flat list of similar items | **Stacked List** |
| Collapsible grouped content | **Accordion List** |
| Structured grid data | **Data Table** |

## **🧑‍🎨 General Tips**

- Pick the simplest option that fits the content.
- Don’t overload a page with multiple data display patterns side-by-side.
- Use clear headings and consistent spacing.