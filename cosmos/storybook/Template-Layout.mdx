import { Meta } from '@storybook/addon-docs/blocks';
import fullTemplate from './template-layouts/assets/full-template.png';
import centerTemplate from './template-layouts/assets/center-template.png';
import interstitialTemplate from './template-layouts/assets/interstitial-template.png';

<Meta title="Macro Layout Components/Macro Layout Guide" />

# Template types

- [Full & Centered](https://www.figma.com/design/LhHaXLLZ2iyNGFprqPmQKp/Cosmos--Modules?node-id=2498-67332&t=wsvauc4t3rMeZB74-4):
Heavy hitter—this template accounts for 98% of Drata with its two layout options.
- [Interstitial](https://www.figma.com/design/LhHaXLLZ2iyNGFprqPmQKp/Cosmos--Modules?node-id=2498-67384&t=wsvauc4t3rMeZB74-4):
Used rarely, this template supports mostly pre-authorization experiences.

## Full & Centered
Our go-to template—Full and centered template supports index pages, creation experiences, and everything in-between.

### Full layout

Full layout allows for the maximum surface to work in Drata. This variation will support our complex dashboard, table, gallery, and detail page experiences, as well as some unique outliers like the Workflow preview and builder canvas. You need space?—you need the full layout.

<img src={fullTemplate} alt="add full template picture" />

**Examples of full layout experiences:**

- Controls index page with table.
- Control detail page with all tabs.

### Centered layout

With a max content width of 820—centered layout has a smaller surface area that provides focus for our users while creating and adding objects, or filling out long forms. Centered layout ensures that the content identifiers in the page header will always stay aligned and accessible while working through the activity in the page content. You need focus?—you need the centered layout.

<img src={centerTemplate} alt="add centered template picture" />

**Examples of centered layout experiences:**

- add asset
- add evidence
- add former personnel
- add questionnaire
- add risk
- create api key
- create connection
- create control
- create field

- create formula
- create framework
- create pipeline key
- create policy
- create review period
- create task
- create test
- create ticket rule

- create current vendor
- create prospective vendor
- create vendor security review
- create workflow
- create workspace
- start audit

## Interstitial

This is a rarely used but necessary template that supports login and auth screens, full screen error states, and likely some of our external user experiences in the future.

<img src={interstitialTemplate} alt="add interstitial template picture" />

- The background is an open canvas for graphics, animation, etc.
- The content area can be left-aligned or centered and is also open to accept any layout.
- Because of the open layout, it is up to the team implementing to address any responsiveness or layout concerns when targeting small viewports like mobile devices.

**Example interstitial template experiences:**

- login
- accept terms
- 404 error screen

*This documentation is part of the Cosmos Design System. For more information about individual components, see their specific documentation pages.*
